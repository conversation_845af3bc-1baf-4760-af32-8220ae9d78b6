# 监测数据列表功能说明

## 功能概述

本项目已经实现了完整的监测数据列表功能，包括数据埋点、统计和查询等功能。

## 核心功能

### 1. 数据埋点 (PetStatService.AddPetStatLog)
- **功能**: 记录用户行为事件
- **API**: `POST /marketing-app/awen/api/pet-stat/log`
- **支持的指标类型**:
  - 1: 阿闻首页弹窗点击uv
  - 2: 阿闻首页banner点击uv
  - 3: 阿闻首页浮标点击uv
  - 4: 小闻首页广告点击uv
  - 5: 贵族首页弹窗点击uv
  - 6: 贵族首页banner点击uv
  - 7: 贵族活动主页访问的pv
  - 8: 贵族活动主页访问uv
  - 9: 作品助力页面访问pv
  - 10: 作品助力页面访问uv
  - 11: 生成贵族创作图的用户数
  - 12: 分享的用户数
  - 13: 贵族宠物图分享总次数
  - 14: 好友助力总次数
  - 15: 达到5票的用户数
  - 16: 达到25票的用户数

### 2. 监测数据列表查询 (PetActivityService.ListPetStats)
- **功能**: 按日期分组查询监测数据统计
- **API**: `GET /marketing-app/awen/manager/pet_stat/list`
- **支持功能**:
  - 分页查询
  - 日期范围筛选
  - 数据导出

### 3. 定时统计任务 (PetStatService.StatVoteUserCountPerDay)
- **功能**: 每日自动统计投票用户数
- **执行时间**: 每分钟执行一次
- **统计指标**: 达到5票和25票的用户数

## 数据库表结构

### pet_stat (监测数据统计表)
```sql
CREATE TABLE pet_stat (
    id INT AUTO_INCREMENT PRIMARY KEY,
    daliy_date VARCHAR(10) NOT NULL COMMENT '日期',
    metric_type INT NOT NULL COMMENT '指标编码',
    num INT NOT NULL DEFAULT 0 COMMENT '事件数值',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### pet_stat_log (监测数据事件流水表)
```sql
CREATE TABLE pet_stat_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    metric_type INT NOT NULL COMMENT '指标类型',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    work_code VARCHAR(50) DEFAULT '' COMMENT '作品编号',
    user_id VARCHAR(50) DEFAULT '' COMMENT '用户ID',
    unique_id VARCHAR(100) DEFAULT '' COMMENT '唯一ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## API 接口说明

### 1. 埋点上报接口
```
POST /marketing-app/awen/api/pet-stat/log
Content-Type: application/json

{
    "unique_id": "UNIQUE001",
    "user_id": "USER001", 
    "metric_type": 1,
    "work_code": "WORK001"
}
```

### 2. 监测数据列表接口
```
GET /marketing-app/awen/manager/pet_stat/list?page_index=1&page_size=10&date_start=2024-01-01&date_end=2024-12-31&is_export=false
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "total": 3,
    "data": [
        {
            "daliy_date": "2024-08-15",
            "metric1": 120,
            "metric2": 85,
            "metric3": 95,
            "metric4": 78,
            "metric5": 156,
            "metric6": 134,
            "metric7": 2340,
            "metric8": 1890,
            "metric9": 1567,
            "metric10": 1234,
            "metric11": 89,
            "metric12": 67,
            "metric13": 234,
            "metric14": 456,
            "metric15": 23,
            "metric16": 8
        }
    ]
}
```

## 快速开始

### 1. 环境准备
```bash
# 安装依赖
go mod tidy

# 配置数据库连接 (修改 appsetting.toml)
[mysql]
eshop="root:password@tcp(localhost:3306)/eshop?charset=utf8mb4&parseTime=True&loc=Local"

[redis]
PassAddr="localhost:6379"
```

### 2. 初始化数据库
```bash
# 执行数据库初始化脚本
mysql -u root -p < init_monitoring_data.sql
```

### 3. 启动服务
```bash
# 方式1: 启动完整的marketing-app
go run applications/marketing-app/main.go

# 方式2: 启动简化测试版本
go run test_marketing_app.go
```

### 4. 测试功能
```bash
# 运行测试程序
go run test_monitoring_data.go
```

## 项目加载问题解决方案

根据你遇到的项目加载问题，主要原因是缺少完整的配置文件。已经为你：

1. **补充了配置文件** (`appsetting.toml`) - 添加了数据库和Redis配置
2. **创建了数据库初始化脚本** (`init_monitoring_data.sql`) - 包含表结构和测试数据
3. **提供了简化的测试版本** (`test_marketing_app.go`) - 可以独立运行，不依赖完整的配置
4. **创建了功能测试程序** (`test_monitoring_data.go`) - 用于验证API功能

## 注意事项

1. **数据库配置**: 请根据实际环境修改 `appsetting.toml` 中的数据库连接字符串
2. **Redis配置**: 如果没有Redis，可以注释掉相关代码
3. **端口冲突**: 默认使用8153端口，如有冲突请修改
4. **权限验证**: 生产环境需要添加JWT认证中间件

## 文件说明

- `applications/marketing-app/controllers/pet_activity_controller.go` - 监测数据API控制器
- `services/marketing-service/services/pet_activity_service.go` - 监测数据业务逻辑
- `services/marketing-service/services/pet_stat_service.go` - 埋点和统计服务
- `view-model/marketing-vo/pet_contestant_vo.go` - 数据模型定义
- `domain/marketing-po/pet_stat.go` - 数据库实体模型
- `applications/cron-job/async-task/job.go` - 定时任务配置

监测数据列表功能已经完整实现，你可以根据需要进行测试和使用！
