package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

// PetStatListReq 监测数据列表请求参数
type PetStatListReq struct {
	PageIndex int    `json:"page_index"`
	PageSize  int    `json:"page_size"`
	DateStart string `json:"date_start"` // yyyy-MM-dd
	DateEnd   string `json:"date_end"`
	IsExport  bool   `json:"is_export"`
}

// PetStatListItem 监测数据列表项
type PetStatListItem struct {
	DaliyDate string `json:"daliy_date"`
	Metric1   int    `json:"metric1"`  // 阿闻首页弹窗点击uv
	Metric2   int    `json:"metric2"`  // 阿闻首页banner点击uv
	Metric3   int    `json:"metric3"`  // 阿闻首页浮标点击uv
	Metric4   int    `json:"metric4"`  // 小闻首页广告点击uv
	Metric5   int    `json:"metric5"`  // 贵族首页弹窗点击uv
	Metric6   int    `json:"metric6"`  // 贵族首页banner点击uv
	Metric7   int    `json:"metric7"`  // 贵族活动主页访问的pv
	Metric8   int    `json:"metric8"`  // 贵族活动主页访问uv
	Metric9   int    `json:"metric9"`  // 作品助力页面访问pv
	Metric10  int    `json:"metric10"` // 作品助力页面访问uv
	Metric11  int    `json:"metric11"` // 生成贵族创作图的用户数
	Metric12  int    `json:"metric12"` // 分享的用户数
	Metric13  int    `json:"metric13"` // 贵族宠物图分享总次数
	Metric14  int    `json:"metric14"` // 好友助力总次数
	Metric15  int    `json:"metric15"` // 达到5票的用户数
	Metric16  int    `json:"metric16"` // 达到25票的用户数
}

// PetStatListRes 监测数据列表响应
type PetStatListRes struct {
	Code    int               `json:"code"`
	Message string            `json:"message"`
	Data    []PetStatListItem `json:"data"`
	Total   int               `json:"total"`
}

func main() {
	fmt.Println("=== 监测数据列表功能测试 ===")
	
	// 测试参数
	req := PetStatListReq{
		PageIndex: 1,
		PageSize:  10,
		DateStart: "2024-01-01",
		DateEnd:   "2024-12-31",
		IsExport:  false,
	}
	
	// 构建请求URL
	baseURL := "http://localhost:8153/marketing-app/awen/manager/pet_stat/list"
	params := url.Values{}
	params.Add("page_index", fmt.Sprintf("%d", req.PageIndex))
	params.Add("page_size", fmt.Sprintf("%d", req.PageSize))
	params.Add("date_start", req.DateStart)
	params.Add("date_end", req.DateEnd)
	params.Add("is_export", fmt.Sprintf("%t", req.IsExport))
	
	fullURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())
	
	fmt.Printf("请求URL: %s\n", fullURL)
	
	// 发送HTTP请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Get(fullURL)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}
	
	fmt.Printf("响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("响应内容: %s\n", string(body))
	
	// 解析JSON响应
	var result PetStatListRes
	if err := json.Unmarshal(body, &result); err != nil {
		fmt.Printf("解析JSON失败: %v\n", err)
		return
	}
	
	// 显示结果
	fmt.Printf("\n=== 解析结果 ===\n")
	fmt.Printf("响应码: %d\n", result.Code)
	fmt.Printf("消息: %s\n", result.Message)
	fmt.Printf("总数: %d\n", result.Total)
	fmt.Printf("数据条数: %d\n", len(result.Data))
	
	if len(result.Data) > 0 {
		fmt.Printf("\n=== 监测数据详情 ===\n")
		for i, item := range result.Data {
			fmt.Printf("第%d条数据:\n", i+1)
			fmt.Printf("  日期: %s\n", item.DaliyDate)
			fmt.Printf("  阿闻首页弹窗点击uv: %d\n", item.Metric1)
			fmt.Printf("  阿闻首页banner点击uv: %d\n", item.Metric2)
			fmt.Printf("  阿闻首页浮标点击uv: %d\n", item.Metric3)
			fmt.Printf("  小闻首页广告点击uv: %d\n", item.Metric4)
			fmt.Printf("  贵族首页弹窗点击uv: %d\n", item.Metric5)
			fmt.Printf("  贵族首页banner点击uv: %d\n", item.Metric6)
			fmt.Printf("  贵族活动主页访问pv: %d\n", item.Metric7)
			fmt.Printf("  贵族活动主页访问uv: %d\n", item.Metric8)
			fmt.Printf("  作品助力页面访问pv: %d\n", item.Metric9)
			fmt.Printf("  作品助力页面访问uv: %d\n", item.Metric10)
			fmt.Printf("  生成贵族创作图的用户数: %d\n", item.Metric11)
			fmt.Printf("  分享的用户数: %d\n", item.Metric12)
			fmt.Printf("  贵族宠物图分享总次数: %d\n", item.Metric13)
			fmt.Printf("  好友助力总次数: %d\n", item.Metric14)
			fmt.Printf("  达到5票的用户数: %d\n", item.Metric15)
			fmt.Printf("  达到25票的用户数: %d\n", item.Metric16)
			fmt.Printf("\n")
		}
	}
}
