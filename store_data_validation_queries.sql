-- =====================================================
-- 数据删除前后校验查询语句
-- 用于验证 store_id=1 相关数据的删除操作
-- =====================================================

-- =====================================================
-- 商品模块数据校验
-- =====================================================

-- 地址数据
SELECT COUNT(*) as address_count FROM upetmart.upet_address WHERE store_id = 1;

-- 品牌数据
SELECT COUNT(*) as brand_count FROM upetmart.upet_brand WHERE store_id IN (0,1);

-- 购物车数据
SELECT COUNT(*) as cart_count FROM upetmart.upet_cart WHERE store_id = 1;

-- 商品操作日志
SELECT COUNT(*) as goods_handlelog_count FROM upetmart.upet_goods_handlelog WHERE store_id = 1;

-- 商品图片
SELECT COUNT(*) as goods_images_count FROM upetmart.upet_goods_images WHERE store_id = 1;

-- 商品关联
SELECT COUNT(*) as goods_relevance_count FROM upetmart.upet_goods_relevance WHERE store_id = 1;

-- 商品关联SKU
SELECT COUNT(*) as goods_relevance_sku_count FROM upetmart.upet_goods_relevance_sku WHERE store_id = 1;

-- 商品主表
SELECT COUNT(*) as goods_count FROM upetmart.upet_goods WHERE store_id = 1;

-- 商品公共信息
SELECT COUNT(*) as goods_common_count FROM upetmart.upet_goods_common WHERE store_id = 1;

-- =====================================================
-- 分销数据校验
-- =====================================================

-- 分销支付数据
SELECT COUNT(*) as dis_pay_count FROM upetmart.upet_dis_pay WHERE store_id = 1;

-- =====================================================
-- 促销活动数据校验
-- =====================================================

-- 促销时间
SELECT COUNT(*) as p_time_count FROM upetmart.upet_p_time WHERE store_id = 1;

-- 限时促销
SELECT COUNT(*) as p_xianshi_count FROM upetmart.upet_p_xianshi WHERE store_id = 1;

-- 限时促销商品
SELECT COUNT(*) as p_xianshi_goods_count FROM upetmart.upet_p_xianshi_goods WHERE store_id = 1;

-- 优惠券
SELECT COUNT(*) as voucher_count FROM upetmart.upet_voucher WHERE voucher_store_id = 1;

-- 优惠券商品
SELECT COUNT(*) as voucher_goods_count FROM upetmart.upet_voucher_goods WHERE store_id = 1;

-- 运费模板及扩展（关联查询）
SELECT COUNT(*) as transport_count 
FROM upetmart.upet_transport r 
INNER JOIN upetmart.upet_transport_extend d ON r.id = d.transport_id 
WHERE r.store_id = 1;

-- =====================================================
-- 订单模块数据校验
-- =====================================================

-- 订单日志（关联查询）
SELECT COUNT(*) as order_log_count 
FROM upetmart.upet_order_log log 
INNER JOIN upetmart.upet_orders orders ON log.order_id = orders.order_id 
WHERE orders.store_id = 1;

-- 订单MQ日志（关联查询）
SELECT COUNT(*) as order_mqlog_count 
FROM upetmart.upet_order_mqlog log 
INNER JOIN upetmart.upet_orders orders ON log.om_order_sn = orders.order_sn 
WHERE orders.store_id = 1;

-- 退款退货及详情（关联查询）
SELECT COUNT(*) as refund_return_count 
FROM upetmart.upet_refund_return r 
INNER JOIN upetmart.upet_refund_detail d ON r.refund_id = d.refund_id 
WHERE r.store_id = 1;

-- VR退款及详情（关联查询）
SELECT COUNT(*) as vr_refund_count 
FROM upetmart.upet_vr_refund r 
INNER JOIN upetmart.upet_vr_refund_detail d ON r.refund_id = d.refund_id 
WHERE r.store_id = 1;

-- =====================================================
-- 账号数据校验
-- =====================================================

-- 卖家账号
SELECT COUNT(*) as seller_count FROM upetmart.upet_seller WHERE store_id = 1;

-- 卖家组
SELECT COUNT(*) as seller_group_count FROM upetmart.upet_seller_group WHERE store_id = 1;

-- 卖家日志
SELECT COUNT(*) as seller_log_count FROM upetmart.upet_seller_log WHERE log_store_id = 1;

-- =====================================================
-- 订单相关数据校验（最后删除的表）
-- =====================================================

-- 订单商品
SELECT COUNT(*) as order_goods_count FROM upetmart.upet_order_goods WHERE store_id = 1;

-- 订单公共信息
SELECT COUNT(*) as order_common_count FROM upetmart.upet_order_common WHERE store_id = 1;

-- 订单主表
SELECT COUNT(*) as orders_count FROM upetmart.upet_orders WHERE store_id = 1;

-- VR订单快照（关联查询）
SELECT COUNT(*) as vr_order_snapshot_count 
FROM upetmart.upet_vr_order r 
INNER JOIN upetmart.upet_vr_order_snapshot d ON r.order_id = d.order_id 
WHERE r.store_id = 1;

-- VR订单码
SELECT COUNT(*) as vr_order_code_count FROM upetmart.upet_vr_order_code WHERE store_id = 1;

-- VR订单
SELECT COUNT(*) as vr_order_count FROM upetmart.upet_vr_order WHERE store_id = 1;

-- =====================================================
-- 扩展表数据校验
-- =====================================================

-- 商品扩展表
SELECT COUNT(*) as goods_eshop_count FROM upetmart.upet_goods_eshop;

-- 订单销售人员表
SELECT COUNT(*) as orders_salesperson_count FROM upetmart.upet_orders_salesperson;

-- =====================================================
-- 汇总查询 - 一次性查看所有相关数据量
-- =====================================================

SELECT 
    'upet_address' as table_name, COUNT(*) as record_count FROM upetmart.upet_address WHERE store_id = 1
UNION ALL
SELECT 
    'upet_brand' as table_name, COUNT(*) as record_count FROM upetmart.upet_brand WHERE store_id IN (0,1)
UNION ALL
SELECT 
    'upet_cart' as table_name, COUNT(*) as record_count FROM upetmart.upet_cart WHERE store_id = 1
UNION ALL
SELECT 
    'upet_goods_handlelog' as table_name, COUNT(*) as record_count FROM upetmart.upet_goods_handlelog WHERE store_id = 1
UNION ALL
SELECT 
    'upet_goods_images' as table_name, COUNT(*) as record_count FROM upetmart.upet_goods_images WHERE store_id = 1
UNION ALL
SELECT 
    'upet_goods_relevance' as table_name, COUNT(*) as record_count FROM upetmart.upet_goods_relevance WHERE store_id = 1
UNION ALL
SELECT 
    'upet_goods_relevance_sku' as table_name, COUNT(*) as record_count FROM upetmart.upet_goods_relevance_sku WHERE store_id = 1
UNION ALL
SELECT 
    'upet_goods' as table_name, COUNT(*) as record_count FROM upetmart.upet_goods WHERE store_id = 1
UNION ALL
SELECT 
    'upet_goods_common' as table_name, COUNT(*) as record_count FROM upetmart.upet_goods_common WHERE store_id = 1
UNION ALL
SELECT 
    'upet_dis_pay' as table_name, COUNT(*) as record_count FROM upetmart.upet_dis_pay WHERE store_id = 1
UNION ALL
SELECT 
    'upet_p_time' as table_name, COUNT(*) as record_count FROM upetmart.upet_p_time WHERE store_id = 1
UNION ALL
SELECT 
    'upet_p_xianshi' as table_name, COUNT(*) as record_count FROM upetmart.upet_p_xianshi WHERE store_id = 1
UNION ALL
SELECT 
    'upet_p_xianshi_goods' as table_name, COUNT(*) as record_count FROM upetmart.upet_p_xianshi_goods WHERE store_id = 1
UNION ALL
SELECT 
    'upet_voucher' as table_name, COUNT(*) as record_count FROM upetmart.upet_voucher WHERE voucher_store_id = 1
UNION ALL
SELECT 
    'upet_voucher_goods' as table_name, COUNT(*) as record_count FROM upetmart.upet_voucher_goods WHERE store_id = 1
UNION ALL
SELECT 
    'upet_seller' as table_name, COUNT(*) as record_count FROM upetmart.upet_seller WHERE store_id = 1
UNION ALL
SELECT 
    'upet_seller_group' as table_name, COUNT(*) as record_count FROM upetmart.upet_seller_group WHERE store_id = 1
UNION ALL
SELECT 
    'upet_seller_log' as table_name, COUNT(*) as record_count FROM upetmart.upet_seller_log WHERE log_store_id = 1
UNION ALL
SELECT 
    'upet_order_goods' as table_name, COUNT(*) as record_count FROM upetmart.upet_order_goods WHERE store_id = 1
UNION ALL
SELECT 
    'upet_order_common' as table_name, COUNT(*) as record_count FROM upetmart.upet_order_common WHERE store_id = 1
UNION ALL
SELECT 
    'upet_orders' as table_name, COUNT(*) as record_count FROM upetmart.upet_orders WHERE store_id = 1
UNION ALL
SELECT 
    'upet_vr_order_code' as table_name, COUNT(*) as record_count FROM upetmart.upet_vr_order_code WHERE store_id = 1
UNION ALL
SELECT 
    'upet_vr_order' as table_name, COUNT(*) as record_count FROM upetmart.upet_vr_order WHERE store_id = 1
UNION ALL
SELECT 
    'upet_goods_eshop' as table_name, COUNT(*) as record_count FROM upetmart.upet_goods_eshop
UNION ALL
SELECT 
    'upet_orders_salesperson' as table_name, COUNT(*) as record_count FROM upetmart.upet_orders_salesperson
ORDER BY table_name;
