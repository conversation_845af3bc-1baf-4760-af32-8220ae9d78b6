package main

import (
	_ "eShop/docs"
	"eShop/infra/log"
	"net/http"

	"github.com/go-chi/chi/v5"
	httpSwagger "github.com/swaggo/http-swagger"
)

func service() http.Handler {
	//接下来才是正题
	r := chi.NewRouter()

	r.<PERSON>("/swagger", httpSwagger.WrapHandler)
	return r
}

// 这个Main只是用来生成swag的，不做别的功能
// @title store接口
// @version 1.0
// @description 这里是描述
// @host 0.0.0.0:8151
func main() {

	// The HTTP Server
	server := &http.Server{Addr: "0.0.0.0:8188", Handler: service()}

	// Run the server
	err := server.ListenAndServe()
	if err != nil && err != http.ErrServerClosed {
		log.Fatal(err)
	}

}
