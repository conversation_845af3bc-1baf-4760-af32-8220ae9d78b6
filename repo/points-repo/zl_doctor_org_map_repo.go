package points_repo

import (
	po "eShop/domain/points-po"
)

// ZlDoctorOrgMapRepo 医生组织映射仓库
type ZlDoctorOrgMapRepo struct {
	SuperRepo[po.ZlDoctorOrgMap]
}

// NewZlDoctorOrgMapRepo 创建医生组织映射仓库实例
func NewZlDoctorOrgMapRepo() ZlDoctorOrgMapRepo {
	return ZlDoctorOrgMapRepo{
		NewSuperRepo[po.ZlDoctorOrgMap](),
	}
}

// FindByUserId 根据员工ID查询医生组织映射
// func (r ZlDoctorOrgMapRepo) FindByUserId(session *xorm.Session, userId string) (po.ZlDoctorOrgMap, error) {
// 	var entity po.ZlDoctorOrgMap
// 	exists, err := session.Where("user_id = ?", userId).Get(&entity)
// 	if err != nil {
// 		return entity, errors.NewInternalServerError("查询医生组织映射失败: " + err.Error())
// 	}
// 	if !exists {
// 		return entity, errors.NewNotFoundError("未找到医生组织映射")
// 	}
// 	return entity, nil
// }

// // FindByCoSocialReditCode 根据社会信用代码查询医生组织映射列表
// func (r ZlDoctorOrgMapRepo) FindByCoSocialReditCode(session *xorm.Session, code string) ([]po.ZlDoctorOrgMap, error) {
// 	var entities []po.ZlDoctorOrgMap
// 	err := session.Where("co_social_redit_code = ?", code).Find(&entities)
// 	if err != nil {
// 		return nil, errors.NewInternalServerError("查询医生组织映射失败: " + err.Error())
// 	}
// 	return entities, nil
// }

// // FindByUserIds 根据员工ID列表批量查询医生组织映射
// func (r ZlDoctorOrgMapRepo) FindByUserIds(session *xorm.Session, userIds []string) ([]po.ZlDoctorOrgMap, error) {
// 	var entities []po.ZlDoctorOrgMap
// 	err := session.In("user_id", userIds).Find(&entities)
// 	if err != nil {
// 		return nil, errors.NewInternalServerError("批量查询医生组织映射失败: " + err.Error())
// 	}
// 	return entities, nil
// }

// // ListByQuery 根据查询条件获取医生组织映射列表
// func (r ZlDoctorOrgMapRepo) ListByQuery(session *xorm.Session, queryVO vo.ZlDoctorOrgMapQueryVO) ([]po.ZlDoctorOrgMap, int64, error) {
// 	return r.Page(session, queryVO)
// }
