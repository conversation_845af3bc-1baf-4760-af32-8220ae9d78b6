package points_repo

import (
	po "eShop/domain/points-po"
	points_vo "eShop/view-model/points-vo"

	"fmt"

	"xorm.io/xorm"
)

type ClsPointsAccountRepo struct {
	SuperRepo[po.ClsPointsAccount]
}

func NewClsPointsAccountRepo() ClsPointsAccountRepo {
	return ClsPointsAccountRepo{
		NewSuperRepo[po.ClsPointsAccount](),
	}
}

// ListAccounts 查询积分账户列表
func (c ClsPointsAccountRepo) ListAccounts(session *xorm.Session, queryVO points_vo.ClsPointsAccountQueryVO) ([]points_vo.DistributorVO, int64, error) {
	var result []points_vo.DistributorVO

	query := session.Table("dis_distributor").
		Alias("dd").
		Select("dd.id AS dis_id, de.id AS enterprise_id, dd.dis_role, dd.real_name").
		Join("LEFT", "shop s", "s.id = dd.shop_id").
		Join("LEFT", "dis_enterprise de", "de.scrm_enterprise_id = s.enterprise_id").
		Where("de.id = ?", queryVO.EnterpriseId).
		And("dd.id <> ?", queryVO.DisId)

	if queryVO.Size > 0 {
		query = query.Limit(queryVO.Size, (queryVO.Current-1)*queryVO.Size)
	}

	total, err := query.FindAndCount(&result)
	if err != nil {
		return nil, 0, fmt.Errorf("查询列表失败: %v", err)
	}

	return result, total, nil
}
