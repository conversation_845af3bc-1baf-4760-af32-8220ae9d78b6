package points_repo

import (
	po "eShop/domain/points-po"
	"eShop/infra/errors"
	"eShop/infra/utils"
	vo "eShop/view-model/points-vo"
	"reflect"
	"strconv"

	"xorm.io/xorm"
)

type ISuperRepo[E po.Entity] interface {
	Create(session *xorm.Session, entity E) (E, error)
	BatchCreate(session *xorm.Session, entities any) ([]E, error)
	Update(session *xorm.Session, entity E) (E, error)
	GetById(session *xorm.Session, id any) (E, error)
	QueryOne(session *xorm.Session, queryVO vo.QueryVO) (E, error)
	Page(session *xorm.Session, queryVO vo.QueryVO) ([]E, int64, error)
	List(session *xorm.Session, queryVO vo.QueryVO) ([]E, error)
	Delete(session *xorm.Session, ids ...any) error
}

type SuperRepo[E po.Entity] struct {
}

func NewSuperRepo[E po.Entity]() SuperRepo[E] {
	return SuperRepo[E]{}
}

func (r SuperRepo[E]) Create(session *xorm.Session, entity E) (E, error) {
	ptr := entity.AsPointer()
	_, err := session.Table(entity.TableName()).Insert(ptr)
	if err != nil {
		return entity, errors.NewBadRequest("表：" + entity.TableName() + "创建失败：" + err.Error())
	}

	return reflect.ValueOf(ptr).Elem().Interface().(E), nil
}

func (r SuperRepo[E]) BatchCreate(session *xorm.Session, entities any) ([]E, error) {
	if entities == nil {
		return nil, errors.NewBadRequest("批量创建的实体列表不能为空")
	}

	entitiesSlice, ok := entities.([]E)
	if !ok || len(entitiesSlice) == 0 {
		return nil, errors.NewBadRequest("批量创建的实体列表不能为空")
	}

	// 获取表名
	tableName := entitiesSlice[0].TableName()

	// 创建一个切片用于存储实体的指针
	ptrs := make([]interface{}, len(entitiesSlice))
	for i, entity := range entitiesSlice {
		ptrs[i] = entity.AsPointer()
	}

	// 执行批量插入
	_, err := session.Table(tableName).Insert(ptrs...)
	if err != nil {
		return nil, errors.NewBadRequest("表：" + tableName + "批量创建失败：" + err.Error())
	}

	// 将指针转换回实体
	result := make([]E, len(entitiesSlice))
	for i, ptr := range ptrs {
		result[i] = reflect.ValueOf(ptr).Elem().Interface().(E)
	}

	return result, nil
}

func (r SuperRepo[E]) Update(session *xorm.Session, entity E) (E, error) {
	ptr := entity.AsPointer()
	_, err := session.Table(entity.TableName()).ID(entity.GetId()).Update(ptr)
	if err != nil {
		return entity, errors.NewBadRequest("表：" + entity.TableName() + "更新失败：" + err.Error())
	}

	return reflect.ValueOf(ptr).Elem().Interface().(E), nil
}

func (r SuperRepo[E]) GetById(session *xorm.Session, id any) (E, error) {
	var e E
	ptr := e.AsPointer()
	_, err := session.Table(e.TableName()).ID(id).Get(ptr)
	if err != nil {
		return e, errors.NewBadRequest("表：" + e.TableName() + "根据id查询实体失败：" + err.Error())
	}

	e = reflect.ValueOf(ptr).Elem().Interface().(E)
	if e.GetId() == 0 {
		return e, errors.NewBadRequest("表：" + e.TableName() + "根据id查询实体失败：" + "未找到实体")
	}

	return e, nil
}

func (r SuperRepo[E]) QueryOne(session *xorm.Session, queryVO vo.QueryVO) (E, error) {
	var e E
	ptr := e.AsPointer()

	// 执行查询
	query := session.Table(e.TableName())

	// 添加查询条件，使用 queryVO 构建查询条件
	conditions := utils.GetQueryCondition(queryVO)
	if conditions != "" {
		query = query.Where(conditions)
	}

	_, err := query.Limit(1).OrderBy(queryVO.GetOrderBy()).Get(ptr)
	if err != nil {
		return e, errors.NewBadRequest("表：" + e.TableName() + "条件查询实体失败：" + err.Error())
	}

	e = reflect.ValueOf(ptr).Elem().Interface().(E)
	if e.GetId() == 0 {
		return e, nil
	}

	return e, nil
}

func (r SuperRepo[E]) Page(session *xorm.Session, queryVO vo.QueryVO) ([]E, int64, error) {
	var e E
	// 计算偏移量
	offset := (queryVO.GetCurrent() - 1) * queryVO.GetSize()

	// 创建一个切片用于存储结果
	sliceType := reflect.SliceOf(reflect.TypeOf(e))
	slice := reflect.MakeSlice(sliceType, 0, 0)
	slicePtr := reflect.New(slice.Type())
	slicePtr.Elem().Set(slice)

	// 执行分页查询
	query := session.Table(e.TableName())

	// 添加查询条件，使用 queryVO 构建查询条件
	conditions := utils.GetQueryCondition(queryVO)
	if conditions != "" {
		query = query.Where(conditions)
	}

	total, err := query.Limit(queryVO.GetSize(), offset).OrderBy(queryVO.GetOrderBy()).FindAndCount(slicePtr.Interface())
	if err != nil {
		return nil, 0, errors.NewBadRequest("表：" + e.TableName() + "分页查询失败：" + err.Error())
	}

	// 将结果转换为Entity切片
	resultSlice := make([]E, slicePtr.Elem().Len())
	for i := 0; i < slicePtr.Elem().Len(); i++ {
		resultSlice[i] = slicePtr.Elem().Index(i).Interface().(E)
	}

	return resultSlice, total, nil
}

// List 获取所有数据
func (r SuperRepo[E]) List(session *xorm.Session, queryVO vo.QueryVO) ([]E, error) {
	var e E
	// 创建一个切片用于存储结果
	sliceType := reflect.SliceOf(reflect.TypeOf(e))
	slice := reflect.MakeSlice(sliceType, 0, 0)
	slicePtr := reflect.New(slice.Type())
	slicePtr.Elem().Set(slice)

	// 执行查询
	query := session.Table(e.TableName())

	// 添加查询条件，使用 queryVO 构建查询条件
	conditions := utils.GetQueryCondition(queryVO)
	if conditions != "" {
		query = query.Where(conditions)
	}

	err := query.Find(slicePtr.Interface())
	if err != nil {
		return nil, errors.NewBadRequest("表：" + e.TableName() + "查询所有数据失败：" + err.Error())
	}

	// 将结果转换为Entity切片
	resultSlice := make([]E, slicePtr.Elem().Len())
	for i := 0; i < slicePtr.Elem().Len(); i++ {
		resultSlice[i] = slicePtr.Elem().Index(i).Interface().(E)
	}

	return resultSlice, nil
}

// Delete 删除数据，支持物理删除和逻辑删除
func (r SuperRepo[E]) Delete(session *xorm.Session, ids ...any) error {
	var e E
	// 获取实体的反射类型
	typ := reflect.TypeOf(e)
	// 如果是指针类型，获取其基础类型
	if typ.Kind() == reflect.Ptr {
		typ = typ.Elem()
	}

	// 检查是否存在逻辑删除字段
	deleteField, logicDelete, deleteValue := findLogicDeleteField(typ)

	if logicDelete {
		// 执行逻辑删除
		ptr := e.AsPointer()

		// 设置逻辑删除字段的值
		field := reflect.ValueOf(ptr).Elem().FieldByName(deleteField.Name)
		switch field.Kind() {
		case reflect.Bool:
			field.SetBool(deleteValue.(bool))
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			field.SetInt(deleteValue.(int64))
		}

		// 获取数据库列名
		colName := utils.GetXormColumnName(deleteField)

		// 更新记录，使用数据库列名
		_, err := session.Table(e.TableName()).In("id", ids).
			MustCols(colName).
			Update(ptr)
		if err != nil {
			return errors.NewBadRequest("表：" + e.TableName() + "逻辑删除失败：" + err.Error())
		}
	} else {
		// 执行物理删除
		_, err := session.Table(e.TableName()).In("id", ids).Delete(e.AsPointer())
		if err != nil {
			return errors.NewBadRequest("表：" + e.TableName() + "物理删除失败：" + err.Error())
		}
	}

	return nil
}

// findLogicDeleteField 查找逻辑删除字段
func findLogicDeleteField(t reflect.Type) (reflect.StructField, bool, any) {
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)

		// 获取 logic_delete 标签值
		if tag := field.Tag.Get("logic_delete"); tag != "" {
			// 检查字段类型是否支持
			switch field.Type.Kind() {
			case reflect.Bool:
				if tag == "true" {
					return field, true, true
				} else if tag == "false" {
					return field, true, false
				}
				return field, true, true // 默认为 true
			case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
				if val, err := strconv.ParseInt(tag, 10, 64); err == nil {
					return field, true, val
				}
				return field, true, int64(1) // 默认为 1
			default:
				continue
			}
		}
	}
	return reflect.StructField{}, false, nil
}
