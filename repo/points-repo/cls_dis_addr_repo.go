package points_repo

import (
	po "eShop/domain/points-po"

	"xorm.io/xorm"
)

type ClsDisAddrRepo struct {
	SuperRepo[po.ClsDisAddr]
}

func NewClsDisAddrRepo() ClsDisAddrRepo {
	return ClsDisAddrRepo{
		NewSuperRepo[po.ClsDisAddr](),
	}
}

func (r ClsDisAddrRepo) SetDefault(session *xorm.Session, IsDefault int, id int) error {
	_, err := session.Table("cls_dis_address").ID(id).Cols("is_default").Update(po.ClsDisAddr{IsDefault: IsDefault})
	if err != nil {
		return err
	}
	return nil
}

func (r ClsDisAddrRepo) ClearDefaultAddr(session *xorm.Session, disId, id int) error {
	_, err := session.Table("cls_dis_address").Where("dis_id = ? AND id != ?", disId, id).Cols("is_default").Update(&po.ClsDisAddr{IsDefault: 0})
	if err != nil {
		return err

	}
	return nil
}
