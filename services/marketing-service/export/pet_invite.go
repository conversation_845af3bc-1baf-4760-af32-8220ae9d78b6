package export

import (
	"eShop/infra/utils"
	"eShop/services/common"
	service "eShop/services/marketing-service/services" // 修正导入路径
	vo "eShop/view-model/marketing-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type PetInviteTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.PetInvitePageReq // 修正为 PetInvitePageReq
	writer       *excelize.StreamWriter
	common.BaseService
}

func (e PetInviteTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.PetInvitePageReq) // 修正为 PetInvitePageReq
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.PageIndex = 1
	e.ExportParams.PageSize = 10000

	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	e.SetSheetName()
	client := service.NewPetInviteService()

	k := 0
	for {
		// Page 方法的第一个参数是 *xorm.Session，这里传入 nil
		ret, _, err := client.Page(nil, *e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {
			k++
			axis := fmt.Sprintf("A%d", k+1)

			// 根据 pet_invite 的实际字段进行调整，使用 PetInviteResp 的字段
			_ = e.writer.SetRow(axis, []interface{}{
				ret[i].CreateTime,                           // 邀请时间
				ret[i].InviterId,                            // 邀请用户id
				ret[i].InviterNickname,                      // 邀请人昵称
				utils.MobileDecrypt(ret[i].InviterEnMobile), // 邀请人手机号
				func() string {
					switch ret[i].InviterType {
					case 0:
						return "新客"
					case 1:
						return "老客"
					default:
						return fmt.Sprintf("%d", ret[i].InviterType)
					}
				}(), // 邀请人客户类型
				func() string {
					switch ret[i].InviteeRegisterStatus {
					case 0:
						return "未注册"
					case 1:
						return "已注册"
					default:
						return fmt.Sprintf("%d", ret[i].InviteeRegisterStatus)
					}
				}(), // 被邀请用户注册状态
				ret[i].InviteeRegisterTime, // 被邀请用户注册时间
				ret[i].InviteeId,           // 被邀请用户id
				ret[i].InviteeNickname,     // 被邀请用户昵称
				utils.MobileDecrypt(ret[i].InviteeEnMobile), // 被邀请人加密手机号
				func() string {
					switch ret[i].InviteeType {
					case 0:
						return "新客"
					case 1:
						return "老客"
					default:
						return fmt.Sprintf("%d", ret[i].InviteeType)
					}
				}(), // 被邀请客户类型
				func() string {
					switch ret[i].VoteStatus {
					case 0:
						return "未投票"
					case 1:
						return "已投票"
					case 2:
						return "投票失败"
					default:
						return fmt.Sprintf("%d", ret[i].VoteStatus)
					}
				}(), // 被邀请客户投票状态
				ret[i].WorkCode,  // 关联的作品编号
				ret[i].VoteTime,  // 投票时间
				ret[i].VoteCount, // 投票数
			})
		}
		if len(ret) < int(e.ExportParams.PageSize) {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return
}

func (e PetInviteTask) SetSheetName(args ...interface{}) {
	// 根据 pet_invite 的实际字段进行调整，使用 PetInviteResp 的字段
	nameList := []interface{}{
		"邀请时间", "邀请用户id", "邀请人昵称", "邀请人手机号", "邀请人客户类型", "被邀请用户注册状态", "被邀请用户注册时间", "被邀请用户id", "被邀请用户昵称", "被邀请用户手机号", "被邀请客户类型", "被邀请客户投票状态", "关联的作品编号", "投票时间", "投票数",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e PetInviteTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("邀请助力明细导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e PetInviteTask) OperationFunc(row []string, orgId int) string {
	return ""
}
