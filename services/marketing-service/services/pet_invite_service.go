package services

import (
	po "eShop/domain/marketing-po"
	upetmart_po "eShop/domain/upetmart-po"
	"eShop/infra/log"
	"eShop/infra/transaction"
	"eShop/infra/utils"
	vo "eShop/view-model/marketing-vo"
	"errors"
	"fmt"
	"time"

	"xorm.io/xorm"
)

// petInviteService 邀请服务实现
type PetInviteService struct {
	tm transaction.TransactionManager
}

// NewPetInviteService 创建邀请服务实例
func NewPetInviteService() PetInviteService {
	return PetInviteService{
		tm: transaction.NewTransactionManager(),
	}
}

// Page 邀请分页查询
func (s PetInviteService) Page(session *xorm.Session, query vo.PetInvitePageReq) ([]vo.PetInviteResp, int64, error) {
	var resps []vo.PetInviteResp
	var total int64
	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		var err error

		if len(query.InviterMobile) > 0 {
			query.InviterMobile = utils.MobileEncrypt(query.InviterMobile)
		}
		if len(query.InviteeMobile) > 0 {
			query.InviteeMobile = utils.MobileEncrypt(query.InviteeMobile)
		}
		conditions := utils.GetQueryCondition(query)
		if conditions != "" {
			tx.Where(conditions)
		}

		if query.PageIndex > 0 && query.PageSize > 0 {
			tx.Limit(query.PageSize, (query.PageIndex-1)*query.PageSize)
		}

		total, err = tx.Table("pet_invite").OrderBy("id DESC").FindAndCount(&resps)
		if err != nil {
			return err
		}
		return nil
	})
	return resps, total, err
}

// SharePetArtwork 分享作品
func (s *PetInviteService) SharePetArtwork(req *vo.SharePetArtworkReq) (*vo.SharePetArtworkData, error) {
	var result = &vo.SharePetArtworkData{
		Success: false,
	}
	err := s.tm.NotSupported(nil, func(tx *xorm.Session) error {
		if req.UserId == "" {
			return errors.New("用户ID不能为空")
		}
		if req.InviterNickname == "" {
			return errors.New("邀请人昵称不能为空")
		}
		if req.InviterMobile == "" {
			return errors.New("邀请人手机号不能为空")
		}
		if req.WorkCode == "" {
			return errors.New("作品编号不能为空")
		}
		if req.InviteeOpenId == "" {
			return errors.New("被邀请人open_id不能为空")
		}

		//邀请人信息
		user := &upetmart_po.User{}
		_, err := user.GetByScrmUserId(tx, req.UserId, 2)

		if err != nil {
			log.Error("查询用户信息失败", err)
			return err
		}
		//自己邀请自己不记录
		if user.WeixinMiniOpenid == req.InviteeOpenId {

			return nil
		}
		//
		InviteeType := 0
		//根据用户的openID判断是否是新用户
		//被邀请人信息
		inviteeUser := &upetmart_po.User{}
		isHave := false
		if req.InviteeUserId != "" {
			isHave, err = inviteeUser.GetByScrmUserId(tx, req.InviteeUserId, 2)
			if err != nil {
				log.Error("查询用户信息失败", err)
				return err
			}
		} else {
			isHave, err = inviteeUser.GetByOpenId(tx, req.InviteeOpenId, 2)
			if err != nil {
				log.Error("查询用户信息失败", err)
				return err
			}
		}

		if !isHave || inviteeUser.Source == 1 {
			InviteeType = 1
		}

		// 验证作品是否存在
		var artwork po.PetArtwork
		exists, err := tx.Table(po.PetArtwork{}.TableName()).
			Where("work_code = ? and pk_status=1", req.WorkCode).
			Get(&artwork)
		if err != nil {
			return err
		}
		//没参加PK的作品不处理
		if !exists {
			return nil
			//return errors.New("作品不存在")
		}

		// 检查是否已经分享过
		var existingInvite po.PetInvite
		exists, err = tx.Table(po.PetInvite{}.TableName()).
			Where("inviter_id = ? AND work_code = ? AND invitee_open_id = ?", req.UserId, req.WorkCode, req.InviteeOpenId).
			Get(&existingInvite)
		if err != nil {
			return err
		}
		if !exists || existingInvite.VoteCount == 0 {
			result.Success = true
		}
		//如果存在了就不插入了
		if exists {
			return nil
		}

		// 处理手机号加密和加星
		inviterEnMobile := utils.MobileEncrypt(req.InviterMobile)
		inviterMobileStar := utils.AddStar(req.InviterMobile)
		InviterType := 0
		if user.Source == 1 {
			InviterType = 1
		}

		// 处理手机号加密和加星
		InviteeEnMobile := ""
		InviteeMobileStar := ""
		if req.InviteeMobile != "" {
			InviteeEnMobile = utils.MobileEncrypt(req.InviteeMobile)
			InviteeEnMobile = utils.AddStar(req.InviteeMobile)
		}

		// 创建邀请记录
		invite := po.PetInvite{
			InviterId:             req.UserId,
			InviterNickname:       req.InviterNickname,
			InviterMobile:         inviterMobileStar,
			InviterEnMobile:       inviterEnMobile,
			InviterType:           InviterType, // 默认新客
			InviteeId:             req.InviteeUserId,
			InviteeNickname:       req.InviteeNickname,
			InviteeMobile:         InviteeMobileStar,
			InviteeEnMobile:       InviteeEnMobile,
			InviteeRegisterStatus: 0,
			InviteeType:           InviteeType,
			VoteStatus:            0, // 未投票
			WorkCode:              req.WorkCode,
			VoteCount:             0,
			CreateTime:            time.Now(),
			UpdateTime:            time.Now(),
			InviteeOpenId:         req.InviteeOpenId,
		}
		//如果被邀请人的openID已经注册过，补充信息
		if isHave && req.InviteeUserId != "" {
			invite.InviteeRegisterStatus = 1
			invite.InviteeRegisterTime = inviteeUser.CreateTime
		}

		_, err = tx.Table(po.PetInvite{}.TableName()).Insert(&invite)
		if err != nil {
			return err
		}
		result.Success = true
		return nil
	})
	return result, err
}

// VotePetArtwork 投票
func (s *PetInviteService) VotePetArtwork(req *vo.VotePetArtworkReq) (*vo.VotePetArtworkData, error) {
	var result *vo.VotePetArtworkData
	err := s.tm.NotSupported(nil, func(tx *xorm.Session) error {
		if req.UserId == "" {
			return errors.New("用户ID不能为空")
		}
		if req.InviterNickname == "" {
			return errors.New("邀请人昵称不能为空")
		}
		if req.InviterMobile == "" {
			return errors.New("邀请人手机号不能为空")
		}
		if req.WorkCode == "" {
			return errors.New("作品编号不能为空")
		}
		if req.InviteeOpenId == "" {
			return errors.New("被邀请人open_id不能为空")
		}

		activityService := PetActivityService{}

		info, err := activityService.GetActivityInfo(nil)
		if err != nil {
			return err
		}
		if info.State == 2 {
			return errors.New("活动已经结束！")
		}

		// 查找邀请记录
		var invite po.PetInvite
		exists, err := tx.Table(po.PetInvite{}.TableName()).
			Where("work_code = ? AND invitee_open_id = ?", req.WorkCode, req.InviteeOpenId).
			Get(&invite)
		if err != nil {
			return err
		}
		if !exists {
			return errors.New("未找到邀请记录")
		}

		// 检查是否已经投票
		if invite.VoteStatus == 1 {
			return errors.New("已经投票过了")
		}

		user := &upetmart_po.User{}
		_, err = user.GetByScrmUserId(tx, invite.InviterId, 2)
		if err != nil {
			log.Error("查询用户信息失败", err)
			return err
		}
		//自己不能给自己投票
		if user.WeixinMiniOpenid == req.InviteeOpenId {
			return errors.New("自己不能给自己投票")
		}

		// 检查是否是首次投票（该用户是否给任何作品投过票）
		var firstVote po.PetInvite
		exists, err = tx.Table(po.PetInvite{}.TableName()).
			Where("invitee_open_id = ? AND vote_status = 1", req.InviteeOpenId).
			Get(&firstVote)
		if err != nil {
			return err
		}

		isFirstVote := !exists
		voteCount := 1
		if isFirstVote {
			voteCount = 3 // 首次投票加3票
		}

		// ====== 新增：投票前查询当前票数 ======
		var artwork po.PetArtwork
		has, err := tx.Table(po.PetArtwork{}.TableName()).
			Where("work_code = ?", req.WorkCode).
			Get(&artwork)
		if err != nil {
			return err
		}
		if !has {
			return errors.New("作品不存在")
		}
		preVoteCount := artwork.VoteCount
		postVoteCount := preVoteCount + voteCount
		// ====== END ======

		// 处理被邀请人手机号加密和加星
		// 注意：这里需要从请求中获取被邀请人的手机号，暂时使用邀请人的手机号作为示例
		inviteeEnMobile := utils.MobileEncrypt(req.InviterMobile)
		inviteeMobileStar := utils.AddStar(req.InviterMobile)

		// 更新邀请记录
		invite.InviteeId = req.UserId
		invite.InviteeNickname = req.InviterNickname // 注意：这里应该获取被邀请人的昵称
		invite.InviteeMobile = inviteeMobileStar
		invite.InviteeEnMobile = inviteeEnMobile
		invite.InviteeRegisterStatus = 1
		invite.InviteeRegisterTime = time.Now()
		invite.VoteStatus = 1
		invite.VoteTime = time.Now()
		invite.VoteCount = voteCount
		invite.UpdateTime = time.Now()
		tx.Begin()

		_, err = tx.Table(po.PetInvite{}.TableName()).
			Where("id = ?", invite.Id).
			Update(&invite)
		if err != nil {
			tx.Rollback()
			return err
		}

		// 更新作品票数
		_, err = tx.Table(po.PetArtwork{}.TableName()).
			Where("work_code = ?", req.WorkCode).
			Incr("vote_count", voteCount).
			Incr("assist_count", 1).
			Update(&po.PetArtwork{})
		if err != nil {
			tx.Rollback()
			return err
		}

		prizeType := 0
		prizeName := ""
		voucherType := 0

		petActivityService := PetActivityService{}
		// ====== 新增：判断是否跨越5票/25票门槛，发券 ======
		if preVoteCount < 5 && postVoteCount >= 5 {
			prizeType = 5
			prizeName = "15元无门槛券"
			voucherType = 15
		}
		if preVoteCount < 25 && postVoteCount >= 25 {
			prizeType = 25
			prizeName = "30元无门槛券"
			voucherType = 30
		}

		// 如果需要发放优惠券，先尝试发放
		var voucherResult *utils.ExchangeVoucherResponse
		var voucherCode string
		if prizeType > 0 {
			voucherResult, err = petActivityService.ReceiveVoucherByType(invite.InviterId, voucherType, 2)
			if err != nil {
				tx.Rollback()
				return err
			}

			// 解析优惠券码 - 使用更简洁的方式
			if voucherResult != nil && voucherResult.Code == 200 {
				if voucherDataSlice, ok := voucherResult.Datas.([]utils.VoucherExchangeData); ok && len(voucherDataSlice) > 0 {
					// 直接使用VoucherExchangeData的字段
					voucherCode = fmt.Sprintf("%v", voucherDataSlice[0].VoucherCode)
				}
			}
		}

		// 只有在需要发放奖品时才插入PetPrize记录
		if prizeType > 0 {
			prize := po.PetPrize{
				UserId:        invite.InviterId,
				NickName:      invite.InviterNickname,
				Mobile:        invite.InviterMobile,
				EnMobile:      invite.InviterEnMobile,
				PrizeType:     2, // 投票奖
				WorkCode:      invite.WorkCode,
				PrizeCount:    prizeType,
				ReceiveStatus: 2, // 优惠券已发放
				PrizeContent:  prizeName,
				CouponCode:    voucherCode, // 保存优惠券ID
				ReceiveTime:   time.Now(),
				CreateTime:    time.Now(),
				UpdateTime:    time.Now(),
			}

			_, err = tx.Table(po.PetPrize{}.TableName()).Insert(&prize)
			if err != nil {
				tx.Rollback()
				return err
			}
		}

		result = &vo.VotePetArtworkData{
			Success:     true,
			VoteCount:   voteCount,
			IsFirstVote: isFirstVote,
		}
		tx.Commit()
		return nil
	})
	return result, err
}
