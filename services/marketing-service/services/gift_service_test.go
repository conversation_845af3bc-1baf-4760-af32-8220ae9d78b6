package services

import (
	marketing_po "eShop/domain/marketing-po"
	product_po "eShop/domain/product-po"
	"eShop/infra/log"
	marketing_vo "eShop/view-model/marketing-vo"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
)

func TestGiftService_SaveGift(t *testing.T) {
	log.Init()
	// 初始化服务
	svc := &GiftService{}
	svc.Begin()
	defer svc.Close()

	// 清理测试数据
	cleanup := func() {
		svc.Engine.Where("name LIKE ?", "test_%").Delete(new(marketing_po.MarketingGift))
	}
	cleanup()
	defer cleanup()

	// 准备测试数据：模拟商品信息
	testSku := &product_po.ProProductStoreInfo{
		Id:        1001,
		StoreId:   "store_001",
		ProductId: 2001,
	}
	_, err := svc.Engine.Insert(testSku)
	assert.NoError(t, err)

	// 测试用例
	tests := []struct {
		name    string
		req     *marketing_vo.SaveGiftReq
		wantErr bool
	}{
		{
			name: "正常保存-长期有效",
			req: &marketing_vo.SaveGiftReq{
				ChainId:     530185752454150978,
				StoreId:     "530236368643759103",
				Name:        "test_gift_1",
				ProductId:   100095,
				SkuId:       100095002,
				IsPermanent: 1,
				PersonLimit: 3,
			},
			wantErr: true,
		},
		{
			name: "正常保存-固定期限",
			req: &marketing_vo.SaveGiftReq{
				ChainId:     530185752454150978,
				StoreId:     "530236368643759103",
				Name:        "test_gift_2",
				ProductId:   2001,
				SkuId:       1001,
				IsPermanent: 0,
				EnableTime:  "2024-12-28 10:00:00",
				ExpireTime:  "2025-01-28 10:00:00",
				PersonLimit: 2,
			},
			wantErr: false,
		},
		{
			name: "商品不存在",
			req: &marketing_vo.SaveGiftReq{
				ChainId:     530185752454150978,
				StoreId:     "530236368643759103",
				Name:        "test_gift_3",
				ProductId:   9999,
				SkuId:       9999,
				IsPermanent: 1,
				PersonLimit: 1,
			},
			wantErr: true,
		},
		{
			name: "更新已存在的赠品",
			req: &marketing_vo.SaveGiftReq{
				Id:          0, // 将在测试中设置
				ChainId:     530185752454150978,
				StoreId:     "530236368643759103",
				Name:        "test_gift_4_updated",
				ProductId:   2001,
				SkuId:       1001,
				IsPermanent: 1,
				PersonLimit: 5,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			// 执行测试
			if err = svc.SaveGift(tt.req); (err != nil) != tt.wantErr {
				t.Errorf("DeductCommission() error = %v, wantErr %v", err, tt.wantErr)
			}
			// 验证结果
			//if tt.wantErr {
			//	assert.Error(t, err)
			//} else {
			//	assert.NoError(t, err)
			//
			//	// 验证保存的数据
			//	var saved marketing_po.MarketingGift
			//	exists, err := svc.Engine.Where("name = ?", tt.req.Name).Get(&saved)
			//	assert.NoError(t, err)
			//	assert.True(t, exists)
			//
			//	// 验证字段值
			//	assert.Equal(t, tt.req.ChainId, saved.ChainId)
			//	assert.Equal(t, tt.req.StoreId, saved.StoreId)
			//	assert.Equal(t, tt.req.Name, saved.Name)
			//	assert.Equal(t, tt.req.ProductId, saved.ProductId)
			//	assert.Equal(t, tt.req.SkuId, saved.SkuId)
			//	assert.Equal(t, tt.req.IsPermanent, saved.IsPermanent)
			//	assert.Equal(t, tt.req.PersonLimit, saved.PersonLimit)
			//	assert.Equal(t, tt.req.TotalCount, saved.TotalCount)
			//
			//	// 对于新增的赠品，验证剩余数量是否等于总数量
			//	if tt.req.Id == 0 {
			//		assert.Equal(t, tt.req.TotalCount, saved.RemainCount)
			//	}
			//}
		})
	}
}
