package services

import (
	marketing_po "eShop/domain/marketing-po"
	"eShop/infra/cache"
	"eShop/services/common"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	marketing_vo "eShop/view-model/marketing-vo"
	"time"
)

type PetStatService struct {
	common.BaseService
}

func metricTypeName(metricType int) string {
	switch metricType {
	case 1:
		return "阿闻首页弹窗点击uv"
	case 2:
		return "阿闻首页banner点击uv"
	case 3:
		return "阿闻首页浮标点击uv"
	case 4:
		return "小闻首页广告点击uv"
	case 5:
		return "贵族首页弹窗点击uv"
	case 6:
		return "贵族首页banner点击uv"
	case 7:
		return "贵族活动主页访问的pv"
	case 8:
		return "贵族活动主页访问uv"
	case 9:
		return "作品助力页面访问pv"
	case 10:
		return "作品助力页面访问uv"
	case 11:
		return "生成贵族创作图的用户数"
	case 12:
		return "分享的用户数"
	case 13:
		return "贵族宠物图分享总次数"
	case 14:
		return "好友助力总次数"
	case 15:
		return "达到5票的用户数"
	case 16:
		return "达到25票的用户数"
	default:
		return ""
	}
}

// 埋点写入
func (s *PetStatService) AddPetStatLog(req *marketing_vo.PetStatLogReq) error {

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	isUv := true
	switch req.MetricType {
	case 7, 9, 14:
		isUv = false
	}
	today := time.Now().Format("2006-01-02")
	startOfDay := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Now().Location())
	endOfDay := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 23, 59, 59, 0, time.Now().Location())
	where := "metric_type=? AND create_time>=? AND create_time<=?"
	args := []interface{}{req.MetricType, startOfDay, endOfDay}
	if req.UniqueId != "" && isUv {
		where += " AND unique_id=?"
		args = append(args, req.UniqueId)
		exist, err := session.Table(marketing_po.PetStatLog{}.TableName()).Where(where, args...).Exist()
		if err != nil {
			return err
		}
		if exist {
			return nil // 已埋点过，直接返回
		}
	}
	//if req.UserId != "" {
	//	where += " AND user_id=?"
	//	args = append(args, req.UserId)
	//}
	//if req.WorkCode != "" {
	//	where += " AND work_code=?"
	//	args = append(args, req.WorkCode)
	//}

	metricName := metricTypeName(req.MetricType)

	log := marketing_po.PetStatLog{
		MetricType: req.MetricType,
		MetricName: metricName,
		WorkCode:   req.WorkCode,
		UserId:     req.UserId,
		UniqueId:   req.UniqueId,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}
	var err error
	if isUv {
		_, err = session.Table(marketing_po.PetStatLog{}.TableName()).Insert(&log)
		if err != nil {
			return err
		}
	}

	stat := marketing_po.PetStat{}
	has, _ := session.Table(marketing_po.PetStat{}.TableName()).
		Where("daliy_date=? AND metric_type=?", today, req.MetricType).Get(&stat)
	if has {
		_, err = session.Table(marketing_po.PetStat{}.TableName()).
			Where("id=?", stat.Id).
			Incr("num", 1).
			Update(&marketing_po.PetStat{})
	} else {
		stat.DaliyDate = today
		stat.MetricType = req.MetricType
		stat.Num = 1
		stat.CreateTime = time.Now()
		stat.UpdateTime = time.Now()
		_, err = session.Table(marketing_po.PetStat{}.TableName()).Insert(&stat)
	}
	return err
}

// 每日统计参与PK作品投票数，统计达到5票和25票的用户数，写入pet_stat表
func (s *PetStatService) StatVoteUserCountPerDay() error {
	s.Begin()
	defer s.Close()

	// 幂等控制：检查是否已发放
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	// redis加锁，防止并发
	lockKey2 := cachekey.PetPrize5Or25Lock
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey2, time.Minute*10)
	if !setNxReslt {
		return nil
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey2)

	// 获取今天日期
	today := time.Now().Format("2006-01-02")
	// 查询所有参与PK的作品（pk_status=1），每个用户只统计一个作品
	session := s.Engine.NewSession()
	defer session.Close()

	// 先查出每个用户最新的一个参与PK的作品
	var artworks []marketing_po.PetArtwork
	err := session.Table(marketing_po.PetArtwork{}.TableName()).
		Where("pk_status=1").
		Cols("scrm_user_id", "vote_count").
		Find(&artworks)
	if err != nil {
		return err
	}

	// 用map去重，每个用户只统计一个作品
	userVoteMap := make(map[string]int)
	for _, a := range artworks {
		if a.ScrmUserId == "" {
			continue
		}
		// 只保留第一个作品（如需最新可加时间判断）
		if _, ok := userVoteMap[a.ScrmUserId]; !ok {
			userVoteMap[a.ScrmUserId] = a.VoteCount
		}
	}

	// 统计达到5票和25票的用户数
	count5 := 0
	count25 := 0
	for _, v := range userVoteMap {
		if v >= 5 {
			count5++
		}
		if v >= 25 {
			count25++
		}
	}

	// 写入pet_stat表，指标编码：15-达到5票的用户数,16-达到25票的用户数
	metrics := []struct {
		MetricType int
		Num        int
		MetricName string
	}{
		{15, count5, metricTypeName(15)},
		{16, count25, metricTypeName(16)},
	}

	// 选取每个用户的作品 work_code（如需更精细可调整）
	userWorkCodeMap := make(map[string]string)
	for _, a := range artworks {
		if a.ScrmUserId == "" {
			continue
		}
		if _, ok := userWorkCodeMap[a.ScrmUserId]; !ok {
			userWorkCodeMap[a.ScrmUserId] = a.WorkCode
		}
	}

	for _, m := range metrics {
		var stat marketing_po.PetStat
		has, err := session.Table(marketing_po.PetStat{}.TableName()).
			Where("daliy_date=? AND metric_type=?", today, m.MetricType).
			Get(&stat)
		if err != nil {
			return err
		}
		if has {
			// 存在则更新
			stat.Num = m.Num
			stat.UpdateTime = time.Now()
			_, err = session.Table(marketing_po.PetStat{}.TableName()).
				Where("id=?", stat.Id).
				Update(&stat)
			if err != nil {
				return err
			}
		} else {
			// 不存在则插入
			stat.DaliyDate = today
			stat.MetricType = m.MetricType
			stat.Num = m.Num
			stat.CreateTime = time.Now()
			stat.UpdateTime = time.Now()
			_, err = session.Table(marketing_po.PetStat{}.TableName()).Insert(&stat)
			if err != nil {
				return err
			}
		}
	}
	return nil
}
