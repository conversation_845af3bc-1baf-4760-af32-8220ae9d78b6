package services

import (
	marketing_po "eShop/domain/marketing-po"
	product_po "eShop/domain/product-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	marketing_vo "eShop/view-model/marketing-vo"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"xorm.io/xorm"
)

type ActivityService struct {
	common.BaseService
}

// 获取活动详情
func (s *ActivityService) GetActivityDetail(param *marketing_vo.GetActivityDetailReq) (data marketing_vo.ActivityInfo, err error) {
	logPrefix := fmt.Sprintf("获取活动详情数据====，入参：%s", utils.JsonEncode(param))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	query := marketing_po.ActivityListQuery{
		ChainId: param.ChainId,
		StoreId: param.StoreId,
		Id:      param.Id,
	}

	activityList, _, err := new(marketing_po.MarketingActivity).GetActivityList(session, query)
	if err != nil {
		log.Error(logPrefix, "获取活动列表失败,err=", err.Error())
		err = errors.New("获取活动列表失败")
		return
	}
	if len(activityList) == 0 {
		err = errors.New("活动不存在")
		return
	}

	_, productsMap, _, err := new(marketing_po.MarketingProduct).GetActivityProductList(session, marketing_po.ActivityProductListQuery{ActivityId: param.Id, OutType: 1, ChainId: param.ChainId, StoreId: param.StoreId})
	if err != nil {
		log.Error(logPrefix, "获取活动商品列表失败,err=", err.Error())
		err = errors.New("获取活动商品列表失败")
		return
	}

	data.MarketingActivity = activityList[0]

	if data.MarketingActivity.Type == marketing_po.ActivityTypeFullReduction {
		if err = json.Unmarshal([]byte(data.MarketingActivity.Rule), &data.FullReductionRules); err != nil {
			log.Error(logPrefix, "	,err=", err.Error())
			err = errors.New("解析满减规则失败")
			return
		}
		if products, ok := productsMap[fmt.Sprintf("%s_%d", data.MarketingActivity.StoreId, data.MarketingActivity.Id)]; ok {
			if len(products) > 0 {
				for _, product := range products {
					if product.ApplyType == marketing_po.ProductApplyTypeCategory {
						data.FullReductionSetting.FullReductionSetting = marketing_po.ProductApplyTypeCategory
						data.FullReductionSetting.CategoryIds = append(data.FullReductionSetting.CategoryIds, product.ProductRefId)
					} else if product.ApplyType == marketing_po.ExcludeApplyTypeGoods {
						data.ExcludePriceProducts = append(data.ExcludePriceProducts, marketing_po.SpecialPriceProduct{
							ProductRefId:  product.ProductRefId,
							SkuId:         product.SkuId,
							DiscountRate:  product.DiscountRate,
							ActivityPrice: product.ActivityPrice,
						})
					} else {
						data.FullReductionSetting.FullReductionSetting = marketing_po.ProductApplyTypeGoods
					}
				}
			}
		}

	} else if data.MarketingActivity.Type == marketing_po.ActivityTypeSpecialPrice {
		if err = json.Unmarshal([]byte(data.MarketingActivity.Rule), &data.SpecialPriceRule); err != nil {
			log.Error(logPrefix, "解析特价规则失败,err=", err.Error())
			err = errors.New("解析特价规则失败")
			return
		}
		if products, ok := productsMap[fmt.Sprintf("%s_%d", data.MarketingActivity.StoreId, data.MarketingActivity.Id)]; ok {
			if len(products) > 0 {
				for _, product := range products {
					data.SpecialPriceProducts = append(data.SpecialPriceProducts, marketing_po.SpecialPriceProduct{
						ProductRefId:  product.ProductRefId,
						SkuId:         product.SkuId,
						DiscountRate:  product.DiscountRate,
						ActivityPrice: product.ActivityPrice,
					})
				}
			}
		}
	}

	return
}

// 获取活动列表
func (s *ActivityService) GetActivityList(param *marketing_vo.ActivityListReq) (data []marketing_vo.ActivityListData, total int64, err error) {
	logPrefix := fmt.Sprintf("获取活动列表数据====，入参：%+v", param)
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	query := marketing_po.ActivityListQuery{
		ChainId:   param.ChainId,
		StoreId:   param.StoreId,
		Status:    param.Status,
		Type:      param.Type,
		PageIndex: param.PageIndex,
		PageSize:  param.PageSize,
		OrderBy:   param.OrderBy,
		Name:      param.Name,
	}

	activityList, total, err := new(marketing_po.MarketingActivity).GetActivityList(session, query)
	if err != nil {
		log.Error(logPrefix, "获取活动列表失败,err=", err.Error())
		err = errors.New("获取活动列表失败")
		return
	}
	data = make([]marketing_vo.ActivityListData, 0)
	for _, activity := range activityList {
		residueTime := ""
		if activity.Status != marketing_po.ActivityStatusEnded {
			if activity.StartTime > time.Now().Format("2006-01-02 15:04:05") {
				activity.Status = marketing_po.ActivityStatusPending
			} else if activity.EndTime < time.Now().Format("2006-01-02 15:04:05") {
				activity.Status = marketing_po.ActivityStatusEnded
			} else {
				activity.Status = marketing_po.ActivityStatusRunning
				residueTime = utils.GetTimeDiff(activity.EndTime, time.Now().Format("2006-01-02 15:04:05"))
			}
		}
		data = append(data, marketing_vo.ActivityListData{
			Id:           activity.Id,
			Name:         activity.Name,
			Type:         activity.Type,
			Status:       activity.Status,
			StartTime:    activity.StartTime,
			EndTime:      activity.EndTime,
			CreatedTime:  activity.CreatedTime,
			ResidueTime:  residueTime,
			RuleDescribe: activity.RuleDescribe,
		})
	}

	return
}

// 获取正在参与特价活动的商品列表
func (s *ActivityService) GetRunningActivityProducts(param *marketing_vo.RunningActivityProductsReq) (data []marketing_vo.RunningActivityProducts, total int64, err error) {
	logPrefix := fmt.Sprintf("获取正在参与特价活动的商品列表数据====，入参：%s", utils.JsonEncode(param))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	data = make([]marketing_vo.RunningActivityProducts, 0)
	skuIdsli := make([]int, 0)
	if param.SkuIds != "" {
		skuIdsli = utils.StringSliceToIntSlice(strings.Split(param.SkuIds, ","))
	}
	if param.StoreId == "" {
		err = errors.New("门店id不能为空")
		return
	}

	if param.ChannelId == 0 {
		param.ChannelId = common.ChannelIdOfflineShop
	}
	// 第一步： 获取正在参与特价活动的商品列表
	RunningActivityProductsRes, total, err := new(marketing_po.MarketingProduct).GetRunningActivityProducts(session, marketing_po.RunningActivityProductsQuery{
		ChainId:   param.ChainId,
		StoreId:   param.StoreId,
		PageIndex: param.PageIndex,
		PageSize:  param.PageSize,
		SkuIds:    skuIdsli,
		Type:      marketing_po.TypeSpecialPrice,
	})
	if err != nil {
		log.Error(logPrefix, "获取正在参与特价活动的商品列表失败,err=", err.Error())
		err = errors.New("获取正在参与特价活动的商品列表失败")
		return
	}

	// 第二步：组织商品相关信息
	productIds := make([]int, 0)
	skuIds := make([]int, 0)
	for _, v := range RunningActivityProductsRes {
		productIds = append(productIds, v.MarketingProduct.ProductRefId)
		skuIds = append(skuIds, v.MarketingProduct.SkuId)
	}
	if len(productIds) == 0 || len(skuIds) == 0 {
		log.Info(logPrefix, "没有商品参与活动")
		return data, total, nil
	}

	productMap, skuMap, spuMap, productStoreInfoMap, err := product_po.GetProductSnapByPid(session, product_po.ProductSnapReq{
		ChannelId:  param.ChannelId,
		ChainId:    param.ChainId,
		StoreId:    param.StoreId,
		ProductIds: productIds,
	})
	if err != nil {
		log.Error(logPrefix, "获取商品相关信息失败,err=", err.Error())
		err = errors.New("获取商品相关信息失败")
		return
	}

	for _, v := range RunningActivityProductsRes {
		fullReductionRules := make([]marketing_po.FullReductionRule, 0)
		specialPriceRule := marketing_po.SpecialPriceRule{}
		if v.MarketingActivity.Type == marketing_po.ActivityTypeFullReduction {
			err = json.Unmarshal([]byte(v.MarketingActivity.Rule), &fullReductionRules)
		} else if v.MarketingActivity.Type == marketing_po.ActivityTypeSpecialPrice {
			err = json.Unmarshal([]byte(v.MarketingActivity.Rule), &specialPriceRule)
		}
		if err != nil {
			log.Error(logPrefix, "解析活动规则失败,err=", err.Error())
			err = errors.New("解析活动规则失败")
			return
		}
		activityDetail := marketing_po.MarketingActivityInfo{
			MarketingActivity:  v.MarketingActivity,
			FullReductionRules: fullReductionRules,
			SpecialPriceRule:   specialPriceRule,
		}
		name, pic, productSpecs := "", "", ""
		stock, retailPrice, specialPrice, serviceDuration := 0, 0, 0, 0
		if productStoreInfo, ok := productStoreInfoMap[fmt.Sprintf("%d_%s_%d_%d", param.ChannelId, param.StoreId, v.ProductRefId, v.SkuId)]; ok {
			retailPrice = productStoreInfo.RetailPrice
			if productStoreInfo.ProductType == product_po.ProductTypeService {
				name = productStoreInfo.ProductName
				pic = productStoreInfo.ProductPic
				serviceDuration = productStoreInfo.ServiceDuration
				productSpecs = name

			} else {
				name = productMap[v.ProductRefId].Name
				pic = productMap[v.ProductRefId].Pic
				productSpecs = skuMap[v.SkuId].ProductSpecs

			}
			if v.MarketingActivity.TypeClass == marketing_po.ActivityTypeClassSpecialPrice {
				specialPrice = v.ActivityPrice
			} else {
				// 根据零售价和折扣百分比计算特价
				specialPrice = int(float64(retailPrice) * v.DiscountRate / 10)

			}

		}

		categoryId := 0
		if v.MarketingProduct.ApplyType == marketing_po.ProductApplyTypeGoods {
			spuInfo, ok := spuMap[fmt.Sprintf("%d_%s_%d", param.ChannelId, param.StoreId, v.ProductRefId)]
			if ok {
				categoryId = spuInfo.ChannelCategoryId
			}
		}
		data = append(data, marketing_vo.RunningActivityProducts{
			ChainId:         param.ChainId,
			StoreId:         param.StoreId,
			Type:            v.MarketingActivity.Type,
			ApplyType:       v.MarketingProduct.ApplyType,
			ProductId:       v.ProductRefId,
			SkuId:           v.SkuId,
			CategoryId:      categoryId,
			Name:            name,
			Pic:             pic,
			Stock:           stock,
			RetailPrice:     retailPrice,
			ActivityPrice:   specialPrice,
			DiscountRate:    v.DiscountRate,
			RuleDescribe:    v.MarketingActivity.RuleDescribe,
			ActivityDetail:  activityDetail,
			ServiceDuration: serviceDuration,
			ProductSpecs:    productSpecs,
			ProductType:     v.ProductType,
		})
	}

	return
}

// SaveActivity 保存满减活动或特价活动
// 满减活动：
// - 活动时间：设置活动的开始时间和结束时间及生效周数，在活动时间内顾客购买商品才能享受满减优惠。
// - 满减门槛与优惠金额：满减门槛>0，0<满减金额<满减门槛，一次活动最多可设置3个梯度。门槛金额最多2位小数，优惠金额最多1位小数.
// - 参与商品范围：对于全店满减，默认所有商品参与；对于品类满减，需准确界定参与活动的品类（使用后台分类设置）。
// - 限制规则：门店内，所有进行中/待开始活动的门槛金额不允许相同不能同时存在相同满减门槛的活动；同个品类不能同时存在多个活动。
// - 叠加规则：支持与其他活动同享。
// 特价活动：
// - 活动时间：规定商品折扣活动的生效时间范围及生效周数。
// - 折扣商品范围：确定哪些商品参与折扣活动，可以是单个商品、多个商品。
// - 折扣范围设置：支持按折扣比例（0.1-9.9）及特定活动价设置。
// - 限购规则：可设置每个顾客每天、或活动期内购买折扣商品的数量限制（是针对活动内的每个商品而不是整个活动），如每人每天限购 3 件，或每人在活动期内限购3件。
// - 限制规则：一个商品仅能同时参与一个进行中的折扣活动。
// - 叠加规则：支持与其他活动同享（储值卡折扣、商品改价外）。
// - 使用说明：由门店在收银台主动添加折扣商品，折扣商品不支持改价。
func (s *ActivityService) SaveActivity(param *marketing_vo.SaveActivityReq) (err error) {
	logPrefix := fmt.Sprintf("保存满减活动或特价活动====，入参：%s", utils.JsonEncode(param))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	session.Begin()

	// 入参校验
	info, err := s.checkActivityParam(session, param)
	if err != nil {
		session.Rollback()
		return err
	}

	if err = info.MarketingActivity.Create(session); err != nil {
		session.Rollback()
		return err
	}

	// 保存满减活动规则
	for k := range info.MarketingProducts {
		info.MarketingProducts[k].RefId = info.MarketingActivity.Id
	}

	if len(info.MarketingProducts) > 0 {
		if err = new(marketing_po.MarketingProduct).BatchCreate(session, info.MarketingProducts); err != nil {
			session.Rollback()
			return err
		}
	}
	session.Commit()

	return nil
}

func (s *ActivityService) EditActivity(param *marketing_vo.SaveActivityReq) (err error) {
	logPrefix := fmt.Sprintf("编辑满减活动或特价活动====，入参：%+v", param)
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	if param.MarketingActivity.Id == 0 {
		return errors.New("活动id不能为空")
	}
	session.Begin()
	// 查询活动是否存在
	query := marketing_po.ActivityListQuery{
		ChainId: param.ChainId,
		StoreId: param.StoreId,
		Id:      param.MarketingActivity.Id,
	}
	activityList, _, err := new(marketing_po.MarketingActivity).GetActivityList(session, query)
	if err != nil {
		session.Rollback()
		return err
	}
	if len(activityList) == 0 {
		session.Rollback()
		return errors.New("活动不存在")
	}

	if activityList[0].Status == marketing_po.ActivityStatusEnded {
		session.Rollback()
		return errors.New("活动已结束，不能编辑")
	}
	if activityList[0].Type != param.MarketingActivity.Type {
		session.Rollback()
		return errors.New("活动类型不一致")
	}
	if activityList[0].TypeClass != param.MarketingActivity.TypeClass {
		session.Rollback()
		return errors.New("活动子类型不一致")
	}

	// 入参校验
	info, err := s.checkActivityParam(session, param)
	if err != nil {
		session.Rollback()
		return err
	}

	if err = info.MarketingActivity.Update(session); err != nil {
		session.Rollback()
		return err
	}

	inMarketingProductsMap := make(map[string]*marketing_po.MarketingProduct, 0)
	for k, v := range info.MarketingProducts {
		info.MarketingProducts[k].RefId = info.MarketingActivity.Id
		inMarketingProductsMap[fmt.Sprintf("%s_%d_%d_%d_%d", v.StoreId, v.RefId, v.ApplyType, v.ProductRefId, v.SkuId)] = v
	}

	// 查出活动商品
	query2 := marketing_po.ActivityProductListQuery{
		ActivityId: activityList[0].Id,
		ChainId:    activityList[0].ChainId,
		StoreId:    activityList[0].StoreId,
		OutType:    2,
	}
	_, _, marketingProducts, err := new(marketing_po.MarketingProduct).GetActivityProductList(session, query2)
	if err != nil {
		session.Rollback()
		return err
	}

	// 获取需要修改的数据 和 需要删除的数据 和 需要新增的数据
	editProducts := make([]*marketing_po.MarketingProduct, 0)
	deleteProducts := make([]int, 0)
	addProducts := make([]*marketing_po.MarketingProduct, 0)
	for _, product := range info.MarketingProducts {
		if p, ok := marketingProducts[fmt.Sprintf("%s_%d_%d_%d_%d", product.StoreId, product.RefId, product.ApplyType, product.ProductRefId, product.SkuId)]; ok {
			// 修改
			product.Id = p.Id
			editProducts = append(editProducts, product)
		} else {
			// 新增
			addProducts = append(addProducts, product)
		}
	}

	for _, product := range marketingProducts {
		if _, ok := inMarketingProductsMap[fmt.Sprintf("%s_%d_%d_%d_%d", product.StoreId, product.RefId, product.ApplyType, product.ProductRefId, product.SkuId)]; !ok {
			// 删除
			deleteProducts = append(deleteProducts, product.Id)
		}
	}

	if len(addProducts) > 0 {
		for k := range addProducts {
			addProducts[k].RefId = info.MarketingActivity.Id
		}
		if err = new(marketing_po.MarketingProduct).BatchCreate(session, addProducts); err != nil {
			session.Rollback()
			return err
		}
	}
	if len(editProducts) > 0 {
		for _, product := range editProducts {
			if err = product.Update(session); err != nil {
				session.Rollback()
				return err
			}
		}
	}

	if len(deleteProducts) > 0 {

		if err = new(marketing_po.MarketingProduct).Delete(session, deleteProducts); err != nil {
			session.Rollback()
			return err
		}
	}
	session.Commit()

	return nil
}

// EndActivity 结束营销活动
func (s *ActivityService) EndActivity(param *marketing_vo.EndActivityReq) (err error) {
	logPrefix := fmt.Sprintf("结束营销活动====，入参：%+v", param)
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	if param.Id == 0 {
		return errors.New("活动id不能为空")
	}
	session.Begin()
	//根据活动id获取活动信息
	query := marketing_po.ActivityListQuery{
		ChainId: param.ChainId,
		StoreId: param.StoreId,
		Id:      param.Id,
	}
	activityList, _, err := new(marketing_po.MarketingActivity).GetActivityList(session, query)
	if err != nil {
		session.Rollback()
		return err
	}
	if len(activityList) == 0 {
		session.Rollback()
		return errors.New("活动不存在")
	}
	if activityList[0].Status == marketing_po.ActivityStatusEnded {
		session.Rollback()
		return errors.New("活动已结束")
	}

	activityList[0].Status = marketing_po.ActivityStatusEnded
	if err = activityList[0].Update(session); err != nil {
		session.Rollback()
		return err
	}
	session.Commit()

	return nil
}

// 保存和编辑活动时， 检查活动参数
func (s *ActivityService) checkActivityParam(session *xorm.Session, param *marketing_vo.SaveActivityReq) (data marketing_po.MarketingActivityInfo, err error) {

	// 组织数据
	data.MarketingActivity = param.MarketingActivity
	data.FullReductionRules = param.FullReductionRules
	data.SpecialPriceRule = param.SpecialPriceRule
	data.MarketingProducts = make([]*marketing_po.MarketingProduct, 0)

	// 新增活动时，才需判断开始时间大于当前时间， 编辑活动时， 不需要判断，可能活动已经开始了， 需要编辑活动结束时间
	if param.MarketingActivity.Id == 0 {
		// 检查活动时间格式
		if start, e := time.Parse("2006-01-02 15:04:05", data.StartTime); e != nil {
			err = fmt.Errorf("开始时间格式错误: %s", e.Error())
			return
		} else if start.Before(time.Now()) {
			err = errors.New("开始时间不能小于当前时间")
			return
		}
	}

	if _, err = time.Parse("2006-01-02 15:04:05", data.EndTime); err != nil {
		err = fmt.Errorf("结束时间格式错误: %s", err.Error())
		return
	}

	// 检查活动时间有效性
	if data.StartTime >= data.EndTime {
		err = errors.New("开始时间必须早于结束时间")
		return
	}
	// 检查结束时间不能小于当前时间
	if data.EndTime <= time.Now().Format("2006-01-02 15:04:05") {
		err = errors.New("结束时间不能小于当前时间")
		return
	}

	// 检查活动类型
	if data.Type != marketing_po.ActivityTypeSpecialPrice && data.Type != marketing_po.ActivityTypeFullReduction {
		err = errors.New("无效的活动类型")
		return
	}

	// 满减活动特殊检查
	if data.Type == marketing_po.ActivityTypeFullReduction {
		if data.TypeClass != marketing_po.ActivityTypeClassFullReduction {
			err = errors.New("无效的活动子类型")
			return
		}

		// 检查满减规则
		if len(data.FullReductionRules) == 0 || len(data.FullReductionRules) > 3 {
			err = errors.New("满减规则数量必须在1-3个之间")
			return
		}

		// 检查每条规则的门槛金额和优惠金额
		for _, rule := range data.FullReductionRules {
			threshold, e := strconv.ParseFloat(rule.ThresholdAmount, 64)
			if e != nil || threshold <= 0 {
				err = errors.New("满减门槛金额必须大于0")
				return
			}

			discount, e := strconv.ParseFloat(rule.DiscountAmount, 64)
			if e != nil || discount <= 0 || discount >= threshold {
				err = errors.New("优惠金额必须大于0且小于门槛金额")
				return
			}
		}

		if param.FullReductionSetting.FullReductionSetting != 1 && param.FullReductionSetting.FullReductionSetting != 2 {
			err = errors.New("满减活动设置错误")
			return
		}

		data.Rule = utils.JsonEncode(data.FullReductionRules)
		data.RuleDescribe = "店铺满减："
		for k, rule := range param.FullReductionRules {
			data.RuleDescribe += fmt.Sprintf("满%s元减%s元", rule.ThresholdAmount, rule.DiscountAmount)
			if k != len(param.FullReductionRules)-1 {
				data.RuleDescribe += "，"
			} else {
				data.RuleDescribe += "。"
			}

		}
		//  限制规则：不能同时存在相同的满减门槛的活动，同个品类不能同时存在多个活动中
		if err = s.checkFullReductionRules(session, cast.ToInt64(data.ChainId), cast.ToString(data.StoreId), data.FullReductionRules, data.Id); err != nil {
			return
		}
		if param.FullReductionSetting.FullReductionSetting == 1 {
			data.MarketingProducts = append(data.MarketingProducts, &marketing_po.MarketingProduct{
				ChainId:       cast.ToInt64(data.ChainId),
				StoreId:       cast.ToString(data.StoreId),
				Type:          marketing_po.TypeFullReduction,
				ApplyType:     marketing_po.ProductApplyTypeGoods,
				ProductType:   0,
				ProductRefId:  0,
				SkuId:         0,
				DiscountRate:  0,
				ActivityPrice: 0,
			})
			// 把排除的商品加到数据库
			for _, product := range param.ExcludePriceProducts {
				data.MarketingProducts = append(data.MarketingProducts, &marketing_po.MarketingProduct{
					ChainId:       cast.ToInt64(data.ChainId),
					StoreId:       cast.ToString(data.StoreId),
					Type:          marketing_po.TypeFullReduction,
					ApplyType:     marketing_po.ExcludeApplyTypeGoods,
					ProductType:   0,
					ProductRefId:  product.ProductRefId,
					SkuId:         product.SkuId,
					DiscountRate:  0,
					ActivityPrice: 0,
				})
			}
		} else {
			if len(param.FullReductionSetting.CategoryIds) == 0 {
				err = errors.New("品类id不能为空")
				return
			}

			// 查询品类是否存在， 且是后台分类
			categoryWhere := product_po.CategoryMapInfoReq{
				ChainId: cast.ToInt64(data.ChainId),
				Ids:     param.FullReductionSetting.CategoryIds,
				OutType: 2,
			}
			_, _, categoriesMap, e := new(product_po.ProCategory).GetCategoryMapInfo(s.Engine, categoryWhere)
			if e != nil {
				err = fmt.Errorf("查询品类失败: %v", err)
				return
			}
			for _, cateId := range param.FullReductionSetting.CategoryIds {
				if _, ok := categoriesMap[cateId]; !ok {
					err = fmt.Errorf("品类%d不存在", cateId)
					return
				}
			}

			// 同个品类不能同时存在多个活动
			if err = s.checkCategoryFullReduction(session, cast.ToInt64(data.ChainId), cast.ToString(data.StoreId), categoriesMap, data.MarketingActivity); err != nil {
				return
			}

			// 将品类添加到营销商品中
			for _, category := range categoriesMap {
				data.MarketingProducts = append(data.MarketingProducts, &marketing_po.MarketingProduct{
					ChainId:       cast.ToInt64(data.ChainId),
					StoreId:       cast.ToString(data.StoreId),
					Type:          marketing_po.TypeFullReduction,
					ApplyType:     marketing_po.ProductApplyTypeCategory,
					ProductType:   0,
					ProductRefId:  category.Id,
					SkuId:         0,
					DiscountRate:  0,
					ActivityPrice: 0,
				})
			}
			// 把排除的商品加到数据库
			for _, product := range param.ExcludePriceProducts {
				data.MarketingProducts = append(data.MarketingProducts, &marketing_po.MarketingProduct{
					ChainId:       cast.ToInt64(data.ChainId),
					StoreId:       cast.ToString(data.StoreId),
					Type:          marketing_po.TypeFullReduction,
					ApplyType:     marketing_po.ExcludeApplyTypeGoods,
					ProductType:   0,
					ProductRefId:  product.ProductRefId,
					SkuId:         product.SkuId,
					DiscountRate:  0,
					ActivityPrice: 0,
				})
			}
		}

	}

	// 特价活动特殊检查
	if data.Type == marketing_po.ActivityTypeSpecialPrice {
		if data.TypeClass != marketing_po.ActivityTypeClassSpecialPrice && data.TypeClass != marketing_po.ActivityTypeClassDiscount {
			err = errors.New("无效的活动子类型")
			return
		}

		if data.SpecialPriceRule.LimitBuyType != "limit_by_day" && data.SpecialPriceRule.LimitBuyType != "limit_by_period" {
			err = errors.New("无效的购买限制类型")
			return
		}
		if data.SpecialPriceRule.LimitBuyNum <= 0 {
			err = errors.New("购买限制数量必须大于0")
			return
		}
		if len(param.SpecialPriceProducts) == 0 {
			err = errors.New("折扣商品信息不能为空")
			return
		}
		data.Rule = utils.JsonEncode(param.SpecialPriceRule)
		if param.SpecialPriceRule.LimitBuyType == "limit_by_day" {
			data.RuleDescribe = fmt.Sprintf("每人每天限购%d件", param.SpecialPriceRule.LimitBuyNum)
		} else {
			data.RuleDescribe = fmt.Sprintf("每人活动期间限购%d件", param.SpecialPriceRule.LimitBuyNum)
		}
		skuIds := make([]int, 0)
		productIds := make([]int, 0)
		for _, product := range param.SpecialPriceProducts {
			if product.ProductRefId <= 0 {
				err = errors.New("商品id不能为空")
				return
			}
			if product.SkuId <= 0 {
				err = errors.New("skuId不能为空")
				return
			}
			if data.TypeClass == marketing_po.ActivityTypeClassDiscount {
				// 检查折扣率是否只包含一位小数
				// 将折扣率格式化为两位小数的字符串,用于后续检查小数位数
				// 检查折扣率是否只包含一位小数
				discountStr := fmt.Sprintf("%.1f", product.DiscountRate) // 格式化为1位小数
				if product.DiscountRate != cast.ToFloat64(discountStr) { // 如果格式化后不等于原值,说明原值包含多位小数
					err = errors.New("折扣率只能包含一位小数")
					return
				}
				if product.DiscountRate < 0.1 || product.DiscountRate > 9.9 {
					err = errors.New("折扣率必须在0.1-9.9之间")
					return
				}
			} else if data.TypeClass == marketing_po.ActivityTypeClassSpecialPrice {
				if product.ActivityPrice <= 0 {
					err = errors.New("活动价必须大于0")
					return
				}
			}
			skuIds = append(skuIds, product.SkuId)
			productIds = append(productIds, product.ProductRefId)
		}

		// 判断skuId是否存在
		where := product_po.GetProductStoreInfoReq{
			StoreId:    data.StoreId,
			SkuIds:     skuIds,
			ProductIds: productIds,
			ChannelId:  common.ChannelIdOfflineShop,
			OutType:    2,
		}
		_, _, storeInfoMap, e := product_po.GetProductStoreInfo(session, where)
		if e != nil {
			err = fmt.Errorf("查询商品铺品信息失败: %v", e)
			return
		}
		// 限制规则：一个商品仅能同时参与一个进行中的折扣活动。
		if err = s.checkSpecialPrice(session, cast.ToInt64(data.ChainId), cast.ToString(data.StoreId), param.SpecialPriceProducts, data.MarketingActivity); err != nil {
			return
		}
		for _, product := range param.SpecialPriceProducts {
			key := fmt.Sprintf("%d_%s_%d_%d", common.ChannelIdOfflineShop, data.StoreId, product.ProductRefId, product.SkuId)
			if _, ok := storeInfoMap[key]; !ok {
				err = fmt.Errorf("商品不存在,skuId=%d", product.SkuId)
				return
			}
			var discountRate float64
			var activityPrice int
			if data.TypeClass == marketing_po.ActivityTypeClassDiscount {
				discountRate = product.DiscountRate
			} else if data.TypeClass == marketing_po.ActivityTypeClassSpecialPrice {
				activityPrice = product.ActivityPrice
			}
			data.MarketingProducts = append(data.MarketingProducts, &marketing_po.MarketingProduct{
				ChainId:       cast.ToInt64(data.ChainId),
				StoreId:       cast.ToString(data.StoreId),
				Type:          marketing_po.TypeSpecialPrice,
				ApplyType:     marketing_po.ProductApplyTypeGoods,
				ProductType:   storeInfoMap[key].ProductType,
				ProductRefId:  product.ProductRefId,
				SkuId:         product.SkuId,
				DiscountRate:  discountRate,
				ActivityPrice: activityPrice,
			})
		}
		// 把排除的商品加到数据库
		for _, product := range param.ExcludePriceProducts {
			data.MarketingProducts = append(data.MarketingProducts, &marketing_po.MarketingProduct{
				ChainId:       cast.ToInt64(data.ChainId),
				StoreId:       cast.ToString(data.StoreId),
				Type:          marketing_po.TypeFullReduction,
				ApplyType:     marketing_po.ExcludeApplyTypeGoods,
				ProductType:   0,
				ProductRefId:  product.ProductRefId,
				SkuId:         product.SkuId,
				DiscountRate:  0,
				ActivityPrice: 0,
			})
		}

	}

	return
}

// 满减活动限制规则
// checkFullReductionRules 检查满减活动限制规则
// 门店内，所有进行中/待开始活动的门槛金额不允许相同不能同时存在相同满减门槛的活动
func (s *ActivityService) checkFullReductionRules(session *xorm.Session, chainId int64, storeId string, rules []marketing_po.FullReductionRule, activityId int) error {

	// rules不能有相同的门槛金额
	ruleMap := make(map[string]bool)
	for _, rule := range rules {
		if _, ok := ruleMap[rule.ThresholdAmount]; ok {
			return fmt.Errorf("门槛金额(%s)已存在", rule.ThresholdAmount)
		}
		ruleMap[rule.ThresholdAmount] = true
	}

	// 获取门店内所有进行中和待开始的满减活动
	query := marketing_po.ActivityListQuery{
		ChainId: chainId,
		StoreId: storeId,
		Type:    marketing_po.ActivityTypeFullReduction,
		Status:  4,
	}
	activities, _, err := new(marketing_po.MarketingActivity).GetActivityList(session, query)
	if err != nil {
		return fmt.Errorf("查询活动列表失败: %v", err)
	}

	// 遍历所有活动的满减规则
	existingThresholds := make(map[string]string)
	for _, activity := range activities {
		// 跳过当前编辑的活动
		if activity.Id == activityId {
			continue
		}

		var existingRules []marketing_po.FullReductionRule
		if err := json.Unmarshal([]byte(activity.Rule), &existingRules); err != nil {
			return fmt.Errorf("解析活动规则失败: %v", err)
		}

		for _, rule := range existingRules {
			existingThresholds[rule.ThresholdAmount] = activity.Name
		}
	}

	// 检查新规则是否与现有规则冲突
	for _, rule := range rules {
		if activityName, ok := existingThresholds[rule.ThresholdAmount]; ok {
			return fmt.Errorf("已存在相同门槛金额(%s)的活动(%s)", rule.ThresholdAmount, activityName)
		}
	}

	return nil
}

// 同个品类不能同时存在多个活动
func (s *ActivityService) checkCategoryFullReduction(session *xorm.Session, chainId int64, storeId string, categoriesMap map[int]product_po.ProCategory, activityInfo marketing_po.MarketingActivity) error {

	categoryIdOfflines := make([]int, 0)
	for _, v := range categoriesMap {
		categoryIdOfflines = append(categoryIdOfflines, v.Id)
	}
	CheckSkuIsParticipateOtherActivityReq := marketing_po.CheckSkuIsParticipateOtherActivity{
		ChainId:            chainId,
		StoreId:            storeId,
		CategoryIdOfflines: categoryIdOfflines,
		Type:               marketing_po.TypeFullReduction,
	}
	if activityInfo.Id > 0 {
		CheckSkuIsParticipateOtherActivityReq.NotEqualActivityId = activityInfo.Id
	}
	// 获取活动关联的商品信息
	GetPendingAndRunningActivityProducts, err := new(marketing_po.MarketingProduct).CheckSkuIsParticipateOtherActivity(session, CheckSkuIsParticipateOtherActivityReq)

	if err != nil {
		return fmt.Errorf("检查活动商品是否存在: %s", err.Error())
	}
	msg := ""
	newStartTime, _ := time.Parse("2006-01-02 15:04:05", activityInfo.StartTime)
	newEndTime, _ := time.Parse("2006-01-02 15:04:05", activityInfo.EndTime)

	for _, p2 := range GetPendingAndRunningActivityProducts {
		startTime, _ := time.Parse("2006-01-02 15:04:05", p2.MarketingActivity.StartTime)
		endTime, _ := time.Parse("2006-01-02 15:04:05", p2.MarketingActivity.EndTime)
		// 检查时间是否重叠
		if !(newEndTime.Before(startTime) || newStartTime.After(endTime)) {
			msg = fmt.Sprintf("%s,商品分类(categoryId:%d)已参与活动Id(%d)活动名称(%s)", msg, p2.ProductRefId, p2.MarketingActivity.Id, p2.MarketingActivity.Name)
		}

	}

	if msg != "" {
		return errors.New(strings.Trim(msg, ","))
	}

	return nil

}

// checkSpecialPrice 检查特价活动限制规则
// 一个商品仅能同时参与一个进行中的折扣活动
func (s *ActivityService) checkSpecialPrice(session *xorm.Session, chainId int64, storeId string, products []marketing_po.SpecialPriceProduct, activityInfo marketing_po.MarketingActivity) error {

	skuids := make([]int, 0)
	for _, product := range products {
		skuids = append(skuids, product.SkuId)
	}
	CheckSkuIsParticipateOtherActivityReq := marketing_po.CheckSkuIsParticipateOtherActivity{
		ChainId: chainId,
		StoreId: storeId,
		SkuIds:  skuids,
		Type:    marketing_po.TypeSpecialPrice,
	}
	if activityInfo.Id > 0 {
		CheckSkuIsParticipateOtherActivityReq.NotEqualActivityId = activityInfo.Id
	}
	// 获取活动关联的商品信息
	GetPendingAndRunningActivityProducts, err := new(marketing_po.MarketingProduct).CheckSkuIsParticipateOtherActivity(session, CheckSkuIsParticipateOtherActivityReq)

	if err != nil {
		return fmt.Errorf("检查活动商品是否存在: %s", err.Error())
	}
	msg := ""
	newStartTime, _ := time.Parse("2006-01-02 15:04:05", activityInfo.StartTime)
	newEndTime, _ := time.Parse("2006-01-02 15:04:05", activityInfo.EndTime)

	for _, p2 := range GetPendingAndRunningActivityProducts {
		startTime, _ := time.Parse("2006-01-02 15:04:05", p2.MarketingActivity.StartTime)
		endTime, _ := time.Parse("2006-01-02 15:04:05", p2.MarketingActivity.EndTime)
		// 检查时间是否重叠
		if !(newEndTime.Before(startTime) || newStartTime.After(endTime)) {
			msg = fmt.Sprintf("%s,商品(skuId:%d)已参与活动Id(%d)活动名称(%s)", msg, p2.SkuId, p2.MarketingActivity.Id, p2.MarketingActivity.Name)
		}

	}

	if msg != "" {
		return errors.New(strings.Trim(msg, ","))
	}

	return nil

}
