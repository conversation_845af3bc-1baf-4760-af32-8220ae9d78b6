package export

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/omnibus-service/services"
	omnibus_vo "eShop/view-model/omnibus-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type PerformanceExport struct {
	F            *excelize.File
	SheetName    string
	ExportParams *omnibus_vo.GetPerformanceListReq
	writer       *excelize.StreamWriter
	common.BaseService
}

// {"id":1549,"task_content":104,"task_status":1,"task_detail":"","operation_file_url":"{\"real_name\":\"\",\"mobile\":\"\",\"role_id\":0,\"start_date\":\"2025-01-01 00:00:00\",\"end_date\":\"2025-05-07 23:59:59\",\"page_index\":1,\"page_size\":10,\"store_id\":577549921650660693,\"source_chain_id\":577549921650660692,\"is_export\":1}","request_header":"{\"TenantId\":\"577549921650660693\",\"TenantIds\":\"\",\"Uuid\":\"bd5dc5f95bad4c78a7c8508f56c49dd5\",\"UserId\":\"577552661839797251\",\"user_name\":\"老板\",\"CustomerId\":\"0\",\"EmployeeId\":\"577549921650660700\",\"ChainId\":\"577549921650660692\",\"SourceChainId\":\"577549921650660692\",\"iat\":1746499646,\"nbf\":1746499646,\"exp\":1749091646,\"role_type\":1,\"token\":\"\"}","resulte_file_url":"","create_id":"577552661839797251","create_time":"2025-05-07T18:51:43.4891582+08:00","create_name":"老板","create_mobile":"","create_ip":"127.0.0.1","ip_location":"","success_num":0,"fail_num":0,"extended_data":"员工业绩导出","context_data":"","org_id":6,"do_count":0,"err_mes":"","update_time":"2025-05-07T18:51:43.4891582+08:00","operation_type":0,"org_type":0}
// DataExport 分销员导出
func (e PerformanceExport) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(omnibus_vo.GetPerformanceListReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 10000

	//使用流式写入，会更节省内存
	// 1. 先把 Sheet1 重命名为“员工业绩”
	err = e.F.SetSheetName("Sheet1", "员工业绩")
	if err != nil {
		log.Error("重命名sheet失败:", err.Error())
		return 0, 0, errors.New("重命名sheet失败, " + err.Error())
	}

	// 2. 用流式写入到“员工业绩”
	e.writer, err = e.F.NewStreamWriter("员工业绩")
	if err != nil {
		return 0, 0, errors.New("生成文件失败, " + err.Error())
	}

	//设置表头
	e.SetSheetName()
	client := services.CommissionService{}

	ret, _, err := client.GetEmployeePerformance(*e.ExportParams)
	if err != nil {
		err = errors.New("获取导出数据失败, " + err.Error())
		return 0, 0, err
	}
	e.ExportParams.PageIndex += 1
	for i := 0; i < len(ret); i++ {
		axis := fmt.Sprintf("A%d", i+2)
		_ = e.writer.SetRow(axis, []interface{}{
			ret[i].Mobile,   // 员工账号
			ret[i].RealName, // 员工姓名
			fmt.Sprintf("%.2f", float64(ret[i].ProductSales)/100),      // 商品业绩(元)
			fmt.Sprintf("%.2f", float64(ret[i].ProductCommission)/100), // 商品提成(元)
			fmt.Sprintf("%.2f", float64(ret[i].ServiceSales)/100),      // 服务业绩(元)
			fmt.Sprintf("%.2f", float64(ret[i].ServiceCommission)/100), // 服务提成(元)
			fmt.Sprintf("%.2f", float64(ret[i].FosterSales)/100),       // 寄养业绩(元)
			fmt.Sprintf("%.2f", float64(ret[i].FosterCommission)/100),  // 寄养提成(元)
			fmt.Sprintf("%.2f", float64(ret[i].LiveSales)/100),         // 活体业绩(元)
			fmt.Sprintf("%.2f", float64(ret[i].LiveCommission)/100),    // 活体提成(元)
			fmt.Sprintf("%.2f", float64(ret[i].TotalSales)/100),        // 总业绩(元)
			fmt.Sprintf("%.2f", float64(ret[i].TotalCommission)/100),   // 总提成(元)
		})
	}
	_ = e.writer.Flush()

	_, err = e.F.NewSheet("员工详情")
	if err != nil {
		log.Error("创建员工详情sheet失败:", err.Error())
		err = errors.New("创建员工详情sheet失败, " + err.Error())
		return 0, 0, err
	}

	// 导出员工详情
	detailWriter, err := e.F.NewStreamWriter("员工详情")
	if err != nil {
		err = errors.New("生成员工详情sheet失败, " + err.Error())
		return 0, 0, err
	}

	// 设置员工详情表头
	detailHeaders := []interface{}{
		"员工姓名", "订单时间", "客户姓名", "商品名称", "单价", "数量", "业绩", "提成",
	}
	_ = detailWriter.SetRow("A1", detailHeaders)

	// 获取员工详情数据
	detailReq := omnibus_vo.GetPerformanceDetailReq{
		EmployeeId: ret[0].EmployeeId,
		StoreId:    e.ExportParams.StoreId,
		StartDate:  e.ExportParams.StartDate,
		EndDate:    e.ExportParams.EndDate,
		PageSize:   10000,
	}

	detailResp, err := client.GetEmployeePerformanceDetail(detailReq)
	if err != nil {
		err = errors.New("获取员工详情数据失败, " + err.Error())
		return 0, 0, err
	}
	// 写入员工详情数据
	for i, item := range detailResp.List {
		axis := fmt.Sprintf("A%d", i+2)
		_ = detailWriter.SetRow(axis, []interface{}{
			item.RealName,     // 员工姓名
			item.OrderTime,    // 订单时间
			item.CustomerName, // 客户姓名
			item.ProductName,  // 商品名称
			fmt.Sprintf("%.2f", float64(item.UnitPrice)/100), // 单价
			item.Quantity, // 数量
			fmt.Sprintf("%.2f", float64(item.SalesAmount)/100),      // 业绩
			fmt.Sprintf("%.2f", float64(item.CommissionAmount)/100), // 提成
		})
	}
	_ = detailWriter.Flush()

	return
}

// SetSheetName 分销员导出列表头
func (e PerformanceExport) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"员工账号", "员工姓名", "商品业绩(元)", "商品提成(元)", "服务业绩(元)", "服务提成(元)", "寄养业绩(元)", "寄养提成(元)", "活体业绩(元)", "活体提成(元)", "总业绩(元)", "总提成(元)",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e PerformanceExport) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("员工业绩(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e PerformanceExport) OperationFunc(row []string, orgId int) string {
	return ""
}
