package services

import (
	omnibus_po "eShop/domain/omnibus-po"
	"eShop/infra/log"
	"eShop/services/common"
	omnibus_vo "eShop/view-model/omnibus-vo"
	"encoding/json"

	"github.com/spf13/cast"
)

type InventoryService struct {
	common.BaseService
}

func (s InventoryService) QueryStock(in omnibus_vo.QueryStockReq) (out omnibus_vo.QueryStockRes) {
	out = omnibus_vo.QueryStockRes{
		Code:  400,
		Stock: make(map[int64]omnibus_vo.VInventory),
	}
	s.<PERSON>gin()
	defer s.Close()

	var stocks = make([]omnibus_vo.VInventory, 0)
	db := s.Engine.Table("eshop.inventory").Alias("i").
		Select("i.sku_id, i.available_num, l.code as location_code").
		Join("LEFT", "eshop.inventory_location l", "i.store_id = l.store_id AND i.sku_id = l.sku_id").
		Where("i.store_id = ?", in.ShopId)

	if len(in.SkuIds) > 0 {
		db = db.In("i.sku_id", in.SkuIds)
	}
	if len(in.ProductIds) > 0 {
		db = db.In("i.product_id", in.ProductIds)
	}

	err := db.Find(&stocks)
	if err != nil {
		log.Error("查询库存出错", err.Error())
		out.Message = err.Error()
		return
	}

	if len(stocks) > 0 {
		for _, v := range stocks {
			out.Stock[v.SkuId] = omnibus_vo.VInventory{
				AvailableNum: v.AvailableNum,
				LocationCode: v.LocationCode,
			}
		}
	}
	out.Code = 200
	return
}

func (s InventoryService) SyncStock(Data []omnibus_vo.SyncStockData) {
	var mqInfos []omnibus_po.MqInfo

	for _, info := range Data {
		var (
			foodData     []omnibus_vo.FoodData
			AvailableNum int32
		)
		//查询库存
		stockRes := new(InventoryService).QueryStock(omnibus_vo.QueryStockReq{
			ShopId: info.StoreId,
			SkuIds: []int64{cast.ToInt64(info.SkuId)},
		})
		if stockRes.Code != 200 {
			log.Infof("查询库存失败，门店：%s,商品：%d,库存：%s", info.StoreId, info.SkuId, stockRes.Message)
			continue
		}
		if stockRes.Code == 200 {
			AvailableNum = stockRes.Stock[info.SkuId].AvailableNum
		}
		foodData = append(foodData, omnibus_vo.FoodData{
			App_food_code: cast.ToString(info.ProductId),
			Skus: []omnibus_vo.Skus{{
				Sku_id: cast.ToString(info.SkuId),
				Stock:  cast.ToString(AvailableNum),
			}},
		})
		content, _ := json.Marshal(omnibus_vo.RetailSkuStock{
			App_poi_code: info.StoreId,
			Food_data:    foodData,
		})
		mqInfo := omnibus_po.MqInfo{
			Exchange: "datacenter",
			Content:  string(content),
		}
		if info.ChannelId == common.ChannelIdMT {
			mqInfo.Quene = "dc_sz_stock_mq"
		} else if info.ChannelId == common.ChannelIdELM {
			mqInfo.Quene = "dc_sz_stock_mq_elm"
		}
		mqInfos = append(mqInfos, mqInfo)
	}
	s.Begin()
	defer s.Close()
	db := s.Engine
	if _, err := db.Table("dc_order.mq_info").Insert(mqInfos); err != nil {
		log.Errorf("mq_info插入失败，err:%s", err.Error())
	}
}
