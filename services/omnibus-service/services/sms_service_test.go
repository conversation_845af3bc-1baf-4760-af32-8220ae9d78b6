package services

import (
	"context"
	"eShop/infra/log"
	"eShop/services/common"
	omnibus_vo "eShop/view-model/omnibus-vo"
	"reflect"
	"testing"
)

func TestSmsService_GetSmsConfigs(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		ctx     context.Context
		storeId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []omnibus_vo.SmsConfigItem
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "获取短信配置",
			fields: fields{
				BaseService: common.BaseService{},
			},
			args: args{
				ctx:     context.Background(),
				storeId: "576534157590154085",
			},
			want:    []omnibus_vo.SmsConfigItem{},
			wantErr: false,
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SmsService{
				BaseService: tt.fields.BaseService,
			}
			got, err := s.GetSmsConfigs(tt.args.ctx, tt.args.storeId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSmsConfigs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSmsConfigs() got = %v, want %v", got, tt.want)
			}
		})
	}
}
