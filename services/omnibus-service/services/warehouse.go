package services

import (
	"eShop/services/common"
	order_vo "eShop/view-model/order-vo"
)

type WarehouseService struct {
	common.BaseService
}

// 查询渠道的仓库设置信息
func (s WarehouseService) GetChannelWarehouses(finance_code string) ([]order_vo.ChannelWarehouse, error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	sql := `
		select wrs.shop_id,
		wrs.shop_name,
	   w.code as warehouse_code,
	   w.name as warehouse_name,
	   w.id as warehouse_id,
	   w.category, 
	   wrs.channel_id
from dc_dispatch.warehouse_relation_shop wrs 
join dc_dispatch.warehouse w on wrs.warehouse_id  = w.id
where wrs.shop_id  = ? ;
`
	channelWarehouses := make([]order_vo.ChannelWarehouse, 0)
	err := session.SQL(sql, finance_code).Find(&channelWarehouses)
	if err != nil {
		return channelWarehouses, err
	}

	return channelWarehouses, nil
}
