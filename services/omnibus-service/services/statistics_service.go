package services

import (
	omnibus_po "eShop/domain/omnibus-po"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	omnibus_vo "eShop/view-model/omnibus-vo"
	"fmt"
	"time"

	"github.com/spf13/cast"
)

// 首页六宫格统计
func GetStatisticsOverview(storeId string) (omnibus_vo.StatisticsOverviewRes, error) {
	var res omnibus_vo.StatisticsOverviewRes

	db := utils.NeWOrderDbConn()
	// 获取今日和昨日的开始结束时间
	now := time.Now()
	itemtodayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	itemtodayEnd := itemtodayStart.Add(24 * time.Hour)
	todayStart := itemtodayStart.Format(utils.DateTimeLayout)
	todayEnd := itemtodayEnd.Format(utils.DateTimeLayout)
	yesterdayStart := itemtodayStart.Add(-24 * time.Hour).Format(utils.DateTimeLayout)
	yesterdayEnd := todayStart

	redis := cache.GetRedisConn()
	countKey := omnibus_po.StoreSmsCountKey + storeId
	count, _ := redis.Get(countKey).Int64()
	res.SurplusCount = cast.ToInt(count)

	// 1. 统计今日实际到账（已支付订单金额 - 退款金额）
	var todayPayTotal, yesterdayPayTotal int
	var todayRefundAmount, yesterdayRefundAmount float64
	_, err := db.SQL(`
		SELECT COALESCE(SUM(pay_total), 0)
		FROM order_main 
		WHERE shop_id = ? 
		AND create_time >= ? 
		AND create_time < ? 
		AND is_pay = 1 and parent_order_sn!='' and order_status=30 `,
		storeId, todayStart, todayEnd).Get(&todayPayTotal)
	if err != nil {
		return res, fmt.Errorf("查询今日支付金额失败: %v", err)
	}

	_, err = db.SQL(`
		SELECT COALESCE(SUM(CAST(a.refund_amount AS DECIMAL(10,2))), CAST(0 AS DECIMAL(10,2)))
		FROM refund_order a inner join order_main b on a.order_sn=b.order_sn 
		WHERE a.shop_id = ? 
		AND a.create_time >= ? 
		AND a.create_time < ?
		AND a.refund_state = 3 and b.order_status=30 `,
		storeId, todayStart, todayEnd).Get(&todayRefundAmount)
	if err != nil {
		return res, fmt.Errorf("查询今日退款金额失败: %v", err)
	}

	_, err = db.SQL(`
		SELECT COALESCE(SUM(pay_total), 0)
		FROM order_main 
		WHERE shop_id = ? 
		AND create_time >= ? 
		AND create_time < ?
		AND is_pay = 1 and parent_order_sn!='' and order_status=30`,
		storeId, yesterdayStart, yesterdayEnd).Get(&yesterdayPayTotal)
	if err != nil {
		return res, fmt.Errorf("查询昨日支付金额失败: %v", err)
	}

	_, err = db.SQL(`
		SELECT COALESCE(SUM(CAST(a.refund_amount AS DECIMAL(10,2))), CAST(0 AS DECIMAL(10,2)))
		FROM refund_order a inner join order_main b on a.order_sn=b.order_sn 
		WHERE a.shop_id = ? 
		AND a.create_time >= ? 
		AND a.create_time < ?
		AND a.refund_state = 3 and b.order_status=30 `,
		storeId, yesterdayStart, yesterdayEnd).Get(&yesterdayRefundAmount)
	if err != nil {
		return res, fmt.Errorf("查询昨日退款金额失败: %v", err)
	}

	// 2. 统计今日和昨日支付订单数
	var todayPayOrderCount, yesterdayPayOrderCount int
	_, err = db.SQL(`
		SELECT COUNT(*) 
		FROM order_main 
		WHERE shop_id = ? 
		AND create_time >= ? 
		AND create_time < ?
		AND is_pay = 1
		and parent_order_sn!='' `,
		storeId, todayStart, todayEnd).Get(&todayPayOrderCount)
	if err != nil {
		return res, fmt.Errorf("查询今日支付订单数失败: %v", err)
	}

	_, err = db.SQL(`
		SELECT COUNT(*) 
		FROM order_main 
		WHERE shop_id = ? 
		AND create_time >= ? 
		AND create_time < ?
		AND is_pay = 1 and parent_order_sn!=''`,
		storeId, yesterdayStart, yesterdayEnd).Get(&yesterdayPayOrderCount)
	if err != nil {
		return res, fmt.Errorf("查询昨日支付订单数失败: %v", err)
	}

	// 3. 统计今日和昨日预约订单数
	var todayReserveOrderCount, yesterdayReserveOrderCount int
	_, err = db.SQL(`
		SELECT COUNT(*) 
		FROM eshop_saas.r_reservation_base 
		WHERE tenant_id = ? 
		AND created_time >= ? 
		AND created_time < ?`,
		cast.ToInt64(storeId), todayStart, todayEnd).Get(&todayReserveOrderCount)
	if err != nil {
		return res, fmt.Errorf("查询今日预约订单数失败: %v", err)
	}

	_, err = db.SQL(`
		SELECT COUNT(*) 
		FROM eshop_saas.r_reservation_base 
		WHERE tenant_id = ? 
		AND created_time >= ? 
		AND created_time < ?`,
		cast.ToInt64(storeId), yesterdayStart, yesterdayEnd).Get(&yesterdayReserveOrderCount)
	if err != nil {
		return res, fmt.Errorf("查询昨日预约订单数失败: %v", err)
	}

	// 4. 统计今日和昨日营业流水
	var todayBusinessAmount, yesterdayBusinessAmount int64
	_, err = db.SQL(`
		SELECT COALESCE(SUM(pay_total), 0)
		FROM order_main 
		WHERE shop_id = ? 
		AND create_time >= ? 
		AND create_time < ?
		AND is_pay = 1 and parent_order_sn!='' and order_status=30`,
		storeId, todayStart, todayEnd).Get(&todayBusinessAmount)
	if err != nil {
		return res, fmt.Errorf("查询今日营业流水失败: %v", err)
	}

	_, err = db.SQL(`
		SELECT COALESCE(SUM(pay_total), 0)
		FROM order_main 
		WHERE shop_id = ? 
		AND create_time >= ? 
		AND create_time < ?
		AND is_pay = 1 and parent_order_sn!='' and order_status=30`,
		storeId, yesterdayStart, yesterdayEnd).Get(&yesterdayBusinessAmount)
	if err != nil {
		return res, fmt.Errorf("查询昨日营业流水失败: %v", err)
	}

	// 5. 统计商品数（今日和昨日）
	var todayProductCount, yesterdayProductCount int
	_, err = db.SQL(`
		SELECT COUNT(DISTINCT op.sku_id)
		FROM eshop.pro_product_store_info op
		WHERE op.store_id = ? 
		AND op.create_date < ?`,
		storeId, todayEnd).Get(&todayProductCount)
	if err != nil {
		return res, fmt.Errorf("查询今日商品数失败: %v", err)
	}

	_, err = db.SQL(`
			SELECT COUNT(DISTINCT op.sku_id)
		FROM eshop.pro_product_store_info op
		WHERE op.store_id = ? 
		AND op.create_date < ?`,
		storeId, yesterdayEnd).Get(&yesterdayProductCount)
	if err != nil {
		return res, fmt.Errorf("查询昨日商品数失败: %v", err)
	}

	// 6. 统计客户数（今日和昨日）
	var todayCustomerCount, yesterdayCustomerCount int
	_, err = db.SQL(`
		SELECT COUNT(id)
		FROM eshop_saas.c_customer_info 
		WHERE tenant_id = ? 
		AND created_time < ?`,
		cast.ToInt64(storeId), todayEnd).Get(&todayCustomerCount)
	if err != nil {
		return res, fmt.Errorf("查询今日客户数失败: %v", err)
	}

	_, err = db.SQL(`
		SELECT COUNT(id)
		FROM eshop_saas.c_customer_info 
		WHERE tenant_id = ? 
		AND created_time < ?`,
		cast.ToInt64(storeId), yesterdayEnd).Get(&yesterdayCustomerCount)
	if err != nil {
		return res, fmt.Errorf("查询昨日客户数失败: %v", err)
	}

	//查询记账金额
	var todayChargeAmount, yesterdayChargeAmount float64
	_, err = db.SQL(`
    SELECT COALESCE(
        SUM(
            CASE WHEN income_type = 'INCOME' 
                THEN CAST(amount AS DECIMAL(10, 2)) 
                ELSE CAST(-amount AS DECIMAL(10, 2)) 
            END
        ), 
        CAST(0 AS DECIMAL(10, 2))
    )
    FROM eshop_saas.p_charge_account 
    WHERE tenant_id = ? 
    AND created_time >= ? 
    AND created_time < ? 
    AND is_deleted = 0`,
		cast.ToInt64(storeId), todayStart, todayEnd).Get(&todayChargeAmount)
	if err != nil {
		return res, fmt.Errorf("查询今日记账金额失败: %v", err)
	}

	_, err = db.SQL(`
    SELECT COALESCE(
        SUM(
            CASE WHEN income_type = 'INCOME' 
                THEN CAST(amount AS DECIMAL(10, 2)) 
                ELSE CAST(-amount AS DECIMAL(10, 2)) 
            END
        ), 
        CAST(0 AS DECIMAL(10, 2))
    )
    FROM eshop_saas.p_charge_account 
    WHERE tenant_id = ? 
    AND created_time >= ? 
    AND created_time < ? 
    AND is_deleted = 0`,
		cast.ToInt64(storeId), yesterdayStart, yesterdayEnd).Get(&yesterdayChargeAmount)
	if err != nil {
		return res, fmt.Errorf("查询昨日记账金额失败: %v", err)
	}

	//查询储值卡使用记录
	var todayStoreCardConsume, yesterdayStoreCardConsume float64

	// 查询今日储值卡消费金额
	_, err = db.SQL(`
	SELECT 
    COALESCE(
        SUM(CAST(balance_change AS DECIMAL(10, 2))), 
        CAST(0 AS DECIMAL(10, 2))
    ) AS total_balance
		FROM eshop_saas.m_store_card_use_record 
		WHERE tenant_id = ? 
		AND created_time >= ? 
		AND created_time < ? 
		AND operate_type in ('CONSUMPTION','REFUND')
		AND is_deleted = 0`,
		cast.ToInt64(storeId), todayStart, todayEnd).Get(&todayStoreCardConsume)
	if err != nil {
		return res, fmt.Errorf("查询今日储值卡消费金额失败: %v", err)
	}

	// 查询昨日储值卡消费金额
	_, err = db.SQL(`
	SELECT 
    COALESCE(
        SUM(CAST(balance_change AS DECIMAL(10, 2))), 
        CAST(0 AS DECIMAL(10, 2))
    ) AS total_balance
		FROM eshop_saas.m_store_card_use_record 
		WHERE tenant_id = ? 
		AND created_time >= ? 
		AND created_time < ? 
		AND operate_type in ('CONSUMPTION','REFUND')
		AND is_deleted = 0`,
		cast.ToInt64(storeId), yesterdayStart, yesterdayEnd).Get(&yesterdayStoreCardConsume)
	if err != nil {
		return res, fmt.Errorf("查询昨日储值卡消费金额失败: %v", err)
	}

	// 组装返回结果，使用utils.Fen2Yuan()转换金额
	res = omnibus_vo.StatisticsOverviewRes{
		ActualIncome:       utils.Fen2Yuan(todayPayTotal - utils.Yuan2Fen(todayRefundAmount) + utils.Yuan2Fen(todayChargeAmount) + utils.Yuan2Fen(todayStoreCardConsume)),
		ActualIncomeYest:   utils.Fen2Yuan(yesterdayPayTotal - utils.Yuan2Fen(yesterdayRefundAmount) + utils.Yuan2Fen(yesterdayChargeAmount) + utils.Yuan2Fen(yesterdayStoreCardConsume)),
		PayOrderCount:      todayPayOrderCount,
		PayOrderCountYest:  yesterdayPayOrderCount,
		ReserveOrderCount:  todayReserveOrderCount,
		ReserveOrderYest:   yesterdayReserveOrderCount,
		BusinessAmount:     utils.Fen2Yuan(todayBusinessAmount),
		BusinessAmountYest: utils.Fen2Yuan(yesterdayBusinessAmount),
		ProductCount:       todayProductCount,
		ProductCountYest:   yesterdayProductCount,
		CustomerCount:      todayCustomerCount,
		CustomerCountYest:  yesterdayCustomerCount,
	}

	return res, nil
}

// 首页趋势图统计
// trendType: 1-储值卡销售，2-商品销售，3-服务销售，4-活体销售，5-次卡销售
func GetStatisticsTrend(storeId, startDate, endDate string, trendType int) (omnibus_vo.StatisticsTrendRes, error) {
	var res omnibus_vo.StatisticsTrendRes
	db := utils.NeWOrderDbConn()

	// 解析日期范围
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return res, fmt.Errorf("开始日期格式错误: %v", err)
	}
	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return res, fmt.Errorf("结束日期格式错误: %v", err)
	}
	end = time.Date(end.Year(), end.Month(), end.Day(), 23, 59, 59, 0, end.Location())
	// 生成日期列表
	var dates []string
	for d := start; !d.After(end); d = d.AddDate(0, 0, 1) {
		dates = append(dates, d.Format("2006-01-02"))
	}

	// 查询已支付订单金额，按天分组
	orderSql := ""
	switch trendType {
	case 1: // 储值卡销售
		orderSql = `SELECT DATE_FORMAT(om.create_time, '%Y-%m-%d') as day, COALESCE(SUM(om.pay_total),0) as total FROM order_main om WHERE om.shop_id = ? AND om.is_pay = 1 AND om.order_type = 23 AND om.parent_order_sn!='' AND om.create_time >= ? AND om.create_time <= ? GROUP BY day`
	case 2: // 商品销售
		orderSql = `SELECT DATE_FORMAT(om.create_time, '%Y-%m-%d') as day, COALESCE(SUM(op.payment_total),0) as total FROM order_product op JOIN order_main om ON op.order_sn = om.order_sn WHERE om.shop_id = ? AND om.is_pay = 1 AND op.product_type = 1 AND om.parent_order_sn!='' AND om.create_time >= ? AND om.create_time <= ? GROUP BY day`
	case 3: // 服务销售
		orderSql = `SELECT DATE_FORMAT(om.create_time, '%Y-%m-%d') as day, COALESCE(SUM(op.payment_total),0) as total FROM order_product op JOIN order_main om ON op.order_sn = om.order_sn WHERE om.shop_id = ? AND om.is_pay = 1 AND op.product_type = 4 AND om.parent_order_sn!='' AND om.create_time >= ? AND om.create_time <= ? GROUP BY day`
	case 4: // 活体销售
		orderSql = `SELECT DATE_FORMAT(om.create_time, '%Y-%m-%d') as day, COALESCE(SUM(op.payment_total),0) as total FROM order_product op JOIN order_main om ON op.order_sn = om.order_sn WHERE om.shop_id = ? AND om.is_pay = 1 AND op.product_type = 5 AND om.parent_order_sn!='' AND om.create_time >= ? AND om.create_time <= ? GROUP BY day`
	case 5: // 次卡销售
		orderSql = `SELECT DATE_FORMAT(om.create_time, '%Y-%m-%d') as day, COALESCE(SUM(om.pay_total),0) as total FROM order_main om WHERE om.shop_id = ? AND om.is_pay = 1 AND om.order_type = 22 AND om.parent_order_sn!='' AND om.create_time >= ? AND om.create_time <= ? GROUP BY day`
	default:
		return res, fmt.Errorf("无效的趋势类型: %d", trendType)
	}
	db.ShowSQL()
	orderRows, err := db.QueryString(orderSql, storeId, start, end)
	if err != nil {
		return res, fmt.Errorf("查询订单金额失败: %v", err)
	}
	orderMap := make(map[string]int64)
	for _, row := range orderRows {
		orderMap[row["day"]] = cast.ToInt64(row["total"])
	}

	// 查询退款金额，按天分组
	refundSql := ""
	switch trendType {
	case 1: // 储值卡销售
		refundSql = `SELECT DATE_FORMAT(ro.create_time, '%Y-%m-%d') as day, COALESCE(SUM(CAST(ro.refund_amount AS DECIMAL(10,2)) * 100),CAST(0 AS DECIMAL(12,2))) as total FROM refund_order ro JOIN order_main om ON ro.order_sn = om.order_sn WHERE ro.shop_id = ? AND ro.refund_state = 3 AND om.order_type = 23  AND ro.create_time >= ? AND ro.create_time <= ? GROUP BY day`
	case 2: // 商品销售
		refundSql = `SELECT DATE_FORMAT(ro.create_time, '%Y-%m-%d') as day, COALESCE(SUM(CAST(rop.refund_amount AS DECIMAL(10,2)) * 100),CAST(0 AS DECIMAL(12,2))) as total FROM refund_order_product rop JOIN refund_order ro ON rop.refund_sn = ro.refund_sn WHERE ro.shop_id = ? AND ro.refund_state = 3 AND rop.product_type = 1  AND ro.create_time >= ? AND ro.create_time <= ? GROUP BY day`
	case 3: // 服务销售
		refundSql = `SELECT DATE_FORMAT(ro.create_time, '%Y-%m-%d') as day, COALESCE(SUM(CAST(rop.refund_amount AS DECIMAL(10,2)) * 100),CAST(0 AS DECIMAL(12,2))) as total FROM refund_order_product rop JOIN refund_order ro ON rop.refund_sn = ro.refund_sn WHERE ro.shop_id = ? AND ro.refund_state = 3 AND rop.product_type = 4  AND ro.create_time >= ? AND ro.create_time <= ? GROUP BY day`
	case 4: // 活体销售
		refundSql = `SELECT DATE_FORMAT(ro.create_time, '%Y-%m-%d') as day, COALESCE(SUM(CAST(rop.refund_amount AS DECIMAL(10,2)) * 100),CAST(0 AS DECIMAL(12,2))) as total FROM refund_order_product rop JOIN refund_order ro ON rop.refund_sn = ro.refund_sn WHERE ro.shop_id = ? AND ro.refund_state = 3 AND rop.product_type = 5  AND ro.create_time >= ? AND ro.create_time <= ? GROUP BY day`
	case 5: // 次卡销售
		refundSql = `SELECT DATE_FORMAT(ro.create_time, '%Y-%m-%d') as day, COALESCE(SUM(CAST(ro.refund_amount AS DECIMAL(10,2)) * 100),CAST(0 AS DECIMAL(12,2))) as total FROM refund_order ro JOIN order_main om ON ro.order_sn = om.order_sn WHERE ro.shop_id = ? AND ro.refund_state = 3 AND om.order_type = 22  AND ro.create_time >= ? AND ro.create_time <= ? GROUP BY day`
	default:
		return res, fmt.Errorf("无效的趋势类型: %d", trendType)
	}
	refundRows, err := db.QueryString(refundSql, storeId, start, end)
	if err != nil {
		return res, fmt.Errorf("查询退款金额失败: %v", err)
	}
	refundMap := make(map[string]int64)
	for _, row := range refundRows {
		refundMap[row["day"]] = cast.ToInt64(row["total"])
	}

	// 组装趋势数据
	for _, date := range dates {
		pay := orderMap[date]
		refund := refundMap[date]
		res.List = append(res.List, omnibus_vo.StatisticsTrendItem{
			Date:  date,
			Value: utils.Fen2Yuan(pay - refund),
		})
	}
	return res, nil
}
