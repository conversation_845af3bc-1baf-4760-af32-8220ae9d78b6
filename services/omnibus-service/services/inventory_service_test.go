package services

import (
	"eShop/services/common"
	"eShop/view-model/omnibus-vo"
	"reflect"
	"testing"
)

func TestInventoryService_QueryStock(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in omnibus_vo.QueryStockReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut omnibus_vo.QueryStockRes
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				in: omnibus_vo.QueryStockReq{
					ProductIds: []int64{100138},
					//SkuIds: []int64{100138001},
					ShopId: "530094312600386773",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := InventoryService{
				BaseService: tt.fields.BaseService,
			}
			if gotOut := s.QueryStock(tt.args.in); !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.<PERSON><PERSON>rf("QueryStock() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestInventoryService_SyncStock(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		Data []omnibus_vo.SyncStockData
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				Data: []omnibus_vo.SyncStockData{
					{
						SkuId:      77,
						ProductId:  81,
						ChannelId:  2,
						StoreId:    "530219708465609002",
						SkuThirdId: 533513845302683649,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := InventoryService{
				BaseService: tt.fields.BaseService,
			}
			s.SyncStock(tt.args.Data)
		})
	}
}
