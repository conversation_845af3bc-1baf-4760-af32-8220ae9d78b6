package services

import (
	"eShop/infra/log"
	"eShop/services/common"
	store_vo "eShop/view-model/omnibus-vo"
	_ "github.com/go-sql-driver/mysql"
	"reflect"
	"testing"
)

func TestStoreService_AddStore(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req store_vo.AddStoreRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "addStore",
			args: args{
				req: store_vo.AddStoreRequest{
					ShopId:   "222222544444465",
					ChainId:  "1654644556",
					Name:     "store1",
					OrgId:    5,
					Address:  "guangdong shenzhen futian",
					PointX:   "113.95325",
					PointY:   "22.63325",
					Province: "广东省",
					City:     "深圳市",
					County:   "福田区",
				},
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := StoreService{
				BaseService: tt.fields.BaseService,
			}
			if err := s.AddStore(tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("AddStore() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStoreService_StoreThirdRelationEdit(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req store_vo.StoreEditReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "编辑门店信息"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := StoreService{
				BaseService: tt.fields.BaseService,
			}
			req := store_vo.StoreEditReq{}
			req.TenantId = "RP0217"
			var ss []store_vo.StoreRelation
			var ss1 store_vo.StoreRelation
			var ss2 store_vo.StoreRelation
			ss1.ChannelStoreId = "11111"
			ss1.ChannelId = 1
			ss2.ChannelStoreId = "22222"
			ss2.ChannelId = 2
			ss = append(ss, ss1)
			ss = append(ss, ss2)
			req.StoreIds = ss

			if err := s.StoreThirdRelationEdit(req); (err != nil) != tt.wantErr {
				t.Errorf("StoreThirdRelationEdit() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStoreService_StoreToEs(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req store_vo.StoreGetReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "门店信息插入ES"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := StoreService{
				BaseService: tt.fields.BaseService,
			}
			req := store_vo.StoreGetReq{}
			req.TenantId = "10000007"
			if err := s.StoreToEs(req); (err != nil) != tt.wantErr {
				t.Errorf("StoreToEs() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStoreService_StoreList(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req store_vo.StoreListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    store_vo.StoreListRes
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "获取门店列表信息"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := StoreService{
				BaseService: tt.fields.BaseService,
			}
			var req store_vo.StoreListReq
			req.PointX = "106.5023630000"
			req.PointY = "29.5915620000"
			got, err := s.StoreList(req)
			if (err != nil) != tt.wantErr {
				t.Errorf("StoreList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("StoreList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStoreService_StoreInfo(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req store_vo.StoreGetReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    store_vo.StoreGet
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				req: store_vo.StoreGetReq{
					TenantId: "10000007",
					OrgId:    6,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := StoreService{
				BaseService: tt.fields.BaseService,
			}
			got, err := s.StoreInfo(tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("StoreInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("StoreInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStoreService_GetShopWxAppCode(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	log.Init()
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
		{
			name: "aaa",
			fields: fields{
				BaseService: common.BaseService{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := StoreService{
				BaseService: tt.fields.BaseService,
			}
			s.GetShopWxAppCode()
		})
	}
}
func TestStoreService_SyncStoreRegister(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req store_vo.SyncStoreRegisterReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "同步店铺注册信息",
			fields: fields{
				BaseService: common.BaseService{},
			},
			args: args{
				//req: store_vo.SyncStoreRegisterReq{
				//	OrgId:            3,
				//	Mobile:           "13802561854",
				//	IdCard:           "******************",
				//	StoreId:          "530185752454150979",
				//	SocialCodeImage:  "",
				//	SocialCreditCode: "91440300306110974N",
				//	StoreName:        "测试门店",
				//	StoreLogo:        "https://file.vetscloud.com/ced265a2e912447aa28ae0db0fb6bacf.jpg",
				//	//UserName:         "SYSTEM",
				//	IdcardFront:    "https://file.vetscloud.com/ced265a2e912447aa28ae0db0fb6bacf.jpg",
				//	IdcardReverse:  "https://file.vetscloud.com/ced265a2e912447aa28ae0db0fb6bacf.jpg",
				//	EnterpriseName: "11111111111111",
				//},
				req: store_vo.SyncStoreRegisterReq{
					OrgId:            3,
					Mobile:           "19173178463",
					IdCard:           "239005198704061012",
					StoreId:          "579527874349594987",
					SocialCodeImage:  "https://rpets-saas-cos-pre.rvet.cn/530094312600386773/product/2025/01/07/b9397a04dd7f4f3d8310d66a49c8790c.jpg",
					SocialCreditCode: "91440106MACR8QXWXF",
					StoreName:        "润合云店测试",
					StoreLogo:        "https://file.vetscloud.com/ced265a2e912447aa28ae0db0fb6bacf.jpg",
					//UserName:         "SYSTEM",
					IdcardFront:    "https://file.vetscloud.com/ced265a2e912447aa28ae0db0fb6bacf.jpg",
					IdcardReverse:  "https://file.vetscloud.com/ced265a2e912447aa28ae0db0fb6bacf.jpg",
					EnterpriseName: "广州市诺克动物医院有限公司",
					Type:           1,
				},
			},
			wantErr: false,
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := StoreService{
				BaseService: tt.fields.BaseService,
			}
			if err := s.SyncStoreRegister(tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("SyncStoreRegister() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStoreService_SyncEmployee(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req store_vo.SyncEmployeeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "同步员工信息",
			fields: fields{
				BaseService: common.BaseService{},
			},
			args: args{
				req: store_vo.SyncEmployeeReq{
					StoreId: "576534157590154085",
					OrgId:   6,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := StoreService{
				BaseService: tt.fields.BaseService,
			}
			log.Init()
			if err := s.SyncEmployee(tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("SyncEmployee() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStoreService_InitCategoryV2(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		chainId  int64
		userId   int64
		userName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := StoreService{
				BaseService: tt.fields.BaseService,
			}
			if err := c.InitCategoryV2(tt.args.chainId, tt.args.userId, tt.args.userName); (err != nil) != tt.wantErr {
				t.Errorf("InitCategoryV2() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
