package services

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	dispatch_po "eShop/domain/dispatch-po"
	distribution_po "eShop/domain/distribution-po"
	omnibus_po "eShop/domain/omnibus-po"
	product_po "eShop/domain/product-po"
	"encoding/base64"
	"io/ioutil"
	"net/http"
	"sort"

	"github.com/golang/glog"
	"github.com/google/uuid"
	"github.com/limitedlee/microservice/common/config"

	distribution_vo "eShop/view-model/distribution-vo"
	so "eShop/view-model/omnibus-vo"
	"regexp"
	"strings"

	"eShop/infra/log"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/proto/dac"
	"eShop/services/common"
	enum2 "eShop/services/distribution-service/enum"
	"eShop/services/distribution-service/services"
	"eShop/services/omnibus-service/enum"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"xorm.io/xorm"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
)

type StoreService struct {
	common.BaseService
}

// 获取门店列表，根据连锁ID
func (s StoreService) StoreList(req so.StoreListReq) ([]so.StoreListView, error) {
	s.Begin()
	defer s.Close()
	out := make([]so.StoreListView, 0)
	session := s.Session

	sql := `select a.*,b.image,b.mobile,c.app_type,IFNULL(6371 * ACOS(  
        COS(RADIANS(?)) * COS(RADIANS(a.pointY)) *  
        COS(RADIANS(a.pointX) - RADIANS(?)) +  
        SIN(RADIANS(?)) * SIN(RADIANS(a.pointY))  
    ), 999999)
     AS distance   from datacenter.store a
inner join datacenter.store_business_setup b on a.finance_code=b.finance_code
left join  eshop_saas.p_epay_config c on a.finance_code=c.tenant_id and c.app_type='WX_MINI'
 where chain_id=? and b.business_status=1 and c.app_type='WX_MINI' ORDER BY  distance ASC`

	err := session.SQL(sql, req.PointY, req.PointX, req.PointY, cast.ToInt64(req.ChainId)).Find(&out)
	if err != nil {
		log.Error("查询店铺信息失败：err=", err.Error())
		return out, err
	}

	return out, nil
}

func (s StoreService) StoreMode(req so.StoreGetReq) (omnibus_po.Store, error) {
	s.Begin()
	defer s.Close()
	session := s.Session
	var info omnibus_po.Store
	isok, err := session.SQL("select * from datacenter.store where finance_code=?", req.TenantId).Get(&info)
	if err != nil {
		log.Error("查询店铺信息失败：err=", err.Error())
		return info, err
	}
	if !isok {
		return info, errors.New("未查询到对应的店铺信息")
	}

	return info, nil
}

// 获取门店详情信息
func (s StoreService) StoreInfo(req so.StoreGetReq) (so.StoreGet, error) {
	s.Begin()
	defer s.Close()
	out := so.StoreGet{}
	session := s.Session
	var info omnibus_po.Store
	isok, err := session.SQL("select * from datacenter.store where zilong_id=?", req.TenantId).Get(&info)
	if err != nil {
		log.Error("查询店铺信息失败：err=", err.Error())
		return out, err
	}
	if !isok {
		return out, errors.New("未查询到对应的店铺信息")
	}

	//查询是否支付
	isHave := 0
	isok, err = session.SQL("select 1 from eshop_saas.p_payment_config where tenant_id=? and pay_type=? ", cast.ToInt64(req.TenantId), "WECHAT").Get(&isHave)
	if err != nil {
		log.Error("查询店铺信息失败：err=", err.Error())
		return out, err
	}
	if isok {
		out.IsSetPay = 1
	}
	out.TenantId = req.TenantId
	out.TenantName = info.Name

	//查询绑定仓库信息
	out.TenantId = req.TenantId
	//req.TenantId = "CX0013"
	var tempwarehouse []so.WarehouseRelationShop
	session.SQL("select * from dc_dispatch.warehouse_relation_shop where shop_id=? ", req.TenantId)
	err = session.Find(&tempwarehouse)
	if err != nil {
		log.Error("查询仓库信息失败：err=", err.Error())
		return out, err
	}
	mapwarehousemap := map[int]so.WarehouseRelationShop{}
	for _, x := range tempwarehouse {
		mapwarehousemap[x.ChannelId] = x
	}

	//查询第三方ID绑定信息
	var tempThird []so.StoreRelation
	session.SQL("select * from datacenter.store_relation where finance_code=? and channel_id < 4", req.TenantId)
	err = session.Find(&tempThird)
	if err != nil {
		log.Error("查询仓库信息失败：err=", err.Error())
		return out, err
	}
	for i, _ := range tempThird {
		channleId := tempThird[i].ChannelId
		if _, ok := mapwarehousemap[channleId]; ok {
			tempThird[i].WarehouseId = mapwarehousemap[channleId].WarehouseId
			tempThird[i].WarehouseName = mapwarehousemap[channleId].WarehouseName
		}
	}
	//out.Warehouses = tempwarehouse
	out.StoreIds = tempThird

	return out, nil
}

// 编辑门店第三方信息
func (s StoreService) StoreThirdRelationEdit(req so.StoreEditReq) error {
	s.Begin()
	defer s.Close()
	//session := s.Session
	session := s.Engine.NewSession()
	session.Begin()

	redisConn := cache.GetRedisConn()
	//构建管道
	pip := redisConn.Pipeline()

	//渠道门店关系redis key映射
	relationKeyMap := map[int][]string{
		common.ChannelIdWeChatApp: {"store:relation:dctozl", "store:relation:zltodc"},
		common.ChannelIdMT:        {"store:relation:dctomt", "store:relation:mttodc"},
		common.ChannelIdELM:       {"store:relation:dctoele", "store:relation:eletodc"},
	}

	//store_relation表关联关系
	var srSlice []omnibus_po.StoreRelation
	if err := session.Select("id,channel_id,channel_store_id").Where("finance_code = ?", req.TenantId).Find(&srSlice); err != nil {
		err = status.Error(codes.Internal, req.TenantId+" 获取门店关联关系错误，"+err.Error())
		log.Error(err)
		return errors.New("编辑门店出错")
	}

	for _, storeRelation := range srSlice {
		//先删除旧缓存
		if storeRelation.ChannelId != 10 && storeRelation.ChannelId != 100 {
			pip.HDel(relationKeyMap[storeRelation.ChannelId][0], req.TenantId)
			pip.HDel(relationKeyMap[storeRelation.ChannelId][1], storeRelation.ChannelStoreId)
		}
	}

	StoreRelation := omnibus_po.StoreRelation{}
	_, err := session.Where("finance_code=?", req.TenantId).Delete(&StoreRelation)
	if err != nil {
		session.Rollback()
		log.Error("编辑门店出错删除对应关系报错：", req.TenantId, err.Error())
		return errors.New("编辑门店出错")
	}
	var StoreRelationIn []omnibus_po.StoreRelation
	for _, x := range req.StoreIds {
		item := omnibus_po.StoreRelation{}
		item.ChannelStoreId = strings.TrimSpace(x.ChannelStoreId)
		item.ChannelId = x.ChannelId
		item.FinanceCode = req.TenantId
		StoreRelationIn = append(StoreRelationIn, item)

		//渠道门店存在则添加新缓存
		if len(x.ChannelStoreId) > 0 {
			pip.HSet(relationKeyMap[x.ChannelId][0], req.TenantId, x.ChannelStoreId)
			pip.HSet(relationKeyMap[x.ChannelId][1], x.ChannelStoreId, req.TenantId)
		}
	}

	_, err = session.Insert(&StoreRelationIn)
	if err != nil {
		session.Rollback()
		log.Error("编辑门店出错 插入对应关系报错：", req.TenantId, err.Error())
		return errors.New("编辑门店出错")
	}

	session.Commit()

	//通过管道执行redis命令
	if _, err = pip.Exec(); err != nil {
		err = status.Error(codes.Internal, req.TenantId+" 门店关系写入缓存失败，"+err.Error())
		log.Error(err)
		return err
	}
	return nil
}

// 添加店铺
// 1 添加omnibus_po.Store表
// 2 添加omnibus_po.Warehouse表
// 3 添加omnibus_po.WarehouseRelationShop
func (s StoreService) AddStore(req so.AddStoreRequest) error {
	logPrefix := fmt.Sprintf("AddStore insert store ,入参为: %s", utils.JsonEncode(req))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	//异常捕获
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("AddStore panic error:%v", err)
		}
	}()
	session := s.Engine.NewSession()
	session.Begin()
	var hasCount int64
	if _, err := session.SQL("select count(1) from datacenter.store where chain_id=?", cast.ToInt64(req.ChainId)).Get(&hasCount); err != nil {
		log.Errorf(logPrefix, "初始化分类查询失败,err:", err.Error())
		return errors.New("初始化分类查询失败")
	}
	if hasCount == 0 {
		if err := s.InitCategoryV2(cast.ToInt64(req.ChainId), 0, "系统操作"); err != nil {
			//session.Rollback()
			log.Errorf(logPrefix, "初始化分类失败,err:", err.Error())
			//return errors.New("初始化分类失败")
		}
	}

	financeCode := cast.ToString(req.ShopId)
	store := &omnibus_po.Store{
		FinanceCode: financeCode,
		ZilongId:    financeCode,
		ChainId:     cast.ToInt64(req.ChainId),
		OrgId:       req.OrgId,
		Name:        req.Name,
		Shortname:   req.Name,
		Address:     req.Address,
		PointX:      req.PointX,
		PointY:      req.PointY,
		Province:    req.Province,
		City:        req.City,
		County:      req.County,
		AppChannel:  12, //取datacenter.store_master的固定值
	}
	_, err := session.Table("datacenter.store").Insert(store)
	if err != nil {
		session.Rollback()
		log.Errorf(logPrefix, "AddStore insert store error:%v", err.Error())
		return errors.New("添加店铺失败")
	}
	// 2 添加omnibus_po.Warehouse表
	warehouse := &dispatch_po.Warehouse{
		Thirdid:  cast.ToString(req.ShopId),
		Code:     financeCode,
		Name:     req.Name + "(门店仓)",
		Category: enum.WarehouseTypeStoreWarehouse,
		Address:  req.Address,
		Status:   1,
		City:     req.City,
		Lng:      cast.ToInt32(req.PointX),
		Lat:      cast.ToInt32(req.PointY),
		ChainId:  cast.ToInt32(req.ChainId),
		OrgId:    cast.ToInt32(req.OrgId),
	}
	_, err = session.Table("dc_dispatch.warehouse").Insert(warehouse)
	if err != nil {
		session.Rollback()
		log.Errorf(logPrefix, "AddStore insert warehouse error:%v", err)
		return errors.New("添加仓库失败")
	}

	//  添加零售仓的记录
	frontWarehouse := &dispatch_po.Warehouse{
		Thirdid:  cast.ToString(req.ShopId),
		Code:     financeCode,
		Name:     req.Name + "(零售仓)",
		Category: enum.WarehouseTypeFrontWarehouse,
		Address:  req.Address,
		Status:   0,
		City:     req.City,
		Lng:      cast.ToInt32(req.PointX),
		Lat:      cast.ToInt32(req.PointY),
		ChainId:  cast.ToInt32(req.ChainId),
		OrgId:    cast.ToInt32(req.OrgId),
	}
	_, err = session.Table("dc_dispatch.warehouse").Insert(frontWarehouse)
	if err != nil {
		session.Rollback()
		log.Errorf(logPrefix, "AddStore insert warehouse error:%v", err)
		return errors.New("添加仓库失败")
	}

	// 3 添加omnibus_po.WarehouseRelationShop
	//批量添加 渠道1 2 3
	var channelIds = []int{common.ChannelIdWeChatApp, common.ChannelIdMT, common.ChannelIdELM, common.ChannelIdOfflineShop}
	var warehouseRelationShop []omnibus_po.WarehouseRelationShop
	var storeRelation []omnibus_po.StoreRelation
	for _, channelId := range channelIds {
		warehouseRelationShop = append(warehouseRelationShop, omnibus_po.WarehouseRelationShop{
			ShopId:        cast.ToString(req.ShopId),
			ShopName:      req.Name,
			WarehouseId:   cast.ToInt(warehouse.Id),
			WarehouseName: req.Name,
			ChannelId:     channelId,
		})

		storeRelation = append(storeRelation, omnibus_po.StoreRelation{
			FinanceCode: financeCode,
			ChannelId:   channelId,
		})
		if channelId == common.ChannelIdWeChatApp {
			storeRelation[0].ChannelStoreId = req.ChannelStoreId
		}
	}
	_, err = session.Table("dc_dispatch.warehouse_relation_shop").Insert(warehouseRelationShop)
	if err != nil {
		session.Rollback()
		log.Errorf(logPrefix, "AddStore insert warehouseRelationShop error:%v", err)
		return errors.New("添加仓库关联失败")
	}
	_, err = session.Table("datacenter.store_relation").Insert(storeRelation)
	if err != nil {
		session.Rollback()
		log.Errorf(logPrefix, "AddStore insert storeRelation error:%v", err)
		return errors.New("添加门店关联失败")
	}

	storeBusinessSetup := &omnibus_po.StoreBusinessSetup{
		FinanceCode:    financeCode,
		ChannelId:      common.ChannelIdWeChatApp,
		BusinessStatus: req.BusinessStatus,
		Mobile:         req.Mobile,
		Image:          req.Image,
		MiniAppCode:    "", // 店铺小程序码

	}
	_, err = session.Table("datacenter.store_business_setup").Insert(storeBusinessSetup)
	if err != nil {
		session.Rollback()
		log.Errorf(logPrefix, "AddStore insert store_business_setup error:%v", err)
		return errors.New("添加店铺基本设置表失败")
	}
	//店铺基本设置表
	shopSet := &omnibus_po.Store_set{
		Shop_id:       financeCode,
		Is_aotu_order: 1,
		Is_getorder:   1,
		Getordertype:  2,
		Is_reminder:   1,
		Remindertype:  1,
		Is_backorder:  1,
		Backordertype: 2,
	}
	_, err = session.Table("datacenter.store_set").Insert(shopSet)
	if err != nil {
		session.Rollback()
		log.Errorf(logPrefix, "AddStore insert store_set error:%v", err)
		return errors.New("添加店铺系统配置表失败")
	}

	var deliveryChannelIds = []int{common.ChannelIdWeChatApp, common.ChannelIdMT, common.ChannelIdELM}
	var deliveryConfig []omnibus_po.DeliveryConfig
	//添加配送配置默认值
	for _, channelId := range deliveryChannelIds {
		deliveryConfig = append(deliveryConfig, omnibus_po.DeliveryConfig{
			FinanceCode:    cast.ToString(req.ShopId),
			ShopName:       req.Name,
			ChannelID:      channelId,
			OrgID:          req.OrgId,
			DeliveryMethod: 4,
		})
	}
	_, err = session.Table("datacenter.delivery_config").Insert(deliveryConfig)
	if err != nil {
		session.Rollback()
		log.Errorf(logPrefix, "AddStore insert delivery_config error:%v", err)
		return errors.New("添加默认配送配置失败")
	}

	go func() {
		time.Sleep(200 * time.Millisecond)
		if err = s.StoreToEs(so.StoreGetReq{OrgId: req.OrgId, TenantId: req.ShopId}); err != nil {
			session.Rollback()
			log.Errorf(logPrefix, "AddStore StoreToEs error:%v", err)
		}
	}()
	//缓存多个门店app_channel的对应关系
	redisConn := cache.GetRedisConn()
	key := enum.StoreAppChannelKey
	val := redisConn.HGet(key, financeCode).Val()
	if val == "" {
		redisConn.HSet(key, financeCode, fmt.Sprintf("%d|%d", 12, 6))
	}

	session.Commit()

	go func() {
		// 生成店铺二维码
		//店铺基本设置表
		QiniuUrl, err := GetShopWxAppCode(s.Engine, &dac.GetShopWxAppCodeRequest{FinanceCode: financeCode, ChainId: req.ChainId})
		if err != nil {
			log.Error(logPrefix, "获取授权方accessToken失败:", err.Error())
		}
		if QiniuUrl == "" {
			return
		}
		storeBusinessSetup = &omnibus_po.StoreBusinessSetup{
			MiniAppCode: QiniuUrl, // 店铺小程序码
		}
		if _, err := s.Engine.Table("datacenter.store_business_setup").Cols("miniAppCode").
			Where("finance_code=?", financeCode).
			Where("channel_id=?", common.ChannelIdWeChatApp).
			Update(storeBusinessSetup); err != nil {
			log.Error(logPrefix, "编辑店铺小程序码失败，err:", err.Error())
		}
	}()

	return nil
}

func (s StoreService) GetShopWxAppCode() (string, error) {
	s.Begin()
	defer s.Close()
	// 530219708465609002
	return GetShopWxAppCode(s.Engine, &dac.GetShopWxAppCodeRequest{FinanceCode: "564644794761542063"})
}

// 编辑店铺
func (s StoreService) EditStore(req so.AddStoreRequest) error {
	s.Begin()
	defer s.Close()
	session := s.Session
	//店铺基本信息表
	store := &omnibus_po.Store{
		Name:      req.Name,
		Shortname: req.Name,
		Address:   req.Address,
		PointX:    req.PointX,
		PointY:    req.PointY,
		Province:  req.Province,
		City:      req.City,
		County:    req.County,
	}
	_, err := session.Table("datacenter.store").Where("finance_code=?", req.ShopId).Update(store)
	if err != nil {
		session.Rollback()
		log.Errorf("EditStore update store error:%v", err)
		return errors.New("编辑店铺失败")
	}
	//店铺基本设置表
	storeBusinessSetup := &omnibus_po.StoreBusinessSetup{
		BusinessStatus: req.BusinessStatus,
		Mobile:         req.Mobile,
		Image:          req.Image,
	}
	_, err = session.Table("datacenter.store_business_setup").Where("finance_code=?", req.ShopId).Update(storeBusinessSetup)
	if err != nil {
		session.Rollback()
		log.Errorf("AddStore insert store_business_setup error:%v", err)
		return errors.New("更新店铺基本设置表失败")
	}

	//删除redis地址
	redisConn := cache.GetRedisConn()
	cacheKey := "datacenter:store:info"
	data := redisConn.HMGet(cacheKey, req.ShopId).Val()
	if data != nil {
		redisConn.HDel(cacheKey, req.ShopId)
	}

	return nil
}

// 更新门店信息到ES
func (s StoreService) StoreToEs(req so.StoreGetReq) error {
	s.Begin()
	defer s.Close()
	session := s.Session
	var info so.SaasStore
	isok, err := session.SQL("select * from datacenter.store where zilong_id=?", req.TenantId).Get(&info)
	if err != nil {
		log.Error("查询店铺信息失败：err=", err.Error())
		return err
	}

	SaasIndex := "SaasStore"

	//先删除ES数据
	esClient := utils.NewEsClient()
	utils.CreateESIndexByName(SaasIndex)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("zilong_id", info.ZilongId))
	if _, err = esClient.DeleteByQuery().Index(SaasIndex).Query(query).Do(context.Background()); err != nil {
		log.Error("finance_Code: "+info.ZilongId, " ,删除SAAS门店数据失败:", err.Error())

		//return err
	}

	//如果存在，就按最新的数据加入ES
	if isok {
		info.LocationPoint = fmt.Sprintf(`%s,%s`,
			info.PointY, info.PointX,
		)
		body, err := json.Marshal(info)
		if err != nil {
			log.Error("finance_Code: "+info.FinanceCode, " ,SAAS门店数据JSON出错:", err.Error())
			return err
		}

		if _, err = esClient.Index().Index(SaasIndex).Id(info.ZilongId).BodyString(string(body)).Do(context.Background()); err != nil {
			log.Error("finance_Code: "+info.FinanceCode, " ,SAAS门店数据同步ES失败:", err.Error())
			return err
		}
	}
	return nil
}

// 获取连锁所有门店信息列表
func (s StoreService) GetStoreList(req so.GetStoreListReq) (out []so.GetStoreList, err error) {
	s.Begin()
	defer s.Close()
	logPrefix := fmt.Sprintf("获取门店列表。入参：%s", utils.InterfaceToJSON(req))
	log.Info(logPrefix)
	storeModel := omnibus_po.NewStore()
	storeReq := map[string]interface{}{
		"userId": req.UserId,
	}
	// 如果右上角选中某个店铺， 则只能看到该店铺
	if req.StoreId > 0 {
		storeReq["storeId"] = req.StoreId
	}
	data, _, err := storeModel.GetStores(context.Background(), s.Engine, storeReq)
	if err != nil {
		log.Error(logPrefix, "获取可下发门店列表失败，err=", err.Error())
		err = errors.New("获取可下发门店列表失败")
		return
	}
	productStoreMap := make(map[string]bool)
	if req.ProductId > 0 {
		productStoreMap, err = product_po.GetPushedProductStore(s.Engine, map[string]interface{}{"productId": req.ProductId})
		if err != nil {
			log.Error(logPrefix, "获取已下发店铺列表失败，err=", err.Error())
			err = errors.New("获取已下发店铺列表失败")
			return
		}

	}

	out = make([]so.GetStoreList, 0)
	for _, v := range data {
		IsDistri := 0
		if _, ok := productStoreMap[fmt.Sprintf("%s_%d", v.FinanceCode, req.ProductId)]; ok {
			IsDistri = 1
		}
		// 是否需要显示店铺被代运营的标识(只有代运营账号登录进去，需要将代运营的店铺标识出来
		IsAuthorized := 0
		if v.AuthorizedChainId > 0 && v.SourceChainId == v.AuthorizedChainId {
			IsAuthorized = 1
		}
		out = append(out, so.GetStoreList{
			StoreId:      cast.ToString(v.FinanceCode),
			StoreName:    v.Name,
			IsDistri:     IsDistri,
			IsAuthorized: IsAuthorized,
		})
	}
	return
}

// 获取已下发的店铺列表
func (s StoreService) GetDistriStoreList(req so.GetDistriStoreListReq) (out []product_po.ProProductStore, err error) {
	logPrefix := fmt.Sprintf("获取已下发门店列表。入参：%s", utils.InterfaceToJSON(req))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	if req.ProductId == 0 {
		return nil, nil
	}
	productStoreModel := product_po.NewProProductStoreModel()
	productStoreList, _, err := productStoreModel.QueryProductStores(context.Background(), s.Engine, product_po.QueryProductStoresReq{ProductId: req.ProductId})
	if err != nil {
		log.Error(logPrefix, "获取已下发的店铺列表，err=", err.Error())
		err = errors.New("获取已下发的店铺列表失败")
		return
	}
	out = productStoreList[req.ProductId]

	return
}

// 履约配置
func (s StoreService) AddOrEditWarehouse(req so.AddOrEditWarehouseReq) (err error) {
	logPrefix := fmt.Sprintf("新增或编辑门店的履约配置，入参：%s", utils.InterfaceToJSON(req))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	warehouseId := 0
	exist, err := s.Engine.Table("dc_dispatch.warehouse_relation_shop").Select("warehouse_id").Where("shop_id=?", req.StoreId).Get(&warehouseId)
	if err != nil {
		log.Error(logPrefix, "获取店铺的仓库信息失败，err=", err.Error())
		err = errors.New("获取店铺的仓库信息失败" + err.Error())
		return
	}
	if !exist {
		log.Error(logPrefix, "未找到门店对应的仓库信息")
		err = errors.New("未找到门店对应的仓库信息")
		return
	}

	data := omnibus_po.WarehouseDeliveryRelation{
		WarehouseId: warehouseId,
		DeliveryId:  0,
		ShopNo:      req.ShopNo,
	}
	oldData := omnibus_po.WarehouseDeliveryRelation{}
	exist, err = s.Engine.Table("dc_dispatch.warehouse_delivery_relation").Where("warehouse_id=?", warehouseId).Where("delivery_id=0").Get(&oldData)
	if err != nil {
		log.Error(logPrefix, "获取仓库对应履约配置失败，err=", err.Error())
		return
	}
	if exist {
		_, err = s.Engine.Table("dc_dispatch.warehouse_delivery_relation").Where("id=?", oldData.Id).Update(&data)
	} else {
		_, err = s.Engine.Table("dc_dispatch.warehouse_delivery_relation").Insert(&data)

	}

	if err != nil {
		log.Error(logPrefix, "插入门店的履约配置失败，err=", err.Error())
		err = errors.New("插入门店的履约配置失败")
		return
	}

	return
}

// 获取履约配置
func (s StoreService) GetWarehouseConfig(req so.GetWarehouseConfigReq) (out omnibus_po.WarehouseDeliveryRelation, err error) {
	logPrefix := fmt.Sprintf("获取门店的履约配置，入参：%s", utils.InterfaceToJSON(req))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	warehouseId := 0
	exist, err := s.Engine.Table("dc_dispatch.warehouse_relation_shop").Select("warehouse_id").Where("shop_id=?", req.StoreId).Get(&warehouseId)
	if err != nil {
		log.Error(logPrefix, "获取店铺的仓库信息失败，err=", err.Error())
		return
	}
	if !exist {
		log.Error(logPrefix, "未找到门店对应的仓库信息")
		return
	}

	_, err = s.Engine.Table("dc_dispatch.warehouse_delivery_relation").Where("warehouse_id=?", warehouseId).Get(&out)
	if err != nil {
		log.Error(logPrefix, "获取门店的履约配置失败，err=", err.Error())
		err = errors.New("获取门店的履约配置失败")
		return
	}

	return
}

// SyncStoreRegister 同步店铺注册
func (s StoreService) SyncStoreRegister(req so.SyncStoreRegisterReq) error {
	logPrefix := fmt.Sprintf("同步店铺注册,入参:%s", utils.JsonEncode(req))
	log.Info(logPrefix)

	// 参数校验
	if err := s.validateSyncStoreRegister(req); err != nil {
		return err
	}

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 开启事务
	if err := session.Begin(); err != nil {
		return fmt.Errorf("开启事务失败: %v", err)
	}
	defer func() {
		if err := recover(); err != nil {
			session.Rollback()
			log.Errorf("SyncStoreRegister panic: %v", err)
		}
	}()

	// 1. 检查并获取企业信息
	enterprise, err := s.checkAndGetEnterprise(session, req)
	if err != nil {
		session.Rollback()
		return err
	}

	// 2. 处理店铺信息
	shopId, err := s.processShopInfo(session, req, enterprise.Id)
	if err != nil {
		session.Rollback()
		return err
	}

	// 3. 查询店铺所有员工
	employeeModel := omnibus_po.NewEmployee()
	employees, err := employeeModel.GetStoreEmployees(session, cast.ToInt64(req.StoreId))
	if err != nil {
		session.Rollback()
		return fmt.Errorf("获取店铺员工信息失败: %v", err)
	}
	// 3. 处理会员信息
	for _, employee := range employees {
		memberId, memberName, err := s.processMemberInfo(session, employee.Mobile, employee.RealName)
		if err != nil {
			session.Rollback()
			return err
		}

		// 4. 处理分销商信息
		if err := s.processDistributorInfo(session, req, shopId, memberId, memberName); err != nil {
			session.Rollback()
			return err
		}
	}

	// 5. 同步商品信息
	if err := s.syncShopGoods(session, shopId); err != nil {
		session.Rollback()
		return err
	}

	// 提交事务
	if err := session.Commit(); err != nil {
		session.Rollback()
		return fmt.Errorf("提交事务失败: %v", err)
	}

	// 异步处理
	go s.asyncProcessAfterSync(req)

	return nil
}

// validateSyncStoreRegister 参数校验
func (s StoreService) validateSyncStoreRegister(req so.SyncStoreRegisterReq) error {
	if req.StoreId == "" {
		return errors.New("店铺ID不能为空")
	}
	if req.StoreName == "" {
		return errors.New("店铺名称不能为空")
	}
	if req.SocialCreditCode == "" {
		return errors.New("统一社会信用代码不能为空")
	}
	if req.OrgId == 0 {
		return errors.New("主体ID不能为空")
	}
	if req.Mobile == "" {
		return errors.New("手机号不能为空")
	}
	if !regexp.MustCompile(`^1[3-9]\d{9}$`).MatchString(req.Mobile) {
		return errors.New("手机号格式不正确")
	}
	//个体商户要校验身份证
	if req.Type == 1 {
		if req.IdCard == "" {
			return errors.New("身份证不能为空")
		}
		if req.IdcardFront == "" {
			return errors.New("身份证正面不能为空")
		}
		if req.IdcardReverse == "" {
			return errors.New("身份证反面不能为空")
		}
		if !utils.ValidateIDCard(req.IdCard) {
			return errors.New("个体工商户老板身份证号码不正确")
		}
	}
	if !utils.ValidateUSCC(req.SocialCreditCode) {
		return errors.New("企业老板社会信用代码不正确")
	}
	return nil
}

// checkAndGetEnterprise 检查并获取企业信息
func (s StoreService) checkAndGetEnterprise(session *xorm.Session, req so.SyncStoreRegisterReq) (*struct {
	Id             string
	Code           string
	EnterpriseType int
	IdCardNo       string
}, error) {
	var enterprise struct {
		Id             string
		Code           string
		EnterpriseType int
		IdCardNo       string
	}
	exists, err := session.Table("eshop.scrm_enterprise").Where("social_credit_code = ?", req.SocialCreditCode).
		Get(&enterprise)
	if err != nil {
		return nil, fmt.Errorf("查询企业信息失败: %v", err)
	}
	if !exists {
		return nil, errors.New("未找到对应的企业信息")
	}
	if enterprise.EnterpriseType != req.Type {
		return nil, errors.New("企业类型不匹配")
	}
	if req.Type == 1 && req.IdCard != enterprise.IdCardNo {
		return nil, errors.New("个体工商户身份证号码不匹配")
	}
	return &enterprise, nil
}

// processShopInfo 处理店铺信息
func (s StoreService) processShopInfo(session *xorm.Session, req so.SyncStoreRegisterReq, enterpriseId string) (int, error) {
	// 检查当前企业是否已绑定其他店铺
	var existingShop distribution_po.Shop
	exists, err := session.Table("eshop.shop").
		Where("enterprise_id = ? AND saas_shop_id != ? AND saas_shop_id != ''", enterpriseId, req.StoreId).
		Get(&existingShop)
	if err != nil {
		return 0, fmt.Errorf("查询企业绑定信息失败: %v", err)
	}
	if exists {
		return 0, fmt.Errorf("该企业已绑定其他门店")
	}

	// 检查当前企业的店铺信息
	var shop distribution_po.Shop
	exists, err = session.Table("eshop.shop").
		Where("enterprise_id = ?", enterpriseId).
		Get(&shop)
	if err != nil {
		return 0, fmt.Errorf("查询店铺信息失败: %v", err)
	}

	if exists {
		// 更新店铺信息
		_, err = session.Table("eshop.shop").
			Where("id = ?", shop.Id).
			Update(map[string]interface{}{
				"saas_shop_id": req.StoreId,
				"update_time":  time.Now(),
			})
		if err != nil {
			return 0, fmt.Errorf("更新店铺信息失败: %v", err)
		}
		return shop.Id, nil
	}

	// 新增店铺信息
	shop = distribution_po.Shop{
		EnterpriseId: cast.ToInt64(enterpriseId),
		SaasShopId:   req.StoreId,
		ShopName:     req.StoreName + "的小店",
		Welcome:      "欢迎来到我的小店，快来选购宠物用品吧",
		HeadImage:    req.StoreLogo,
		OrgId:        req.OrgId,
		IsMain:       2,
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
	}

	_, err = session.Table("eshop.shop").Insert(&shop)
	if err != nil {
		return 0, fmt.Errorf("新增店铺信息失败: %v", err)
	}

	return shop.Id, nil
}

// processMemberInfo 处理会员信息
func (s StoreService) processMemberInfo(session *xorm.Session, mobile, name string) (int, string, error) {
	var member distribution_po.Member
	exists, err := session.Table("upetmart.upet_member").
		Where("member_mobile = ?", mobile).
		Get(&member)
	if err != nil {
		return 0, "", fmt.Errorf("查询会员信息失败: %v", err)
	}

	if exists {
		return int(member.MemberId), member.MemberName, nil
	}

	// 生成会员名称和信息
	memberName := "upet_3" + mobile[len(mobile)-4:]
	member = distribution_po.Member{
		MemberName:         memberName,
		MemberTruename:     name,
		MemberMobile:       mobile,
		ScrmUserId:         mobile,
		MemberMobileBind:   1,
		GevalCommentStatus: 9,
		MemberTime:         time.Now().Unix(),
		MemberLoginTime:    time.Now().Unix(),
		MemberOldLoginTime: time.Now().Unix(),
		MemberBirthday:     "2000-01-01", // 设置默认生日
	}

	_, err = session.Table("upetmart.upet_member").Insert(&member)
	if err != nil {
		return 0, "", fmt.Errorf("新增会员信息失败: %v", err)
	}

	return int(member.MemberId), memberName, nil
}

// processDistributorInfo 处理分销商信息
func (s StoreService) processDistributorInfo(session *xorm.Session, req so.SyncStoreRegisterReq, shopId int, memberId int, memberName string) error {
	// 先检查分销商是否已存在
	var existingDistributor distribution_po.DisDistributor
	exists, err := session.Table("eshop.dis_distributor").
		Where("org_id = ? and member_id = ? and shop_id=?", req.OrgId, memberId, shopId).
		Get(&existingDistributor)
	if err != nil {
		return fmt.Errorf("查询分销商信息失败: %v", err)
	}

	// 判断分销员身份 - 检查该店铺是否已有老板
	disRole := 1 // 默认为老板
	var bossCount int64
	if exists && existingDistributor.DisRole == 1 {
		// 如果当前分销商已存在且是老板，保持老板身份
		disRole = 1
	} else {
		// 检查是否有其他老板（排除自己）
		bossCount, err = session.Table("eshop.dis_distributor").
			Where("shop_id = ? AND dis_role = 1", shopId).
			And("member_id != ?", memberId).
			Count()
		if err != nil {
			return fmt.Errorf("查询店铺老板信息失败: %v", err)
		}
		if bossCount > 0 {
			disRole = 2 // 如果店铺已有其他老板，则设置为会员
		}
	}

	// 准备基础分销商信息
	distributorInfo := map[string]interface{}{
		"social_credit_code": req.SocialCreditCode,
		"social_code_image":  utils.MobileEncrypt(req.SocialCodeImage),
		"dis_role":           disRole,
	}

	// 根据类型添加不同的字段
	if req.Type == 2 { // 个体工商户
		distributorInfo["id_card"] = utils.AddStar(req.IdCard)
		distributorInfo["encrypt_id_card"] = utils.MobileEncrypt(req.IdCard)
		distributorInfo["idcard_front"] = utils.MobileEncrypt(req.IdcardFront)
		distributorInfo["idcard_reverse"] = utils.MobileEncrypt(req.IdcardReverse)
	}

	if exists {
		// 更新现有分销商信息
		_, err = session.Table("eshop.dis_distributor").
			Where("id = ?", existingDistributor.Id).
			Update(distributorInfo)
		if err != nil {
			return fmt.Errorf("更新分销商信息失败: %v", err)
		}
		return nil
	}

	// 创建新的分销商信息
	distributor := &distribution_po.DisDistributor{
		Name:             memberName,
		RealName:         memberName,
		MemberId:         memberId,
		OrgId:            req.OrgId,
		ShopId:           shopId,
		IdCard:           utils.AddStar(req.IdCard),
		EncryptIdCard:    utils.MobileEncrypt(req.IdCard),
		IdcardFront:      utils.MobileEncrypt(req.IdcardFront),
		IdcardReverse:    utils.MobileEncrypt(req.IdcardReverse),
		Status:           1,
		DisRole:          disRole,
		Mobile:           utils.AddStar(req.Mobile),
		EncryptMobile:    utils.MobileEncrypt(req.Mobile),
		ApproveState:     2,
		SourceType:       1,
		SocialCreditCode: req.SocialCreditCode,
		SocialCodeImage:  utils.MobileEncrypt(req.SocialCodeImage),
		ApproveTime:      time.Now(),
		CreateTime:       time.Now(),
		UpdateTime:       time.Now(),
	}

	_, err = session.Table("eshop.dis_distributor").Insert(distributor)
	if err != nil {
		return fmt.Errorf("新增分销商信息失败: %v", err)
	}

	// 新增：创建分销商统计数据
	distributorTotal := &distribution_po.DisDistributorTotal{
		OrgId:               req.OrgId,
		DisId:               distributor.Id,
		MemberId:            memberId,
		ShopId:              shopId,
		OrderNum:            0,
		TotalSales:          0,
		SettledCommission:   0,
		UnsettledCommission: 0,
		WithdrawSuccess:     0,
		WithdrawApply:       0,
		WaitWithdraw:        0,
		TotalCustomer:       0,
		CreateTime:          time.Now(),
		UpdateTime:          time.Now(),
	}

	_, err = session.Table("eshop.dis_distributor_total").Insert(distributorTotal)
	if err != nil {
		return fmt.Errorf("新增分销商统计数据失败: %v", err)
	}

	return nil
}

// syncShopGoods 同步商品信息
func (s StoreService) syncShopGoods(session *xorm.Session, shopId int) error {
	var count int64
	count, err := session.Table("upetmart.upet_goods_eshop").Where("shop_id = ?", shopId).Count()
	if err != nil {
		return fmt.Errorf("查询店铺商品信息失败: %v", err)
	}

	if count == 0 {
		var goodIds []int
		err = session.SQL("select goods_id from upetmart.upet_goods where store_id = 3 and goods_state =1 and goods_verify=1 and is_dis =1").Find(&goodIds)
		if err != nil {
			return fmt.Errorf("查询店铺商品信息失败: %v", err)
		}

		err = services.SyncEsGoods(session, goodIds, shopId, enum2.SyncEsSetShopEnum)
		if err != nil {
			return err
		}
	}
	return nil
}

// asyncProcessAfterSync 异步处理
func (s StoreService) asyncProcessAfterSync(req so.SyncStoreRegisterReq) {
	// 生成店铺二维码
	time.Sleep(200 * time.Millisecond)
	QiniuUrl, err := GetShopWxAppCode(s.Engine, &dac.GetShopWxAppCodeRequest{
		FinanceCode: req.StoreId,
		ChainId:     req.ChainId,
	})
	if err != nil {
		log.Error("获取授权方accessToken失败:", err.Error())
		return
	}
	if QiniuUrl == "" {
		return
	}

	// 更新小程序码
	_, err = s.Engine.Table("datacenter.store_business_setup").
		Cols("miniAppCode").
		Where("finance_code=?", req.StoreId).
		Where("channel_id=?", common.ChannelIdWeChatApp).
		Update(&omnibus_po.StoreBusinessSetup{MiniAppCode: QiniuUrl})
	if err != nil {
		log.Error("编辑店铺小程序码失败，err:", err.Error())
	}
}

// AddWithUser 添加操作日志记录(不需要request)
func (s StoreService) AddWithUser(userInfo map[string]interface{}, req distribution_vo.OperateLogReq) error {
	// operateLog 校验
	if req.ModuleType <= 0 {
		return fmt.Errorf("写入操作日志失败，业务模块id不能为空：ModuleType=%d", req.ModuleType)
	}
	if req.Type <= 0 {
		return fmt.Errorf("写入操作日志失败，操作类型不能为空：Type=%d", req.Type)
	}
	if req.FromId == "" {
		return fmt.Errorf("写入操作日志失败，业务数据id不能为空：From_id=%s", req.FromId)
	}

	s.Begin()
	defer s.Close()
	session := s.Session

	operateLog := &distribution_po.OperateLog{
		ModuleType:  req.ModuleType,
		Type:        req.Type,
		FromId:      req.FromId,
		Description: req.Description,
		UserNo:      cast.ToString(userInfo["userNo"]),
		UserName:    cast.ToString(userInfo["userName"]),
		CreateTime:  time.Now(),
	}

	_, err := session.Insert(operateLog)
	if err != nil {
		log.Error("写入操作日志失败: err=", err.Error())
		return err
	}

	return nil
}

// SaveDeliveryConfig 保存配送方式配置
func (s *StoreService) SaveDeliveryConfig(req so.DeliveryConfigReq) error {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 开启事务
	if err := session.Begin(); err != nil {
		return fmt.Errorf("开启事务失败: %v", err)
	}

	deliveryConfig := &omnibus_po.DeliveryConfig{
		DeliveryName:   req.DeliveryName,
		AppKey:         req.AppKey,
		AppSecret:      req.AppSecret,
		SourceID:       req.SourceID,
		DeliveryType:   req.DeliveryType,
		Code:           req.Code,
		RefreshToken:   req.RefreshToken,
		OrgID:          req.OrgId,
		StoreID:        req.StoreId,
		ChannelID:      req.ChannelID,
		FinanceCode:    req.FinanceCode,
		DeliveryMethod: req.DeliveryMethod,
		ThirdType:      req.ThirdType,
		ServiceCode:    req.ServiceCode,
		Category:       req.Category,
		MytType:        req.MytType,
		Location:       req.Location,
		ShopName:       req.ShopName,
	}
	clo := "delivery_name,app_key,app_secret,source_id,delivery_type,store_id,delivery_method,third_type,service_code"
	if req.DeliveryMethod == 2 && req.ThirdType == 1 {
		clo += ",category,myt_type,location,shop_name"
	}

	if req.ID > 0 {
		// 更新
		deliveryConfig.ID = req.ID

		_, err := session.ID(req.ID).Cols(clo).Update(deliveryConfig)
		if err != nil {
			session.Rollback()
			return fmt.Errorf("更新配送方式配置失败: %v", err)
		}
	} else {
		// 新增
		_, err := session.Insert(deliveryConfig)
		if err != nil {
			session.Rollback()
			return fmt.Errorf("新增配送方式配置失败: %v", err)
		}
	}
	if req.DeliveryMethod == 2 && req.ThirdType == 1 && req.ChannelID == 1 && req.Refresh == 1 {
		SignData := new(so.SignData)
		SignData.ShopId = req.StoreId
		requestToken := so.GetMytTokenRequest{}
		requestToken.Code = req.Code
		requestToken.ShopID = req.StoreId
		requestToken.Name = req.ShopName
		requestToken.Category = "chaoshi"
		requestToken.Type = req.MytType
		requestToken.GrantType = "shop"
		if len(req.Location) <= 3 || !strings.Contains(req.Location, ",") {
			session.Rollback()
			return fmt.Errorf("经纬度错误")
		}
		requestToken.Longitude = strings.Split(req.Location, ",")[0]
		requestToken.Latitude = strings.Split(req.Location, ",")[1]
		// 构建请求体
		jsonData, err := json.Marshal(requestToken)
		if err != nil {
			session.Rollback()
			return fmt.Errorf("反序列化失败: %v", err)
		}
		SignData.Data = string(jsonData)
		result, err := HttpPostToMyt("access_token", *SignData, nil)
		if err != nil {
			session.Rollback()
			return fmt.Errorf("生产token失败: %v", err)
		}
		var tokenRes so.MytTokenResponse
		if err = json.Unmarshal(result, &tokenRes); err != nil {
			session.Rollback()
			return fmt.Errorf("生产token失败: %v", err)
		}

		if tokenRes.Code == 200 {
			redisConn := cache.GetRedisConn()
			key := "myt:token:" + req.StoreId
			tokenData := so.DataToken{}
			if err = json.Unmarshal([]byte(tokenRes.Data), &tokenData); err != nil {
				session.Rollback()
				return fmt.Errorf("生产token失败: %v", err)
			}
			// 保存token到redis
			redisConn.Set(key, tokenData.AccessToken, 0)

		} else {
			session.Rollback()
			return fmt.Errorf("生产token失败:" + tokenRes.Message + tokenRes.Msg)
		}

	}

	// 提交事务
	if err := session.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}

// GetDeliveryConfig 获取配送方式配置
func (s *StoreService) GetDeliveryConfig(req so.GetDeliveryConfigReq) (*omnibus_po.DeliveryConfig, error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	deliveryConfig := new(omnibus_po.DeliveryConfig)
	_, err := session.Where("finance_code = ?", req.FinanceCode).
		Where("channel_id = ?", req.ChannelID).
		Where("org_id = ?", req.OrgId).
		Get(deliveryConfig)

	if err != nil {
		return nil, fmt.Errorf("查询配送方式配置失败: %v", err)
	}

	//if !exists {
	//	return nil, fmt.Errorf("配送方式配置不存在")
	//}

	return deliveryConfig, nil
}

var (
	// 麦芽田API基础URL
	MytBaseURL = "https://open-api-test.maiyatian.com/v1/channel/"
	// Redis中存储token的key前缀
	MytTokenKeyPrefix = "myt:token:"
)

func init() {
	MytBaseURL = config.GetString("maiyatianUrl") // "https://openapi.jddj.com/mockapi/"
	if MytBaseURL == "" {
		glog.Error("config.GetString-MytBaseURL failed")
	}
}

// HttpPostToMyt 发送POST请求到麦芽田API
func HttpPostToMyt(path string, params so.SignData, session *xorm.Session) ([]byte, error) {
	// 获取Redis连接
	redis := cache.GetRedisConn()

	// 获取token
	tokenKey := MytTokenKeyPrefix + params.ShopId
	token := redis.Get(tokenKey).Val()
	// 添加token到请求参数
	params.Token = token
	// 生成签名
	params.Timestamp = time.Now().Unix()
	//db := session
	//deliveryConfig := new(omnibus_po.DeliveryConfig)
	//
	//_, err := db.Where("store_id = ?", params.ShopId).
	//	Where("channel_id = ?", 1).
	//	Where("org_id = ?", 6).
	//	Get(deliveryConfig)
	//if err != nil {
	//	return nil, errors.New("查询配送配置出错")
	//}
	//if deliveryConfig.ID == 0 {
	//	return nil, errors.New("未查询到当前渠道的配送方式")
	//}
	params.AppKey = config.GetString("mytAppKey")
	newUUID := uuid.New()
	params.RequestId = newUUID.String()
	params.Command = path
	params.Signature = GenSignString(params, config.GetString("mytAppSecret"))

	// 发起请求
	return httpPostToMytWithoutToken(path, params)
}

// httpPostToMytWithoutToken 不带token的HTTP POST请求
func httpPostToMytWithoutToken(path string, params so.SignData) ([]byte, error) {
	// 构建请求URL
	url := MytBaseURL + path

	// 构建请求体
	jsonData, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := utils.HttpTransportClientTimeout.Do(req)
	if err != nil {
		log.Error(err.Error())
		return nil, err
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("HTTP request failed with status code: %d", resp.StatusCode)
	}

	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	log.Info(fmt.Sprintf("麦芽田接口:%s,参数:%s,返回:%s", path, string(jsonData), string(body)))

	return body, nil
}

func GenSign(data so.SignData, secretKey string) []byte {
	signData := map[string]interface{}{
		"app_key":   data.AppKey,
		"token":     data.Token,
		"timestamp": data.Timestamp,
		"data":      data.Data,
		"command":   data.Command,
	}

	keys := make([]string, len(signData))
	i := 0
	for k, _ := range signData {
		keys[i] = k
		i++
	}
	sort.Strings(keys)
	dataToSign := ""
	for _, k := range keys {
		dataToSign += fmt.Sprintf("%s=%v,", k, signData[k])
	}
	dataToSign = dataToSign[:len(dataToSign)-1]
	bs := []byte(dataToSign)
	mac := hmac.New(sha256.New, []byte(secretKey))
	mac.Write(bs)
	sign := mac.Sum(nil)

	return sign
}

func GenSignString(data so.SignData, secretKey string) string {
	return base64.URLEncoding.EncodeToString(GenSign(data, secretKey))
}

// GetPrintType 获取打印类型
func (s *StoreService) GetPrintType(ctx context.Context, financeCode string) (*omnibus_po.StoreBusinessSetup, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	// 查询店铺打印设置
	var setup omnibus_po.StoreBusinessSetup
	has, err := session.Where("finance_code = ?", financeCode).Get(&setup)
	if err != nil {
		return nil, fmt.Errorf("查询店铺打印设置失败: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("店铺打印设置不存在")
	}

	return &setup, nil
}

// UpdatePrintType 修改打印类型
func (s *StoreService) UpdatePrintType(ctx context.Context, req so.UpdatePrintTypeRequest) error {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return err
	}

	// 构建更新字段map
	updateFields := make(map[string]interface{})

	// 只有当字段值不为0时才更新
	if req.WhatPrint != 0 {
		updateFields["what_print"] = req.WhatPrint
	}
	if req.IsCheck != 0 {
		updateFields["is_check"] = req.IsCheck
	}

	// 如果没有需要更新的字段,直接返回
	if len(updateFields) == 0 {
		return nil
	}

	// 更新打印类型
	_, err := session.Table(new(omnibus_po.StoreBusinessSetup).TableName()).
		Where("finance_code = ?", req.FinanceCode).
		Update(updateFields)
	if err != nil {
		session.Rollback()
		return fmt.Errorf("更新打印类型失败: %v", err)
	}

	return session.Commit()
}

// SyncEmployee 同步员工信息
func (s StoreService) SyncEmployee(req so.SyncEmployeeReq) error {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 开启事务
	if err := session.Begin(); err != nil {
		return fmt.Errorf("开启事务失败: %v", err)
	}
	defer func() {
		if err := recover(); err != nil {
			session.Rollback()
			log.Errorf("SyncEmployee panic: %v", err)
		}
	}()

	// 2. 获取店铺信息
	var shop distribution_po.Shop
	exists, err := session.Table("eshop.shop").
		Where("saas_shop_id = ?", req.StoreId).
		Get(&shop)
	if err != nil {
		session.Rollback()
		return fmt.Errorf("查询店铺信息失败: %v", err)
	}
	if !exists {
		session.Rollback()
		return errors.New("未找到对应的店铺信息")
	}

	// 3. 处理会员信息
	memberId, memberName, err := s.processMemberInfo(session, req.Mobile, req.Name)
	if err != nil {
		session.Rollback()
		return err
	}

	// 4. 处理分销商信息
	if err := s.processDistributorInfo(session, so.SyncStoreRegisterReq{
		StoreId: req.StoreId,
		OrgId:   req.OrgId,
		Mobile:  req.Mobile,
	}, shop.Id, memberId, memberName); err != nil {
		session.Rollback()
		return err
	}

	// 提交事务
	if err := session.Commit(); err != nil {
		session.Rollback()
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}

// DeleteEmployee 删除员工信息（软删除）
func (s StoreService) DeleteEmployee(req so.DeleteEmployeeReq) error {
	logPrefix := fmt.Sprintf("删除员工信息,入参:%s", utils.JsonEncode(req))
	log.Info(logPrefix)

	s.Begin()
	defer s.Close()

	// 根据手机号查询会员ID
	var memberId int64
	exists, err := s.Session.Table("upetmart.upet_member").
		Where("member_mobile = ?", req.Mobile).
		Cols("member_id").
		Get(&memberId)

	if err != nil {
		log.Error(logPrefix, "查询会员信息失败：err=", err.Error())
		return errors.New("查询会员信息失败")
	}

	if !exists {
		return errors.New("未找到该手机号对应的会员")
	}

	// 查询分销商信息
	var shopId int
	exists, err = s.Session.Table("eshop.shop").
		Where("saas_shop_id = ?", req.StoreId).
		Cols("id").
		Get(&shopId)

	if err != nil {
		log.Error(logPrefix, "查询店铺信息失败：err=", err.Error())
		return errors.New("查询店铺信息失败")
	}

	if !exists {
		return errors.New("未找到该店铺")
	}

	// 直接更新分销商状态
	result, err := s.Session.Table("eshop.dis_distributor").
		Where("member_id = ? AND shop_id = ? AND org_id = ?", memberId, shopId, req.OrgId).
		Update(map[string]interface{}{
			"status":      0,
			"update_time": time.Now(),
		})

	if err != nil {
		log.Error(logPrefix, "更新分销商状态失败：err=", err.Error())
		return errors.New("更新分销商状态失败")
	}

	if result == 0 {
		return errors.New("未找到对应的分销商记录")
	}

	return nil
}

// 批量初始化分类（新版，支持多级结构）
func (c StoreService) InitCategoryV2(chainId int64, userId int64, userName string) error {
	type Cat struct {
		Name string
		Subs []string
		Type int
	}
	cats := []Cat{
		// 前端分类 type=1
		{"狗狗主粮", []string{"狗处方粮", "狗冻干粮", "狗国产粮", "狗进口粮"}, 1},
		{"猫咪主粮", []string{"猫处方粮", "猫冻干粮", "猫国产粮", "猫进口粮"}, 1},
		{"猫砂猫厕", []string{"矿物猫砂", "猫砂除臭", "排便清洁", "膨润土猫砂", "纸砂豆腐砂"}, 1},
		{"宠物零食", []string{"罐头湿粮", "猫草薄荷", "肉类零食", "休闲零食", "磨牙洁齿", "奶酪饼干"}, 1},
		{"宠物玩具", []string{"互动玩具", "漏食玩具", "毛绒玩具", "磨牙玩具", "益智玩具"}, 1},
		{"营养保健", []string{"补钙健骨", "肠胃调理", "猫狗通用", "美毛护肤", "美毛化毛", "综合营养"}, 1},
		{"生活日用", []string{"口腔清洁", "猫咪住所", "排便清理", "训练用品"}, 1},
		{"宠物美容", []string{"猫狗共用", "梳剪工具", "洗浴香波", "洗澡用品"}, 1},
		{"医疗护理", []string{"其它护理", "内外驱虫", "皮肤治疗", "常备药品"}, 1},
		{"其它", []string{"其它用品"}, 1},
		// 线下分类 type=2
		{"狗狗主粮", []string{"狗处方粮", "狗冻干粮", "狗国产粮", "狗进口粮"}, 2},
		{"猫咪主粮", []string{"猫处方粮", "猫冻干粮", "猫国产粮", "猫进口粮"}, 2},
		{"猫砂猫厕", []string{"矿物猫砂", "猫砂除臭", "排便清洁", "膨润土猫砂", "纸砂豆腐砂"}, 2},
		{"宠物零食", []string{"罐头湿粮", "猫草薄荷", "肉类零食", "休闲零食", "磨牙洁齿", "奶酪饼干"}, 2},
		{"宠物玩具", []string{"互动玩具", "漏食玩具", "毛绒玩具", "磨牙玩具", "益智玩具"}, 2},
		{"营养保健", []string{"补钙健骨", "肠胃调理", "猫狗通用", "美毛护肤", "美毛化毛", "综合营养"}, 2},
		{"生活日用", []string{"口腔清洁", "猫咪住所", "排便清理", "训练用品"}, 2},
		{"宠物美容", []string{"猫狗共用", "梳剪工具", "洗浴香波", "洗澡用品"}, 2},
		{"医疗护理", []string{"其它护理", "内外驱虫", "皮肤治疗", "常备药品"}, 2},
		{"其它", []string{"其它用品"}, 2},
		// 服务 type=3
		{"美容", []string{"犬美容", "猫美容"}, 3},
		{"洗护", []string{"犬洗护", "猫洗护"}, 3},
		{"SPA", []string{"犬SPA", "猫SPA"}, 3},
		{"洁牙", []string{"犬洁牙", "猫洁牙"}, 3},
		{"驱虫防疫", []string{"狗狗驱虫", "猫咪驱虫", "狗狗疫苗", "猫咪疫苗"}, 3},
		// 活体 type=4
		{"狗狗", []string{"狗宠"}, 4},
		{"猫咪", []string{"猫宠"}, 4},
		{"异宠", []string{"仓鼠", "兔子", "乌龟", "淡水鱼", "海水鱼", "其它"}, 4},
	}

	c.Begin()
	defer c.Close()
	session := c.Engine.NewSession()
	defer session.Close()
	session.Begin()

	for _, cat := range cats {
		parent := product_po.ProCategory{
			ChainId:     chainId,
			Name:        cat.Name,
			ParentId:    0,
			Sort:        1,
			Img:         "",
			CreatedBy:   userId,
			CreatedName: userName,
			UpdatedBy:   userId,
			UpdatedName: userName,
			Type:        cat.Type,
		}
		_, err := session.Table("eshop.pro_category").Insert(&parent)
		if err != nil {
			session.Rollback()
			return err
		}
		if len(cat.Subs) > 0 {
			for index, sub := range cat.Subs {
				subCat := product_po.ProCategory{
					ChainId:     chainId,
					Name:        sub,
					ParentId:    parent.Id,
					Sort:        index + 1,
					Img:         "",
					CreatedBy:   userId,
					CreatedName: userName,
					UpdatedBy:   userId,
					UpdatedName: userName,
					Type:        cat.Type,
				}
				_, err := session.Table("eshop.pro_category").Insert(&subCat)
				if err != nil {
					session.Rollback()
					return err
				}
			}
		}
	}
	session.Commit()
	return nil
}
