package services

import (
	"context"
	omnibus_po "eShop/domain/omnibus-po"
	"eShop/services/common"
	omnibus_vo "eShop/view-model/omnibus-vo"
	"fmt"

	"github.com/spf13/cast"
)

// CommissionService 提成服务
type CommissionService struct {
	common.BaseService
}

// CreateCommissionSetup 创建提成设置
func (s *CommissionService) CreateCommissionSetup(ctx context.Context, req omnibus_vo.CreateCommissionSetupReq) (*omnibus_vo.CommissionSetupResp, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return nil, err
	}

	// 1. 创建提成设置主表
	setup := &omnibus_po.CommissionSetup{
		ChainId:    req.ChainId,
		StoreId:    req.StoreId,
		SetupName:  req.SetupName,
		Status:     req.Status,
		Operator:   req.Operator,
		OperatorId: req.OperatorId,
	}

	if err := setup.Create(session); err != nil {
		session.Rollback()
		return nil, fmt.Errorf("创建提成设置失败: %v", err)
	}

	// 2. 创建提成设置明细
	for _, detail := range req.Details {
		setupDetail := &omnibus_po.CommissionDetail{
			SetupId:        setup.Id,
			CommissionType: detail.CommissionType,
			CalcType:       detail.CalcType,
			CommissionRate: detail.CommissionRate,
			ScopeType:      detail.ScopeType,
		}

		if err := setupDetail.Create(session); err != nil {
			session.Rollback()
			return nil, fmt.Errorf("创建提成明细失败: %v", err)
		}

		// 3. 如果是指定范围，创建提成商品记录，且仅商品、服务、寄养类型支持指定
		if detail.ScopeType == 2 && len(detail.Products) > 0 &&
			(detail.CommissionType == 1 || detail.CommissionType == 2 || detail.CommissionType == 3 || detail.CommissionType == 4) {
			productItems := make([]*omnibus_po.CommissionProduct, 0, len(detail.Products))
			for _, product := range detail.Products {
				productItem := &omnibus_po.CommissionProduct{
					DetailId:    setupDetail.Id,
					SkuId:       product.SkuId,
					ProductName: product.ProductName,
					ProductType: detail.CommissionType, // 使用明细的提成类型作为商品类型
				}
				productItems = append(productItems, productItem)
			}

			if len(productItems) > 0 {
				productObj := &omnibus_po.CommissionProduct{}
				if err := productObj.BatchCreate(session, productItems); err != nil {
					session.Rollback()
					return nil, fmt.Errorf("创建提成商品失败: %v", err)
				}
			}
		}
	}

	if err := session.Commit(); err != nil {
		session.Rollback()
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	return &omnibus_vo.CommissionSetupResp{
		Id:        setup.Id,
		SetupName: setup.SetupName,
	}, nil
}

// UpdateCommissionSetup 更新提成设置
func (s *CommissionService) UpdateCommissionSetup(ctx context.Context, req omnibus_vo.UpdateCommissionSetupReq) error {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return err
	}

	// 1. 查询提成设置是否存在
	setup := &omnibus_po.CommissionSetup{}
	setupObj, err := setup.GetById(session, req.Id)
	if err != nil {
		session.Rollback()
		return fmt.Errorf("查询提成设置失败: %v", err)
	}

	if setupObj == nil {
		session.Rollback()
		return fmt.Errorf("提成设置不存在")
	}

	// 2. 更新提成设置主表
	setupObj.SetupName = req.SetupName
	setupObj.Status = req.Status
	setupObj.Operator = req.Operator
	setupObj.OperatorId = req.OperatorId

	if err := setupObj.Update(session); err != nil {
		session.Rollback()
		return fmt.Errorf("更新提成设置失败: %v", err)
	}

	// 3. 获取旧的提成明细
	detailObj := &omnibus_po.CommissionDetail{}
	oldDetails, err := detailObj.GetBySetupId(session, req.Id)
	if err != nil {
		session.Rollback()
		return fmt.Errorf("获取旧提成明细失败: %v", err)
	}

	// 创建旧明细的映射，用于快速查找
	oldDetailMap := make(map[int]*omnibus_po.CommissionDetail)
	for _, detail := range oldDetails {
		oldDetailMap[detail.Id] = detail
	}

	// 处理明细数据
	processedDetailIds := make(map[int]bool)

	// 4. 处理请求中的明细数据
	for _, reqDetail := range req.Details {
		if reqDetail.Id > 0 && oldDetailMap[reqDetail.Id] != nil {
			// 4.1 更新已存在的明细
			oldDetail := oldDetailMap[reqDetail.Id]
			oldDetail.CommissionType = reqDetail.CommissionType
			oldDetail.CalcType = reqDetail.CalcType
			oldDetail.CommissionRate = reqDetail.CommissionRate
			oldDetail.ScopeType = reqDetail.ScopeType

			if err := oldDetail.Update(session); err != nil {
				session.Rollback()
				return fmt.Errorf("更新提成明细失败: %v", err)
			}

			processedDetailIds[reqDetail.Id] = true

			// 4.2 处理明细的商品数据
			if reqDetail.ScopeType == 2 &&
				(reqDetail.CommissionType == 1 || reqDetail.CommissionType == 2 || reqDetail.CommissionType == 4) {

				// 4.2.1 删除旧的商品数据
				productObj := &omnibus_po.CommissionProduct{}
				if err := productObj.DeleteByDetailId(session, reqDetail.Id); err != nil {
					session.Rollback()
					return fmt.Errorf("删除旧提成商品失败: %v", err)
				}

				// 4.2.2 添加新的商品数据
				if len(reqDetail.Products) > 0 {
					productItems := make([]*omnibus_po.CommissionProduct, 0, len(reqDetail.Products))
					for _, product := range reqDetail.Products {
						productItem := &omnibus_po.CommissionProduct{
							DetailId:    reqDetail.Id,
							SkuId:       product.SkuId,
							ProductName: product.ProductName,
							ProductType: reqDetail.CommissionType,
						}
						productItems = append(productItems, productItem)
					}

					if err := productObj.BatchCreate(session, productItems); err != nil {
						session.Rollback()
						return fmt.Errorf("创建新提成商品失败: %v", err)
					}
				}
			} else {
				// 如果不是指定范围，删除可能存在的商品数据
				productObj := &omnibus_po.CommissionProduct{}
				if err := productObj.DeleteByDetailId(session, reqDetail.Id); err != nil {
					session.Rollback()
					return fmt.Errorf("删除旧提成商品失败: %v", err)
				}
			}
		} else {
			// 4.3 创建新的明细记录
			newDetail := &omnibus_po.CommissionDetail{
				SetupId:        req.Id,
				CommissionType: reqDetail.CommissionType,
				CalcType:       reqDetail.CalcType,
				CommissionRate: reqDetail.CommissionRate,
				ScopeType:      reqDetail.ScopeType,
			}

			if err := newDetail.Create(session); err != nil {
				session.Rollback()
				return fmt.Errorf("创建新提成明细失败: %v", err)
			}

			// 4.4 处理新明细的商品数据
			if reqDetail.ScopeType == 2 && len(reqDetail.Products) > 0 &&
				(reqDetail.CommissionType == 1 || reqDetail.CommissionType == 2 || reqDetail.CommissionType == 4) {

				productItems := make([]*omnibus_po.CommissionProduct, 0, len(reqDetail.Products))
				for _, product := range reqDetail.Products {
					productItem := &omnibus_po.CommissionProduct{
						DetailId:    newDetail.Id,
						SkuId:       product.SkuId,
						ProductName: product.ProductName,
						ProductType: reqDetail.CommissionType,
					}
					productItems = append(productItems, productItem)
				}

				if len(productItems) > 0 {
					productObj := &omnibus_po.CommissionProduct{}
					if err := productObj.BatchCreate(session, productItems); err != nil {
						session.Rollback()
						return fmt.Errorf("创建新提成商品失败: %v", err)
					}
				}
			}
		}
	}

	// 5. 删除请求中不存在的旧明细及其商品数据
	for _, oldDetail := range oldDetails {
		if !processedDetailIds[oldDetail.Id] {
			// 5.1 删除明细关联的商品数据
			productObj := &omnibus_po.CommissionProduct{}
			if err := productObj.DeleteByDetailId(session, oldDetail.Id); err != nil {
				session.Rollback()
				return fmt.Errorf("删除旧提成商品失败: %v", err)
			}

			// 5.2 删除明细本身
			if err := oldDetail.Delete(session); err != nil {
				session.Rollback()
				return fmt.Errorf("删除旧提成明细失败: %v", err)
			}
		}
	}

	if err := session.Commit(); err != nil {
		session.Rollback()
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}

// GetCommissionSetupById 根据ID获取提成设置详情
func (s *CommissionService) GetCommissionSetupById(ctx context.Context, id int) (*omnibus_vo.CommissionSetupDetailResp, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	// 1. 查询提成设置主表
	setup := &omnibus_po.CommissionSetup{}
	setupObj, err := setup.GetById(session, id)
	if err != nil {
		return nil, fmt.Errorf("查询提成设置失败: %v", err)
	}

	if setupObj == nil {
		return nil, fmt.Errorf("提成设置不存在")
	}

	// 2. 查询提成设置明细
	detailObj := &omnibus_po.CommissionDetail{}
	details, err := detailObj.GetBySetupId(session, id)
	if err != nil {
		return nil, fmt.Errorf("查询提成明细失败: %v", err)
	}

	// 3. 提取明细ID列表
	detailIds := make([]int, 0, len(details))
	for _, detail := range details {
		detailIds = append(detailIds, detail.Id)
	}

	// 4. 查询提成商品
	var products []*omnibus_po.CommissionProduct
	if len(detailIds) > 0 {
		productObj := &omnibus_po.CommissionProduct{}
		products, err = productObj.GetByDetailIds(session, detailIds)
		if err != nil {
			return nil, fmt.Errorf("查询提成商品失败: %v", err)
		}
	}

	// 5. 整理提成商品数据，按明细ID分组
	productMap := make(map[int][]*omnibus_vo.CommissionProductItem)
	for _, product := range products {
		if _, ok := productMap[product.DetailId]; !ok {
			productMap[product.DetailId] = make([]*omnibus_vo.CommissionProductItem, 0)
		}

		productItem := &omnibus_vo.CommissionProductItem{
			Id:          product.Id,
			DetailId:    product.DetailId,
			SkuId:       product.SkuId,
			ProductName: product.ProductName,
			ProductType: product.ProductType,
		}

		productMap[product.DetailId] = append(productMap[product.DetailId], productItem)
	}

	// 6. 组装明细数据
	detailsResp := make([]*omnibus_vo.CommissionDetailItem, 0)
	for _, detail := range details {
		item := &omnibus_vo.CommissionDetailItem{
			Id:             detail.Id,
			CommissionType: detail.CommissionType,
			CalcType:       detail.CalcType,
			CommissionRate: detail.CommissionRate,
			ScopeType:      detail.ScopeType,
		}

		// 添加提成商品数据
		if detail.ScopeType == 2 {
			if productItems, ok := productMap[detail.Id]; ok {
				item.Products = productItems
			}
		}

		detailsResp = append(detailsResp, item)
	}

	// 7. 组装返回数据
	resp := &omnibus_vo.CommissionSetupDetailResp{
		Id:          setupObj.Id,
		StoreId:     setupObj.StoreId,
		SetupName:   setupObj.SetupName,
		Status:      setupObj.Status,
		Operator:    setupObj.Operator,
		OperatorId:  setupObj.OperatorId,
		CreatedTime: setupObj.CreatedTime,
		UpdatedTime: setupObj.UpdatedTime,
		Details:     detailsResp,
	}

	return resp, nil
}

// GetCommissionSetupList 获取提成设置列表
func (s *CommissionService) GetCommissionSetupList(ctx context.Context, req omnibus_vo.GetCommissionSetupListReq) (*omnibus_vo.GetCommissionSetupListResp, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	// 1. 查询提成设置列表
	setup := &omnibus_po.CommissionSetup{}

	// 处理分页参数
	offset := (req.PageIndex - 1) * req.PageSize
	if offset < 0 {
		offset = 0
	}

	list, total, err := setup.GetList(session, req.StoreId, req.ChainId, req.SetupName, req.Status, offset, req.PageSize)
	if err != nil {
		return nil, fmt.Errorf("查询提成设置列表失败: %v", err)
	}

	// 2. 组装返回数据
	items := make([]*omnibus_vo.CommissionSetupItem, 0)
	for _, item := range list {
		// 2.1 查询提成明细
		detailObj := &omnibus_po.CommissionDetail{}
		details, err := detailObj.GetBySetupId(session, item.Id)
		if err != nil {
			return nil, fmt.Errorf("查询提成明细失败: %v", err)
		}

		// 2.2 查询相关员工数量
		employeeObj := &omnibus_po.CommissionEmployee{}
		employeeCount, err := employeeObj.GetEmployeeCountBySetupId(session, item.Id)
		if err != nil {
			return nil, fmt.Errorf("查询员工数量失败: %v", err)
		}

		// 2.3 统计各类型提成
		productCommission := ""
		serviceCommission := ""
		fosterCommission := ""
		liveCommission := ""

		for _, detail := range details {
			rate := fmt.Sprintf("%.2f%%", detail.CommissionRate)
			switch detail.CommissionType {
			case 1: // 商品提成
				if productCommission == "" {
					productCommission = "全部商品" + rate
				}
			case 2: // 服务提成
				if serviceCommission == "" {
					serviceCommission = "全部服务" + rate
				}
			case 3: // 寄养提成
				if fosterCommission == "" {
					fosterCommission = "全部寄养" + rate
				}
			case 4: // 活体提成
				if liveCommission == "" {
					liveCommission = "全部活体" + rate
				}
			}
		}

		items = append(items, &omnibus_vo.CommissionSetupItem{
			Id:                item.Id,
			StoreId:           item.StoreId,
			SetupName:         item.SetupName,
			Status:            item.Status,
			Operator:          item.Operator,
			ProductCommission: productCommission,
			ServiceCommission: serviceCommission,
			FosterCommission:  fosterCommission,
			LiveCommission:    liveCommission,
			EmployeeCount:     employeeCount,
			CreatedTime:       item.CreatedTime,
			UpdatedTime:       item.UpdatedTime,
		})
	}

	return &omnibus_vo.GetCommissionSetupListResp{
		Total: total,
		List:  items,
	}, nil
}

// ChangeCommissionSetupStatus 修改提成设置状态
func (s *CommissionService) ChangeCommissionSetupStatus(ctx context.Context, req omnibus_vo.UpdateCommissionSetupStatusReq) error {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	// 1. 查询提成设置是否存在
	setup := &omnibus_po.CommissionSetup{}
	setupObj, err := setup.GetById(session, req.Id)
	if err != nil {
		return fmt.Errorf("查询提成设置失败: %v", err)
	}

	if setupObj == nil {
		return fmt.Errorf("提成设置不存在")
	}

	// 2. 更新状态
	setupObj.Status = req.Status
	setupObj.Operator = req.Operator
	setupObj.OperatorId = req.OperatorId

	if err := setupObj.Update(session); err != nil {
		return fmt.Errorf("更新提成设置状态失败: %v", err)
	}

	return nil
}

// GetEmployeeList 获取员工列表（包含选中状态和提成活动名称）
func (s *CommissionService) GetEmployeeList(ctx context.Context, req omnibus_vo.GetEmployeeListReq) (*omnibus_vo.GetEmployeeListResp, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	// 1. 检查提成设置是否存在（如果有指定）
	if req.SetupId > 0 {
		setup := &omnibus_po.CommissionSetup{}
		setupObj, err := setup.GetById(session, req.SetupId)
		if err != nil {
			return nil, fmt.Errorf("查询提成设置失败: %v", err)
		}

		if setupObj == nil {
			return nil, fmt.Errorf("提成设置不存在")
		}
	}

	// 2. 设置默认分页参数
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 3. 使用领域层方法查询员工列表
	employeeObj := &omnibus_po.CommissionEmployee{}
	items, total, err := employeeObj.GetEmployeeListWithSetup(
		session,
		req.SetupId,
		req.TenantId,
		req.RealName,
		req.Mobile,
		req.RoleId,
		req.SourceChainId,
		req.PageIndex,
		req.PageSize,
	)
	if err != nil {
		return nil, fmt.Errorf("查询员工列表失败: %v", err)
	}

	// 4. 转换为视图模型
	result := make([]*omnibus_vo.EmployeeItem, 0, len(items))
	for _, item := range items {
		employeeItem := &omnibus_vo.EmployeeItem{
			Id:          cast.ToString(item.Id),
			TenantId:    item.TenantId,
			RealName:    item.RealName,
			Mobile:      item.Mobile,
			RoleName:    item.RoleName,
			IsSelected:  item.IsSelected,
			SetupName:   item.SetupName,
			Operator:    item.Operator,
			CreatedTime: item.CreatedTime.Format("2006-01-02 15:04:05"),
		}
		result = append(result, employeeItem)
	}

	return &omnibus_vo.GetEmployeeListResp{
		Total: total,
		List:  result,
	}, nil
}

// UpdateCommissionEmployees 更新提成设置的员工
func (s *CommissionService) UpdateCommissionEmployees(ctx context.Context, req omnibus_vo.UpdateCommissionEmployeesReq) error {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	// 开启事务
	if err := session.Begin(); err != nil {
		return fmt.Errorf("开启事务失败: %v", err)
	}

	// 1. 检查提成设置是否存在
	setup := &omnibus_po.CommissionSetup{}
	setupObj, err := setup.GetById(session, req.SetupId)
	if err != nil {
		session.Rollback()
		return fmt.Errorf("查询提成设置失败: %v", err)
	}

	if setupObj == nil {
		session.Rollback()
		return fmt.Errorf("提成设置不存在")
	}

	// 2. 检查员工是否已参与其他提成活动
	employee := &omnibus_po.CommissionEmployee{}
	for _, employeeId := range req.Data {
		has, setupName, err := employee.CheckEmployeeInOtherSetup(session, req.SetupId, cast.ToInt64(employeeId), req.StoreId)
		if err != nil {
			session.Rollback()
			return fmt.Errorf("检查员工是否参与其他提成活动失败: %v", err)
		}
		if has {
			session.Rollback()
			return fmt.Errorf("员工已参与提成活动'%s'，不能重复参与", setupName)
		}
	}

	// 3. 删除旧的员工记录
	if err := employee.DeleteBySetupId(session, req.SetupId); err != nil {
		session.Rollback()
		return fmt.Errorf("删除旧员工记录失败: %v", err)
	}

	// 4. 创建新的员工记录
	if len(req.Data) > 0 {
		employees := make([]*omnibus_po.CommissionEmployee, 0, len(req.Data))
		for _, employeeId := range req.Data {
			employees = append(employees, &omnibus_po.CommissionEmployee{
				SetupId:    req.SetupId,
				EmployeeId: cast.ToInt64(employeeId),
				Operator:   req.Operator,
				OperatorId: req.OperatorId,
			})
		}
		if err := employee.BatchCreate(session, employees); err != nil {
			session.Rollback()
			return fmt.Errorf("创建员工记录失败: %v", err)
		}
	}

	// 提交事务
	if err := session.Commit(); err != nil {
		session.Rollback()
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}

// GetEmployeePerformance 获取员工业绩统计，只返回指定时间范围内有业绩记录的员工
func (s *CommissionService) GetEmployeePerformance(req omnibus_vo.GetPerformanceListReq) ([]*omnibus_vo.EmployeePerformance, int64, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	// 构建基础查询，从员工表出发
	query := session.Table("eshop_saas.t_employee").Alias("e").
		Join("INNER", "eshop_saas.t_role r", "e.role_id = r.id")

	// 添加员工筛选条件
	query = query.Where("e.is_deleted = 0 AND e.state = 1")

	if req.StoreId > 0 {
		query = query.And("e.tenant_id = ?", req.StoreId)
	}

	if req.RealName != "" {
		query = query.And("e.real_name LIKE ?", "%"+req.RealName+"%")
	}

	if req.Mobile != "" {
		query = query.And("e.account LIKE ?", "%"+req.Mobile+"%")
	}

	if req.RoleId > 0 {
		query = query.And("e.role_id = ?", req.RoleId)
	}

	startTime := req.StartDate
	endTime := req.EndDate

	// 添加筛选条件：只显示有业绩记录的员工
	hasPerformanceSubQuery := fmt.Sprintf(
		"(SELECT DISTINCT employee_id FROM eshop.commission_performance "+
			"WHERE order_time BETWEEN '%s' AND '%s' AND store_id = %d) has_performance",
		startTime, endTime, req.StoreId)

	query = query.Join("INNER", hasPerformanceSubQuery, "e.id = has_performance.employee_id")

	// 商品提成子查询(类型1)
	productSubQuery := fmt.Sprintf(
		"(SELECT employee_id, SUM(sales_amount) as sales_amount, "+
			"SUM(commission_amount) as commission_amount FROM eshop.commission_performance "+
			"WHERE product_type = 1 AND order_time BETWEEN '%s' AND '%s' AND store_id = %d "+
			"GROUP BY employee_id) product",
		startTime,
		endTime,
		req.StoreId)

	// 服务提成子查询(类型2)
	serviceSubQuery := fmt.Sprintf(
		"(SELECT employee_id, SUM(sales_amount) as sales_amount, "+
			"SUM(commission_amount) as commission_amount FROM eshop.commission_performance "+
			"WHERE product_type = 4 AND order_time BETWEEN '%s' AND '%s' AND store_id = %d "+
			"GROUP BY employee_id) service",
		startTime,
		endTime,
		req.StoreId)

	// 寄养提成子查询(类型3)
	fosterSubQuery := fmt.Sprintf(
		"(SELECT employee_id, SUM(sales_amount) as sales_amount, "+
			"SUM(commission_amount) as commission_amount FROM eshop.commission_performance "+
			"WHERE product_type = 6 AND order_time BETWEEN '%s' AND '%s' AND store_id = %d "+
			"GROUP BY employee_id) foster",
		startTime,
		endTime,
		req.StoreId)

	// 活体提成子查询(类型4)
	liveSubQuery := fmt.Sprintf(
		"(SELECT employee_id, SUM(sales_amount) as sales_amount, "+
			"SUM(commission_amount) as commission_amount FROM eshop.commission_performance "+
			"WHERE product_type = 5 AND order_time BETWEEN '%s' AND '%s' AND store_id = %d "+
			"GROUP BY employee_id) live",
		startTime,
		endTime,
		req.StoreId)

	// 连接各个子查询
	query = query.
		Join("LEFT", productSubQuery, "e.id = product.employee_id").
		Join("LEFT", serviceSubQuery, "e.id = service.employee_id").
		Join("LEFT", fosterSubQuery, "e.id = foster.employee_id").
		Join("LEFT", liveSubQuery, "e.id = live.employee_id")

	// 设置查询字段
	query = query.Select(`
		e.id as employee_id,
		e.real_name,
		e.account as mobile,
		r.name as role_name,
		IFNULL(product.sales_amount, 0) as product_sales,
		IFNULL(product.commission_amount, 0) as product_commission,
		IFNULL(service.sales_amount, 0) as service_sales,
		IFNULL(service.commission_amount, 0) as service_commission,
		IFNULL(foster.sales_amount, 0) as foster_sales,
		IFNULL(foster.commission_amount, 0) as foster_commission,
		IFNULL(live.sales_amount, 0) as live_sales,
		IFNULL(live.commission_amount, 0) as live_commission,
		(IFNULL(product.sales_amount, 0) + IFNULL(service.sales_amount, 0) + 
		 IFNULL(foster.sales_amount, 0) + IFNULL(live.sales_amount, 0)) as total_sales,
		(IFNULL(product.commission_amount, 0) + IFNULL(service.commission_amount, 0) + 
		 IFNULL(foster.commission_amount, 0) + IFNULL(live.commission_amount, 0)) as total_commission
	`)

	// 按总提成降序排序
	query = query.OrderBy("total_commission DESC")

	// 计算偏移量
	offset := (req.PageIndex - 1) * req.PageSize
	if offset < 0 {
		offset = 0
	}

	var items []*omnibus_vo.EmployeePerformance
	count, err := query.Limit(req.PageSize, offset).FindAndCount(&items)

	return items, count, err
}

// GetEmployeePerformanceDetail 获取员工业绩明细
func (s *CommissionService) GetEmployeePerformanceDetail(req omnibus_vo.GetPerformanceDetailReq) (*omnibus_vo.GetPerformanceDetailListResp, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	// 验证参数
	if req.EmployeeId == "" {
		return nil, fmt.Errorf("员工ID不能为空")
	}

	if req.StoreId <= 0 {
		return nil, fmt.Errorf("店铺ID不能为空")
	}

	// 构建基础查询
	queryBuilder := session.Table("eshop.commission_performance").Alias("cp").
		Join("LEFT", "eshop_saas.t_employee e", "cp.employee_id = e.id").
		Join("LEFT", "eshop_saas.t_role r", "e.role_id = r.id").
		Where("cp.employee_id = ? AND cp.store_id = ? AND cp.order_time BETWEEN ? AND ?",
			cast.ToInt64(req.EmployeeId), req.StoreId, req.StartDate, req.EndDate)

	// 获取总数 (仅在需要分页时计算)
	var total int64

	// 设置分页 (如果 PageSize > 0)
	if req.PageSize > 0 {
		if req.PageIndex <= 0 {
			req.PageIndex = 1
		}
		offset := (req.PageIndex - 1) * req.PageSize
		queryBuilder.Limit(req.PageSize, offset)
	} else {
		// 如果不需要分页（导出），则不需要计算总数，或者在控制器层面处理
		// total = 0 // 或者根据需要设置
	}

	// 查询明细列表
	var detailItems []*omnibus_vo.GetPerformanceDetailResp
	total, err := queryBuilder.Select("cp.id, cp.order_id, cp.order_no, cp.sku_id, cp.product_name, " +
		"cp.product_type, cp.sales_amount, cp.commission_rate, cp.commission_amount, " +
		"cp.order_time, e.real_name, e.account as mobile, r.name as role_name, cp.store_id,cp.customer_name,cp.unit_price,cp.quantity").
		OrderBy("cp.order_time DESC").
		FindAndCount(&detailItems)

	if err != nil {
		return nil, fmt.Errorf("查询业绩明细失败: %v", err)
	}

	return &omnibus_vo.GetPerformanceDetailListResp{
		List:  detailItems,
		Total: total,
	}, nil
}

// AssignOrderCommission
// AssignOrderCommission 分配订单业绩给员工
// 根据订单信息和商品信息，匹配员工提成设置和规则，计算提成金额并保存业绩记录
// 参数:
//   - req: 订单业绩分配请求
//
// 返回:
//   - 成功分配的业绩记录数量
//   - 错误信息
func (s *CommissionService) AssignOrderCommission(req *omnibus_vo.AssignOrderCommissionReq) (int, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	if req == nil {
		return 0, fmt.Errorf("请求参数不能为空")
	}

	if len(req.OrderItems) == 0 {
		return 0, fmt.Errorf("订单项不能为空")
	}

	// 开启事务
	if err := session.Begin(); err != nil {
		return 0, fmt.Errorf("开启事务失败: %v", err)
	}

	// 将VO层对象转换为PO层对象
	poOrderItems := make([]omnibus_po.OrderItemCommission, len(req.OrderItems))
	for i, item := range req.OrderItems {
		poOrderItems[i] = omnibus_po.OrderItemCommission{
			OrderId:       item.OrderId,
			OrderNo:       item.OrderNo,
			SkuId:         item.SkuId,
			ProductName:   item.ProductName,
			ProductType:   item.ProductType,
			SalesAmount:   item.SalesAmount,
			ActualAmount:  item.ActualAmount,
			OrderTime:     item.OrderTime,
			StoreId:       item.StoreId,
			Channel:       item.Channel,
			AssignedEmpId: item.AssignedEmpId,
			UnitPrice:     item.UnitPrice,
			Quantity:      item.Quantity,
			CustomerName:  item.CustomerName,
		}
	}

	// 调用领域层方法分配订单业绩
	commissionPerformance := &omnibus_po.CommissionPerformance{}
	count, err := commissionPerformance.AssignOrderCommission(
		session,
		poOrderItems,
		req.Operator,
		req.OperatorId,
	)

	if err != nil {
		session.Rollback()
		return 0, fmt.Errorf("分配订单业绩失败: %v", err)
	}

	// 提交事务
	if err = session.Commit(); err != nil {
		return 0, fmt.Errorf("提交事务失败: %v", err)
	}

	return count, nil
}
