package export

import (
	product_po2 "eShop/domain/product-po"
	"eShop/infra/jwtauth"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/product-service/services"
	product_vo "eShop/view-model/product-vo"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

type SaasChainProductExport struct {
	F            *excelize.File
	SheetName    string
	ExportParams *product_vo.FindChainProductListReq
	writer       *excelize.StreamWriter
	common.BaseService
	ChainId int64
	JwtInfo *jwtauth.XCShopPayload
}

// DataExport 宠物saas 连锁商品列表导出
func (e SaasChainProductExport) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(product_vo.FindChainProductListReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 1000

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()
	client := services.ProductService{
		JwtInfo: e.JwtInfo,
	}

	k := 0
	for {
		ret, _, err := client.FindChainProductList(*e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {
			for kk := range ret[i].Sku {
				k++
				axis := fmt.Sprintf("A%d", k+1)

				//连锁商品Id	商品条码	商品名称	商品一级分类>商品二级分类	品牌	供应商	规格	库存单位	零售价（元）
				//市场价	重量（kg）	平台货号	新零售销售	店铺一级分类	店铺一级分类	美团商品类目	饿了么商品类目	京东到家商品类目
				tmp := []interface{}{
					ret[i].ProductId,                            // 连锁商品Id
					ret[i].Sku[kk].Id,                           // 连锁商品skuId
					ret[i].Sku[kk].BarCode,                      // 商品条码
					ret[i].Name,                                 // 商品名称
					ret[i].CategoryNav,                          // 商品分类
					ret[i].ShopCateFirst,                        // 一级前台分类
					ret[i].ShopCateSecond,                       // 二级前台级分类
					ret[i].Sku[kk].ProductSpecs,                 //规格
					ret[i].Sku[kk].StoreUnit,                    //库存单位
					ret[i].Sku[kk].StoreUnitKey,                 //库存单位key
					utils.Fen2Yuan(ret[i].Sku[kk].BasicPrice),   //成本价（元）
					utils.Fen2Yuan(ret[i].Sku[kk].StorePrice),   //门店价
					utils.Fen2Yuan(ret[i].Sku[kk].XcxPrice),     //小程序价
					utils.Fen2Yuan(ret[i].Sku[kk].MtPrice),      //美团价
					utils.Fen2Yuan(ret[i].Sku[kk].ElmPrice),     //饿了么价
					ret[i].Sku[kk].WeightForUnit,                //重量（kg）
					ret[i].Sku[kk].SkuCode,                      //平台货号
					fmt.Sprintf("%v%%", ret[i].Sku[kk].TaxRate), //税率
					ret[i].CategoryThirdNameMt,                  //美团商品类目
					ret[i].CategoryThirdNameElm,                 //饿了么商品类目
					ret[i].CategoryThirdNameJddj,                //京东到家商品类目

				}
				_ = e.writer.SetRow(axis, tmp)
			}

		}
		if len(ret) < int(e.ExportParams.PageSize) {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return
}

// SetSheetName 分销员导出列表头

func (e SaasChainProductExport) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"连锁商品Id", "skuID", "商品条码", "商品名称", "后台分类", "一级前台分类", "二级前台级分类", "规格", "库存单位", "库存单位key",
		"成本价（元）", "门店价(元)", "小程序价(元)", "美团价(元)", "饿了么(价)", "重量（kg）", "平台货号", "税率(%)", "美团商品类目", "饿了么商品类目", "京东到家商品类目",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e SaasChainProductExport) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("宠物SAAS连锁商品导出-%s%d.xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

// 连锁商品导入 msg为返回错误信息
func (e SaasChainProductExport) OperationFunc(row []string, orgId int) (msg string) {
	e.Begin()
	defer e.Close()

	//先校验数据
	client := services.ProductService{}
	var chainProduct product_vo.ChainProduct
	var err error
	//校验不能为空
	if len(row) < 27 {
		msg = "模板格式错误，请上传正确模板"
		return msg
	}
	barCode := row[0]                     // 商品条码
	productName := row[1]                 // 商品名称
	categoryIdOfflineFirstName := row[2]  // 一级后台分类
	categoryIdOfflineSecondName := row[3] // 二级后台分类
	categoryNameFirst := row[4]           // 一级前台分类
	categoryNameSecond := row[5]          // 二级前台分类
	pic := row[6]                         // 商品主图
	contentPc := row[7]                   // 商品详情
	IsMoreSku := row[8]                   // 是否多规格
	productSpecs := row[9]                // 规格
	storeUnit := row[10]                  // 库存单位
	storeUnitKey := row[11]               // 库存单位key
	BasicPrice := row[12]                 // 成本价
	StorePrice := row[13]                 // 门店价
	XcxPrice := row[14]                   // 小程序价
	MtPrice := row[15]                    // 美团价
	ElmPrice := row[16]                   // 饿了么价
	// JddjPrice := row[17]                  // 京东到家价
	weightForUnit := row[18]        // 重量
	skuCode := row[19]              // 平台货号
	RateRax := row[20]              // 税率
	categoryThirdNameMt := row[21]  // 美团外卖分类
	categoryThirdIdMt := row[22]    // 美团外卖分类id
	categoryThirdNameElm := row[23] // 饿了么分类
	categoryThirdIdElm := row[24]   // 饿了么分类id

	//如果条码为空， 则自动生成一条
	if len(barCode) == 0 {
		barCode, err = client.GenerateBarCode(e.ChainId)
		if err != nil {
			msg = "生成条码失败，错误为" + err.Error()
			return
		}
	}
	if len(productName) == 0 {
		msg = "商品名称不能为空"
		return
	}
	if len([]rune(productName)) > 45 {
		msg = "商品名称最大长度为45个字符"
		return
	}

	if len(categoryIdOfflineFirstName) == 0 {
		msg = "一级后台分类不能为空"
		return
	}

	if len(categoryIdOfflineSecondName) == 0 {
		msg = "二级后台分类不能为空"
		return
	}
	if len(categoryNameFirst) == 0 {
		categoryNameFirst = "未分类"
	}

	if len(categoryNameSecond) == 0 {
		categoryNameSecond = "未分类商品"
	}

	if len(productSpecs) == 0 {
		msg = "规格不能为空"
		return
	}
	if len([]rune(productSpecs)) > 15 {
		msg = "规格最多15个字符"
		return
	}

	if len(storeUnit) == 0 {
		msg = "库存单位不能为空"
		return
	}

	if len(storeUnitKey) == 0 {
		msg = "库存单位key不能为空"
		return
	}

	if cast.ToFloat64(BasicPrice) <= 0 {
		msg = "成本价不能为空"
		return
	}
	if cast.ToFloat64(StorePrice) <= 0 {
		msg = "门店价不能为空"
		return
	}

	if cast.ToFloat64(weightForUnit) <= 0 {
		msg = "重量不能为空"
		return
	}

	//  categoryIdOfflineFirstName根据一级后台分类名称 获取一级后台分类id；categoryIdOfflineSecondName根据二级后台分类名称 获取二级后台分类id； 判断一级后台分类是不是真的是 二级分类的父级
	_, categoryInfoMap, _, err := new(product_po2.ProCategory).GetCategoryMapInfo(e.Engine, product_po2.CategoryMapInfoReq{
		ChainId:       cast.ToInt64(e.ChainId),
		CategoryNames: []string{categoryIdOfflineFirstName, categoryIdOfflineSecondName, categoryNameFirst, categoryNameSecond},
		OutType:       1,
	})
	if err != nil {
		msg = "根据店铺分类名称获取店铺分类信息失败，错误为" + err.Error()
		return
	}
	categoryIdOfflineFirst, ok := categoryInfoMap[categoryIdOfflineFirstName]
	if !ok {
		msg = "未找到一级后台分类名称对应的分类id"
		return
	}
	categoryIdOfflineSecond, ok := categoryInfoMap[categoryIdOfflineSecondName]
	if !ok {
		msg = "未找到二级后台分类名称对应的分类id"
		return
	}
	if categoryIdOfflineSecond.ParentId != categoryIdOfflineFirst.Id {
		msg = "二级后台分类名称的父级分类错误"
		return
	}

	categoryIdFirst, ok := categoryInfoMap[categoryNameFirst]
	if categoryNameFirst == "未分类" {
		if !ok {
			categoryIdFirst = product_po2.ProCategory{
				ChainId:     e.ChainId,
				Name:        categoryNameFirst,
				ParentId:    0,
				Sort:        0,
				Type:        product_po2.CategoryTypeOnline,
				CreatedBy:   cast.ToInt64(e.JwtInfo.UserId),
				CreatedName: e.JwtInfo.UserName,
			}
			if _, err := e.Engine.Insert(&categoryIdFirst); err != nil {
				msg = "插入一级前台分类-未分类 失败"
				return
			}
		}

	} else {
		if !ok {
			msg = "未找到一级前台分类名称对应的分类id"
			return
		}
	}

	categoryIdSecond, ok := categoryInfoMap[categoryNameSecond]
	if categoryNameSecond == "未分类商品" {
		if !ok {
			categoryIdSecond = product_po2.ProCategory{
				ChainId:     e.ChainId,
				Name:        categoryNameSecond,
				ParentId:    categoryIdFirst.Id,
				Sort:        0,
				Type:        product_po2.CategoryTypeOnline,
				CreatedBy:   cast.ToInt64(e.JwtInfo.UserId),
				CreatedName: e.JwtInfo.UserName,
			}
			if _, err := e.Engine.Insert(&categoryIdSecond); err != nil {
				msg = "插入二级前台分类-未分类商品 失败"
				return
			}
		}
	} else if !ok {
		msg = "未找到二级前台分类名称对应的分类id"
		return
	}

	if categoryIdSecond.Id == 0 {
		msg = "二级前端分类不存在"
		return
	}
	if categoryIdSecond.ParentId != categoryIdFirst.Id && categoryNameSecond != "未分类商品" {
		msg = "二级前端分类名称的父级分类错误"
		return
	}

	if (cast.ToInt(categoryThirdIdMt) > 0 && len(categoryThirdNameMt) == 0) || (cast.ToInt(categoryThirdIdMt) == 0 && len(categoryThirdNameMt) > 0) {
		msg = "美团外卖类目id或类目名称要么都填，要么都不填"
		return
	}

	// 如果开启了饿了么渠道， 则饿了么类目id必填
	if (cast.ToInt(categoryThirdIdElm) > 0 && len(categoryThirdNameElm) == 0) || (cast.ToInt(categoryThirdIdElm) == 0 && len(categoryThirdNameElm) > 0) {
		msg = "饿了么类目id或类目名称要么都填，要么都不填"
		return
	}
	newSell := product_po2.NewSellStop
	if (cast.ToInt(categoryThirdIdMt) > 0 && cast.ToInt(categoryThirdIdElm) == 0) || (cast.ToInt(categoryThirdIdMt) == 0 && cast.ToInt(categoryThirdIdElm) > 0) {
		msg = "美团外卖类目和饿了么类目要么都填，要么都不填"
		return
	} else {
		newSell = product_po2.NewSellOpen
	}
	if IsMoreSku != "是" {
		chainProduct.DataSource = 1
	} else {
		chainProduct.DataSource = 2
	}
	chainProduct.Product = product_po2.ProProduct{
		ChainId:           cast.ToString(e.ChainId),
		CategoryName:      categoryIdOfflineSecondName,
		CategoryNav:       categoryIdOfflineFirstName + ">" + categoryIdOfflineSecondName,
		Name:              productName,
		CategoryIdOffline: categoryIdOfflineSecond.Id,
		CategoryId:        categoryIdSecond.Id, //  根据名称获取分类id
		Pic:               pic,
		ContentPc:         contentPc,
		NewSell:           newSell,
		NewSellStr:        fmt.Sprintf("%d,%d", cast.ToInt(categoryThirdIdMt), cast.ToInt(categoryThirdIdElm)),
		ProductType:       product_po2.ProductTypeGoods,
		CreatedBy:         "", //todo
		CreatedName:       "", //todo
	}

	chainProduct.Sku = append(chainProduct.Sku, product_po2.ProSku{
		BarCode:       barCode,
		ProductSpecs:  productSpecs,
		StoreUnitKey:  storeUnitKey,
		StoreUnit:     storeUnit,
		BasicPrice:    utils.Yuan2Fen(cast.ToFloat64(BasicPrice)),
		StorePrice:    utils.Yuan2Fen(cast.ToFloat64(StorePrice)),
		XcxPrice:      utils.Yuan2Fen(cast.ToFloat64(XcxPrice)),
		MtPrice:       utils.Yuan2Fen(cast.ToFloat64(MtPrice)),
		ElmPrice:      utils.Yuan2Fen(cast.ToFloat64(ElmPrice)),
		WeightForUnit: cast.ToFloat64(cast.ToInt(cast.ToFloat64(weightForUnit)*100)) / 100,
		SkuCode:       skuCode,
		TaxRate:       cast.ToFloat64(RateRax),
	})

	if cast.ToInt(categoryThirdIdMt) > 0 {
		chainProduct.ProductChannel = append(chainProduct.ProductChannel, product_po2.ProProductChannel{
			ChannelId:         common.ChannelIdMT,
			CategoryThirdId:   cast.ToInt(categoryThirdIdMt),
			CategoryThirdName: categoryThirdNameMt,
		})
	}
	if cast.ToInt(categoryThirdIdElm) > 0 {
		chainProduct.ProductChannel = append(chainProduct.ProductChannel, product_po2.ProProductChannel{
			ChannelId:         common.ChannelIdELM,
			CategoryThirdId:   cast.ToInt(categoryThirdIdElm),
			CategoryThirdName: categoryThirdNameElm,
		})
	}

	// 组装数据
	if err := client.AddChainProduct(chainProduct); err != nil {
		msg = "添加连锁商品失败" + err.Error()
		return
	}

	return
}

// 导入商品
func (e SaasChainProductExport) BatchImportProduct(requery product_vo.BatchImportProductAndCategoryReq, req *http.Request) error {
	e.Begin()
	defer e.Close()
	//resp, err := http.Get(requery.FileUrl)
	//if err != nil {
	//	return err
	//}
	//defer resp.Body.Close()
	//f, err := excelize.OpenReader(resp.Body)
	//
	////f, err := excelize.OpenFile("C:\\Users\\<USER>\\Desktop\\连锁实物商品总部导入模板.xlsx")
	//if err != nil {
	//	return err
	//}

	file, _, err := req.FormFile("file")
	if err != nil {
		return err
	}
	defer file.Close()

	// 使用excelize库打开Excel文件
	f, err := excelize.OpenReader(file)
	if err != nil {
		return err
	}

	rows, err := f.GetRows("实物商品")
	if err != nil {
		return err
	}

	rowsMt, err := f.GetRows("美团外卖类目")
	if err != nil {
		return err
	}

	rowsElm, err := f.GetRows("饿了么类目")
	if err != nil {
		return err
	}

	errStr := ""
	for k, row := range rows {
		if k == 0 || k == 1 {
			continue
		}

		//防止溢出
		row = append(row, []string{"", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""}...)

		if row[0] == "" && row[1] == "" && row[2] == "" && row[3] == "" {
			break
		}

		//先通过商品号判断，商品是否已存在
		if isExist, err := e.Engine.Table("eshop.pro_product").Where("bar_code = ? and chain_id = ? and is_del = 0", row[0], requery.ChainId).Exist(); err != nil {
			return err
		} else if isExist {
			//存在则不导入
			continue
		}

		//通过平台号得到商品id
		channelSkuThirdMap, err := e.Engine.QueryString(fmt.Sprintf("select * from dc_product.gj_sku_third where third_sku_id = '%s' and erp_id = 2", row[12]))
		if err != nil {
			return err
		}

		if len(channelSkuThirdMap) == 0 {
			errStr += fmt.Sprintf("第%d行，平台货号查询不到商品|", k+1)
			continue
		}

		if len(channelSkuThirdMap) > 1 {
			errStr += fmt.Sprintf("第%d行，平台货号查询商品存着多条|", k+1)
			continue
		}

		//得到商品信息
		productmap, err := e.Engine.QueryString(fmt.Sprintf("select pic,content_pc from dc_product.product where id = %s", channelSkuThirdMap[0]["product_id"]))
		if err != nil {
			return err
		}
		if len(productmap) == 0 {
			errStr += fmt.Sprintf("第%d行，商品不存在|", k+1)
			continue
		}

		row[4] = productmap[0]["pic"]
		row[5] = productmap[0]["content_pc"]

		newSellSli := strings.Split(row[15], ";")
		row15Str := ""

		for _, v := range newSellSli {
			channelIds := strings.Split(v, "-")

			//如果选择了类型，则不需要从阿闻获取类目
			if (channelIds[0] == "2" && (row[18] == "" || row[19] == "")) || (channelIds[0] == "3" && (row[20] == "" || row[21] == "")) {
				//获取类目
				productChannelmap, err := e.Engine.QueryString(fmt.Sprintf("select * from dc_product.gj_product_channel where product_id = %s and channel_id = %s", channelSkuThirdMap[0]["product_id"], channelIds[0]))
				if err != nil {
					return err
				}

				//如果找不到对应的类目，则设置为空
				if len(productChannelmap) == 0 {
					if channelIds[0] == "2" {
						row[19] = ""
						row[18] = ""
					} else if channelIds[0] == "3" {
						row[21] = ""
						row[20] = ""
					}
					continue
				}

				if len(productChannelmap) > 0 {
					if channelIds[0] == "2" {
						//美团
						row[19] = productChannelmap[0]["category_id"]
						row[18] = row[19]
						//得到名字
						for _, rowMt := range rowsMt {
							if rowMt[3] == row[19] {
								row[18] = rowMt[2]
								break
							}
						}
					} else if channelIds[0] == "3" {
						//饿了么
						row[21] = productChannelmap[0]["category_id"]
						row[20] = row[21]
						//得到名字
						for _, rowElm := range rowsElm {
							if rowElm[3] == row[21] {
								row[20] = rowElm[2]
								break
							}
						}
					}

				}
			}

			row15Str += v + ";"
		}

		row[15] = strings.Trim(row15Str, ";")

		msg := e.OperationFunc(row, 6)
		if msg != "" {
			errStr += fmt.Sprintf("第%d行，%s|", k+1, msg)
			continue
		}
	}

	if errStr != "" {
		strings.Trim(errStr, "|")
		return errors.New(errStr)
	}

	return nil
}
