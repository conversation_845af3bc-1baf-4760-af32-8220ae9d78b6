package export

import (
	product_po "eShop/domain/product-po"
	"eShop/infra/jwtauth"
	"eShop/infra/utils"
	"eShop/services/common"
	proproductstoreinfo "eShop/services/product-service/enum/pro-product-store-info"
	"eShop/services/product-service/services"
	product_vo2 "eShop/view-model/product-vo"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cast"

	"github.com/xuri/excelize/v2"
)

type SaasStoreProductExport struct {
	F            *excelize.File
	SheetName    string
	ExportParams *product_vo2.FindStoreProductListReq
	writer       *excelize.StreamWriter
	common.BaseService
	//店铺id
	StoreId string
	//类型
	Type    int
	JwtInfo *jwtauth.XCShopPayload
}

// DataExport 分销员导出
func (e SaasStoreProductExport) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(product_vo2.FindStoreProductListReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 10000
	//导出类型：查询条件
	e.ExportParams.Exprot = 1
	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()
	client := services.StoreProductService{}
	client.JwtInfo = e.JwtInfo
	k := 0
	for {
		ret, _, err := client.FindStoreProductList(*e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}

		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {

			ISMore := "否"
			//美团类目ID
			MTCategoryThirdId := ret[i].MtCategoryThirdId
			//美团类目名称
			MTCategoryThirdName := ret[i].MtCategoryThirdName
			//饿了么类目ID
			ELMCategoryThirdId := ret[i].ElmCategoryThirdId
			//饿了么类目名称
			ELMCategoryThirdName := ret[i].ElmCategoryThirdName

			if len(ret[i].Sku) > 1 {
				ISMore = "是"
			}
			//后台1级分类
			HTFirstCategory := ""
			//后台2级分类
			HTSecondCategory := ""
			//前台1级分类
			QTFirstCategory := ""
			//前台2级分类
			QTSecondCategory := ""

			QtCategory := strings.Split(ret[i].CategoryNavOnline, ">")
			HtCategory := strings.Split(ret[i].CategoryNav, ">")
			if len(HtCategory) > 1 {
				//后台1级分类
				HTFirstCategory = HtCategory[0]
				//后台2级分类
				HTSecondCategory = HtCategory[1]
			}
			if len(QtCategory) > 1 {
				//前台1级分类
				QTFirstCategory = QtCategory[0]
				//前台2级分类
				QTSecondCategory = QtCategory[1]
			}

			for _, base := range ret[i].Sku {
				k++
				axis := fmt.Sprintf("A%d", k+1)
				x := base.BaseSku

				_ = e.writer.SetRow(axis, []interface{}{
					ret[i].ProductId, // 商品skuId
					x.Id,             //SKUID
					x.BarCode,        // 商品条形码
					ret[i].Name,      // 商品名称
					HTFirstCategory,
					HTSecondCategory,
					QTFirstCategory,  // 商品一级分类
					QTSecondCategory, // 商品二级分类
					ISMore,           // 是否多规格
					x.ProductSpecs,   // 规格
					"",               //库存  等后面实现
					x.StoreUnit,
					x.StoreUnitKey,
					utils.Fen2Yuan(x.BasicPrice),
					utils.Fen2Yuan(x.StorePrice),
					utils.Fen2Yuan(x.XcxPrice),
					utils.Fen2Yuan(x.MtPrice),
					utils.Fen2Yuan(x.ElmPrice),
					"0",
					x.WeightForUnit,
					x.SkuCode,
					x.TaxRate,
					//美团类目名称
					MTCategoryThirdName,
					//美团类目ID
					MTCategoryThirdId,
					//饿了么类目名称
					ELMCategoryThirdName,
					//饿了么类目ID
					ELMCategoryThirdId,
					"",
					"",
				})

			}

		}
		if len(ret) < int(e.ExportParams.PageSize) {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return
}

// SetSheetName 门店商品导出列表头
func (e SaasStoreProductExport) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"商品Id", "skuID", "商品条码", "商品名称", "一级后台分类", "二级后台分类", "一级前台分类", "二级前台分类", "是否多规格", "规格", "库存数", "库存单位", "库存单位key", "成本价", "门店价", "小程序价", "美团价", "饿了么价", "京东价", "重量（kg）", "平台货号", "税率（%）", "美团外卖类目", "美团外卖类目Id", "饿了么类目", "饿了么类目Id",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e SaasStoreProductExport) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("店铺实物商品导出-%s%d.xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e SaasStoreProductExport) OperationFunc(row []string, orgId int) string {
	e.Begin()
	defer e.Close()

	//校验不能为空
	if e.Type == proproductstoreinfo.ProductTypeDel {
		if len(row) < 2 {
			return "模板格式错误，请上传正确模板"
		}
	} else {
		if len(row) < 3 {
			return "模板格式错误，请上传正确模板"
		}
	}
	//必填
	if len(row[0]) == 0 {
		return "商品Id不能为空"
	}
	var proProductStoreInfo product_po.ProProductStoreInfo
	if exist, err := e.Engine.Table("eshop.pro_product_store_info").
		Where("store_id =?", e.StoreId).
		Where("product_id=?", row[0]).Get(&proProductStoreInfo); err != nil {
		return "获取商品信息失败" + err.Error()
	} else if !exist {
		return "查询不到商品信息"
	}
	req := product_vo2.BatchStoreProductReq{
		TenantId:   e.StoreId,
		ProductIds: strings.Trim(row[0], ""),
		Type:       e.Type,
	}
	var channelIds []string

	switch e.Type {
	case proproductstoreinfo.ProductTypeLaunch:
		if len(row[2]) > 0 { //小程序
			channelIds = append(channelIds, "1")
		}
		if len(row[3]) > 0 { //美团
			channelIds = append(channelIds, "2")
		}
		if len(row[4]) > 0 { //饿了么
			channelIds = append(channelIds, "3")
		}
		req.ChannelIds = strings.Join(channelIds, ",")
	case proproductstoreinfo.ProductTypeUp, proproductstoreinfo.ProductTypeDown, proproductstoreinfo.ProductTypeStock:
		if len(row[4]) > 0 { //小程序
			channelIds = append(channelIds, "1")
		}
		if len(row[5]) > 0 { //美团
			channelIds = append(channelIds, "2")
		}
		if len(row[6]) > 0 { //饿了么
			channelIds = append(channelIds, "3")
		}
		req.ChannelIds = strings.Join(channelIds, ",")
	case proproductstoreinfo.ProductTypePrice:
		if len(row[3]) > 0 { //线下门店价格
			channelPrice := fmt.Sprintf("100-%d", utils.Yuan2Fen(cast.ToFloat64(row[3])))
			channelIds = append(channelIds, channelPrice)
		}
		if len(row[4]) > 0 { //小程序价格
			channelPrice := fmt.Sprintf("1-%d", utils.Yuan2Fen(cast.ToFloat64(row[4])))
			channelIds = append(channelIds, channelPrice)
		}
		if len(row[5]) > 0 { //美团价格
			channelPrice := fmt.Sprintf("2-%d", utils.Yuan2Fen(cast.ToFloat64(row[5])))
			channelIds = append(channelIds, channelPrice)
		}
		if len(row[6]) > 0 { //饿了么价格
			channelPrice := fmt.Sprintf("3-%d", utils.Yuan2Fen(cast.ToFloat64(row[6])))
			channelIds = append(channelIds, channelPrice)
		}
		req.PriceType = 2
		req.ChannlePriceStr = strings.Join(channelIds, "|")
	}

	server := services.StoreProductService{}
	_, err := server.BatchStoreProduct(req)
	if err != nil {
		return err.Error()
	}

	return ""
}
