package export

import (
	product_po "eShop/domain/product-po"
	jwt "eShop/infra/jwtauth"
	glog "eShop/infra/log"
	"eShop/services/common"
	"eShop/services/product-service/services"
	vo "eShop/view-model/product-vo"
	"encoding/json"
	"strconv"
	"strings"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

type StoreServiceImport struct {
	common.BaseService
	F             *excelize.File
	SheetName     string
	writer        *excelize.StreamWriter
	RequestHeader string
}

// 添加新的接口方法
func (e StoreServiceImport) DataExport(fileName string) (int, int, error) {
	// TODO: 实现导出逻辑
	return 0, 0, nil
}

func (e StoreServiceImport) SetSheetName(args ...interface{}) {
	// TODO: 实现设置表头逻辑
}

func (e StoreServiceImport) GenerateDownUrl() (string, error) {
	// TODO: 实现生成下载链接逻辑
	return "", nil
}

func (e StoreServiceImport) OperationFunc(row []string, orgId int) string {
	e.Begin()
	defer e.Close()
	mes := ""
	//异常捕获
	defer func() {
		if err := recover(); err != nil {
			glog.Error("导入服务失败", err)
			mes = "导入服务失败"
		}
	}()

	// 校验不能为空
	if len(row) < 7 {
		mes = "模板格式错误，请上传正确模板"
		return mes
	}

	if len(row[1]) == 0 { // 商品名称
		mes = "服务名称不能为空"
		return mes
	}
	if len(row[2]) == 0 { // 商品一级分类
		mes = "商品一级分类不能为空"
		return mes
	}
	if len(row[3]) == 0 { // 商品二级分类
		mes = "商品二级分类不能为空"
		return mes
	}
	if len(row[4]) == 0 { // 商品二级分类
		mes = "零售价不能为空"
		return mes
	}
	retailPrice, err := strconv.ParseFloat(row[4], 64)
	// 校验零售价必须为浮点型
	if err != nil {
		mes = "零售价必须为数字格式"
		return mes
	} else if retailPrice <= 0 {
		mes = "零售价必须大于0"
		return mes
	}
	if len(row[4]) == 0 { // 零售价
		mes = "零售价不能为空"
		return mes
	}
	if len(row[5]) == 0 { // 服务时长
		mes = "服务时长不能为空"
		return mes
	}
	serviceDuration, err := strconv.ParseInt(row[5], 10, 64)
	if err != nil {
		mes = "服务时长必须为整数格式"
		return mes
	} else if serviceDuration <= 0 {
		mes = "服务时长必须大于0"
		return mes
	}

	var marketPrice float64
	if len(row[6]) > 0 { // 市场价
		if marketPrice1, err := strconv.ParseFloat(row[6], 64); err != nil {
			mes = "市场价必须为数字格式"
			return mes
		} else if marketPrice1 <= retailPrice {
			mes = "市场价必须大于零售价"
			return mes
		} else {
			marketPrice = marketPrice1
		}
	}

	categoryIdOfflineFirstName := row[2]
	categoryIdOfflineSecondName := row[3]

	t := &jwt.XCShopPayload{}
	err = json.Unmarshal([]byte(e.RequestHeader), t)
	if err != nil {
		mes = "获取请求头格式错误"
		return mes
	}

	//根据分类名称查询分类ID
	//map[string]interface{}{"chainId": t.ChainId, "type": po.CategoryTypeService, "outType": 1, "categoryNames": []string{categoryIdOfflineFirstName, categoryIdOfflineSecondName}
	_, categoryInfoMap, _, err := new(product_po.ProCategory).GetCategoryMapInfo(e.Engine, product_po.CategoryMapInfoReq{
		ChainId:       cast.ToInt64(t.ChainId),
		Type:          product_po.CategoryTypeService,
		OutType:       1,
		CategoryNames: []string{categoryIdOfflineFirstName, categoryIdOfflineSecondName},
	})
	if err != nil {
		mes = "根据后端分类名称获取后端分类信息失败，错误为" + err.Error()
		return mes
	}
	categoryIdOfflineFirst, ok := categoryInfoMap[categoryIdOfflineFirstName]
	if !ok {
		mes = "未找到一级后台分类名称对应的分类id"
		return mes
	}
	categoryIdOfflineSecond, ok := categoryInfoMap[categoryIdOfflineSecondName]
	if !ok {
		mes = "未找到二级后台分类名称对应的分类id"
		return mes
	}
	if categoryIdOfflineSecond.ParentId != categoryIdOfflineFirst.Id {
		mes = "二级后台分类名称的父级分类错误"
		return mes
	}

	code := row[0]
	// 必填字段校验
	if len(code) == 0 { // 商品编码
		//如果条码是空的就生成一个
		var s services.ProductService
		resCode, err := s.GenerateBarCode(cast.ToInt64(t.ChainId))
		if err != nil {
			mes = err.Error()
			return mes
		}
		code = resCode
	}

	// 构建请求参数
	req := vo.SaveServiceReq{
		StoreId:         t.TenantId,
		BarCode:         code,
		Name:            strings.TrimSpace(row[1]),
		NamePath:        strings.TrimSpace(row[2]) + ">" + strings.TrimSpace(row[3]), // 拼接分类路径
		CategoryId:      categoryIdOfflineSecond.Id,                                  // 需要根据分类名称查询对应的分类ID
		Price:           retailPrice,
		ServiceDuration: cast.ToInt(strings.TrimSpace(row[5])),
		MarketPrice:     cast.ToFloat64(marketPrice), // 市场价，可选
		ProductType:     4,                           // 服务类型
	}

	// 调用服务保存
	server := services.StoreProductService{}
	if err := server.SaveService(req); err != nil {
		mes = "保存服务失败: " + err.Error()
		return mes
	}

	return mes
}

type StoreAliveImport struct {
	RequestHeader string
	common.BaseService
	F         *excelize.File
	SheetName string
	writer    *excelize.StreamWriter
}

// 添加新的接口方法
func (e StoreAliveImport) DataExport(fileName string) (int, int, error) {
	// TODO: 实现导出逻辑
	return 0, 0, nil
}

func (e StoreAliveImport) SetSheetName(args ...interface{}) {
	// TODO: 实现设置表头逻辑
}

func (e StoreAliveImport) GenerateDownUrl() (string, error) {
	// TODO: 实现生成下载链接逻辑
	return "", nil
}

// 活体导入
func (e StoreAliveImport) OperationFunc(row []string, orgId int) string {
	mes := ""
	//异常捕获
	defer func() {
		if err := recover(); err != nil {
			glog.Error("导入活体失败", err)
			mes = "导入活体失败"
			return
		}
	}()
	e.Begin()
	defer e.Close()

	// 校验不能为空
	if len(row) < 8 {
		mes = "模板格式错误，请上传正确模板"
		return mes
	}

	if len(row[1]) == 0 { // 商品名称
		mes = "活体名称不能为空"
		return mes
	}
	if len(row[2]) == 0 { // 商品一级分类
		mes = "商品一级分类不能为空"
		return mes
	}
	if len(row[3]) == 0 { // 商品二级分类
		mes = "商品二级分类不能为空"
		return mes
	}
	if len(row[4]) == 0 { //品种
		mes = "品种不能为空"
		return mes
	}
	if len(row[5]) == 0 { // 零售价
		mes = "零售价不能为空"
		return mes
	}
	retailPrice, err := strconv.ParseFloat(row[5], 64)
	// 校验零售价必须为浮点型
	if err != nil {
		mes = "零售价必须为数字格式"
		return mes
	} else if retailPrice <= 0 {
		mes = "零售价必须大于0"
		return mes
	}
	if len(row[5]) == 0 { // 零售价
		mes = "零售价不能为空"
		return mes
	}
	var marketPrice float64
	if len(row[6]) > 0 { // 市场价
		if marketPrice1, err := strconv.ParseFloat(row[6], 64); err != nil {
			mes = "市场价必须为数字格式"
			return mes
		} else if marketPrice1 <= retailPrice {
			mes = "市场价必须大于零售价"
			return mes
		} else {
			marketPrice = marketPrice1
		}
	}

	categoryIdOfflineFirstName := row[2]
	categoryIdOfflineSecondName := row[3]

	t := &jwt.XCShopPayload{}
	err = json.Unmarshal([]byte(e.RequestHeader), t)
	if err != nil {
		mes = "获取请求头格式错误"
		return mes
	}

	//根据分类名称查询分类ID
	_, categoryInfoMap, _, err := new(product_po.ProCategory).GetCategoryMapInfo(e.Engine, product_po.CategoryMapInfoReq{
		ChainId:       cast.ToInt64(t.ChainId),
		Type:          product_po.CategoryTypeLive,
		OutType:       1,
		CategoryNames: []string{categoryIdOfflineFirstName, categoryIdOfflineSecondName},
	})
	if err != nil {
		mes = "根据后端分类名称获取后端分类信息失败，错误为" + err.Error()
		return mes
	}
	categoryIdOfflineFirst, ok := categoryInfoMap[categoryIdOfflineFirstName]
	if !ok {
		mes = "未找到一级后台分类名称对应的分类id"
		return mes
	}
	categoryIdOfflineSecond, ok := categoryInfoMap[categoryIdOfflineSecondName]
	if !ok {
		mes = "未找到二级后台分类名称对应的分类id"
		return mes
	}
	if categoryIdOfflineSecond.ParentId != categoryIdOfflineFirst.Id {
		mes = "二级后台分类名称的父级分类错误"
		return mes
	}

	code := row[0]
	// 必填字段校验
	if len(code) == 0 { // 商品编码
		//如果条码是空的就生成一个
		var s services.ProductService
		resCode, err := s.GenerateBarCode(cast.ToInt64(t.ChainId))
		if err != nil {
			mes = err.Error()
			return mes
		}
		code = resCode
	}
	//获取品种
	var DictValue product_po.CDictValue
	DictValue.DictName = row[4]
	data, err := DictValue.GetCDictValueByName(e.Engine)
	if err != nil {
		mes = "获取品种出错: " + err.Error()
		return mes
	}

	// 构建请求参数
	req := vo.SaveServiceReq{
		StoreId:        t.TenantId,
		BarCode:        code,
		Name:           strings.TrimSpace(row[1]),
		NamePath:       strings.TrimSpace(row[2]) + ">" + strings.TrimSpace(row[3]), // 拼接分类路径
		CategoryId:     categoryIdOfflineSecond.Id,                                  // 需要根据分类名称查询对应的分类ID
		Price:          retailPrice,
		MarketPrice:    marketPrice, // 市场价，可选
		ProductType:    5,           // 服务类型
		BirthDate:      strings.TrimSpace(row[7]),
		PetVariety:     cast.ToString(data.Id),
		PetVarietyName: data.DictName,
	}

	// 调用服务保存
	server := services.StoreProductService{}
	if err := server.SaveService(req); err != nil {
		mes = "保存服务失败: " + err.Error()
		return mes
	}

	return mes
}
