package services

import (
	product_po2 "eShop/domain/product-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	vo "eShop/view-model/product-vo"
	"errors"
	"fmt"
	"strings"

	"github.com/spf13/cast"
	"xorm.io/xorm"
)

// 管理后台 - 添加单个连锁商品-入参校验
func ChainProductValidate(session *xorm.Session, in *vo.ChainProduct) (err error) {

	in.Product.Name = strings.Trim(in.Product.Name, " ")
	if in.Product.Name == "" {
		err = errors.New("商品名称不能为空")
		return
	}

	if len([]rune(in.Product.Name)) > 100 {
		err = errors.New("商品名称只能输入1-100个字符")
		return
	}

	// 后端分类不能为空（即线下门店端的分类id）
	if in.Product.CategoryIdOffline == 0 {
		err = errors.New("商品后端分类不能为空")
		return
	}
	// 连锁商品批量导入 图片是选填的。
	if in.DataSource == 1 {
		in.Product.Pic = strings.Trim(in.Product.Pic, " ")
		if in.Product.Pic == "" {
			err = errors.New("商品图片不能为空")
			return
		}
	}

	// 前端分类不能为空
	if in.Product.CategoryId == 0 {
		err = errors.New("商品前端分类不能为空")
		return
	}

	// 如果新零售渠道是开启的。
	if in.Product.NewSell == product_po2.NewSellOpen {
		in.Product.NewSellStr = fmt.Sprintf("%d,%d", common.ChannelIdMT, common.ChannelIdELM)

	} else {
		in.Product.NewSellStr = ""
	}

	// 新零售渠道只包含美团和饿了么， 小程序不属于新零售渠道了
	// 若开启了新零售销售
	if in.Product.NewSellStr != "" && in.Product.NewSell == product_po2.NewSellOpen {
		NewSellSli := strings.Split(in.Product.NewSellStr, ",")
		if len(NewSellSli) == 0 {
			err = errors.New("新渠道信息 参数有误")
			return
		}
		ProProductChannel := make(map[int]int) // 新零售渠道对应的商品类目
		for _, v := range in.ProductChannel {
			ProProductChannel[v.ChannelId] = v.CategoryThirdId
		}

		for _, v := range NewSellSli {
			v := cast.ToInt(v)

			if _, ok := common.ChannelIdMap[v]; !ok {
				err = errors.New("渠道信息有误")
				return
			}
			if ProProductChannel[v] <= 0 && v == common.ChannelIdMT {
				err = errors.New("美团商品类目不能为空")
				return
			}

			if ProProductChannel[v] <= 0 && v == common.ChannelIdELM {
				err = errors.New("饿了么商品类目不能为空")
				return
			}
		}

	}

	// 去除CategoryThirdId=0的无效数据
	ProductChannel := make([]product_po2.ProProductChannel, 0, len(in.ProductChannel))
	for _, v := range in.ProductChannel {
		if v.CategoryThirdId == 0 {
			continue
		}
		ProductChannel = append(ProductChannel, v)
	}
	in.ProductChannel = ProductChannel

	// 去除无效的类目属性数据
	ProductChannelAttr := make([]product_po2.ProProductChannelAttr, 0, len(in.ProductChannelAttr))
	for _, v := range in.ProductChannelAttr {
		if v.AttrId == 0 && len(v.AttrName) == 0 {
			continue
		}
		if len(v.AttrValueId) == 0 && len(v.AttrValue) == 0 {
			continue
		}
		ProductChannelAttr = append(ProductChannelAttr, v)
	}
	in.ProductChannelAttr = ProductChannelAttr

	// 判断商品sku信息
	if len(in.Sku) == 0 {
		err = errors.New("请填入商品规格信息")
		return
	}
	// 存储本次传进来的sku的barcode是否有重复， 用于后续判断
	barcodeMap := make(map[string]int)
	for k, v := range in.Sku {
		if strings.Trim(v.ProductSpecs, " ") == "" {
			err = errors.New("商品规格不能为空")
			return
		}
		if len([]rune(v.ProductSpecs)) > 15 {
			err = errors.New("规格字段不能超过15字")
			return
		}
		if strings.Trim(v.StoreUnit, " ") == "" {
			err = errors.New("商品库存单位不能为空")
			return
		}
		if strings.Trim(v.StoreUnitKey, " ") == "" {
			err = errors.New("商品库存单位key不能为空")
			return
		}
		if v.StorePrice == 0 {
			err = errors.New("门店价不能为空")
			return
		}
		if v.BasicPrice == 0 {
			err = errors.New("成本价不能为空")
			return
		}
		if v.MtPrice <= 0 {
			in.Sku[k].MtPrice = v.StorePrice
		}
		if v.ElmPrice <= 0 {
			in.Sku[k].ElmPrice = v.StorePrice
		}
		if v.XcxPrice <= 0 {
			in.Sku[k].XcxPrice = v.StorePrice
		}
		if v.WeightForUnit == 0 {
			err = errors.New("商品重量不能为空")
			return
		}

		if strings.Trim(v.BarCode, " ") == "" {
			err = errors.New("商品条码不能为空")
			return
		}
		if _, ok := barcodeMap[v.BarCode]; ok {
			err = fmt.Errorf("%s条码存在重复，请修改后再操作。", v.BarCode)
			return
		} else {
			barcodeMap[v.BarCode] = 1
		}
	}

	for _, v := range in.Sku {
		// 一个连锁下， sku的条码不能相同
		where := map[string]interface{}{"barCode": v.BarCode, "chainId": in.Product.ChainId}
		if in.Product.Id > 0 {
			where["notEqualSkuId"] = v.Id
		}
		var exist bool
		exist, err = product_po2.SkuBarcodeIsExist(session, where)
		if err != nil {
			err = errors.New("查询条码是否存在失败，err=" + err.Error())
			return
		}
		if exist {
			err = fmt.Errorf("%s条码已经存在，不能重复添加", v.BarCode)
			return
		}
	}
	//一个连锁下商品名称不能相同

	if in.DataSource != 2 {

		where := map[string]interface{}{"productName": in.Product.Name, "chainId": in.Product.ChainId}
		if in.Product.Id > 0 {
			where["notEqualProductId"] = in.Product.Id
		}

		if productList, _, _, err := product_po2.GetProductMapInfo(session, where); err != nil {
			return errors.New("查询商品数据库失败" + err.Error())
		} else {
			if len(productList) > 0 {
				return errors.New("商品名称已存在， 不能重复添加")
			}
		}
	}

	return
}

// 根据 productIdSli 获取 连锁商品信息
func GetChainProductData(session *xorm.Session, productIdSli []int) (out map[int]vo.ChainProduct, err error) {
	logPrefix := "根据商品ID获取连锁商品信息===="
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(productIdSli))

	// 查询连锁商品信息
	_, ProductMapInfo, _, err := product_po2.GetProductMapInfo(session, map[string]interface{}{"productIdSli": productIdSli, "outType": 1})
	if err != nil {
		log.Error(logPrefix, "获取连锁商品信息失败，err=", err.Error())
		return
	}

	//查询连锁商品sku信息
	skuQuery := product_po2.SkuQuery{
		ProductIds: productIdSli,
	}
	SkuMapInfo, _, err := new(product_po2.ProSku).GetSkuMapInfo(session, skuQuery)
	if err != nil {
		log.Error(logPrefix, "获取连锁商品sku信息失败，err=", err.Error())
		return
	}

	// 查询商品的渠道信息
	ProductChannelInfo, _, err := product_po2.GetProductChannelInfo(session, map[string]interface{}{"productIds": productIdSli})
	if err != nil {
		log.Error(logPrefix, "获取连锁商品渠道信息失败，err=", err.Error())
		return
	}

	// 查询商品渠道属性信息
	var proProductChannelAttr product_po2.ProProductChannelAttr
	ProductChannelAttrInfo, _, err := proProductChannelAttr.GetProductChannelAttrInfo(session, product_po2.ProductChannelAttrReq{
		ProductIds: productIdSli,
	})
	if err != nil {
		log.Error(logPrefix, "获取连锁商品渠道属性信息失败，err=", err.Error())
		return
	}

	//组装数据
	out = make(map[int]vo.ChainProduct)

	for productId, v := range ProductMapInfo {

		if productId <= 0 {
			continue
		}
		ChainProduct := out[productId]
		ChainProduct.Product = v
		ChainProduct.Sku = SkuMapInfo[productId]
		ChainProduct.ProductChannel = ProductChannelInfo[productId]
		ChainProduct.ProductChannelAttr = ProductChannelAttrInfo[productId]
		out[productId] = ChainProduct
	}

	return

}

// 用于编辑商品时， 求出需要新增的sku， 编辑的sku， 删除的sku
// a是编辑时，传入的sku列表
// b是从数据库查找出来的sku

// 集合new， 集合old,
// 没有skuid的就是新增的
// 在集合new也在集合old,就是需要更新的
// 在集合old不在集合new的， 是需要删除的。
func OrgSku(new, old []product_po2.ProSku) (add []product_po2.ProSku, editMap map[int]product_po2.ProSku, del []int) {
	add = make([]product_po2.ProSku, 0)
	editMap = make(map[int]product_po2.ProSku)
	del = make([]int, 0)
	m1 := make(map[int]product_po2.ProSku)

	for _, v := range new {
		if v.Id <= 0 {
			add = append(add, v)
			continue
		}
		m1[v.Id] = v
	}

	for _, v := range old {
		if vv, ok := m1[v.Id]; ok {
			editMap[v.Id] = vv
		} else {
			del = append(del, v.Id)
		}

	}

	return
}

func OrgProductChannel(new, old []product_po2.ProProductChannel) (add, edit []product_po2.ProProductChannel, del []int) {
	add, edit = make([]product_po2.ProProductChannel, 0), make([]product_po2.ProProductChannel, 0)
	del = make([]int, 0)
	m1 := make(map[int]product_po2.ProProductChannel)

	for _, v := range new {
		if v.Id <= 0 {
			add = append(add, v)
			continue
		}
		m1[v.Id] = v
	}

	for _, v := range old {
		if vv, ok := m1[v.Id]; ok {
			edit = append(edit, vv)
		} else {
			del = append(del, v.Id)
		}

	}

	return
}

func OrgProductChannelAttr(new, old []product_po2.ProProductChannelAttr) (add, edit []product_po2.ProProductChannelAttr, del []int) {
	add, edit = make([]product_po2.ProProductChannelAttr, 0), make([]product_po2.ProProductChannelAttr, 0)
	del = make([]int, 0)
	m1 := make(map[int]product_po2.ProProductChannelAttr)

	for _, v := range new {
		if v.Id <= 0 {
			add = append(add, v)
			continue
		} else {
			m1[v.Id] = v
		}

	}

	for _, v := range old {
		if vv, ok := m1[v.Id]; ok {
			edit = append(edit, vv)
		} else {
			del = append(del, v.Id)
		}

	}

	return
}
func DealChainProductData(session *xorm.Session, in vo.ChainProduct, chainProductData vo.ChainProduct) (delSku []int, err error) {
	logPrefix := "编辑连锁商品、sku、商品渠道、商品渠道属性数据===="
	log.Infof("%s 前端传参：%s, 从数据库查出数据：%s ", logPrefix, utils.InterfaceToJSON(in), utils.InterfaceToJSON(chainProductData))

	// 第一步： 组织数据
	// 组织sku信息（查询要更新的sku信息， 查出新增的sku， 查出要删除的sku）
	addSku, editSku, delSku := OrgSku(in.Sku, chainProductData.Sku)
	if len(addSku) == 0 && len(editSku) == 0 {
		log.Error(logPrefix, "一个连锁商品至少有一个sku信息")
		err = errors.New("一个连锁商品至少有一个sku信息")
		return
	}

	// 组织渠道信息
	addProductChannel, editProductChannel, delProductChannel := OrgProductChannel(in.ProductChannel, chainProductData.ProductChannel)
	// 组织渠道
	addProductChannelAttr, editProductChannelAttr, delProductChannelAttr := OrgProductChannelAttr(in.ProductChannelAttr, chainProductData.ProductChannelAttr)

	// 第二步： 处理数据
	for _, v := range editSku {
		v := v
		if _, err = session.Table("eshop.pro_sku").AllCols().Where("id=?", v.Id).Update(&v); err != nil {
			log.Error(logPrefix, "更新sku信息失败，数据：", utils.InterfaceToJSON(v), ",err=", err.Error())
			err = errors.New("更新sku信息失败")
			return
		}

	}

	if len(addSku) > 0 {
		maxSkuId, e := product_po2.GetMaxSkuId(session, in.Product.Id)
		if e != nil {
			log.Error(logPrefix, "获取最大skuId失败，err=", err.Error())
			err = errors.New("获取最大skuId失败")
			return
		}

		// 给新增的sku设置id
		for k := range addSku {
			addSku[k].ProductId = in.Product.Id
			addSku[k].Id = maxSkuId + k + 1

		}
		if _, err = session.Table("eshop.pro_sku").Insert(&addSku); err != nil {
			log.Error(logPrefix, "插入新增的sku信息失败，err=", err.Error())
			err = errors.New("插入新增的sku信息失败")
			return
		}
	}

	if len(addProductChannel) > 0 {
		for k := range addProductChannel {
			addProductChannel[k].ProductId = in.Product.Id
		}
		if _, err = session.Table("eshop.pro_product_channel").Insert(&addProductChannel); err != nil {
			log.Error(logPrefix, "插入新增的信息失败，err=", err.Error())
			err = errors.New("插入新增的信息失败")
			return
		}
	}

	if len(addProductChannelAttr) > 0 {
		for k := range addProductChannelAttr {
			addProductChannelAttr[k].ProductId = in.Product.Id
		}
		if affects, e := session.Table("eshop.pro_product_channel_attr").Insert(&addProductChannelAttr); err != nil {
			log.Error(logPrefix, "插入新增的信息失败，err=", e.Error())
			err = errors.New("插入新增的信息失败")
			return
		} else {
			log.Info(logPrefix, "插入新增的渠道属性影响条数：", affects)
		}

	}

	updateData := product_po2.CopyProProductForUpdate(in.Product)

	if _, err = session.Table("eshop.pro_product").AllCols().Where("id=?", in.Product.Id).Update(&updateData); err != nil {
		log.Error(logPrefix, "更新商品数据失败，err=", err.Error())
		err = errors.New("更新商品数据失败")
		return
	}

	for _, v := range editProductChannel {
		v := v
		if _, err = session.Table("eshop.pro_product_channel").AllCols().Where("channel_id=?", v.ChannelId).Where("product_id=?", v.ProductId).Update(&v); err != nil {
			log.Error(logPrefix, "更新商品渠道信息失败，数据：", utils.InterfaceToJSON(v), ",err=", err.Error())
			err = errors.New("更新商品渠道信息失败")
			return
		}
	}

	for _, v := range editProductChannelAttr {
		v := v
		if _, err = session.Table("eshop.pro_product_channel_attr").AllCols().Where("id=?", v.Id).Update(&v); err != nil {
			log.Error(logPrefix, "更新商品渠道属性信息失败，数据：", utils.InterfaceToJSON(v), ",err=", err.Error())
			err = errors.New("更新商品渠道属性信息失败")
			return
		}
	}

	if len(delProductChannel) > 0 {
		if _, err = session.Table("eshop.pro_product_channel").In("id", delProductChannel).Delete(); err != nil {
			log.Error(logPrefix, "删除商品渠道信息失败，数据：,err=", err.Error())
			err = errors.New("删除商品渠道信息失败")
			return
		}
	}

	if len(delProductChannelAttr) > 0 {
		if _, err = session.Table("eshop.pro_product_channel_attr").In("id", delProductChannelAttr).Delete(); err != nil {
			log.Error(logPrefix, "删除商品渠道属性信息失败，数据：,err=", err.Error())
			err = errors.New("删除商品渠道属性信息失败")
			return
		}
	}

	return
}

// GetProductMapInfo 获取商品和SKU信息
func GetProductMapInfo(session *xorm.Session, productIdSli []int, logPrefix string) (productMap map[int]map[int]vo.ProductSkuInfo, err error) {
	var productData []vo.ProductSkuInfo
	productMap = make(map[int]map[int]vo.ProductSkuInfo) // 商品id和skuid为key

	if err = session.Table("eshop.pro_product").Alias("a").
		Select("a.id as product_id,a.chain_id,a.category_id,a.category_id_offline,b.id as sku_id,a.name as product_name,a.product_type,a.category_nav as product_category_path,b.bar_code,b.retail_price,b.basic_price,b.store_price,b.elm_price,b.mt_price,b.xcx_price").
		Join("left", "eshop.pro_sku b ", "a.id=b.product_id").
		In("a.id", productIdSli).
		Where("a.product_type!=?", product_po2.ProductTypeService).
		Where("a.product_type!=?", product_po2.ProductTypeLive).
		Where("b.is_del=0").
		Find(&productData); err != nil {
		log.Error(logPrefix, "获取商品信息失败，err=", err.Error())
		err = errors.New("获取商品信息失败")
		return
	}

	for _, v := range productData {
		if _, ok := productMap[v.ProductId]; !ok {
			productMap[v.ProductId] = make(map[int]vo.ProductSkuInfo)
		}
		if v.MtPrice < 0 {
			v.MtPrice = v.StorePrice
		}
		if v.ElmPrice < 0 {
			v.ElmPrice = v.StorePrice
		}
		if v.XcxPrice < 0 {
			v.XcxPrice = v.StorePrice
		}
		productMap[v.ProductId][v.SkuId] = v
	}
	return
}
