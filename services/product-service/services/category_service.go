package services

import (
	"context"
	"eShop/domain/external-po/offline"
	warehouse_po "eShop/domain/inventory-po/warehouse"
	marketing_po "eShop/domain/marketing-po"
	omnibus_po2 "eShop/domain/omnibus-po"
	product_po2 "eShop/domain/product-po"
	"eShop/infra/enum"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	pro_category "eShop/services/product-service/enum/pro-category"
	proproductstoreinfo "eShop/services/product-service/enum/pro-product-store-info"
	omnibus_vo "eShop/view-model/omnibus-vo"
	vo "eShop/view-model/product-vo"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/GUAIK-ORG/go-snowflake/snowflake"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"xorm.io/xorm"
)

type CategoryService struct {
	common.BaseService
	Request *http.Request
}

func (c CategoryService) DelCategory(in vo.DelCategoryReq) (err error) {

	var category product_po2.ProCategory
	logPrefix := fmt.Sprintf("删除分类，参数是：%s ", utils.InterfaceToJSON(in))
	log.Info(logPrefix)

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(c.Request)
	if err != nil {
		return fmt.Errorf("获取登录信息失败：%s", err.Error())
	}
	// 删除分类 只能删除自己账号真正所属的连锁的分类
	chainId := cast.ToInt64(jwtInfo.ChainId)
	if cast.ToInt64(jwtInfo.SourceChainId) > 0 {
		chainId = cast.ToInt64(jwtInfo.SourceChainId)
	}

	c.Begin()
	defer c.Close()

	session := c.Engine.NewSession()
	defer session.Close()

	_, _, idMap, err := new(product_po2.ProCategory).GetCategoryMapInfo(c.Engine, product_po2.CategoryMapInfoReq{
		ChainId: chainId,
		Id:      &in.Id,
		OutType: 2,
	})
	if err != nil {
		log.Error(logPrefix, "查询分类失败，err=", err.Error())
		err = errors.New("查询分类失败")
		return
	}
	if cat, ok := idMap[in.Id]; ok {
		category = cat
	}

	if category.Id == 0 {
		log.Error(logPrefix, "分类不存在，chain_id："+utils.InterfaceToJSON(jwtInfo)+",id: "+cast.ToString(in.Id))
		err = errors.New("分类不存在")
		return
	}

	// 一级分类需要判断是否存在下级分类,一级关联下级的分类不能删除
	if category.ParentId == 0 {
		cateList := make([]product_po2.ProCategory, 0)
		cateList, _, _, err = new(product_po2.ProCategory).GetCategoryMapInfo(c.Engine, product_po2.CategoryMapInfoReq{
			ChainId:  chainId,
			ParentId: &in.Id,
		})
		if err != nil {
			log.Error(logPrefix, "查询分类失败，err=", err.Error())
			return errors.New("查询分类失败.")
		}
		if len(cateList) > 0 {
			log.Error(logPrefix, "已经关联下级的分类不能删除")
			return errors.New("已经关联下级的分类不能删除")
		}
	}

	// 判断分类是否有关联商品
	// 判断连锁商品是否关联该分类
	if category.Type == product_po2.CategoryTypeOffline || category.Type == product_po2.CategoryTypeOnline {
		where := map[string]interface{}{
			"categoryId": in.Id,
			"chainId":    chainId,
		}
		if category.Type == product_po2.CategoryTypeOffline {
			where = map[string]interface{}{
				"categoryIdOffline": in.Id,
				"chainId":           chainId,
			}
		}
		var products []product_po2.ProProduct
		products, _, _, err = product_po2.GetProductMapInfo(session, where)
		if err != nil {
			log.Error(logPrefix, "查询商品分类是否存在失败，err=", err.Error())
			err = errors.New("查询分类是否存在失败")
			return
		}
		if len(products) > 0 {
			log.Error(logPrefix, "已经关联商品的子类不能删除")
			err = errors.New("已经关联商品的子类不能删除")
			return
		}
	}

	// 判断门店商品是否关联该分类，其中包括服务和活体
	spuQuery := product_po2.SpuQuery{
		ChannelCategoryId: cast.ToString(in.Id),
	}
	_, spuList, err := new(product_po2.ProProductStoreSpu).QueryMap(session, spuQuery)
	if err != nil {
		log.Error(logPrefix, "查询商品SPU失败，err=", err.Error())
		return errors.New("查询商品SPU失败")
	}
	if len(spuList) > 0 {
		log.Error(logPrefix, "已经关联商品的子类不能删除")
		return errors.New("已经关联商品的子类不能删除")
	}

	//如果分类或者下级分类在任务中执行，则不能删除
	var taskCategoryIds = []string{"0", cast.ToString(in.Id)}
	if category.ParentId > 0 {
		taskCategoryIds = append(taskCategoryIds, cast.ToString(category.ParentId))
	}
	// 查询分类任务是否存在
	taskListAsyncReq := omnibus_po2.TaskListAsyncMapInfoReq{
		TaskContent: enum.SyncCategoryTaskContent,
		TaskStatus:  []int{1, 2}, // 未开始和进行中的任务
		KeyStr:      taskCategoryIds,
		ContextData: cast.ToString(chainId),
	}
	_, taskMap, err := new(omnibus_po2.TaskListAsync).GetTaskListAsyncMapInfo(c.Engine, taskListAsyncReq)
	if err != nil {
		log.Error(logPrefix, "查询分类任务失败，err=", err.Error())
		err = errors.New("查询分类任务失败")
		return
	}
	if len(taskMap) > 0 {
		log.Error(logPrefix, "分类任务或者分类的一级任务已经在执行")
		err = errors.New("分类任务或者分类的一级任务已经在执行")
		return
	}

	session.Begin()
	//删除分类
	if _, err = session.Table("eshop.pro_category").ID(in.Id).Where("chain_id = ?", chainId).Delete(&product_po2.ProCategory{}); err != nil {
		session.Rollback()
		log.Error(logPrefix, "删除分类失败，err=", err.Error())
		err = errors.New("删除分类失败")
		return
	}

	//线上分类才需写入异步任务
	if category.Type == product_po2.CategoryTypeOnline {
		if err = c.syncChannelCategoryToThird(&category, pro_category.SyncCategoryDelete, cast.ToInt64(chainId), "", jwtInfo.UserId, jwtInfo.RoleType); err != nil {
			session.Rollback()
			log.Error(logPrefix, "同步分类失败，err=", err.Error())
			err = errors.New("同步分类失败")
			return
		}
	}

	session.Commit()

	return
}

// 同步特定分类到特定的门店列表
// syncType 1 新增 2修改 3 删除
// chainId 需要同步的连锁
func (c CategoryService) syncChannelCategoryToThird(model *product_po2.ProCategory, syncType int, chainId int64, storeId string, userId string, roleType int) error {
	var params []*vo.CategorySync
	keyStr := ""
	if model == nil { //全部分类同步
		keyStr = "0"
		params = append(params, &vo.CategorySync{
			SyncType:     syncType,
			ChainId:      chainId,
			StoreId:      storeId,
			CategoryId:   "",
			CategoryName: "",
			ParentId:     0,
			Sort:         0,
			UserId:       userId,
			RoleType:     roleType,
		})
	} else {
		keyStr = cast.ToString(model.Id)
		params = append(params, &vo.CategorySync{
			SyncType:     syncType,
			ChainId:      chainId,
			CategoryId:   cast.ToString(model.Id),
			StoreId:      storeId,
			CategoryName: model.Name,
			ParentId:     model.ParentId,
			Sort:         model.Sort,
			UserId:       userId,
			RoleType:     roleType,
		})
	}

	return new(common.AsyncCommService).CreatSyncTask(c.Request, omnibus_vo.TaskListAsync{
		Id:               0,
		TaskContent:      enum.SyncCategoryTaskContent,
		TaskStatus:       0,
		TaskDetail:       "",
		OperationFileUrl: utils.JsonEncode(params),
		RequestHeader:    "",
		ResulteFileUrl:   "",
		CreateId:         "",
		CreateName:       "",
		CreateMobile:     "",
		CreateIp:         "",
		IpLocation:       "",
		SuccessNum:       0,
		FailNum:          0,
		ExtendedData:     "",
		ContextData:      cast.ToString(chainId),
		DoCount:          0,
		ErrMes:           "",
		CreateTime:       time.Time{},
		UpdateTime:       time.Time{},
		OrgId:            0,
		KeyStr:           keyStr,
	})
}

// 查询分类列表
func (c CategoryService) CategoryList(in vo.CategoryListReq) (out []product_po2.ProCategory, err error) {
	logPrefix := fmt.Sprintf("查询分类列表，参数是：%s ", utils.InterfaceToJSON(in))
	c.Begin()
	defer c.Close()

	session := c.Engine.NewSession()
	defer session.Close()
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(c.Request)
	if err != nil {
		log.Error(logPrefix, "获取登录信息失败，err=", err.Error())
		return nil, fmt.Errorf("获取登录信息失败：%s", err.Error())
	}

	chainId := cast.ToInt64(jwtInfo.ChainId)
	in.StoreId = jwtInfo.TenantId

	// 前台分类的展示：
	// 场景1：代运营账号 只能查看授权的连锁的前台分类
	// 场景2：门店账号 如果是查看店内数据， 则使用店所属的连锁分类
	// 场景3：门店账号 如果是查看新零售数据， 则使用门店授权的连锁分类
	if in.Type == product_po2.CategoryTypeOnline || in.Type == product_po2.CategoryTypeOffline {
		// 如果传入了仓库id, 则需要判断仓库分类是自营仓，还是加盟仓， 如果是加盟仓， 则只展示代运营连锁的商品分类
		if in.WarehouseId > 0 {
			warePo := warehouse_po.Warehouse{}
			warehouses, err := warePo.GetByID(context.Background(), session, in.WarehouseId)
			if err != nil {
				log.Error(logPrefix, "获取仓库信息失败", err.Error())
				err = errors.New("获取仓库信息失败")
				return nil, err
			}
			// 加盟仓
			if warehouses.Category == 4 {
				chainId = warehouses.AuthorizedChainId
			}
		} else if cast.ToInt(jwtInfo.RoleType) == 2 { // 代运营账号 只能查看授权的连锁商品
			chainId = cast.ToInt64(jwtInfo.SourceChainId)
		} else if cast.ToInt(jwtInfo.RoleType) == 1 && in.DataType == 2 {
			retailCfg := &offline.TTenantRetailCfg{}
			query := offline.RetailCfgQuery{
				TenantId: cast.ToInt64(in.StoreId),
				OutType:  1,
			}
			_, retailCfgMap, err := retailCfg.QueryByTenantId(c.Engine, query)
			if err != nil {
				log.Error(logPrefix, "查询店铺授权信息失败，err=", err.Error())
				return nil, errors.New("查询店铺授权信息失败")
			}
			if info, ok := retailCfgMap[cast.ToInt64(in.StoreId)]; !ok {
				log.Error(logPrefix, "店铺授权信息不存在")
				return nil, errors.New("店铺授权信息不存在")
			} else {
				chainId = int64(info.AuthorizedChainId)
			}

		}
	}

	// 调用GetCategoryMapInfo方法查询分类
	out, _, _, err = new(product_po2.ProCategory).GetCategoryMapInfo(c.Engine, product_po2.CategoryMapInfoReq{
		Ids:     in.Ids,
		Type:    in.Type,
		ChainId: chainId,
		OrderBy: "parent_id,sort",
	})
	if err != nil {
		log.Error(logPrefix, "查询分类列表失败，err=", err.Error())
		err = errors.New("查询分类列表失败")
		return
	}

	return
}

// 判断指定渠道,指定店铺 是否有已上架的 且在参加限时特价的商品
func (s *CategoryService) HasUpRunningSpecialPriceProduct(in vo.CategoryListForApiReq) (has bool, err error) {
	logPrefix := fmt.Sprintf("判断指定渠道,指定店铺 是否有已上架的 且在参加限时特价的商品====，入参：%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 获取正在参与特价活动的商品列表
	marketingProductMarketingActivity, _, err := new(marketing_po.MarketingProduct).GetRunningActivityProducts(session, marketing_po.RunningActivityProductsQuery{
		ChainId: cast.ToInt64(in.ChainId),
		StoreId: in.StoreId,
		Type:    marketing_po.TypeSpecialPrice,
	})
	if err != nil {
		log.Error(logPrefix, "获取正在参与特价活动的商品列表失败,err=", err.Error())
		err = errors.New("获取正在参与特价活动的商品列表失败")
		return
	}

	// 获取正在参与商品特价活动的sku切片
	skuids := make([]int, 0)
	for _, v := range marketingProductMarketingActivity {
		if v.MarketingProduct.SkuId > 0 {
			skuids = append(skuids, v.MarketingProduct.SkuId)
		}
	}

	where := product_po2.GetProductStoreInfoReq{
		SkuIds:      skuids,
		ChannelId:   common.ChannelIdWeChatApp,
		StoreId:     in.StoreId,
		UpDownState: proproductstoreinfo.UpDownStateUp,
	}
	// 判断sku商品是否在指定渠道指定店铺已上架
	productStoreInfoList, _, _, err := product_po2.GetProductStoreInfo(session, where)
	if err != nil {
		log.Error(logPrefix, "获取商品上架信息失败", err.Error())
		err = errors.New("获取商品上架信息失败")
		return
	}
	if len(productStoreInfoList) > 0 {
		has = true
	}
	return
}

// 判断指定渠道,指定店铺 是否有已上架的 且在参加满减活动的商品
func (s *CategoryService) HasUpRunningFullReductionProduct(in vo.HasUpRunningFullReductionProductReq) (has bool, err error) {
	logPrefix := fmt.Sprintf("判断指定渠道,指定店铺 是否有已上架的 且在参加满减活动的商品====，入参：%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 获取正在参与特价活动的商品列表
	MarketingProductMarketingActivity, _, err := new(marketing_po.MarketingProduct).GetRunningActivityProducts(session, marketing_po.RunningActivityProductsQuery{
		ChainId: cast.ToInt64(in.ChainId),
		StoreId: in.StoreId,
		Type:    marketing_po.TypeFullReduction,
	})
	if err != nil {
		log.Error(logPrefix, "获取正在参与满减活动的商品列表失败,err=", err.Error())
		err = errors.New("获取正在参与满减活动的商品列表失败")
		return
	}

	for _, v := range MarketingProductMarketingActivity {
		// 如果满减设置选择的是：全部商品， 则需要判断 该渠道 该店铺 是否有商品正在上架
		if v.MarketingProduct.ApplyType == marketing_po.ProductApplyTypeGoods && len(in.CategoryIds) > 0 {
			has = true
			return
		} else if v.MarketingProduct.ApplyType == marketing_po.ProductApplyTypeCategory {
			// 如果满减设置选择的是：指定分类， 则判断分类是否在有上架商品
			if utils.InIntSlice(v.MarketingProduct.ProductRefId, in.CategoryIds) {
				has = true
				return
			}
		}

	}

	return
}

// 判断指定渠道,指定店铺 是否有已上架的 且在参加优惠券活动的商品
func (s *CategoryService) HasUpRunningCouponProduct(in vo.HasUpRunningCouponProductReq) (has bool, err error) {
	logPrefix := fmt.Sprintf("判断指定渠道,指定店铺 是否有已上架的 且在参加优惠券活动的商品====，入参：%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 获取正在参与优惠券活动的商品列表
	RunningCouponProductsRes, _, marketingCoupons, err := new(marketing_po.MarketingProduct).GetRunningCouponProducts(session, marketing_po.RunningCouponProductsQuery{
		ChainId: cast.ToInt64(in.ChainId),
		StoreId: in.StoreId,
	})
	if err != nil {
		log.Error(logPrefix, "获取正在参与满减活动的商品列表失败,err=", err.Error())
		err = errors.New("获取正在参与满减活动的商品列表失败")
		return
	}

	if len(marketingCoupons) > 0 || len(RunningCouponProductsRes) > 0 {
		GetProductStoreInfoReq := product_po2.GetProductStoreInfoReq{
			ChannelId:   common.ChannelIdWeChatApp,
			StoreId:     in.StoreId,
			UpDownState: proproductstoreinfo.UpDownStateUp,
		}

		if len(RunningCouponProductsRes) > 0 {
			skuIdsNo := make([]int, 0)
			skuIdsYes := make([]int, 0)
			for _, v := range RunningCouponProductsRes {
				if v.MarketingCoupon.ApplyProduct == marketing_po.ApplyProductYes {
					skuIdsYes = append(skuIdsYes, v.SkuId)
				} else if v.MarketingCoupon.ApplyProduct == marketing_po.ApplyProductNo {
					skuIdsNo = append(skuIdsNo, v.SkuId)
				}
			}
			if len(skuIdsYes) > 0 {
				GetProductStoreInfoReq.SkuIds = skuIdsYes
			}
			if len(skuIdsNo) > 0 {
				GetProductStoreInfoReq.SkuIdsNo = skuIdsNo
			}
		}

		// 判断sku商品是否在指定渠道指定店铺已上架
		productStoreInfoList, _, _, e := product_po2.GetProductStoreInfo(session, GetProductStoreInfoReq)
		if e != nil {
			log.Error(logPrefix, "获取商品上架信息失败", e.Error())
			err = errors.New("获取商品上架信息失败" + e.Error())
			return
		}
		if len(productStoreInfoList) > 0 {
			has = true
		}

	}

	return
}

// 小程序端分类
func (c CategoryService) CategoryListForApi(in vo.CategoryListForApiReq) (out []*vo.CategoryInfo, err error) {
	logPrefix := fmt.Sprintf("小程序前端查询分类列表，参数是：%s ", utils.InterfaceToJSON(in))
	log.Info(logPrefix)
	c.Begin()
	defer c.Close()
	session := c.Engine.NewSession()
	defer session.Close()
	// 获取门店已上架商品的所有分类
	where := product_po2.GetProductStoreInfoReq{
		ChannelId:      common.ChannelIdWeChatApp,
		StoreId:        in.StoreId,
		IsDistribution: proproductstoreinfo.IsDistributionLaunched,
		UpDownState:    proproductstoreinfo.UpDownStateUp,
	}
	productStoreInfos, _, _, err := product_po2.GetProductStoreInfo(session, where)
	if err != nil {
		log.Error(logPrefix, "查询门店商品分类失败，err=", err.Error())
		err = errors.New("查询门店商品分类失败")
		return
	}
	categoryIds := make([]int, 0)        // 前台分类
	categoryIdOfflines := make([]int, 0) //后台分类
	for _, v := range productStoreInfos {
		if v.ChannelCategoryId > 0 {
			categoryIds = append(categoryIds, v.ChannelCategoryId)
		}
		if v.CategoryIdOffline > 0 {
			categoryIdOfflines = append(categoryIdOfflines, v.CategoryIdOffline)
		}
	}
	var CategorySli []product_po2.ProCategory
	if len(categoryIds) > 0 {
		CategorySli, _, _, err = new(product_po2.ProCategory).GetCategoryMapInfo(c.Engine, product_po2.CategoryMapInfoReq{Ids: categoryIds})
		if err != nil {
			log.Error(logPrefix, "查询商品分类信息失败，err=", err.Error())
			err = errors.New("查询商品分类信息失败")
			return
		}
		for _, v := range CategorySli {
			categoryIds = append(categoryIds, int(v.ParentId))
		}
	}

	categoryIds = utils.RemoveDuplicates(categoryIds)
	if len(categoryIds) > 0 {
		CategorySli, _, _, err = new(product_po2.ProCategory).GetCategoryMapInfo(c.Engine, product_po2.CategoryMapInfoReq{Ids: categoryIds, Type: in.Type})
		if err != nil {
			log.Error(logPrefix, "查询商品分类信息失败，err=", err.Error())
			err = errors.New("查询商品分类信息失败")
			return
		}
	}

	for _, v := range CategorySli {
		out = append(out, &vo.CategoryInfo{
			Id:       v.Id,
			Name:     v.Name,
			ParentId: v.ParentId,
			Sort:     v.Sort,
		})
	}
	if len(CategorySli) == 0 {
		return
	}

	// 判断指定渠道,指定店铺 是否有已上架的 且在参加限时特价的商品，如果有， 则显示 "限时特价" 分类
	has, err := c.HasUpRunningSpecialPriceProduct(in)
	if err != nil {
		log.Error(logPrefix, err.Error())
		return
	} else if has {
		out = append(out, &vo.CategoryInfo{
			Id:       -10,
			Name:     "限时特价",
			ParentId: 0,
			Sort:     -10,
		})
	}

	// 判断是否有商品正在参与满减活动
	has, err = c.HasUpRunningFullReductionProduct(vo.HasUpRunningFullReductionProductReq{
		ChainId:     in.ChainId,
		StoreId:     in.StoreId,
		CategoryIds: categoryIdOfflines,
	})
	if err != nil {
		log.Error(logPrefix, err.Error())
		return
	} else if has {
		out = append(out, &vo.CategoryInfo{
			Id:       -9,
			Name:     "满减活动",
			ParentId: 0,
			Sort:     -9,
		})

	}
	// 判断是否有商品正在参与优惠券活动
	has, err = c.HasUpRunningCouponProduct(vo.HasUpRunningCouponProductReq{
		ChainId:     in.ChainId,
		StoreId:     in.StoreId,
		CategoryIds: categoryIdOfflines,
	})
	if err != nil {
		log.Error(logPrefix, err.Error())
		return
	} else if has {
		out = append(out, &vo.CategoryInfo{
			Id:       -8,
			Name:     "优惠券",
			ParentId: 0,
			Sort:     -8,
		})

	}

	return
}

func (c CategoryService) AddCategory(in vo.AddCategoryReq) (err error) {
	logPrefix := fmt.Sprintf("添加分类，参数是：%s ", utils.InterfaceToJSON(in))
	log.Info(logPrefix)
	c.Begin()
	defer c.Close()

	session := c.Engine.NewSession()
	defer session.Close()

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(c.Request)
	if err != nil {
		return fmt.Errorf("获取登录信息失败：%s", err.Error())
	}
	log.Info(logPrefix, "获取登录信息成功，jwtInfo=", utils.InterfaceToJSON(jwtInfo))

	in.ChainId = cast.ToString(jwtInfo.ChainId)
	if cast.ToInt64(jwtInfo.SourceChainId) > 0 {
		in.ChainId = jwtInfo.SourceChainId
	}
	//对数据进行验证
	if err = c.AddCategoryValidate(in); err != nil {
		log.Error(logPrefix, "数据验证失败，err=", err.Error())
		return
	}

	session.Begin()

	item := &product_po2.ProCategory{
		Id:          in.Id,
		ChainId:     cast.ToInt64(in.ChainId),
		Name:        in.Name,
		ParentId:    in.ParentId,
		Sort:        in.Sort,
		Img:         "",
		CreatedBy:   cast.ToInt64(jwtInfo.UserId),
		CreatedName: jwtInfo.UserName,
		UpdatedBy:   cast.ToInt64(jwtInfo.UserId),
		UpdatedName: jwtInfo.UserName,
		Type:        in.Type,
	}

	syncType := 0
	if in.Id > 0 {
		if _, err = session.Table("eshop.pro_category").Cols("name,sort,update_date,updated_by,updated_name").ID(in.Id).Update(item); err != nil {
			session.Rollback()
			log.Error(logPrefix, "分类更新表失败，err=", err.Error())
			return
		}
		in.Type = item.Type
		syncType = pro_category.SyncCategoryUpdate
	} else {
		if _, err = session.Table("eshop.pro_category").Insert(item); err != nil {
			session.Rollback()
			log.Error(logPrefix, "分类插入表失败，err=", err.Error())
			return
		}

		syncType = pro_category.SyncCategoryAdd
	}

	//前台分类 才需要写入异步任务， 目的是同步美团饿了么那边的分类
	if in.Type == product_po2.CategoryTypeOnline {
		if err = c.syncChannelCategoryToThird(item, syncType, cast.ToInt64(in.ChainId), "", jwtInfo.UserId, jwtInfo.RoleType); err != nil {
			session.Rollback()
			log.Error(logPrefix, "同步分类失败，err=", err.Error())
			err = errors.New("同步分类失败")
			return
		}
	}

	session.Commit()
	return err
}

// 添加分类 数据验证
func (c CategoryService) AddCategoryValidate(in vo.AddCategoryReq) (err error) {

	if in.Name == "" {
		return errors.New("分类名称不能为空")
	}

	if len([]rune(in.Name)) > 8 {
		return errors.New("分类名称不能超过8个字符")
	}

	//美团和饿了么的排序是反的，饿了么的排序是：10001-in.Sort
	if in.Sort >= pro_category.EleMeCategorySort {
		return errors.New("排序不能超过" + cast.ToString(pro_category.EleMeCategorySort-1))
	}
	c.Begin()
	defer c.Close()
	//名称是否重复
	cateList, _, _, err := new(product_po2.ProCategory).GetCategoryMapInfo(c.Engine, product_po2.CategoryMapInfoReq{
		CategoryName: in.Name,
		ChainId:      cast.ToInt64(in.ChainId),
		NotEqualId:   &in.Id,
		Type:         in.Type,
		OutType:      1,
	})
	if err != nil {
		return err
	}
	if len(cateList) > 0 {
		return errors.New("分类名称重复")
	}

	// 查询任务列表
	KeyStr := make([]string, 0)
	KeyStr = append(KeyStr, "0")
	ids := make([]int, 0)
	if in.Id > 0 {
		ids = append(ids, in.Id)
		KeyStr = append(KeyStr, cast.ToString(in.Id))
	}
	if in.ParentId > 0 {
		ids = append(ids, in.ParentId)
		KeyStr = append(KeyStr, cast.ToString(in.ParentId))

	}

	if len(ids) > 0 {
		_, _, idMap, err := new(product_po2.ProCategory).GetCategoryMapInfo(c.Engine, product_po2.CategoryMapInfoReq{
			ChainId: cast.ToInt64(in.ChainId),
			Ids:     ids,
			OutType: 2,
		})
		if err != nil {
			return err
		}
		if in.Id > 0 {
			if _, ok := idMap[in.Id]; !ok {
				return errors.New("分类不存在，chain_id：" + cast.ToString(in.ChainId) + ",id: " + cast.ToString(in.Id))
			}
		}

		if in.ParentId > 0 {
			if _, ok := idMap[in.ParentId]; !ok {
				return errors.New("父分类不存在，chain_id：" + cast.ToString(in.ChainId) + ",id: " + cast.ToString(in.ParentId))
			}
		}
		//有分类的异步任务的时候退出不让编辑修改
		tasks, _, err := new(omnibus_po2.TaskListAsync).GetTaskListAsyncMapInfo(c.Engine, omnibus_po2.TaskListAsyncMapInfoReq{
			TaskContent: enum.SyncCategoryTaskContent,
			TaskStatus:  []int{omnibus_po2.TaskStatusNotStart, omnibus_po2.TaskStatusDoing},
			KeyStr:      KeyStr,
			ContextData: cast.ToString(in.ChainId),
		})
		if err != nil {
			return err
		}
		if len(tasks) > 0 {
			return errors.New("分类已经在任务列表中不允许编辑,id: " + cast.ToString(in.Id))
		}
	}

	//同一层级的排序值不能相同
	cateList, _, _, err = new(product_po2.ProCategory).GetCategoryMapInfo(c.Engine, product_po2.CategoryMapInfoReq{
		ChainId:    cast.ToInt64(in.ChainId),
		ParentId:   &in.ParentId,
		Type:       in.Type,
		NotEqualId: &in.Id,
		Sort:       &in.Sort,
	})
	if err != nil {
		return err
	}
	if len(cateList) > 0 {
		return errors.New("分类排序在同一层级中已经存在，请重新填写排序值: " + cast.ToString(in.Sort))
	}

	return nil
}
func (c CategoryService) SyncAllCategory(in vo.SyncAllCategoryReq) (err error) {
	logPrefix := fmt.Sprintf("同步所有分类，参数是：%s ", utils.InterfaceToJSON(in))
	log.Info(logPrefix)
	c.Begin()
	defer c.Close()

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(c.Request)
	if err != nil {
		return fmt.Errorf("获取登录信息失败：%s", err.Error())
	}
	chainId := jwtInfo.ChainId
	if cast.ToInt64(jwtInfo.SourceChainId) > 0 {
		chainId = jwtInfo.SourceChainId
	}
	// 查询分类任务是否存在
	taskListAsyncReq := omnibus_po2.TaskListAsyncMapInfoReq{
		TaskContent: enum.SyncCategoryTaskContent,
		TaskStatus:  []int{1, 2}, // 未开始和进行中的任务
		ContextData: cast.ToString(chainId),
	}
	tasks, _, err := new(omnibus_po2.TaskListAsync).GetTaskListAsyncMapInfo(c.Engine, taskListAsyncReq)
	if err != nil {
		log.Error(logPrefix, "查询分类任务失败，err=", err.Error())
		err = errors.New("查询分类任务失败")
		return
	}
	if len(tasks) > 0 {
		log.Error(logPrefix, "分类已经在任务列表中不允许全部同步")
		err = errors.New("分类已经在任务列表中不允许全部同步")
		return
	}

	//写入异步任务
	if err = c.syncChannelCategoryToThird(nil, pro_category.SyncCategoryAll, cast.ToInt64(chainId), jwtInfo.TenantId, jwtInfo.UserId, jwtInfo.RoleType); err != nil {
		log.Error(logPrefix, "同步所有分类失败，err=", err.Error())
		err = errors.New("同步所有分类")
		return
	}

	return err
}

// 导入前台分类
func (c CategoryService) BatchImportFrontCategory(requery vo.BatchImportProductAndCategoryReq) error {
	c.Begin()
	defer c.Close()

	file, _, err := c.Request.FormFile("file")
	if err != nil {
		return err
	}
	defer file.Close()

	// 使用excelize库打开Excel文件
	f, err := excelize.OpenReader(file)
	if err != nil {
		return err
	}

	rows, err := f.GetRows("店铺分类（前端分类）")

	if err != nil {
		return err
	}

	c.Begin()
	defer c.Close()

	session := c.Engine.NewSession()
	defer session.Close()

	session.Begin()

	parentId := 0
	parentName := ""
	for k, row := range rows {
		if k == 0 {
			continue
		}

		//防止溢出
		row = append(row, []string{"", ""}...)

		if row[1] == "" && row[0] == "" {
			break
		}

		if row[0] != "" {
			parentId = 0
			parentName = row[0]
		}

		if parentName == "" {
			session.Rollback()
			return fmt.Errorf("第%d行，一级分类不能为空", k+1)
		}

		if len([]rune(parentName)) > 8 {
			return fmt.Errorf("第%d行，一级分类名称不能超过8个字符", k+1)
		}

		if row[1] == "" {
			session.Rollback()
			return fmt.Errorf("第%d行，一级分类不能为空", k+1)
		}

		if len([]rune(row[1])) > 8 {
			return fmt.Errorf("第%d行，二级分类名称不能超过8个字符", k+1)
		}

		//判断分类名称是否存在
		if isExist, err := session.Table("eshop.pro_category").Where("name = ? and chain_id = ?", row[1], requery.ChainId).Exist(); err != nil {
			session.Rollback()
			return err
		} else if isExist {
			session.Rollback()
			return fmt.Errorf("第%d行，二级分类名称已经存在", k+1)
		}

		if parentId == 0 && parentName != "" {
			//判断一级分类名称是否存在
			var proCategory product_po2.ProCategory
			if _, err := session.Table("eshop.pro_category").Where("name = ? and chain_id = ? and parent_id = 0", parentName, requery.ChainId).Get(&proCategory); err != nil {
				session.Rollback()
				return err
			}

			//如果不存在，则先判断这个名称是否存在，不存在则添加
			if proCategory.Id == 0 {
				if isExist, err := session.Table("eshop.pro_category").Where("name = ? and chain_id = ?", parentName, requery.ChainId).Exist(); err != nil {
					session.Rollback()
					return err
				} else if isExist {
					session.Rollback()
					return fmt.Errorf("第%d行，一级分类名称已经存在", k+1)
				}

				//添加一级分类
				sort, err := c.GetFrontCategorySort(session, 0, k+1)
				if err != nil {
					session.Rollback()
					return err
				}
				fInsertCategory := product_po2.ProCategory{
					ChainId:     cast.ToInt64(requery.ChainId),
					Name:        parentName,
					ParentId:    0,
					Sort:        sort,
					Img:         "",
					CreateDate:  time.Now().Format("2006-01-02 15:04:05"),
					UpdateDate:  time.Now().Format("2006-01-02 15:04:05"),
					CreatedBy:   0,
					CreatedName: "",
					UpdatedBy:   0,
					UpdatedName: "",
				}
				if _, err := session.Table("eshop.pro_category").Insert(&fInsertCategory); err != nil {
					session.Rollback()
					return err
				}
				parentId = fInsertCategory.Id
			} else {
				parentId = proCategory.Id
			}
		}

		//添加二级分类
		sort, err := c.GetFrontCategorySort(session, parentId, k+1)
		if err != nil {
			session.Rollback()
			return err
		}
		sInsertCategory := product_po2.ProCategory{
			ChainId:     cast.ToInt64(requery.ChainId),
			Name:        row[1],
			ParentId:    parentId,
			Sort:        sort,
			Img:         "",
			CreateDate:  time.Now().Format("2006-01-02 15:04:05"),
			UpdateDate:  time.Now().Format("2006-01-02 15:04:05"),
			CreatedBy:   0,
			CreatedName: "",
			UpdatedBy:   0,
			UpdatedName: "",
		}

		if _, err := session.Table("eshop.pro_category").Insert(&sInsertCategory); err != nil {
			session.Rollback()
			return err
		}
	}

	session.Commit()
	return nil
}

// 导入后台分类
func (c CategoryService) BatchImportBackCategory(requery vo.BatchImportProductAndCategoryReq) error {
	c.Begin()
	defer c.Close()
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		return err
	}
	defer file.Close()

	// 使用excelize库打开Excel文件
	f, err := excelize.OpenReader(file)
	if err != nil {
		return err
	}

	rows, err := f.GetRows("后台分类")

	if err != nil {
		return err
	}

	c.Begin()
	defer c.Close()

	s, err := snowflake.NewSnowflake(int64(10), int64(5))
	if err != nil {
		return err
	}

	session := c.Engine.NewSession()
	defer session.Close()

	session.Begin()

	var parentId int64
	parentName := ""
	for k, row := range rows {
		if k == 0 {
			continue
		}
		//防止溢出
		row = append(row, []string{"", ""}...)

		if row[1] == "" && row[0] == "" {
			break
		}

		if row[0] != "" {
			parentId = 0
			parentName = row[0]
		}

		if parentName == "" {
			session.Rollback()
			return fmt.Errorf("第%d行，一级分类不能为空", k+1)
		}

		if row[1] == "" {
			session.Rollback()
			return fmt.Errorf("第%d行，一级分类不能为空", k+1)
		}

		if len([]rune(parentName)) > 10 || len([]rune(parentName)) < 2 {
			return fmt.Errorf("第%d行，一级分类名称2到10字符", k+1)
		}

		if len([]rune(row[1])) > 10 || len([]rune(row[1])) < 2 {
			return fmt.Errorf("第%d行，二级分类名称2到10字符", k+1)
		}

		if parentId == 0 && parentName != "" {
			//判断一级分类名称是否存在
			var proCategory omnibus_po2.TProductCategory
			if _, err := session.Table("eshop_saas.t_product_category").Where("category_name = ? and chain_id = ? and parent_id = 0", parentName, requery.ChainId).Get(&proCategory); err != nil {
				session.Rollback()
				return err
			}

			//如果不存在，则先判断这个名称是否存在，不存在则添加
			if proCategory.Id == 0 {
				//添加一级分类

				fInsertCategory := omnibus_po2.TProductCategory{
					Id:             s.NextVal(),
					ChainId:        cast.ToInt64(requery.ChainId),
					CategoryCode:   cast.ToString(s.NextVal() + 1000000000000000000),
					CategoryName:   parentName,
					ParentId:       0,
					ProductType:    "GOODS",
					ProductTypeNum: 1000,
					TreeGrade:      0,
					IdPath:         "/",
					NamePath:       parentName,
					Type:           "SELF",
					SortValue:      k,
					IsDeleted:      false,
					CreatedBy:      0,
					CreatedTime:    time.Now(),
					UpdatedBy:      0,
					UpdatedTime:    time.Now(),
				}
				if _, err := session.Table("eshop_saas.t_product_category").Insert(&fInsertCategory); err != nil {
					log.Error(err)
					session.Rollback()
					return err
				}
				parentId = fInsertCategory.Id
			} else {
				parentId = proCategory.Id
			}
		}

		//判断分类名称是否存在
		if isExist, err := session.Table("eshop_saas.t_product_category").Where("category_name = ? and chain_id = ? and parent_id = ?", row[1], requery.ChainId, parentId).Exist(); err != nil {
			log.Error(err)
			session.Rollback()
			return err
		} else if isExist {
			session.Rollback()
			return fmt.Errorf("第%d行，二级分类名称已经存在", k+1)
		}

		sInsertCategory := omnibus_po2.TProductCategory{
			Id:             s.NextVal(),
			ChainId:        cast.ToInt64(requery.ChainId),
			CategoryCode:   cast.ToString(s.NextVal() + 1000000000000000000),
			CategoryName:   row[1],
			ParentId:       parentId,
			ProductType:    "GOODS",
			ProductTypeNum: 1000,
			TreeGrade:      1,
			IdPath:         "/" + cast.ToString(parentId) + "/",
			NamePath:       parentName + ">" + row[1],
			Type:           "SELF",
			SortValue:      k,
			IsDeleted:      false,
			CreatedBy:      0,
			CreatedTime:    time.Now(),
			UpdatedBy:      0,
			UpdatedTime:    time.Now(),
		}

		if _, err := session.Table("eshop_saas.t_product_category").Insert(&sInsertCategory); err != nil {
			log.Error(err)
			session.Rollback()
			return err
		}
	}

	session.Commit()
	return nil
}

// 获取前端分类排序
func (c CategoryService) GetFrontCategorySort(session *xorm.Session, parentId int, sort int) (int, error) {
	//判断排序是否存在
	if isExitst, err := session.Table("eshop.pro_category").Where("parent_id = ? and sort = ?", parentId, sort).Exist(); err != nil {
		session.Rollback()
		return 0, err
	} else if isExitst {
		return c.GetFrontCategorySort(session, parentId, sort+7)
	}

	return sort, nil
}

// 导入供应商
func (c CategoryService) BatchImportSupplier(requery vo.BatchImportProductAndCategoryReq) error {
	c.Begin()
	defer c.Close()
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		return err
	}
	defer file.Close()

	// 使用excelize库打开Excel文件
	f, err := excelize.OpenReader(file)
	if err != nil {
		return err
	}

	rows, err := f.GetRows("商品供应商")

	if err != nil {
		return err
	}

	c.Begin()
	defer c.Close()

	s, err := snowflake.NewSnowflake(int64(10), int64(5))
	if err != nil {
		return err
	}

	session := c.Engine.NewSession()
	defer session.Close()

	session.Begin()

	for k, row := range rows {
		if k == 0 {
			continue
		}

		//防止溢出
		row = append(row, []string{"", "", ""}...)

		if row[1] == "" && row[0] == "" && row[2] == "" {
			break
		}

		if row[0] == "" {
			session.Rollback()
			return fmt.Errorf("第%d行，供应商名称不能为空", k+1)
		}

		if row[1] == "" {
			session.Rollback()
			return fmt.Errorf("第%d行，联系人不能为空", k+1)
		}

		if row[2] == "" {
			session.Rollback()
			return fmt.Errorf("第%d行，联系手电话不能为空", k+1)
		}

		if !utils.IsValidPhoneNumber(row[2]) {
			session.Rollback()
			return fmt.Errorf("第%d行，联系手机格式不正确", k+1)
		}

		//判断供应商名称是否存在
		if isExist, err := session.Table("eshop_saas.t_supplier").Where("supplier_name = ? and chain_id = ?", row[0], requery.ChainId).Exist(); err != nil {
			session.Rollback()
			return err
		} else if isExist {
			session.Rollback()
			return fmt.Errorf("第%d行，供应商名称已经存在", k+1)
		}

		//写入数据库
		insertSupplier := omnibus_po2.TSupplier{
			Id:             s.NextVal(),
			ChainId:        cast.ToInt64(requery.ChainId),
			SupplierCode:   "",
			SupplierName:   row[0],
			Address:        "",
			SalesmanName:   row[1],
			SalesmanMobile: row[2],
			SalesmanPhone:  "",
			SalesmanMail:   "",
			SalesmanWeixin: "",
			LicenseFile:    "",
			LicenseImg:     "",
			Remark:         "",
			FinanceName:    "",
			FinanceAccount: "",
			FinanceBank:    "",
			FinanceAdress:  "",
			FinanceSubBank: "",
			FinanceCompany: "",
			IsForbidden:    false,
			IsDeleted:      false,
			CreatedBy:      0,
			CreatedTime:    time.Now(),
			UpdatedBy:      0,
			UpdatedTime:    time.Now(),
		}
		if _, err = session.Table("eshop_saas.t_supplier").Insert(&insertSupplier); err != nil {
			session.Rollback()
			return err
		}
	}

	session.Commit()
	return nil
}

// 导入品牌
func (c CategoryService) BatchImportProductBrand(requery vo.BatchImportProductAndCategoryReq) error {
	c.Begin()
	defer c.Close()
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		return err
	}
	defer file.Close()

	// 使用excelize库打开Excel文件
	f, err := excelize.OpenReader(file)
	if err != nil {
		return err
	}

	rows, err := f.GetRows("商品品牌")

	if err != nil {
		return err
	}

	c.Begin()
	defer c.Close()

	s, err := snowflake.NewSnowflake(int64(10), int64(5))
	if err != nil {
		return err
	}

	session := c.Engine.NewSession()
	defer session.Close()

	session.Begin()

	for k, row := range rows {
		if k == 0 {
			continue
		}

		//防止溢出
		row = append(row, []string{""}...)

		if row[0] == "" {
			break
		}

		if row[0] == "" {
			session.Rollback()
			return fmt.Errorf("第%d行，品牌名称不能为空", k+1)
		}

		//判断品牌名称是否存在
		if isExist, err := session.Table("eshop_saas.t_product_brand").Where("brand_name = ? and chain_id = ?", row[0], requery.ChainId).Exist(); err != nil {
			session.Rollback()
			return err
		} else if isExist {
			session.Rollback()
			return fmt.Errorf("第%d行，品牌名称已经存在", k+1)
		}

		//写入数据库
		insertTProductBrand := omnibus_po2.TProductBrand{
			Id:          s.NextVal(),
			ChainId:     cast.ToInt64(requery.ChainId),
			BrandCode:   "",
			BrandName:   row[0],
			BrandLogo:   "",
			BrandRemark: "",
			UsedNum:     0,
			IsDeleted:   false,
			CreatedBy:   0,
			CreatedTime: time.Now(),
			UpdatedBy:   0,
			UpdatedTime: time.Now(),
		}

		if _, err = session.Table("eshop_saas.t_product_brand").Insert(&insertTProductBrand); err != nil {
			session.Rollback()
			return err
		}
	}

	session.Commit()
	return nil
}
