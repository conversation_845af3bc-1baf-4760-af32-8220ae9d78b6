package services

import (
	"context"
	"eShop/domain/omnibus-po"
	product_po2 "eShop/domain/product-po"
	"eShop/infra/log"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/proto"
	"eShop/services/external-service/services"
	services2 "eShop/services/omnibus-service/services"
	"eShop/view-model/omnibus-vo"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/spf13/cast"
	"xorm.io/xorm"
)

type ChannelInfoStruct struct {
	ChannelStoreId string
	AppChannel     int
}
type StoreChannelKey struct {
	StoreId   string
	ChannelId int
}

func GetChannelInfoByStoreId(db *xorm.Engine, financeCode string, channelId int) (string, int, bool) {
	redisConn := cache.GetRedisConn()
	key := fmt.Sprintf("%s:%s:%d", "ChannelStoreKey", financeCode, channelId)
	channelStoreVal := redisConn.Get(key).Val()
	if channelStoreVal == "" {
		storeChannel := omnibus_po.StoreChannel{}
		if _, err := db.Table("datacenter.store").Alias("a").
			Select("a.finance_code, a.app_channel,b.channel_id,b.channel_store_id").
			Join("left", "datacenter.store_relation b", "a.finance_code =  b.finance_code").
			Where(" a.finance_code =? and b.channel_id =?", financeCode, channelId).Get(&storeChannel); err != nil {
			log.Errorf("获取渠道配置信息异常：请求参数:%s,%d,error:%s", financeCode, channelId, err.Error())
			return "", 0, false
		}
		if storeChannel.ChannelStoreId == "" || storeChannel.AppChannel == 0 {
			return "", 0, false
		}
		redisConn.Set(key, fmt.Sprintf("%s|%d", storeChannel.ChannelStoreId, storeChannel.AppChannel), 60*time.Second)

		return storeChannel.ChannelStoreId, storeChannel.AppChannel, true
	} else {
		channelStoreArr := strings.Split(channelStoreVal, "|")
		return channelStoreArr[0], cast.ToInt(channelStoreArr[1]), true
	}
}

func (s *StoreProductServiceData) UpdateMtProduct(storeProduct *product_po2.ProProductStoreAppChannel) error {
	logPrefix := "铺品商品，美团请求=="
	ChannelStoreId, AppChannel, state := GetChannelInfoByStoreId(s.Engine, storeProduct.StoreId, storeProduct.ChannelId)
	if !state {
		return errors.New("门店信息未设置")
	}

	pattern := `(https?|ftp|file)://[-\w+&@#/%=~|?!:,.;]+[-\w+&@#/%=~|]`
	pattern = `(https?)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]`
	picStr := strings.Join(regexp.MustCompile(pattern).FindAllString(storeProduct.ContentPc, -1), ",")
	commonAttrValue := formatCommonAttrValue(storeProduct.Attr)
	skus := s.FormatSkus(storeProduct)

	if len(skus) == 0 {
		return errors.New("商品sku信息不能为空")
	}
	if storeProduct.CategoryThirdId == 0 {
		return errors.New("第三方类目不能为空")
	}
	pushData := &proto.RetailBatchinitdata{}
	var OperateType int32
	if s.Params.Type == 1 {
		pushData.IsSoldOut = 1 // 商品上下架状态，字段取值范围：0-上架，1-下架。 非必镇
	} else { //编辑
		OperateType = 2
	}
	pushData.AppFoodCode = cast.ToString(storeProduct.ProductId) // APP方商品id，即商家中台系统里商品的编码 必镇
	pushData.Name = storeProduct.Name                            // 商品名称 创建时必镇
	pushData.SellPoint = storeProduct.SellingPoint               // 商品描述  非必镇
	pushData.Skus = skus                                         // APP方商品的skus信息，支持同时传多个sku信息。
	pushData.MinOrderCount = 1                                   // 商品的最小购买量，创建商品时，min_order_count字段信息如不传则默认为1。 必镇
	//限定长度不超过8个字符
	pushData.CategoryName = storeProduct.CategoryName // 分类名称 创建时必填
	pushData.Picture = storeProduct.Pic               // 商品图片： 创建必镇
	// 美团内部商品类目id
	pushData.TagId = int64(storeProduct.CategoryThirdId) // 1.创建时必传，2.若商品创建时未传tag_id，更新时必传(只需传一次)。若门店未启用或未传递销售属性/普通属性则非必传
	pushData.CommonAttrValue = commonAttrValue           // 商品普通属性的json字符串 若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
	pushData.Properties = []*proto.Propertie{}           // 商品属性 非必镇
	pushData.LimitSaleInfo = new(proto.LimitSaleInfo)    // 商品限购详情 非必镇
	pushData.PictureContents = picStr                    // 商品的图片详情
	pushData.IsSpecialty = 0                             //是否力荐商品

	var foodData []*proto.RetailBatchinitdata
	foodData = append(foodData, pushData)
	var req = &proto.RetailBatchinitdataRequest{
		AppPoiCode:    ChannelStoreId,
		FoodData:      foodData,
		OperateType:   OperateType,
		StoreMasterId: cast.ToInt32(AppChannel),
	}
	res, err := new(services.MtProductService).RetailBatchinitdata(context.Background(), req)
	if err != nil {
		log.Errorf("%s接口异常,params:%s,error:%s", logPrefix, utils.JsonEncode(storeProduct), err)
		return errors.New(err.Error())
	} else if res.Code != 200 {
		if len(res.ErrorList) > 0 {
			log.Errorf("%s失败,params:%s,error:%s", logPrefix, utils.JsonEncode(storeProduct), res.Error.Msg)
			return errors.New(res.Error.Msg)
		} else {
			log.Errorf("%s失败,params:%s,error:%s", logPrefix, utils.JsonEncode(storeProduct), res.Message)
			return errors.New(res.Message)
		}
	}
	return nil
}

// 整理美团属性数据
func formatCommonAttrValue(productAttr []product_po2.ProProductChannelAttr) (attrVal []*proto.CommonAttrValue) {
	for _, v := range productAttr {
		//属性值为空跳过
		if len(v.AttrValue) == 0 {
			continue
		}
		valueList := []*proto.ValueList{}
		attrValueId := strings.Split(v.AttrValueId, ",")
		attrValue := strings.Split(v.AttrValue, ",")
		for kk, vv := range attrValueId {
			valueList = append(valueList, &proto.ValueList{
				ValueId: cast.ToInt64(vv),
				Value:   attrValue[kk],
			})
		}
		attrVal = append(attrVal, &proto.CommonAttrValue{
			AttrId:    cast.ToInt64(v.AttrId),
			AttrName:  v.AttrName,
			ValueList: valueList,
		})
	}

	return
}

// 整理美团sku数据
func (s *StoreProductServiceData) FormatSkus(storeProduct *product_po2.ProProductStoreAppChannel) (skus []*proto.SkuParam) {
	// 查库存
	stockRes := new(services2.InventoryService).QueryStock(omnibus_vo.QueryStockReq{
		ShopId:     storeProduct.StoreId,
		ProductIds: []int64{int64(storeProduct.ProductId)},
	})
	if stockRes.Code != 200 {
		log.Errorf("查询库存失败，门店：%s,商品：%d,库存：%s", s.Params.TenantId, storeProduct.ProductId, stockRes.Message)
		stockRes.Stock = make(map[int64]omnibus_vo.VInventory)
	}
	for _, v := range storeProduct.Skus {
		data := &proto.SkuParam{
			SkuId:          cast.ToString(v.SkuId),                      // 是sku唯一标识码 必镇
			Spec:           v.ProductSpecs,                              // sku的规格名称  建时门店启用类目属性且skus中传递了销售属性则为非必填(会自动根据销售属性组合其规格)，其余情况参考字段描述里规则。 更新商品时，本参数非必填。
			Upc:            v.SkuUpc,                                    // 为sku的商品包装上的条形码编号，UPC/EAN码；字符长度8位或者13位 非必镇
			Price:          cast.ToString(float32(v.RetailPrice) / 100), // 默认使用前置仓价格，价格同步时如果是门店仓则修改为门店仓价格
			Unit:           v.StoreUnit,                                 // 商品sku的售卖单位 非必镇
			MinOrderCount:  1,                                           // 一个订单中此商品的最小购买量
			AvailableTimes: new(proto.AvailableTimes),                   // 表示sku可售时间 非必镇
			WeightForUnit:  cast.ToString(v.WeightForUnit),              // 表示sku的重量数值信息  创建时，如填写weight_unit，则weight_for_unit必填且与weight至多填写一个，否则非必填
			// 创建时，如填写weight_for_unit，则weight_unit必填且与weight至多填写一个，否则非必填
			WeightUnit: "千克(kg)", // 表示sku的重量数值单位，枚举值如下： 1."克(g)" 2."千克(kg)" 3."毫升(ml)" 4."升(L)" 5."磅" 6."斤" 7."两"。
		}
		data.Stock = cast.ToString(stockRes.Stock[int64(v.SkuId)].AvailableNum)
		data.LocationCode = stockRes.Stock[int64(v.SkuId)].LocationCode
		skus = append(skus, data)
	}

	return
}

// 美团上下架
func (s *StoreProductServiceData) RetailSellStatus(storeProduct *product_po2.ProProductStoreAppChannel, syncType int32) error {
	ChannelStoreId, AppChannel, state := GetChannelInfoByStoreId(s.Engine, storeProduct.StoreId, storeProduct.ChannelId)
	if !state {
		return errors.New("门店信息未设置")
	}
	req := &proto.RetailSellStatusRequest{
		AppPoiCode: ChannelStoreId,
		FoodData:   []*proto.AppFoodCode{},
		SellStatus: syncType, //1-下架，0-上架
	}
	// 多规格遍历
	req.FoodData = append(req.FoodData, &proto.AppFoodCode{AppFoodCode: cast.ToString(storeProduct.ProductId)})
	req.StoreMasterId = cast.ToInt32(AppChannel)
	res, err := new(services.MtProductService).RetailSellStatus(context.Background(), req)
	if err != nil {
		log.Errorf("上下架商品，美团请求接口异常,params:%s,error:%s", utils.JsonEncode(storeProduct), err)
		return err
	}

	if res.Code != 200 {
		log.Errorf("上下架商品，美团失败,params:%s,error:%s", utils.JsonEncode(storeProduct), res.Error.Msg)
		return errors.New(res.Error.Msg)
	}
	return nil
}

// 美团删除商品
func (s *StoreProductServiceData) RetailSkuDelete(storeProduct *product_po2.ProProductStoreAppChannel) error {
	ChannelStoreId, AppChannel, state := GetChannelInfoByStoreId(s.Engine, storeProduct.StoreId, storeProduct.ChannelId)
	if !state {
		log.Infof("删除商品，门店信息未设置:id:%d,product_id:%d,store_id:%s,channel_id:%d", storeProduct.Id, storeProduct.ProductId, storeProduct.StoreId, storeProduct.ChannelId)
		return nil
	}
	// 多规格遍历
	var lastErr error
	for _, v := range storeProduct.Skus {
		req := &proto.RetailSkuDeleteRequest{
			AppPoiCode:        ChannelStoreId,
			AppFoodCode:       cast.ToString(storeProduct.ProductId),
			SkuId:             cast.ToString(v.SkuId),
			IsDeleteRetailCat: 2,
			StoreMasterId:     cast.ToInt32(AppChannel),
		}
		res, err := new(services.MtProductService).RetailSkuDelete(context.Background(), req)
		if err != nil {
			log.Errorf("删除商品，美团请求接口异常,params:%s,error:%s", utils.JsonEncode(storeProduct), err.Error())
			lastErr = errors.New(err.Error())
		} else if res.Code != 200 {
			log.Errorf("删除商品，美团失败,params:%s,error:%s", utils.JsonEncode(storeProduct), utils.JsonEncode(res))
			lastErr = errors.New(res.Error.Msg)
		}
	}

	return lastErr
}

// 美团商品分类更新
func (s *StoreProductService) RetailInitDataCategory(ChannelId, ProductId int, StoreId, ChannelCategoryName string) error {
	logPrefix := "编辑商品分类，同步给美团===="
	ChannelStoreId, AppChannel, state := GetChannelInfoByStoreId(s.Engine, StoreId, ChannelId)
	if !state {
		log.Error("同步商品分类,查询配置信息异常")
	}
	req := &proto.RetailInitDataCategoryRequest{
		OperateType:   2,
		AppPoiCode:    cast.ToString(ChannelStoreId),
		StoreMasterId: cast.ToInt32(AppChannel),
		AppFoodCode:   cast.ToString(ProductId),
		CategoryName:  ChannelCategoryName,
	}
	res, err := new(services.MtProductService).RetailInitDataCategory(context.Background(), req)
	log.Infof("同步商品分类,美团请求接口：params:%s,返回结果：%s", utils.JsonEncode(req), utils.JsonEncode(res))
	if err != nil {
		log.Errorf("同步商品分类，美团请求接口，%s异常,params:%s,error:%s", logPrefix, utils.JsonEncode(req), err.Error())
		return errors.New(err.Error())
	} else if res.Code != 200 {
		if len(res.ErrorList) > 0 {
			log.Errorf("同步商品分类,%s失败,params:%s,error:%s", logPrefix, utils.JsonEncode(req), res.Error.Msg)
			return errors.New(res.Error.Msg)
		} else {
			log.Errorf("同步商品分类,%s失败,params:%s,error:%s", logPrefix, utils.JsonEncode(req), res.Message)
			return errors.New(res.Message)
		}
	}
	return nil
}

// 美团商品更新价格
func (s *StoreProductServiceData) MtRetailSkuPrice(storeProduct *product_po2.ProProductStoreAppChannel) (err error) {
	ChannelStoreId, AppChannel, state := GetChannelInfoByStoreId(s.Engine, storeProduct.StoreId, storeProduct.ChannelId)
	if !state {
		return errors.New("门店信息未设置")
	}
	var (
		foodData     proto.FoodDataMt
		foodDataList []proto.FoodDataMt
		skus         proto.FoodSku
	)
	foodData.AppFoodCode = cast.ToString(storeProduct.ProductId)
	// 多规格遍历
	for _, v := range storeProduct.Skus {
		skus.SkuId = cast.ToString(v.SkuId)
		skus.Price = cast.ToString(utils.Fen2Yuan(storeProduct.Skus[0].RetailPrice))
		foodData.Skus = append(foodData.Skus, &skus)
	}
	foodDataList = append(foodDataList, foodData)
	str, err := json.Marshal(foodDataList)
	if err != nil {
		return err
	}
	//请求参数
	req := &proto.MtRetailSkuPriceRequest{}
	req.AppPoiCode = ChannelStoreId
	req.StoreMasterId = cast.ToInt32(AppChannel)
	req.FoodData = string(str)
	res, err := new(services.MtProductService).MtRetailSkuPrice(context.Background(), req)
	if err != nil {
		log.Errorf("删除商品，美团请求接口异常,params:%s,error:%s", utils.JsonEncode(storeProduct), err.Error())
		return errors.New(err.Error())
	} else if res.Code != 200 {
		log.Errorf("删除商品，美团失败,params:%s,error:%s", utils.JsonEncode(storeProduct), utils.JsonEncode(res))
		return errors.New(res.Message)
	}
	return nil
}

// 美团查询单个商品信息
func (s *StoreProductServiceData) MtRetailGet(storeProduct *product_po2.ProProductStoreAppChannel) {
	ChannelStoreId, AppChannel, state := GetChannelInfoByStoreId(s.Engine, storeProduct.StoreId, storeProduct.ChannelId)
	if !state {
		log.Infof("MtRetailGet 门店信息未设置")
		return
	}
	//请求参数
	req := &proto.RetailGetRequest{}
	req.AppPoiCode = ChannelStoreId
	req.StoreMasterId = cast.ToInt32(AppChannel)
	req.AppFoodCode = cast.ToString(storeProduct.ProductId)
	res, err := new(services.MtProductService).RetailGet(context.Background(), req)
	if err != nil {
		log.Errorf("查询商品，美团请求接口异常,params:%s,error:%s", utils.JsonEncode(storeProduct), err.Error())
		return
	} else if res.Code != 200 {
		log.Errorf("查询商品，美团失败,params:%s,error:%s", utils.JsonEncode(storeProduct), utils.JsonEncode(res))
		return
	}
	var sku []proto.Sku
	err = json.Unmarshal([]byte(res.Data.Skus), &sku)
	if err != nil {
		log.Errorf("查询商品，解析异常,params:%s,error:%s", utils.JsonEncode(req), err.Error())
		return
	}

}
