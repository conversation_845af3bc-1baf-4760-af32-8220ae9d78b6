package async

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	product_service "eShop/services/product-service/services"
	viewmode "eShop/view-model"
	omnibus_vo "eShop/view-model/omnibus-vo"
	vo "eShop/view-model/product-vo"
	"encoding/json"
	"fmt"
	"time"

	"github.com/spf13/cast"
)

type ProductDelService struct {
	common.BaseService
}

func (h ProductDelService) OperationFunc(parJson string, org_id int) (out *viewmode.ImportResult, err error) {
	logPrefix := "同步删除商品到第三方===="
	log.Info(logPrefix, "入参：", parJson)
	h.Begin()
	defer h.Close()
	session := h.Engine.NewSession()
	defer session.Close()
	out = new(viewmode.ImportResult)
	//200 表示处理正常，会确认MQ，
	out.Code = 200
	in := omnibus_vo.AsyncProductParam{}
	json.Unmarshal([]byte(parJson), &in)
	if err = json.Unmarshal([]byte(parJson), &in); err != nil {
		out.Message = "解析参数失败"
		return
	}

	// 获取商品信息
	productIds := make([]int, 0)
	productIdsMap := make(map[int]int, 0)
	for _, v := range in.Data {
		if _, ok := productIdsMap[v.ProductId]; !ok {
			productIds = append(productIds, v.ProductId)
			productIdsMap[v.ProductId] = 1
		}
	}

	chainProductMap, err := product_service.GetChainProductData(session, productIds)
	if err != nil {
		log.Error(logPrefix, "获取连锁商品信息失败，err=", err.Error())
		out.Message = "获取连锁商品信息失败"
		return
	}

	// 记录操作结果
	channelSucMap := make(map[int]int)              //map[渠道id]成功个数
	channelFailMap := make(map[int]int)             //map[渠道id]失败个数
	channelMap := make(map[int]int)                 // 本次处理了几个渠道
	resultMap := make(map[string]ProductResultStru) //map[渠道id_店铺id_商品id]

	for _, v := range in.Data {
		k := fmt.Sprintf("%d_%s_%d", v.ChannelId, v.StoreId, v.SkuId)
		channelMap[v.ChannelId] = 0
		result := ProductResultStru{
			ChannelName: common.ChannelIdMap[v.ChannelId],
			ChannelId:   v.ChannelId,
			ProductId:   v.ProductId,
			SkuId:       v.SkuId,
			StoreId:     v.StoreId,
			ProductName: "",
			OperateName: "同步删除商品到" + common.ChannelIdMap[v.ChannelId],
			SucOrFail:   "失败",
		}
		info, ok := chainProductMap[v.ProductId]
		if !ok {
			channelFailMap[v.ChannelId]++
			out.FailNum++
			result.ErrorMsg = "商品信息不存在"
			resultMap[k] = result
			continue
		}
		result.ProductName = info.Product.Name

		switch v.ChannelId {
		case common.ChannelIdMT, common.ChannelIdELM:
			productService := product_service.StoreProductService{}
			req := vo.BatchStoreProductReq{
				SkuIds:     cast.ToString(v.SkuId),
				Type:       6,
				ChannelIds: cast.ToString(v.ChannelId),
				TenantId:   v.StoreId,
			}
			log.Info(logPrefix, "同步删除商品到第三方入参：", utils.JsonEncode(req))
			if _, err = productService.BatchStoreProduct(req); err != nil {
				log.Error(logPrefix, "同步删除商品到第三方失败，err=", err.Error())
				channelFailMap[v.ChannelId]++
				out.FailNum++
				result.ErrorMsg = err.Error()
				resultMap[k] = result
				continue
			}
			channelSucMap[v.ChannelId]++
			out.SuccessNum++
			result.SucOrFail = "成功"
			resultMap[k] = result
		default:
			log.Error(logPrefix, "未知的渠道,数据：", utils.InterfaceToJSON(v))
		}

	}

	log.Info(logPrefix, "操作结果：", utils.InterfaceToJSON(resultMap))
	fileName := fmt.Sprintf("同步删除商品到第三方(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	excelObject, _ := NewExcelObject(fileName)
	out.QiniuUrl, _ = excelObject.WriteExcelAndUpload(resultMap)
	message := ""
	for k := range channelMap {
		message = fmt.Sprintf("%s|%s渠道，处理成功商品数：%d,处理失败商品数：%d|", message, common.ChannelIdMap[k], channelSucMap[k], channelFailMap[k])
	}
	out.Message = message
	out.Code = 200
	return out, nil
}
