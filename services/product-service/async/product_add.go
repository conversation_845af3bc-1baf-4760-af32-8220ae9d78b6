package async

import (
	po "eShop/domain/product-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	proproductstoreinfo "eShop/services/product-service/enum/pro-product-store-info"
	product_service "eShop/services/product-service/services"
	viewmode "eShop/view-model"
	omnibus_vo "eShop/view-model/omnibus-vo"
	vo "eShop/view-model/product-vo"
	"encoding/json"
	"fmt"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"

	"time"
)

type ProductAddService struct {
	common.BaseService
}

// parJson  异步任务的参数JSON数据 【样例，不要删掉这个】
// func (h ProductAddService) OperationFunc(parJson string, org_id int) (*viewmode.ImportResult, error) {
// 	out := new(viewmode.ImportResult)

// 	//200 表示处理正常，会确认MQ，
// 	out.Code = 200
// 	//错误数量 有需要就写，比如批量操作100个商品，有成功有失败，就需要设置这个
// 	out.FailNum = 0
// 	out.SuccessNum = 5
// 	//如果有需要上传的文件，自己处理上传后，把URL写入到下面这个参数
// 	out.QiniuUrl = ""
// 	//错误的时候返回错误信息，成功的时候可以返回成功的一些内容，比如 （美团 成功100个商品 失败5个商品，饿了么成功80个 失败100个）  ，内容不超过1000个字符串
// 	out.Message = ""

//		return out, nil
//	}
type ProductResultStru struct {
	ChannelId      int    `json:"channel_id"`       // 渠道id
	StoreId        string `json:"store_id"`         //店铺id
	ProductId      int    `json:"product_id"`       //商品id
	SkuId          int    `json:"sku_id"`           //skuId
	ChannelName    string `json:"channel_name"`     // 渠道名称
	ProductName    string `json:"product_name"`     //商品名称
	OperateName    string `json:"operate_name"`     //操作名称
	SucOrFail      string `json:"suc_or_fail"`      //操作结果:成功或者失败
	ErrorMsg       string `json:"error_msg"`        //错误原因
	ProductThirdId string `json:"product_third_id"` // 第三方商品id
	SkuThirdId     string `json:"sku_third_id"`     // 第三方skuid
}

type Excel struct {
	F        *excelize.File
	Writer   *excelize.StreamWriter
	NameList []interface{}
	FileName string
}

func NewExcelObject(fileName string) (out Excel, err error) {
	out = Excel{
		F:        excelize.NewFile(),
		FileName: fileName,
	}
	out.Writer, err = out.F.NewStreamWriter("Sheet1")

	return

}

func (e Excel) WriteExcelAndUpload(result map[string]ProductResultStru) (url string, err error) {

	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	nameList := []interface{}{
		"渠道名称", "店铺ID", "商品ID", "skuId", "商品名称", "操作名称", "操作结果", "失败原因",
	}
	if err = e.Writer.SetRow("A1", nameList); err != nil {
		return
	}
	i := 0
	for _, v := range result {
		i++
		axis := fmt.Sprintf("A%d", i+1)
		_ = e.Writer.SetRow(axis, []interface{}{
			v.ChannelName, v.StoreId, v.ProductId, v.SkuId, v.ProductName, v.OperateName, v.SucOrFail, v.ErrorMsg,
		})

	}

	if err = e.Writer.Flush(); err != nil {
		return
	}
	url, err = utils.UploadExcelToQiNiu(e.F, e.FileName)
	if err != nil {
		log.Error("上传文件到七牛云失败，err=", err.Error())
	}
	return
}

func (h ProductAddService) OperationFunc(parJson string, org_id int) (out *viewmode.ImportResult, err error) {
	logPrefix := "同步新增商品到第三方===="
	log.Info(logPrefix, "入参：", parJson)
	h.Begin()
	defer h.Close()
	session := h.Engine.NewSession()
	defer session.Close()
	out = new(viewmode.ImportResult)
	//200 表示处理正常，会确认MQ，
	out.Code = 200
	in := omnibus_vo.AsyncProductParam{}
	if err = json.Unmarshal([]byte(parJson), &in); err != nil {
		out.Message = "解析参数失败"
		return
	}

	// 获取商品信息
	productIds := make([]int, 0)
	productIdsMap := make(map[int]int, 0)
	for _, v := range in.Data {
		if _, ok := productIdsMap[v.ProductId]; !ok {
			productIds = append(productIds, v.ProductId)
			productIdsMap[v.ProductId] = 1
		}
	}

	chainProductMap, err := product_service.GetChainProductData(session, productIds)
	if err != nil {
		log.Error(logPrefix, "获取连锁商品信息失败，err=", err.Error())
		out.Message = "获取连锁商品信息失败"
		return
	}

	// 记录操作结果
	channelSucMap := make(map[int]int)              //map[渠道id]成功个数
	channelFailMap := make(map[int]int)             //map[渠道id]失败个数
	channelMap := make(map[int]int)                 // 本次处理了几个渠道
	resultMap := make(map[string]ProductResultStru) //map[渠道id_店铺id_商品id]
	for _, v := range in.Data {
		k := fmt.Sprintf("%d_%s_%d", v.ChannelId, v.StoreId, v.SkuId)
		channelMap[v.ChannelId] = 0
		result := ProductResultStru{
			ChannelName: common.ChannelIdMap[v.ChannelId],
			ChannelId:   v.ChannelId,
			ProductId:   v.ProductId,
			SkuId:       v.SkuId,
			StoreId:     v.StoreId,
			ProductName: "",
			OperateName: "同步新增商品到" + common.ChannelIdMap[v.ChannelId],
			SucOrFail:   "失败",
		}
		info, ok := chainProductMap[v.ProductId]
		if !ok {
			channelFailMap[v.ChannelId]++
			out.FailNum++
			result.ErrorMsg = "商品信息不存在"
			resultMap[k] = result
			continue
		}
		result.ProductName = info.Product.Name
		where := map[string]interface{}{
			"storeId":   v.StoreId,
			"productId": v.ProductId,
			"channelId": v.ChannelId,
			"skuId":     v.SkuId,
		}

		switch v.ChannelId {
		case common.ChannelIdMT, common.ChannelIdELM:
			productService := product_service.StoreProductService{}
			if _, err = productService.BatchStoreProduct(vo.BatchStoreProductReq{
				SkuIds:     cast.ToString(v.SkuId),
				Type:       1,
				ChannelIds: cast.ToString(v.ChannelId),
				TenantId:   v.StoreId,
			}); err != nil {
				channelFailMap[v.ChannelId]++
				out.FailNum++
				result.ErrorMsg = err.Error()
				resultMap[k] = result
				// 更新铺品表数据
				updateData := po.ProProductStoreInfo{SyncError: err.Error(), Status: proproductstoreinfo.StatusLaunchFailed}
				if err := updateData.UpdateProductStoreInfo(h.Engine, where, "sync_error,status"); err != nil {
					log.Error(logPrefix, "更新铺品表失败,数据："+utils.InterfaceToJSON(where)+",err=", err.Error())
				}

				continue
			}
			channelSucMap[v.ChannelId]++
			out.SuccessNum++
			result.SucOrFail = "成功"

			resultMap[k] = result
			updateData := po.ProProductStoreInfo{Status: proproductstoreinfo.StatusNormal, IsDistribution: proproductstoreinfo.IsDistributionLaunched}
			if err = updateData.UpdateProductStoreInfo(h.Engine, where, "status,is_distribution"); err != nil {
				log.Error(logPrefix, "更新铺品表失败,数据："+utils.InterfaceToJSON(where)+",err=", err.Error())
			}

		default:
			log.Error(logPrefix, "未知的渠道,数据：", utils.InterfaceToJSON(v))
		}

	}
	log.Info(logPrefix, "操作结果：", utils.InterfaceToJSON(resultMap))
	fileName := fmt.Sprintf("同步新增商品到线下门店端(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	excelObject, _ := NewExcelObject(fileName)
	out.QiniuUrl, err = excelObject.WriteExcelAndUpload(resultMap)
	if err != nil {
		out.QiniuUrl = utils.InterfaceToJSON(resultMap)
	}
	message := ""
	for k := range channelMap {
		message = fmt.Sprintf("%s|%s渠道，处理成功商品数：%d,处理失败商品数：%d|", message, common.ChannelIdMap[k], channelSucMap[k], channelFailMap[k])
	}
	out.Message = message
	return out, nil
}
