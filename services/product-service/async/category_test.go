package async

import (
	"eShop/infra/log"
	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestOperationFunc(t *testing.T) {
	log.Init()
	c := CategoryAsync{}

	// 测试用例 1: 正常情况
	parJson := `[{"SyncType": 2, "ChainId": 576534157590153849, "StoreId": "", "CategoryId": "20802", "CategoryName": "前进口猫粮1", "ParentId": 20799, "Sort": 1, "UserId": "572116010632043589", "RoleType": 1}]` // 示例 JSON 数据
	orgID := 6

	result, err := c.OperationFunc(parJson, orgID)

	assert.NoError(t, err)
	assert.Equal(t, 200, result.Code)
	assert.Equal(t, 0, result.FailNum)
	assert.Equal(t, 0, result.SuccessNum)

	// 测试用例 2: JSON 解析错误
	//parJsonInvalid := `invalid json`
	//result, err = c.OperationFunc(parJsonInvalid, orgID)
	//
	//assert.Error(t, err)
	//assert.Contains(t, err.Error(), "解析json参数错误")
	//assert.Equal(t, 200, result.Code) // 仍然返回 200
	//assert.Equal(t, 0, result.FailNum)
	//assert.Equal(t, 0, result.SuccessNum)

	// 测试用例 3: 其他边界情况
	// 可以根据需要添加更多测试用例
}
