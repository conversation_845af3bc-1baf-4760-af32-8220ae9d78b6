package enum

//业务需求里 需要用到的一些常量配置

// 分销佣金提现 税率(配置系统没有配置 ， 则取这个 ，示例： 税率为8%， 则该值为0.08)
const CommissionTaxRate = "0.08"

// 分销佣金提现 税率 （这个是在配置系统里配置 如果税率是8%， 则配置系统里配的值是：0.08）
const WithdrawTaxRate = "distribution-withdraw-tax-rate-%d"

// 分销员提现编号 的前缀
const WithdrawNoPrefix = "w"

// 结算编号 的前缀
const SettlementNoPrefix = "s"

// 电商图片地址域名 %d代表的是电商店铺id
const BBCImgPath = "https://oss.upetmart.com/www/shop/store/goods/%d/"

const JCJOrgId int = 2   // 极宠家主体id
const OrgId int = 3      //福码购主体id(也叫润合云店)
const BLKYOrgId int = 4  // 百林康源主体id （后改名为宠利扫主体id）
const SaasOrgId int = 6  //宠物saas线下门店端 主体id
const PetAiOrgId int = 7 //小闻养宠助手主体id

var OrgMap = map[int]string{
	OrgId:      "润合云店",
	BLKYOrgId:  "百林康源",
	SaasOrgId:  "宠物saas",
	PetAiOrgId: "小闻养宠助手",
}

// 主体数据来源：0-润合云店，1-润合SAAS，2-百林康源，3-宠利扫
const (
	OrgDataSourceDefault int = iota // 润合云店
	OrgDataSourceSaas
	OrgDataSourceBLKY
	OrgDataSourceCLS
)

const (
	WithdrawFlagShop = 1 // 提现维度是店铺， 只有老板可提现
	WithdrawFlagSelf = 2 //提现维度是分销员自己
)

const (
	SyncEsDisGoodsEnum  = 1 //设置分销商品
	SyncEsSetShopEnum   = 2 //设置店铺默认
	SyncEsGoodsShopEnum = 3 //设置店铺默认
)
