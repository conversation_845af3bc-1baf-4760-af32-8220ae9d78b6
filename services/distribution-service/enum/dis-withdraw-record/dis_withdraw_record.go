package diswithdrawrecord

// `type` tinyint not null DEFAULT  0 COMMENT '操作类型：0-初始，1-结算转可提现，2-提现申请时，3-提现申请审核通过，4-提现申请审核驳回',
const (
	TypeInit             = 0 //初始
	TypeSetted           = 1 //结算
	TypeApply            = 2 //申请提现
	TypeAgree            = 3 //提现申请审核通过
	TypeJeject           = 4 //提现申请审核驳回
	TypeDeductCommission = 5 //佣金扣减
	TypeBalance          = 6 //佣金扣减平账
	
)

var TypeMap = map[int]string{
	TypeSetted:           "结算",
	TypeApply:            "申请提现",
	TypeAgree:            "提现申请审核通过",
	TypeJeject:           "提现申请审核驳回",
	TypeDeductCommission: "佣金扣减",
}
