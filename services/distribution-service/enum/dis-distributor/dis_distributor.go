package disdistributor

// `dis_role` int NOT NULL DEFAULT '0' COMMENT '分销员角色 0-初始值 1-老板 2-店员',
// 分销员企业角色 dis_distributor.dis_role
const (
	DisRoleBoss   = 1 //老板
	DisRoleWorker = 2 //店员
	DisRoleDoctor = 3 //医生
)

var DisRoleMap = map[int]string{
	DisRoleBoss:   "老板",
	DisRoleWorker: "店员",
	DisRoleDoctor: "医生",
}

// 分销员状态：  1-启用 2-禁用（已清退） 3-未启用',
const (
	StatusInit    = 0 //初始值
	StatusValid   = 1 //启用
	StatusInvalid = 2 //禁用
	StatusNotOpen = 3 //未启用
)

// 认证状态： 0-初始值， 1-待审核 2-审核通过 3-审核失败
const (
	ApproveStateInit = 0 //初始值 这个值是不使用的。
	ApproveStateWait = 1 //	待审核
	ApproveStatePass = 2 //	审核通过
	ApproveStateFail = 3 //	审核失败
)

const (
	XkucunCompanyBlky = 1
	XkucunCompanySzld = 2
)

var XkucunCompanyMap = map[int]string{
	XkucunCompanyBlky: "北京百林康源",
	XkucunCompanySzld: "深圳利都",
}
