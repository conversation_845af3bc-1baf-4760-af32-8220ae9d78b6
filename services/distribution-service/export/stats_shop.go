package export

import (
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type StatsShopTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.StatsShopPageReq
	writer       *excelize.StreamWriter
	common.BaseService
}

// DataExport 分销员导出
func (e StatsShopTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.StatsShopPageReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 10000

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()
	client := services.StatsShopService{}

	k := 0
	for {
		ret, _, err := client.StatsShopPageList(*e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {
			k++

			axis := fmt.Sprintf("A%d", k+1)

			_ = e.writer.SetRow(axis, []interface{}{
				ret[i].Simple.EnterpriseId,                                      //企业编码
				ret[i].Simple.EnterpriseName,                                    //企业名称
				ret[i].Simple.ShopName,                                          //分销店铺名称
				ret[i].Simple.SalesmanName,                                      //所属业务员
				ret[i].Simple.UpetShopName,                                      //电商店铺
				ret[i].Simple.DisCount,                                          //分销员数量
				ret[i].Simple.TransDisCount,                                     //成交分销员
				ret[i].TotalDistribute.TransCustomerCount,                       //分销成交客户数
				ret[i].TotalDistribute.TransCount,                               //分销成交单数
				utils.Fen2Yuan(ret[i].TotalDistribute.TransAmount),              //分销成交金额
				utils.Fen2Yuan(ret[i].TotalDistribute.TransCustomerPrice),       //分销成交客单价
				ret[i].TotalDistribute.PosterTransCount,                         //分销海报成交单数
				utils.Fen2Yuan(ret[i].TotalDistribute.PosterTransAmount),        //分销海报成交金额
				ret[i].TotalDistribute.LinkTransCount,                           //分销链接成交单数
				utils.Fen2Yuan(ret[i].TotalDistribute.LinkTransAmount),          //分销链接成交金额
				ret[i].TotalDistribute.FanRelationTransCount,                    //粉丝关系成交单数
				utils.Fen2Yuan(ret[i].TotalDistribute.FanRelationTransAmount),   //粉丝关系成交金额
				utils.Fen2Yuan(ret[i].TotalDistribute.Commission),               //分销佣金总额
				utils.Fen2Yuan(ret[i].TotalDistribute.SettledCommission),        //已结佣金
				utils.Fen2Yuan(ret[i].TotalDistribute.UnsettledCommission),      //未结佣金
				utils.Fen2Yuan(ret[i].TotalDistribute.PreTaxAmount),             //提现成功
				ret[i].ProductDistribute.TransCustomerCount,                     //分销成交客户数
				ret[i].ProductDistribute.TransCount,                             //分销成交单数
				utils.Fen2Yuan(ret[i].ProductDistribute.TransAmount),            //分销成交金额
				ret[i].ProductDistribute.TransProductCount,                      //分销商品件数
				utils.Fen2Yuan(ret[i].ProductDistribute.TransCustomerPrice),     //分销成交客单价
				ret[i].ProductDistribute.PosterTransCount,                       //分销海报成交单数
				utils.Fen2Yuan(ret[i].ProductDistribute.PosterTransAmount),      //分销海报成交金额
				ret[i].ProductDistribute.LinkTransCount,                         //分销链接成交单数
				utils.Fen2Yuan(ret[i].ProductDistribute.LinkTransAmount),        //分销链接成交金额
				ret[i].ProductDistribute.FanRelationTransCount,                  //粉丝关系成交单数
				utils.Fen2Yuan(ret[i].ProductDistribute.FanRelationTransAmount), //粉丝关系成交金额
				utils.Fen2Yuan(ret[i].ProductDistribute.Commission),             //分销佣金总额
				utils.Fen2Yuan(ret[i].ProductDistribute.SettledCommission),      //已结佣金
				utils.Fen2Yuan(ret[i].ProductDistribute.UnsettledCommission),    //未结佣金
				ret[i].InsureDistribute.TransCustomerCount,                      //分销成交客户数
				ret[i].InsureDistribute.TransCount,                              //分销成交单数
				utils.Fen2Yuan(ret[i].InsureDistribute.TransAmount),             //分销成交金额
				utils.Fen2Yuan(ret[i].InsureDistribute.TransCustomerPrice),      //分销成交客单价
				ret[i].InsureDistribute.PosterTransCount,                        //分销海报成交单数
				utils.Fen2Yuan(ret[i].InsureDistribute.PosterTransAmount),       //分销海报成交金额
				ret[i].InsureDistribute.LinkTransCount,                          //分销链接成交单数
				utils.Fen2Yuan(ret[i].InsureDistribute.LinkTransAmount),         //分销链接成交金额
				ret[i].InsureDistribute.FanRelationTransCount,                   //粉丝关系成交单数
				utils.Fen2Yuan(ret[i].InsureDistribute.FanRelationTransAmount),  //粉丝关系成交金额
				utils.Fen2Yuan(ret[i].InsureDistribute.Commission),              //分销佣金总额
				utils.Fen2Yuan(ret[i].InsureDistribute.SettledCommission),       //已结佣金
				utils.Fen2Yuan(ret[i].InsureDistribute.UnsettledCommission),     //未结佣金
			})
		}
		if len(ret) < int(e.ExportParams.PageSize) {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return

}

// SetSheetName 分销员导出列表头
func (e StatsShopTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"企业编码", "企业名称", "分销店铺名称", "所属业务员", "电商店铺", "分销员数量", "成交分销员", "分销成交客户数", "分销成交单数", "分销成交金额", "分销成交客单价", "分销海报成交单数", "分销海报成交金额", "分销链接成交单数", "分销链接成交金额", "粉丝关系成交单数", "粉丝关系成交金额", "分销佣金总额", "已结佣金", "未结佣金", "提现成功", "分销成交客户数", "分销成交单数", "分销成交金额", "分销商品件数", "分销成交客单价", "分销海报成交单数", "分销海报成交金额", "分销链接成交单数", "分销链接成交金额", "粉丝关系成交单数", "粉丝关系成交金额", "分销佣金总额", "已结佣金", "未结佣金", "分销成交客户数", "分销成交单数", "分销成交金额", "分销成交客单价", "分销海报成交单数", "分销海报成交金额", "分销链接成交单数", "分销链接成交金额", "粉丝关系成交单数", "粉丝关系成交金额", "分销佣金总额", "已结佣金", "未结佣金",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e StatsShopTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("店铺数据-导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e StatsShopTask) OperationFunc(row []string, orgId int) string {
	return ""
}
