package export

import (
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type StatsSalespersonTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.StatsSalespersonPageReq
	writer       *excelize.StreamWriter
	common.BaseService
}

// DataExport 分销员导出
func (e StatsSalespersonTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.StatsSalespersonPageReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 10000

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()
	client := services.StatsSalespersonService{}

	k := 0
	for {
		ret, _, err := client.StatsSalespersonPage(*e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {
			k++

			axis := fmt.Sprintf("A%d", k+1)

			_ = e.writer.SetRow(axis, []interface{}{
				ret[i].SalesmanId,                           // 业务员ID
				ret[i].SalesmanName,                         // 业务员姓名
				ret[i].Phone,                                // 业务员手机号
				ret[i].Organization,                         // 所属组织
				ret[i].ShopCount,                            // 服务分销店铺
				ret[i].ServiceDistributorCount,              // 服务分销员
				ret[i].TransEnterpriseCount,                 // 成交企业数
				ret[i].TransCount,                           // 分销成交单数
				utils.Fen2Yuan(ret[i].TransAmount),          // 分销成交金额
				utils.Fen2Yuan(ret[i].Commission),           // 分销佣金
				ret[i].ProductEnterpriseCount,               // 分销商品成交企业数
				ret[i].ProductTransCount,                    // 分销商品成交单数
				utils.Fen2Yuan(ret[i].ProductTransAmount),   // 分销商品成交金额
				ret[i].InsuranceEnterpriseCount,             // 分销保险成交企业数
				utils.Fen2Yuan(ret[i].ProductCommission),    // 分销商品佣金
				ret[i].InsuranceTransCount,                  // 分销保险成交单数
				utils.Fen2Yuan(ret[i].InsuranceTransAmount), // 分销保险成交金额
				utils.Fen2Yuan(ret[i].InsuranceCommission),  // 分销保险佣金
			})
		}
		if len(ret) < int(e.ExportParams.PageSize) {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return

}

// SetSheetName 分销员导出列表头
func (e StatsSalespersonTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"业务员ID", "业务员姓名", "业务员手机号", "所属组织", "服务分销店铺", "服务分销员", "成交企业数", "分销成交单数", "分销成交金额", "分销佣金", "分销商品成交企业数", "分销商品成交单数", "分销商品成交金额", "分销保险成交企业数", "分销商品佣金", "分销保险成交单数", "分销保险成交金额", "分销保险佣金",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e StatsSalespersonTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("业务员数据-导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e StatsSalespersonTask) OperationFunc(row []string, orgId int) string {
	return ""
}
