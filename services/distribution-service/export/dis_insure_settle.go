package export

import (
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

type DisInsureSettleTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.InsureSettlePageReq
	writer       *excelize.StreamWriter
	common.BaseService
}

// DataExport 分销员导出
func (e DisInsureSettleTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.InsureSettlePageReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 10000

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()
	client := services.DisInsureSettleService{}

	k := 0
	for {
		ret, _, err := client.InsureSettlePage(*e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {
			k++

			// 结算状态：1-待结算，2-已结算
			stateStr := "待结算"
			if ret[i].State == 2 {
				stateStr = "已结算"
			}

			axis := fmt.Sprintf("A%d", k+1)
			//"结算编号", "结算状态", "结算时间", "订单号", "保单号", "下单时间", "保险起期", "保险止期", "支付金额（元）", "退款金额（元）", "保险名称", "佣金比例", "分销佣金（元）", "分销员", "分销员Id", "线下企业", "企业编码",
			_ = e.writer.SetRow(axis, []interface{}{
				ret[i].Id,              // 结算编号
				stateStr,               // 结算状态
				ret[i].SettleTime,      // 结算时间
				ret[i].OrderNo,         // 订单号
				ret[i].PolicyNo,        // 保单号
				ret[i].CreateTime,      // 下单时间
				ret[i].InsureBeginDate, // 保险起期
				ret[i].InsureEndDate,   // 保险止期
				utils.Fen2Yuan(cast.ToInt32(ret[i].PayPrice)),    // 支付金额（元）
				utils.Fen2Yuan(cast.ToInt32(ret[i].RefundPrice)), // 退款金额（元）
				ret[i].ProductName,                    // 保险名称
				fmt.Sprintf("%.2f%%", ret[i].DisRate), // 佣金比例
				utils.Fen2Yuan(ret[i].DisAmount),      // 分销佣金（元）
				ret[i].DisName,                        // 分销员
				ret[i].DisId,                          // 分销员Id
				ret[i].EnterpriseName,                 // 线下企业
				ret[i].Code,                           // 企业编码
			})
		}
		if len(ret) < e.ExportParams.PageSize {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return

}

// SetSheetName 分销员导出列表头
func (e DisInsureSettleTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"结算编号", "结算状态", "结算时间", "订单号", "保单号", "下单时间", "保险起期", "保险止期", "支付金额（元）", "退款金额（元）", "保险名称", "佣金比例", "分销佣金（元）", "分销员", "分销员Id", "线下企业", "企业编码",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e DisInsureSettleTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("保险分销结算导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e DisInsureSettleTask) OperationFunc(row []string, orgId int) string {
	return ""
}
