package export

import (
	"eShop/infra/cache"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

type DisGoodsTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.GoodsPageReq
	writer       *excelize.StreamWriter
	common.BaseService
}

// DataExport 分销商品导出
func (e DisGoodsTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.GoodsPageReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 10000

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName(e.ExportParams.OrgId)
	client := services.DisGoodsService{}

	k := 0
	for {
		ret, _, err := client.GoodsPage(*e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {
			k++

			// 是否分销：0-否，1-是
			isDis := "否"
			if ret[i].IsDis == 1 {
				isDis = "是"
			}

			// 是否为默认佣金设置：0-否，1-是
			isDefaultCommisRate := "自定义佣金设置"
			if ret[i].IsDefaultCommisRate == 1 {
				isDefaultCommisRate = "默认佣金比例"
			}

			// 商品状态 0下架，1正常，10违规（禁售）
			goodsState := ""
			if ret[i].GoodsState == 0 {
				goodsState = "仓库中"
			} else if ret[i].GoodsState == 1 {
				goodsState = "出售中"
			}

			axis := fmt.Sprintf("A%d", k+1)
			//"SPU", "SKU", "商品名称", "商品规格", "所属店铺", "商品价格", "商品分类", "是否分销", "佣金设置", "佣金比例（%）", "更新时间",
			d := []interface{}{
				ret[i].GoodsCommonid,        //SPU
				ret[i].GoodsId,              //SKU
				ret[i].GoodsName,            //商品名称
				ret[i].GoodsSpec,            //商品规格
				enum.OrgMap[ret[i].StoreId], //所属店铺
				ret[i].GoodsPrice,           //商品价格
				ret[i].GcName,               //商品分类
				isDis,                       //是否分销
				isDefaultCommisRate,         //佣金设置
				ret[i].DisCommisRate,        //佣金比例（%）
				goodsState,                  //上架状态
				ret[i].UpdateTime,           //更新时间
			}
			if e.ExportParams.OrgId == enum.BLKYOrgId {
				d = []interface{}{
					ret[i].GoodsCommonid,        //SPU
					ret[i].GoodsId,              //SKU
					ret[i].GoodsSerial,          //百林sku
					ret[i].GoodsName,            //商品名称
					ret[i].GoodsSpec,            //商品规格
					enum.OrgMap[ret[i].StoreId], //所属店铺
					ret[i].GoodsPrice,           //商品价格
					ret[i].GcName,               //商品分类
					isDis,                       //是否分销
					isDefaultCommisRate,         //佣金设置
					ret[i].DisCommisRate,        //佣金比例（%）
					ret[i].UpdateTime,           //更新时间
				}
			}
			_ = e.writer.SetRow(axis, d)
		}
		if len(ret) < int(e.ExportParams.PageSize) {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return

}

// SetSheetName 分销员导出列表头
func (e DisGoodsTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"SPU", "SKU", "商品名称", "商品规格", "所属店铺", "商品价格", "商品分类", "是否分销", "佣金设置", "佣金比例（%）", "上架状态", "更新时间",
	}

	if len(args) > 0 && args[0] == enum.BLKYOrgId {
		nameList = []interface{}{
			"SPU", "SKU", "百林sku", "商品名称", "商品规格", "所属店铺", "商品价格", "商品分类", "是否分销", "佣金设置", "佣金比例（%）", "更新时间",
		}
	}
	_ = e.writer.SetRow("A1", nameList)

}

func (e DisGoodsTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("分销商品导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e DisGoodsTask) OperationFunc(row []string, orgId int) string {
	e.Begin()
	defer e.Close()

	// 将子上下文传入Session
	session := e.Session

	//先校验数据
	// 错误信息
	var msg string
	// 新增商品信息

	// 校验，规则： SKU	 默认佣金比例	佣金比例（百分比数字部分，必填，0-90，最多2位小数）	推广素材（非必填）
	// 第一列：SKU(不能为空)
	// 第二列：佣金设置（值要么为：默认佣金比例，要么为 自定义佣金比例 ）
	// 第三列：佣金比例（百分比数字部，0-90，最多2位小数， 注意： 若佣金设置为默认佣金，则佣金比例可不填，自定义佣金比例则佣金比例为必填）
	// 第四列：推广素材（非必填）
	if len(row) < 2 {
		msg = "模板格式错误，请上传正确模板"
		return msg
	}
	if len(row[0]) == 0 {
		msg = "SKU不能为空"
		return msg
	}
	if len(row[1]) == 0 {
		msg = "佣金设置有误"
		return msg
	}

	// 校验，规则：佣金设置只能是：“默认佣金比例”、“自定义佣金比例”
	commisSet := 0
	commisStr := strings.TrimSpace(row[1])
	if commisStr == "默认佣金比例" {
		commisSet = 1
	} else if commisStr == "自定义佣金比例" {
		commisSet = 0
	} else {
		msg = "佣金设置有误"
		return msg
	}

	// 自定义佣金比例校验：1、第三列不能为空，2比例设置0~90之间
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	commisRate := mCache.Get(string(cache_source.EShop), fmt.Sprintf(cachekey.DefaultCommisRate, orgId))
	rate := cast.ToFloat64(commisRate[0].(string))
	if commisSet == 0 {
		if len(row) < 3 {
			msg = "模板格式错误，请上传正确模板"
			return msg
		}
		if len(row[2]) == 0 {
			msg = "佣金比例不能为空"
			return msg
		}
		rate = cast.ToFloat64(row[2])
		if rate < 0 || rate > 90 {
			msg = "佣金比例有误"
			return msg
		}
	}

	var id int
	//数据校验店铺名称和组织名称
	_, err := session.SQL("select id from "+"upetmart.upet_goods where goods_id=? AND store_id=?", row[0], orgId).Get(&id)
	if err != nil || id == 0 {
		msg = "sku不存在"
		return msg
	}

	var in vo.GoodsCommissionSetReq
	in.GoodsId = cast.ToInt(row[0])
	in.IsDefaultCommisRate = commisSet
	in.DisCommisRate = rate
	in.IsDis = 1
	in.OrgId = orgId
	disWriteStr := strings.TrimSpace(row[3]) //推广素材
	in.DisWrite = disWriteStr

	server := services.DisGoodsService{}
	err = server.CommissionSetting(in)
	if err != nil {
		return err.Error()
	}
	return ""
}
