package export

import (
	"eShop/domain/distribution-po"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type DisSalesmanTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.DisSalesmanListReq
	writer       *excelize.StreamWriter
	common.BaseService
}

//// 业务员导入
//func (e *DisSalesmanTask) DataImport(url string, org_id int) (*vo.ImportResult, error) {
//	return ImportFunc(url, org_id, e.SaleManImport)
//}

// 业务员导出
func (e *DisSalesmanTask) DataExport(taskParams string) (success_num int, fail_num int, err error) {
	e.ExportParams = new(vo.DisSalesmanListReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	fail_num = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 10000

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()
	client := services.DisSalesmanService{}

	k := 0
	for {

		ret, _, err := client.GetList(*e.ExportParams)
		//ret, err = o.OrderDeliveryReportList(context.Background(), e.taskParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {
			k++

			status := "启用"
			if ret[i].Status == 0 {
				status = "停用"
			}

			axis := fmt.Sprintf("A%d", k+1)
			//"业务员Id", "业务员姓名", "业务员手机号", "业务员员工编号", "所属组织", "所属店铺", "服务企业数", "服务分销员",拓客分销员数", "状态", "加入时间", "二维码链接"
			_ = e.writer.SetRow(axis, []interface{}{
				ret[i].Id,            // 业务员Id
				ret[i].Name,          // 业务员姓名
				ret[i].Phone,         // 业务员手机号
				ret[i].Code,          //业务员员工编号
				ret[i].OrgName,       //所属组织
				"润合云店",               //所属店铺
				ret[i].ServiceEntNum, //服务企业数
				ret[i].ServiceDisNum, //服务分销员
				ret[i].TuokeDisNum,   //拓客分销员数
				status,               //状态
				ret[i].CreateTime.Format("2006-01-02 15:04:05"), //加入时间
				ret[i].BarCode, //二维码链接
			})
		}
		if len(ret) < int(e.ExportParams.PageSize) {
			break
		}
	}
	success_num = k
	_ = e.writer.Flush()
	return

}

// 业务员导出头
func (e *DisSalesmanTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"业务员Id", "业务员姓名", "业务员手机号", "业务员员工编号", "所属组织", "所属店铺", "服务企业数", "服务分销员", "拓客分销员数", "状态", "加入时间", "二维码链接",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e DisSalesmanTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("业务员导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

// 业务员导入
func (h DisSalesmanTask) OperationFunc(row []string, org_id int) string {
	h.Begin()
	defer h.Close()

	// 将子上下文传入Session
	session := h.Session

	//先校验数据
	// 错误信息
	var msg string
	// 新增商品信息

	var in vo.DisSalesman
	//校验不能为空
	if len(row) < 5 {
		msg = "模板格式错误，请上传正确模板"
		return msg
	}
	if len(row[0]) == 0 {
		msg = "业务员姓名不能为空"
		return msg
	}
	if len(row[1]) == 0 {
		msg = "业务员手机号不能为空"
		return msg
	}
	if len(row[2]) == 0 {
		msg = "业务员员工编号不能为空"
		return msg
	}
	if len(row[3]) == 0 {
		msg = "所属组织不能为空"
		return msg
	}
	if len(row[4]) == 0 {
		msg = "所属店铺不能为空"
		return msg
	}
	//校验手机格式
	if !utils.IsValidPhoneNumber(row[1]) {
		msg = "手机号格式不正确"
		return msg
	}

	var shopitem distribution_po.Shop
	//数据校验店铺名称和组织名称
	Ishave, err := session.SQL("select id from shop where shop_name=?", row[4]).Get(&shopitem)
	if !Ishave {
		msg = "店铺不存在"
		return msg
	}

	var organizationitem distribution_po.BaseOrganization
	//数据校验店铺名称和组织名称
	Ishave, err = session.SQL("select id from dc_oms.base_organization where name=?", row[3]).Get(&organizationitem)
	if !Ishave {
		msg = "组织不存在"
		return msg
	}
	in.Name = row[0]
	in.Mobile = row[1]
	in.EmployeeNo = row[2]
	in.RegionId = organizationitem.Id
	in.ShopId = shopitem.Id
	in.OrgId = org_id

	server := services.DisSalesmanService{}
	err = server.SaleManAdd(in)

	if err != nil {
		return err.Error()
	}
	return ""
}
