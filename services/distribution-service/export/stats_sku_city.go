package export

import (
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type StatsSkuCityTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.StatsSkuCityDailyPageReq
	writer       *excelize.StreamWriter
	common.BaseService
}

// DataExport 分销员导出
func (e StatsSkuCityTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.StatsSkuCityDailyPageReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 10000

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()
	client := services.StatsSkuCityDailyService{}

	k := 0
	for {
		ret, _, err := client.GetStatsSkuCityDailyBySkuid(*e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {
			k++
			axis := fmt.Sprintf("A%d", k+1)
			_ = e.writer.SetRow(axis, []interface{}{
				e.ExportParams.StartDate,              // 开始时间
				e.ExportParams.EndDate,                // 结束时间
				ret[i].Skuid,                          // 商品skuId
				ret[i].ProductName,                    // 商品名称
				ret[i].Province,                       // 省份
				ret[i].City,                           // 城市
				ret[i].OrderCustomerCount,             // 下单客户数
				ret[i].OrderQuantity,                  // 下单件数
				utils.Fen2Yuan(ret[i].OrderAmount),    // 下单金额
				ret[i].DealCustomerCount,              // 成交客户数
				ret[i].DealQuantity,                   // 成交件数
				utils.Fen2Yuan(ret[i].DealAmount),     // 成交金额
				ret[i].DistDealCustomerCount,          // 分销成交客户数
				ret[i].DistDealQuantity,               // 分销成交件数
				utils.Fen2Yuan(ret[i].DistDealAmount), // 分销成交金额
			})
		}
		if len(ret) < int(e.ExportParams.PageSize) {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return

}

// SetSheetName 分销员导出列表头
func (e StatsSkuCityTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"开始时间", "结束时间", "商品skuId", "商品名称", "省份", "城市", "下单客户数", "下单件数", "下单金额", "成交客户数", "成交件数", "成交金额", "分销成交客户数", "分销成交件数", "分销成交金额",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e StatsSkuCityTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("商品下单区域分析-%s%d.xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e StatsSkuCityTask) OperationFunc(row []string, orgId int) string {
	return ""
}
