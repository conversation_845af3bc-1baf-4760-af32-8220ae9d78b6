package export

import (
	po "eShop/domain/distribution-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
	diswithdraw "eShop/services/distribution-service/enum/dis-withdraw"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

type DisWithdrawTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.GetDisWithdrawListReq
	writer       *excelize.StreamWriter
	common.BaseService
}

// 提现导出
func (e *DisWithdrawTask) DataExport(taskParams string) (success_num int, fail_num int, err error) {
	logPrefix := fmt.Sprintf("提现导出, taskParams: %s", taskParams)
	log.Info(logPrefix)
	e.ExportParams = new(vo.GetDisWithdrawListReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		log.Error(logPrefix, "json解析错误, ", err.Error(), ", json：", taskParams)
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	fail_num = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 10000

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		log.Error(logPrefix, "生成文件失败, ", err.Error())
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName(e.ExportParams.OrgId, e.ExportParams.ExportType)
	client := services.DisWithdrawService{}

	k := 0
	for {

		ret, _, err := client.GetDisWithdrawList(*e.ExportParams)

		if err != nil {
			log.Error(logPrefix, "获取导出数据失败, ", err.Error())
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {
			k++

			axis := fmt.Sprintf("A%d", k+1)
			//提现ID	提现编号	提现状态	分销员姓名 分销员Id 分销员手机号 收款人手机号 绑定线下企业 企业R1编码 企业角色 税前提现金额（元）税后提现金额（元）代缴个税金额（元）收款银行	收款账号	开户姓名	收款银行支行 申请时间 支付时间	更新时间
			d := []interface{}{
				ret[i].Id,
				ret[i].WithdrawNo,
				ret[i].StatusText,
				ret[i].DistributorName,
				ret[i].DistributorId,
				utils.MobileDecrypt(ret[i].EncryptMobile),
				ret[i].EnterpriseName,
				ret[i].EnterpriseId,
				ret[i].DisRoleText,
				fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].PreTaxAmount)),
				fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].AfterTaxAmount)),
				fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].Tax)),
				ret[i].BankName,
				utils.MobileDecrypt(ret[i].EncryptBankAccount),
				utils.MobileDecrypt(ret[i].EncryptIdCard),
				ret[i].AccountName,
				utils.MobileDecrypt(ret[i].BankEncryptMobile),
				ret[i].BankBranch,
				ret[i].CreateTime,
				ret[i].PayTime,
				ret[i].UpdateTime,
			}
			if e.ExportParams.OrgId == enum.BLKYOrgId && e.ExportParams.ExportType == 0 {
				d = []interface{}{
					ret[i].Id,
					ret[i].WithdrawNo,
					ret[i].StatusText,
					ret[i].DistributorName,
					ret[i].DistributorId,
					ret[i].EnterpriseName,
					ret[i].DisRoleText,
					fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].PreTaxAmount)),
					fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].AfterTaxAmount)),
					fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].Tax)),
					ret[i].BankName,
					utils.MobileDecrypt(ret[i].EncryptBankAccount),
					utils.MobileDecrypt(ret[i].EncryptIdCard),
					utils.MobileDecrypt(ret[i].BankEncryptMobile),
					ret[i].AccountName,
					ret[i].BankBranch,
					ret[i].ShopName,
					ret[i].CreateTime,
					ret[i].PayTime,
					ret[i].UpdateTime,
				}
			}
			if e.ExportParams.OrgId == enum.BLKYOrgId && e.ExportParams.ExportType == 1 {
				comm := ret[i].OrderCommission
				if ret[i].SwlmCpsId > 0 {
					comm = -ret[i].OrderCommission
				} else if ret[i].DisRoleText == "医生" {
					comm = ret[i].PreTaxAmount
				}
				d = []interface{}{
					ret[i].Id,
					ret[i].WithdrawNo,
					ret[i].StatusText,
					ret[i].DistributorName,
					ret[i].DistributorId,
					ret[i].EnterpriseName,
					ret[i].DisRoleText,
					ret[i].OrderSn,
					ret[i].GoodsName,
					fmt.Sprintf("%.2f", utils.Fen2Yuan(comm)),
					ret[i].OrgTypeName,
					ret[i].BankName,
					utils.MobileDecrypt(ret[i].EncryptBankAccount),
					utils.MobileDecrypt(ret[i].EncryptIdCard),
					utils.MobileDecrypt(ret[i].BankEncryptMobile),
					ret[i].AccountName,
					ret[i].BankBranch,
					ret[i].ShopName,
					ret[i].CreateTime,
					ret[i].PayTime,
					ret[i].UpdateTime,
				}
			}
			_ = e.writer.SetRow(axis, d)
		}

		if len(ret) < int(e.ExportParams.PageSize) {
			break
		}
	}
	success_num = k
	_ = e.writer.Flush()
	return

}

// 提现导出头
func (e *DisWithdrawTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"提现ID", "提现编号", "提现状态", "分销员姓名", "分销员Id", "分销员手机号", "绑定线下企业", "企业R1编码", "企业角色", "税前提现金额（元）", "税后提现金额（元）", "代缴个税金额（元）", "收款银行", "收款账号", "身份证号", "开户姓名", "收款人手机号", "收款银行支行", "申请时间", "支付时间", "更新时间",
	}

	if len(args) == 2 && args[0] == enum.BLKYOrgId && args[1] == 0 {
		nameList = []interface{}{
			"提现ID", "提现编号", "提现状态", "分销员姓名", "分销员Id", "绑定的线下企业", "提现人角色", "税前提现金额（元）", "税后提现金额（元）", "代缴个税金额（元）", "收款银行", "收款账号", "身份证号", "收款人手机号", "开户姓名", "收款银行支行", "所属店铺", "申请时间", "支付时间", "更新时间",
		}
	} else if len(args) == 2 && args[0] == enum.BLKYOrgId && args[1] == 1 { // 宠利扫 导出导出提现订单数据（含商品明细）
		nameList = []interface{}{
			"提现ID", "提现编号", "提现状态", "分销员姓名", "分销员Id", "绑定的线下企业", "提现人角色", "分销订单", "商品名称", "税前提现金额（元）", "码包所属企业", "收款银行", "收款账号", "身份证号", "收款人手机号", "开户姓名", "收款银行支行", "所属店铺", "申请时间", "支付时间", "更新时间",
		}
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e DisWithdrawTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("提现导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

// 提现导入 msg为返回错误信息
func (h DisWithdrawTask) OperationFunc(row []string, org_id int) (msg string) {
	h.Begin()
	defer h.Close()

	//先校验数据
	var withdraw po.DisWithdraw
	//校验不能为空
	if len(row) < 2 {
		msg = "模板格式错误，请上传正确模板"
		return msg
	}
	if len(row[0]) == 0 {
		msg = "提现ID不能为空"
		return
	}
	if len(row[1]) == 0 {
		msg = "提现编号不能为空"
		return
	}
	exists, err := h.Session.ID(row[0]).Where("withdraw_no=?", row[1]).Get(&withdraw)
	if err != nil {
		msg = "获取提现信息失败" + err.Error()
		return
	}
	if !exists {
		msg = "未找到该提现信息"
		return
	}

	if withdraw.Status != diswithdraw.StatusUncheck {
		msg = "提现状态不是未打款"
		return
	}

	tmp := services.DisWithdrawService{}
	if err := tmp.DisWithdrawCheck(vo.DisWithdrawCheckReq{
		DisWithdrawId: cast.ToInt(row[0]),
		CheckType:     1,
	}); err != nil {
		msg = err.Error()
		return
	}

	// withdraw.Status = diswithdraw.StatusChecked
	// withdraw.PayTime = time.Now()
	// session := h.Engine.NewSession()
	// session.Begin()
	// if affect, err := session.ID(row[0]).Cols("status", "pay_time").Where("status=?", diswithdraw.StatusUncheck).Update(&withdraw); err != nil {
	// 	msg = "更新提现状态失败" + err.Error()
	// 	session.Rollback()
	// 	return
	// } else if affect == 0 {
	// 	msg = "更新提现状态影响条数为0条"
	// 	session.Rollback()
	// 	return
	// }

	// updateSql := `update eshop.dis_distributor_total set withdraw_apply=withdraw_apply-?,withdraw_success=withdraw_success+? where dis_id =? and org_id=? and withdraw_apply >=?`

	// reslt, err := session.Exec(updateSql, withdraw.PreTaxAmount, withdraw.PreTaxAmount, withdraw.DistributorId, withdraw.OrgId, withdraw.PreTaxAmount)
	// if err != nil {
	// 	msg = "更新提现中和成功提现金额失败" + err.Error()
	// 	session.Rollback()
	// 	return

	// }
	// RowsAffected, _ := reslt.RowsAffected()
	// if RowsAffected == 0 { //影响条数为0， 说明提现中金额不够减， 这时也回滚
	// 	session.Rollback()
	// 	msg = "更新提现中和成功提现金额失败,影响条数为0条"
	// 	return
	// }
	// session.Commit()

	return
}
