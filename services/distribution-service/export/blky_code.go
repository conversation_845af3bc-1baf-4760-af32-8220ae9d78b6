package export

import (
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"fmt"
	"regexp"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

type BlkyCodeTask struct {
	F          *excelize.File
	SheetName  string
	ImportType int
	OrgType    int
	common.BaseService
}

func (e BlkyCodeTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	return
}

func (e BlkyCodeTask) SetSheetName(args ...interface{}) {}

func (e BlkyCodeTask) GenerateDownUrl() (url string, err error) {
	return
}

func (e BlkyCodeTask) OperationFunc(row []string, orgId int) string {
	e.Begin()
	defer e.Close()
	// 将子上下文传入Session
	session := e.Session
	// 错误信息
	var msg string
	// 第一列：箱码/物流码(不能为空)
	// 第二列：佣金比例（百分比数字部，0-1，最多2位小数， 注意： 若佣金设置为默认佣金，则佣金比例可不填，自定义佣金比例则佣金比例为必填）
	if len(row) < 2 {
		msg = "模板格式错误，请上传正确模板"
		return msg
	}
	if len(row[0]) == 0 {
		msg = "箱码、物流码id必传"
		return msg
	}
	//1-百林康源12位 2-深圳利都11位
	if e.OrgType == 1 {
		re := regexp.MustCompile(`^\d{12}$`)
		if !re.MatchString(row[0]) {
			return "物流码格式错误"
		}
	} else {
		re := regexp.MustCompile(`^\d{11}$`)
		if !re.MatchString(row[0]) {
			return "物流码格式错误"
		}
	}

	if len(row[1]) == 0 {
		msg = "佣金格式输入错误"
		return msg
	}

	rate := cast.ToFloat64(row[1])
	if rate < 0 || rate > 1 {
		return "佣金格式输入错误"
	}
	if rate == 1 {
		return "佣金比例需大于等于0，且小于1"
	}
	// 查iid、sdxtm
	type Xkucun struct {
		Iid   int
		Sdxtm string
	}
	var xkucunData Xkucun
	if e.OrgType == 1 {
		_, err := session.SQL(`select iid,sdxtm from blky.xkucun where swlm=?`, row[0]).Get(&xkucunData)
		if err != nil {
			msg = fmt.Sprintf("物流码id不存在,err:%s", err.Error())
			return msg
		}
		if xkucunData.Iid == 0 {
			msg = "物流码id不存在"
			return msg
		}
	} else {
		_, err := session.SQL(`select id as iid,source_barcode as sdxtm from blky.sync_outbound_log where barcode=?`, row[0]).Get(&xkucunData)
		if err != nil {
			msg = fmt.Sprintf("物流码id不存在,err:%s", err.Error())
			return msg
		}
		if xkucunData.Iid == 0 {
			msg = "物流码id不存在"
			return msg
		}
	}

	var in vo.SwlmImportReq
	in.Code = row[0]
	in.CommissionRate = cast.ToFloat64(row[1])
	in.Type = e.ImportType
	in.Sdxtm = xkucunData.Sdxtm
	in.OrgType = e.OrgType

	err := new(services.BlkyCodeService).BatchSwlmImport(in)
	if err != nil {
		return err.Error()
	}

	return ""
}
