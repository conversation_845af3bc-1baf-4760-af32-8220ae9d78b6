package services

import (
	"eShop/domain/distribution-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/distribution-service/enum"
	dissettlement "eShop/services/distribution-service/enum/dis-settlement"
	diswithdraw "eShop/services/distribution-service/enum/dis-withdraw"
	"eShop/services/distribution-service/enum/stats"
	vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"sync"

	"xorm.io/xorm"
)

// 获取商品分销订单的佣金、待结佣金和已结佣金字段
func DealGoodsCommission(db *xorm.Engine, where map[string]interface{}) (dataMap map[string]Settlement, err error) {
	dataMap = make(map[string]Settlement)
	statDate, ok := where["statDate"]
	if !ok {
		err = errors.New("统计开始日期不能为空")
		return
	}
	endDate, ok := where["endDate"]
	if !ok {
		err = errors.New("统计结束日期不能为空")
		return
	}
	start := statDate.(string)
	end := endDate.(string)
	// 商品订单分销佣金
	w1 := map[string]interface{}{"orgId": 3, "groupBy": "a.distributor_id ,a.shop_id", "createTimeStart": start, "createTimeEnd": end}
	data1, err := distribution_po.FindSettCommission(db, w1)
	if err != nil {
		return
	}
	for _, v := range data1 {
		if tmp, ok := dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisOrderType)]; !ok {
			dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisOrderType)] = Settlement{
				ShopId:         v.ShopId,
				DisId:          v.DisId,
				Type:           stats.StatDisOrderType, // 类型（1:商品订单，2:商品分销订单，3:保险订单，4:保险分销订单）
				ShopName:       v.ShopName,             // 分销店铺名称
				EnterpriseName: v.EnterpriseName,       //企业名称
				EnterpriseId:   v.EnterpriseId,         // 企业Id
				IsDis:          1,
				StatDate:       start,
				EndDate:        end,
				Commission:     v.Commission,
			}
		} else {
			tmp.Commission = v.Commission
			dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisOrderType)] = tmp
		}

	}

	// 商品订单未结佣金 (产品说：不用统计未结佣金)
	// w2 := map[string]interface{}{"orgId": 3, "status": dissettlement.StatusUnSett, "groupBy": "a.distributor_id ,a.shop_id", "createTimeStart": start, "createTimeEnd": end}
	// data2, err := po.FindSettCommission(db, w2)
	// if err != nil {
	// 	return
	// }

	// for _, v := range data2 {
	// 	if tmp, ok := dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisOrderType)]; !ok {
	// 		dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisOrderType)] = Settlement{
	// 			ShopId:              v.ShopId,
	// 			DisId:               v.DisId,
	// 			Type:                stats.StatDisOrderType, // 类型（1:商品订单，2:商品分销订单，3:保险订单，4:保险分销订单）
	// 			ShopName:            v.ShopName,             // 分销店铺名称
	// 			EnterpriseName:      v.EnterpriseName,       //企业名称
	// 			EnterpriseId:        v.EnterpriseId,         // 企业Id
	// 			IsDis:               1,
	// 			StatDate:            start,
	// 			EndDate:             end,
	// 			UnsettledCommission: v.Commission,
	// 		}
	// 	} else {
	// 		tmp.UnsettledCommission = v.Commission
	// 		dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisOrderType)] = tmp
	// 	}
	// }
	//商品订单已结佣金
	w3 := map[string]interface{}{"orgId": 3, "status": dissettlement.StatusSetted, "groupBy": "a.distributor_id ,a.shop_id", "settlementTimeStart": start, "settlementTimeEnd": end}
	data3, err := distribution_po.FindSettCommission(db, w3)
	if err != nil {
		return
	}
	for _, v := range data3 {
		if tmp, ok := dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisOrderType)]; !ok {
			dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisOrderType)] = Settlement{
				ShopId:            v.ShopId,
				DisId:             v.DisId,
				Type:              stats.StatDisOrderType, // 类型（1:商品订单，2:商品分销订单，3:保险订单，4:保险分销订单）
				ShopName:          v.ShopName,             // 分销店铺名称
				EnterpriseName:    v.EnterpriseName,       //企业名称
				EnterpriseId:      v.EnterpriseId,         // 企业Id
				IsDis:             1,
				StatDate:          start,
				EndDate:           end,
				SettledCommission: v.Commission,
			}
		} else {
			tmp.SettledCommission = v.Commission
			dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisOrderType)] = tmp
		}

	}
	return
}

// 获取保险分销订单的佣金、待结佣金和已结佣金字段
func DealInsCommission(db *xorm.Engine, where map[string]interface{}) (dataMap map[string]Settlement, err error) {
	dataMap = make(map[string]Settlement)
	statDate, ok := where["statDate"]
	if !ok {
		err = errors.New("统计开始日期不能为空")
		return
	}
	start := statDate.(string)
	endDate, ok := where["endDate"]
	if !ok {
		err = errors.New("统计结束日期不能为空")
		return
	}
	end := endDate.(string) + " 23:59:59"
	// 保险订单分销佣金
	w1 := map[string]interface{}{"orgId": 3, "groupBy": "a.dis_id ,a.shop_id", "createTimeStart": start, "createTimeEnd": end}
	data1, err := distribution_po.FindInsSettCommission(db, w1)
	if err != nil {
		return
	}
	for _, v := range data1 {
		if tmp, ok := dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisInsOrderType)]; !ok {
			dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisInsOrderType)] = Settlement{
				ShopId:         v.ShopId,
				DisId:          v.DisId,
				Type:           stats.StatDisInsOrderType, // 类型（1:商品订单，2:商品分销订单，3:保险订单，4:保险分销订单）
				ShopName:       v.ShopName,                // 分销店铺名称
				EnterpriseName: v.EnterpriseName,          //企业名称
				EnterpriseId:   v.EnterpriseId,            // 企业Id
				IsDis:          1,
				StatDate:       start,
				EndDate:        endDate.(string),
				Commission:     v.Commission,
			}
		} else {
			tmp.Commission = v.Commission
			dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisInsOrderType)] = tmp
		}

	}

	// // 保险订单未结佣金
	// w2 := map[string]interface{}{"orgId": 3, "state": dissettlement.StatusUnSett, "groupBy": "a.dis_id ,a.shop_id", "createTimeStart": start, "createTimeEnd": end}
	// data2, err := po.FindInsSettCommission(db, w2)
	// if err != nil {
	// 	return
	// }

	// for _, v := range data2 {
	// 	if tmp, ok := dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisInsOrderType)]; !ok {
	// 		dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisInsOrderType)] = Settlement{
	// 			ShopId:              v.ShopId,
	// 			DisId:               v.DisId,
	// 			Type:                stats.StatDisInsOrderType, // 类型（1:商品订单，2:商品分销订单，3:保险订单，4:保险分销订单）
	// 			ShopName:            v.ShopName,                // 分销店铺名称
	// 			EnterpriseName:      v.EnterpriseName,          //企业名称
	// 			EnterpriseId:        v.EnterpriseId,            // 企业Id
	// 			IsDis:               1,
	// 			StatDate:            start,
	// 			EndDate:             endDate.(string),
	// 			UnsettledCommission: v.Commission,
	// 		}
	// 	} else {
	// 		tmp.UnsettledCommission = v.Commission
	// 		dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisInsOrderType)] = tmp
	// 	}
	// }
	//保险订单已结佣金
	w3 := map[string]interface{}{"orgId": 3, "state": dissettlement.StatusSetted, "groupBy": "a.dis_id ,a.shop_id", "settleTimeStart": start, "settleTimeEnd": end}
	data3, err := distribution_po.FindInsSettCommission(db, w3)
	if err != nil {
		return
	}
	for _, v := range data3 {
		if tmp, ok := dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisInsOrderType)]; !ok {
			dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisInsOrderType)] = Settlement{
				ShopId:            v.ShopId,
				DisId:             v.DisId,
				Type:              stats.StatDisInsOrderType, // 类型（1:商品订单，2:商品分销订单，3:保险订单，4:保险分销订单）
				ShopName:          v.ShopName,                // 分销店铺名称
				EnterpriseName:    v.EnterpriseName,          //企业名称
				EnterpriseId:      v.EnterpriseId,            // 企业Id
				IsDis:             1,
				StatDate:          start,
				EndDate:           end,
				SettledCommission: v.Commission,
			}
		} else {
			tmp.SettledCommission = v.Commission
			dataMap[fmt.Sprintf("%d_%d_%d", v.ShopId, v.DisId, stats.StatDisInsOrderType)] = tmp
		}

	}
	return
}

// 获取 stats_shop_distributor_daily 累计数据 新增数据 上一周期数据
func GetStatsShopDistributorDaily(db *xorm.Engine, where map[string]interface{}) (totalData, curData, lastData distribution_po.StatsShopDistributorDaily, err error) {
	results := make(chan string, 3)
	logPrefix := fmt.Sprintf("GetStatsShopDistributorDaily====入参：%s", utils.InterfaceToJSON(where))
	log.Info(logPrefix)
	lastS, lastE, err := utils.GetLastCycleDate(where["startDate"].(string), where["endDate"].(string))
	if err != nil {
		return
	}
	w1 := make(map[string]interface{})
	w2 := map[string]interface{}{
		"statDateStart": where["startDate"],
		"statDateEnd":   where["endDate"],
	}
	w3 := map[string]interface{}{
		"statDateStart": lastS.Format(utils.DateLayout),
		"statDateEnd":   lastE.Format(utils.DateLayout),
	}
	log.Info(fmt.Sprintf("GetStatsShopDistributorDaily获取数据：当前时间：%s-%s,上一周期时间：%s-%s", where["startDate"].(string), where["endDate"].(string), lastS.Format(utils.DateLayout), lastE.Format(utils.DateLayout)))
	if v, ok := where["types"]; ok {
		w1["types"] = v
		w2["types"] = v
		w3["types"] = v
	}
	if v, ok := where["shopId"]; ok {
		w1["shopId"] = v
		w2["shopId"] = v
		w3["shopId"] = v
	}
	if v, ok := where["disId"]; ok {
		w1["disId"] = v
		w2["disId"] = v
		w3["disId"] = v
	}
	if v, ok := where["isDis"]; ok {
		w1["isDis"] = v
		w2["isDis"] = v
		w3["isDis"] = v
	}
	//区分统计商品订单与保险订单的客户数统计
	if v, ok := where["type"]; ok {
		w1["type"] = v
		w2["type"] = v
		w3["type"] = v
	}
	var waitGroup sync.WaitGroup
	waitGroup.Add(3)
	go func() {
		if totalData, err = distribution_po.GetShopDistributorData(db, w1); err != nil {
			results <- "获取累计数据失败" + err.Error()
		}
		waitGroup.Done()
	}()

	go func() {
		if curData, err = distribution_po.GetShopDistributorData(db, w2); err != nil {
			results <- "获取新增数据失败" + err.Error()
		}
		waitGroup.Done()
	}()

	go func() {
		if lastData, err = distribution_po.GetShopDistributorData(db, w3); err != nil {
			results <- "获取上一周期数据失败" + err.Error()

		}
		waitGroup.Done()
	}()
	waitGroup.Wait()
	close(results)

	for r := range results {
		if len(r) > 0 {
			err = errors.New(r)
			return
		}

	}
	return
}

func GetDisWithdraw(db *xorm.Engine, where map[string]interface{}) (totalData, curData, lastData int, err error) {
	logPrefix := "获取提现数据====" + utils.InterfaceToJSON(where)
	log.Info(logPrefix)
	var waitGroup sync.WaitGroup
	results := make(chan string, 3)
	lastS, lastE, err := utils.GetLastCycleDate(where["startDate"].(string), where["endDate"].(string))
	if err != nil {
		return
	}
	w1 := map[string]interface{}{"orgId": where["orgId"]}
	w2 := map[string]interface{}{"orgId": where["orgId"]}
	w3 := map[string]interface{}{"orgId": where["orgId"]}
	if v, ok := where["status"]; ok {
		w1["status"] = v
		w2["status"] = v
		w3["status"] = v
		if v.(int) == diswithdraw.StatusChecked {
			w2["payTimeStart"] = where["startDate"]
			w2["payTimeEnd"] = where["endDate"].(string) + " 23:59:59"
			w3["payTimeStart"] = lastS.Format(utils.DateLayout)
			w3["payTimeEnd"] = lastE.Format(utils.DateLayout) + " 23:59:59"
		} else if v.(int) == diswithdraw.StatusUncheck {
			w2["createTimeStart"] = where["startDate"]
			w2["createTimeEnd"] = where["endDate"].(string) + " 23:59:59"
			w3["createTimeStart"] = lastS.Format(utils.DateLayout)
			w3["createTimeEnd"] = lastE.Format(utils.DateLayout) + " 23:59:59"
		}
	}
	waitGroup.Add(3)
	go func() {
		if totalData, err = distribution_po.GetDisWithdrawData(db, w1); err != nil {
			results <- "获取累计提现数据失败" + err.Error()

		}
		waitGroup.Done()
	}()

	go func() {
		if curData, err = distribution_po.GetDisWithdrawData(db, w2); err != nil {
			results <- "获取新增提现数据失败" + err.Error()

		}
		waitGroup.Done()
	}()

	go func() {
		if lastData, err = distribution_po.GetDisWithdrawData(db, w3); err != nil {
			results <- "获取上一周期提现数据失败" + err.Error()

		}
		waitGroup.Done()
	}()
	waitGroup.Wait()
	close(results)

	for r := range results {
		if len(r) > 0 {
			err = errors.New(r)
			return
		}

	}
	return
}

// 获取 电商结算表 ：  累计分销佣金 和 累计待结佣金和累计已结佣金
func GetSettleInfo(db *xorm.Engine) (total, unsett, setted int) {
	if _, err := db.Table("eshop.dis_settlement").Select("sum(commission) as commission").Where("org_id=?", enum.OrgId).Get(&total); err != nil {
		log.Error("获取电商累计分销佣金-累计待结佣金-累计已结佣金失败，err=", err.Error())
		return
	}
	if _, err := db.Table("eshop.dis_settlement").Select("sum(commission) as commission").Where("org_id=?", enum.OrgId).Where("status=?", dissettlement.StatusUnSett).Get(&unsett); err != nil {
		log.Error("获取电商累计分销佣金-累计待结佣金-累计已结佣金失败，err=", err.Error())
		return
	}

	if _, err := db.Table("eshop.dis_settlement").Select("sum(commission) as commission").Where("org_id=?", enum.OrgId).Where("status=?", dissettlement.StatusSetted).Get(&setted); err != nil {
		log.Error("获取电商累计分销佣金-累计待结佣金-累计已结佣金失败，err=", err.Error())
		return
	}
	return

}

// 获取 保险结算表 ：  累计分销佣金 和 累计待结佣金和累计已结佣金
func GetInsSettleInfo(db *xorm.Engine) (total, unsett, setted int) {
	if _, err := db.Table("eshop.dis_insurance_settle").Select("sum(dis_amount) as commission").Where("org_id=?", enum.OrgId).Get(&total); err != nil {
		log.Error("获取保险累计分销佣金-累计待结佣金-累计已结佣金失败，err=", err.Error())
		return
	}
	if _, err := db.Table("eshop.dis_insurance_settle").Select("sum(dis_amount) as commission").Where("org_id=?", enum.OrgId).Where("state=?", dissettlement.StatusUnSett).Get(&unsett); err != nil {
		log.Error("获取保险累计分销佣金-累计待结佣金-累计已结佣金失败，err=", err.Error())
		return
	}

	if _, err := db.Table("eshop.dis_insurance_settle").Select("sum(dis_amount) as commission").Where("org_id=?", enum.OrgId).Where("state=?", dissettlement.StatusSetted).Get(&setted); err != nil {
		log.Error("获取保险累计分销佣金-累计待结佣金-累计已结佣金失败，err=", err.Error())
		return
	}
	return

}

// 数据总览-看板 返回结构体数据
func OrgKanbanOverview(totalData, curData, lastData, disTotalData, disCurData, disLastData distribution_po.StatsShopDistributorDaily) (out vo.GetKanbanOverview) {
	// 订单数据概览
	out.OrderData.SubmitOrder = vo.SubmitOrder{
		// 累计下单客户数
		OrderCustomerCountTotal: totalData.OrderCustomerCount,
		// 累计下单单数
		OrderCountTotal: totalData.OrderCount,
		// 累计下单金额
		OrderAmountTotal: totalData.OrderAmount,
		// 累计下单商品数
		OrderProductCountTotal: totalData.OrderProductCount,
		// 累计下单客单价
		OrderCustomerPriceTotal: totalData.OrderCustomerPrice,
		// 新增下单客户数
		OrderCustomerCount: curData.OrderCustomerCount,
		// 新增下单单数
		OrderCount: curData.OrderCount,
		// 新增下单金额
		OrderAmount: curData.OrderAmount,
		// 新增下单商品数
		OrderProductCount: curData.OrderProductCount,
		// 新增下单客单价
		OrderCustomerPrice: curData.OrderCustomerPrice,
		// 新增下单客户数 较上一周期百分比
		OrderCustomerCountPercent: utils.CalProportion(curData.OrderCustomerCount, lastData.OrderCustomerCount),
		// 新增下单单数 较上一周期百分比
		OrderCountPercent: utils.CalProportion(curData.OrderCount, lastData.OrderCount),
		// 新增下单金额 较上一周期百分比
		OrderAmountPercent: utils.CalProportion(curData.OrderAmount, lastData.OrderAmount),
		// 新增下单商品数 较上一周期百分比
		OrderProductCountPercent: utils.CalProportion(curData.OrderProductCount, lastData.OrderProductCount),
		// 新增下单客单价 较上一周期百分比
		OrderCustomerPricePercent: utils.CalProportion(curData.OrderCustomerPrice, lastData.OrderCustomerPrice),
	}
	out.OrderData.TransOrder = vo.TransOrder{
		// 累计成交客户数
		TransCustomerCountTotal: totalData.TransCustomerCount,
		// 累计成交单数
		TransCountTotal: totalData.TransCount,
		// 累计成交金额
		TransAmountTotal: totalData.TransAmount,
		// 累计成交商品数
		TransProductCountTotal: totalData.TransProductCount,
		// 累计成交客单价
		TransCustomerPriceTotal: totalData.TransCustomerPrice,
		// 新增成交客户数
		TransCustomerCount: curData.TransCustomerCount,
		// 新增成交单数
		TransCount: curData.TransCount,
		// 新增成交金额
		TransAmount: curData.TransAmount,
		// 新增成交商品数
		TransProductCount: curData.TransProductCount,
		// 新增成交客单价
		TransCustomerPrice: curData.TransCustomerPrice,
		// 新增成交客户数 较上一周期百分比
		TransCustomerCountPercent: utils.CalProportion(curData.TransCustomerCount, lastData.TransCustomerCount),
		// 新增成交单数 较上一周期百分比
		TransCountPercent: utils.CalProportion(curData.TransCount, lastData.TransCount),
		// 新增成交金额 较上一周期百分比
		TransAmountPercent: utils.CalProportion(curData.TransAmount, lastData.TransAmount),
		// 新增成交商品数 较上一周期百分比
		TransProductCountPercent: utils.CalProportion(curData.TransProductCount, lastData.TransProductCount),
		// 新增成交客单价 较上一周期百分比
		TransCustomerPricePercent: utils.CalProportion(curData.TransCustomerPrice, lastData.TransCustomerPrice),
	}
	// 分销订单数据概览
	out.DisOrderData.SubmitOrder = vo.SubmitOrder{
		// 累计下单客户数
		OrderCustomerCountTotal: disTotalData.OrderCustomerCount,
		// 累计下单单数
		OrderCountTotal: disTotalData.OrderCount,
		// 累计下单金额
		OrderAmountTotal: disTotalData.OrderAmount,
		// 累计下单商品数
		OrderProductCountTotal: disTotalData.OrderProductCount,
		// 累计下单客单价
		OrderCustomerPriceTotal: disTotalData.OrderCustomerPrice,
		// 新增下单客户数
		OrderCustomerCount: disCurData.OrderCustomerCount,
		// 新增下单单数
		OrderCount: disCurData.OrderCount,
		// 新增下单金额
		OrderAmount: disCurData.OrderAmount,
		// 新增下单商品数
		OrderProductCount: disCurData.OrderProductCount,
		// 新增下单客单价
		OrderCustomerPrice: disCurData.OrderCustomerPrice,
		// 新增下单客户数 较上一周期百分比
		OrderCustomerCountPercent: utils.CalProportion(disCurData.OrderCustomerCount, disLastData.OrderCustomerCount),
		// 新增下单单数 较上一周期百分比
		OrderCountPercent: utils.CalProportion(disCurData.OrderCount, disLastData.OrderCount),
		// 新增下单金额 较上一周期百分比
		OrderAmountPercent: utils.CalProportion(disCurData.OrderAmount, disLastData.OrderAmount),
		// 新增下单商品数 较上一周期百分比
		OrderProductCountPercent: utils.CalProportion(disCurData.OrderProductCount, disLastData.OrderProductCount),
		// 新增下单客单价 较上一周期百分比
		OrderCustomerPricePercent: utils.CalProportion(disCurData.OrderCustomerPrice, disLastData.OrderCustomerPrice),
	}
	out.DisOrderData.TransOrder = vo.TransOrder{
		// 累计成交客户数
		TransCustomerCountTotal: disTotalData.TransCustomerCount,
		// 累计成交单数
		TransCountTotal: disTotalData.TransCount,
		// 累计成交金额
		TransAmountTotal: disTotalData.TransAmount,
		// 累计成交商品数
		TransProductCountTotal: disTotalData.TransProductCount,
		// 累计成交客单价
		TransCustomerPriceTotal: disTotalData.TransCustomerPrice,
		// 新增成交客户数
		TransCustomerCount: disCurData.TransCustomerCount,
		// 新增成交单数
		TransCount: disCurData.TransCount,
		// 新增成交金额
		TransAmount: disCurData.TransAmount,
		// 新增成交商品数
		TransProductCount: disCurData.TransProductCount,
		// 新增成交客单价
		TransCustomerPrice: disCurData.TransCustomerPrice,
		// 新增成交客户数 较上一周期百分比
		TransCustomerCountPercent: utils.CalProportion(disCurData.TransCustomerCount, disLastData.TransCustomerCount),
		// 新增成交单数 较上一周期百分比
		TransCountPercent: utils.CalProportion(disCurData.TransCount, disLastData.TransCount),
		// 新增成交金额 较上一周期百分比
		TransAmountPercent: utils.CalProportion(disCurData.TransAmount, disLastData.TransAmount),
		// 新增成交商品数 较上一周期百分比
		TransProductCountPercent: utils.CalProportion(disCurData.TransProductCount, disLastData.TransProductCount),
		// 新增成交客单价 较上一周期百分比
		TransCustomerPricePercent: utils.CalProportion(disCurData.TransCustomerPrice, disLastData.TransCustomerPrice),
	}

	out.Commission.Comm = vo.DisCommission{
		// // 累计分销佣金
		// CommissionTotal: disTotalData.Commission,
		// // 累计未结佣金
		// UnsettledCommissionTotal: disTotalData.UnsettledCommission,
		// // 累计已结佣金
		// SettledCommissionTotal: disTotalData.SettledCommission,
		// 新增分销佣金
		Commission: disCurData.Commission,
		// 新增未结佣金
		UnsettledCommission: disCurData.UnsettledCommission,
		// 新增已结佣金
		SettledCommission: disCurData.SettledCommission,
		// 新增分销佣金 较上一周期百分比
		CommissionPercent: utils.CalProportion(disCurData.Commission, disLastData.Commission),
		// 新增未结佣金 较上一周期百分比
		UnsettledCommissionPercent: utils.CalProportion(disCurData.UnsettledCommission, disLastData.UnsettledCommission),
		// 新增已结佣金 较上一周期百分比
		SettledCommissionPercent: utils.CalProportion(disCurData.SettledCommission, disLastData.SettledCommission),
	}

	return
}

// 商品订单-看板 返回结构体数据
func OrgKanbanOrder(totalData, curData, lastData, disTotalData, disCurData, disLastData distribution_po.StatsShopDistributorDaily) (out vo.GetKanbanOrder) {
	// 订单数据概览
	out.OrderData.SubmitOrder = vo.SubmitOrder{
		// 累计下单客户数
		OrderCustomerCountTotal: totalData.OrderCustomerCount,
		// 累计下单单数
		OrderCountTotal: totalData.OrderCount,
		// 累计下单金额
		OrderAmountTotal: totalData.OrderAmount,
		// 累计下单商品数
		OrderProductCountTotal: totalData.OrderProductCount,
		// 累计下单客单价
		OrderCustomerPriceTotal: totalData.OrderCustomerPrice,
		// 新增下单客户数
		OrderCustomerCount: curData.OrderCustomerCount,
		// 新增下单单数
		OrderCount: curData.OrderCount,
		// 新增下单金额
		OrderAmount: curData.OrderAmount,
		// 新增下单商品数
		OrderProductCount: curData.OrderProductCount,
		// 新增下单客单价
		OrderCustomerPrice: curData.OrderCustomerPrice,
		// 新增下单客户数 较上一周期百分比
		OrderCustomerCountPercent: utils.CalProportion(curData.OrderCustomerCount, lastData.OrderCustomerCount),
		// 新增下单单数 较上一周期百分比
		OrderCountPercent: utils.CalProportion(curData.OrderCount, lastData.OrderCount),
		// 新增下单金额 较上一周期百分比
		OrderAmountPercent: utils.CalProportion(curData.OrderAmount, lastData.OrderAmount),
		// 新增下单商品数 较上一周期百分比
		OrderProductCountPercent: utils.CalProportion(curData.OrderProductCount, lastData.OrderProductCount),
		// 新增下单客单价 较上一周期百分比
		OrderCustomerPricePercent: utils.CalProportion(curData.OrderCustomerPrice, lastData.OrderCustomerPrice),
	}
	out.OrderData.TransOrder = vo.TransOrder{
		// 累计成交客户数
		TransCustomerCountTotal: totalData.TransCustomerCount,
		// 累计成交单数
		TransCountTotal: totalData.TransCount,
		// 累计成交金额
		TransAmountTotal: totalData.TransAmount,
		// 累计成交商品数
		TransProductCountTotal: totalData.TransProductCount,
		// 累计成交客单价
		TransCustomerPriceTotal: totalData.TransCustomerPrice,
		// 新增成交客户数
		TransCustomerCount: curData.TransCustomerCount,
		// 新增成交单数
		TransCount: curData.TransCount,
		// 新增成交金额
		TransAmount: curData.TransAmount,
		// 新增成交商品数
		TransProductCount: curData.TransProductCount,
		// 新增成交客单价
		TransCustomerPrice: curData.TransCustomerPrice,
		// 新增成交客户数 较上一周期百分比
		TransCustomerCountPercent: utils.CalProportion(curData.TransCustomerCount, lastData.TransCustomerCount),
		// 新增成交单数 较上一周期百分比
		TransCountPercent: utils.CalProportion(curData.TransCount, lastData.TransCount),
		// 新增成交金额 较上一周期百分比
		TransAmountPercent: utils.CalProportion(curData.TransAmount, lastData.TransAmount),
		// 新增成交商品数 较上一周期百分比
		TransProductCountPercent: utils.CalProportion(curData.TransProductCount, lastData.TransProductCount),
		// 新增成交客单价 较上一周期百分比
		TransCustomerPricePercent: utils.CalProportion(curData.TransCustomerPrice, lastData.TransCustomerPrice),
	}
	out.OrderData.CancelRefundOrder = vo.CancelRefundOrder{
		// 累计取消单数
		CancelCountTotal: totalData.CancelCount,
		// 累计取消金额
		CancelAmountTotal: totalData.CancelAmount,
		// 累计取消商品数
		CancelProductCountTotal: totalData.CancelProductCount,
		// 累计退款单数
		RefundCountTotal: totalData.RefundCount,
		// 累计退款金额
		RefundAmountTotal: totalData.RefundAmount,
		// 累计退款商品数
		RefundProductCountTotal: totalData.RefundProductCount,
		// 新增取消单数
		CancelCount: curData.CancelCount,
		// 新增取消金额
		CancelAmount: curData.CancelAmount,
		// 新增取消商品数
		CancelProductCount: curData.CancelProductCount,
		// 新增退款单数
		RefundCount: curData.RefundCount,
		// 新增退款金额
		RefundAmount: curData.RefundAmount,
		// 新增退款商品数
		RefundProductCount: curData.RefundProductCount,
		// 新增取消单数  较上一周期百分比
		CancelCountPercent: utils.CalProportion(curData.CancelCount, lastData.CancelCount),
		// 新增取消金额  较上一周期百分比
		CancelAmountPercent: utils.CalProportion(curData.CancelAmount, lastData.CancelAmount),
		// 新增取消商品数  较上一周期百分比
		CancelProductCountPercent: utils.CalProportion(curData.CancelProductCount, lastData.CancelProductCount),
		// 新增退款单数  较上一周期百分比
		RefundCountPercent: utils.CalProportion(curData.RefundCount, lastData.RefundCount),
		// 新增退款金额  较上一周期百分比
		RefundAmountPercent: utils.CalProportion(curData.RefundAmount, lastData.RefundAmount),
		// 新增退款商品数  较上一周期百分比
		RefundProductCountPercent: utils.CalProportion(curData.RefundProductCount, lastData.RefundProductCount),
	}

	// 分销订单数据概览
	out.DisOrderData.SubmitOrder = vo.SubmitOrder{
		// 累计下单客户数
		OrderCustomerCountTotal: disTotalData.OrderCustomerCount,
		// 累计下单单数
		OrderCountTotal: disTotalData.OrderCount,
		// 累计下单金额
		OrderAmountTotal: disTotalData.OrderAmount,
		// 累计下单商品数
		OrderProductCountTotal: disTotalData.OrderProductCount,
		// 累计下单客单价
		OrderCustomerPriceTotal: disTotalData.OrderCustomerPrice,
		// 新增下单客户数
		OrderCustomerCount: disCurData.OrderCustomerCount,
		// 新增下单单数
		OrderCount: disCurData.OrderCount,
		// 新增下单金额
		OrderAmount: disCurData.OrderAmount,
		// 新增下单商品数
		OrderProductCount: disCurData.OrderProductCount,
		// 新增下单客单价
		OrderCustomerPrice: disCurData.OrderCustomerPrice,
		// 新增下单客户数 较上一周期百分比
		OrderCustomerCountPercent: utils.CalProportion(disCurData.OrderCustomerCount, disLastData.OrderCustomerCount),
		// 新增下单单数 较上一周期百分比
		OrderCountPercent: utils.CalProportion(disCurData.OrderCount, disLastData.OrderCount),
		// 新增下单金额 较上一周期百分比
		OrderAmountPercent: utils.CalProportion(disCurData.OrderAmount, disLastData.OrderAmount),
		// 新增下单商品数 较上一周期百分比
		OrderProductCountPercent: utils.CalProportion(disCurData.OrderProductCount, disLastData.OrderProductCount),
		// 新增下单客单价 较上一周期百分比
		OrderCustomerPricePercent: utils.CalProportion(disCurData.OrderCustomerPrice, disLastData.OrderCustomerPrice),
	}
	out.DisOrderData.TransOrder = vo.TransOrder{
		// 累计成交客户数
		TransCustomerCountTotal: disTotalData.TransCustomerCount,
		// 累计成交单数
		TransCountTotal: disTotalData.TransCount,
		// 累计成交金额
		TransAmountTotal: disTotalData.TransAmount,
		// 累计成交商品数
		TransProductCountTotal: disTotalData.TransProductCount,
		// 累计成交客单价
		TransCustomerPriceTotal: disTotalData.TransCustomerPrice,
		// 新增成交客户数
		TransCustomerCount: disCurData.TransCustomerCount,
		// 新增成交单数
		TransCount: disCurData.TransCount,
		// 新增成交金额
		TransAmount: disCurData.TransAmount,
		// 新增成交商品数
		TransProductCount: disCurData.TransProductCount,
		// 新增成交客单价
		TransCustomerPrice: disCurData.TransCustomerPrice,
		// 新增成交客户数 较上一周期百分比
		TransCustomerCountPercent: utils.CalProportion(disCurData.TransCustomerCount, disLastData.TransCustomerCount),
		// 新增成交单数 较上一周期百分比
		TransCountPercent: utils.CalProportion(disCurData.TransCount, disLastData.TransCount),
		// 新增成交金额 较上一周期百分比
		TransAmountPercent: utils.CalProportion(disCurData.TransAmount, disLastData.TransAmount),
		// 新增成交商品数 较上一周期百分比
		TransProductCountPercent: utils.CalProportion(disCurData.TransProductCount, disLastData.TransProductCount),
		// 新增成交客单价 较上一周期百分比
		TransCustomerPricePercent: utils.CalProportion(disCurData.TransCustomerPrice, disLastData.TransCustomerPrice),
	}

	out.DisOrderData.DisSource = vo.DisSource{
		// 分销海报扫码数
		PosterScanCount: curData.PosterScanCount,
		// 分销海报成交单数
		PosterTransCount: curData.PosterTransCount,
		// 分销海报成交金额(分)
		PosterTransAmount: curData.PosterTransAmount,
		// 分销海报扫码数   较上一周期百分比
		PosterScanCountPercent: utils.CalProportion(disCurData.PosterScanCount, disLastData.PosterScanCount),
		// 分销海报成交单数   较上一周期百分比
		PosterTransCountPercent: utils.CalProportion(disCurData.PosterTransCount, disLastData.PosterTransCount),
		// 分销海报成交金额(分)   较上一周期百分比
		PosterTransAmountPercent: utils.CalProportion(disCurData.PosterTransAmount, disLastData.PosterTransAmount),

		// 分销链接点击数
		LinkClickCount: curData.LinkClickCount,
		// 分销链接成交单数
		LinkTransCount: curData.LinkTransCount,
		// 分销链接成交金额
		LinkTransAmount: curData.LinkTransAmount,
		// 分销链接点击数 较上一周期百分比
		LinkClickCountPercent: utils.CalProportion(disCurData.LinkClickCount, disLastData.LinkClickCount),
		// 分销链接成交单数 较上一周期百分比
		LinkTransCountPercent: utils.CalProportion(disCurData.LinkTransCount, disLastData.LinkTransCount),
		// 分销链接成交金额 较上一周期百分比
		LinkTransAmountPercent: utils.CalProportion(disCurData.LinkTransAmount, disLastData.LinkTransAmount),

		// 粉丝关系成交单数
		FanRelationTransCount: curData.FanRelationTransCount,
		// 粉丝关系成交金额
		FanRelationTransAmount: curData.FanRelationTransAmount,
		// 粉丝关系成交单数  较上一周期百分比
		FanRelationTransCountPercent: utils.CalProportion(disCurData.FanRelationTransCount, disLastData.FanRelationTransCount),
		// 粉丝关系成交金额 较上一周期百分比
		FanRelationTransAmountPercent: utils.CalProportion(disCurData.FanRelationTransAmount, disLastData.FanRelationTransAmount),
	}
	out.DisOrderData.DisCommission = vo.DisCommission{
		// // 累计分销佣金
		// CommissionTotal: disTotalData.Commission,
		// // 累计未结佣金
		// UnsettledCommissionTotal: disTotalData.UnsettledCommission,
		// // 累计已结佣金
		// SettledCommissionTotal: disTotalData.SettledCommission,
		// 新增分销佣金
		Commission: disCurData.Commission,
		// 新增未结佣金
		UnsettledCommission: disCurData.UnsettledCommission,
		// 新增已结佣金
		SettledCommission: disCurData.SettledCommission,
		// 新增分销佣金 较上一周期百分比
		CommissionPercent: utils.CalProportion(disCurData.Commission, disLastData.Commission),
		// 新增未结佣金 较上一周期百分比
		UnsettledCommissionPercent: utils.CalProportion(disCurData.UnsettledCommission, disLastData.UnsettledCommission),
		// 新增已结佣金 较上一周期百分比
		SettledCommissionPercent: utils.CalProportion(disCurData.SettledCommission, disLastData.SettledCommission),
	}

	return
}

// 保险订单-看板 返回结构体数据
func OrgKanbanInsOrder(totalData, curData, lastData, disTotalData, disCurData, disLastData distribution_po.StatsShopDistributorDaily) (out vo.GetKanbanInsOrder) {
	// 订单数据概览
	out.OrderData.SubmitOrder = vo.SubmitOrder{
		// 累计下单客户数
		OrderCustomerCountTotal: totalData.OrderCustomerCount,
		// 累计下单单数
		OrderCountTotal: totalData.OrderCount,
		// 累计下单金额
		OrderAmountTotal: totalData.OrderAmount,
		// 累计下单商品数
		OrderProductCountTotal: totalData.OrderProductCount,
		// 累计下单客单价
		OrderCustomerPriceTotal: totalData.OrderCustomerPrice,
		// 新增下单客户数
		OrderCustomerCount: curData.OrderCustomerCount,
		// 新增下单单数
		OrderCount: curData.OrderCount,
		// 新增下单金额
		OrderAmount: curData.OrderAmount,
		// 新增下单商品数
		OrderProductCount: curData.OrderProductCount,
		// 新增下单客单价
		OrderCustomerPrice: curData.OrderCustomerPrice,
		// 新增下单客户数 较上一周期百分比
		OrderCustomerCountPercent: utils.CalProportion(curData.OrderCustomerCount, lastData.OrderCustomerCount),
		// 新增下单单数 较上一周期百分比
		OrderCountPercent: utils.CalProportion(curData.OrderCount, lastData.OrderCount),
		// 新增下单金额 较上一周期百分比
		OrderAmountPercent: utils.CalProportion(curData.OrderAmount, lastData.OrderAmount),
		// 新增下单商品数 较上一周期百分比
		OrderProductCountPercent: utils.CalProportion(curData.OrderProductCount, lastData.OrderProductCount),
		// 新增下单客单价 较上一周期百分比
		OrderCustomerPricePercent: utils.CalProportion(curData.OrderCustomerPrice, lastData.OrderCustomerPrice),
	}
	out.OrderData.TransOrder = vo.TransOrder{
		// 累计成交客户数
		TransCustomerCountTotal: totalData.TransCustomerCount,
		// 累计成交单数
		TransCountTotal: totalData.TransCount,
		// 累计成交金额
		TransAmountTotal: totalData.TransAmount,
		// 累计成交商品数
		TransProductCountTotal: totalData.TransProductCount,
		// 累计成交客单价
		TransCustomerPriceTotal: totalData.TransCustomerPrice,
		// 新增成交客户数
		TransCustomerCount: curData.TransCustomerCount,
		// 新增成交单数
		TransCount: curData.TransCount,
		// 新增成交金额
		TransAmount: curData.TransAmount,
		// 新增成交商品数
		TransProductCount: curData.TransProductCount,
		// 新增成交客单价
		TransCustomerPrice: curData.TransCustomerPrice,
		// 新增成交客户数 较上一周期百分比
		TransCustomerCountPercent: utils.CalProportion(curData.TransCustomerCount, lastData.TransCustomerCount),
		// 新增成交单数 较上一周期百分比
		TransCountPercent: utils.CalProportion(curData.TransCount, lastData.TransCount),
		// 新增成交金额 较上一周期百分比
		TransAmountPercent: utils.CalProportion(curData.TransAmount, lastData.TransAmount),
		// 新增成交商品数 较上一周期百分比
		TransProductCountPercent: utils.CalProportion(curData.TransProductCount, lastData.TransProductCount),
		// 新增成交客单价 较上一周期百分比
		TransCustomerPricePercent: utils.CalProportion(curData.TransCustomerPrice, lastData.TransCustomerPrice),
	}
	out.OrderData.CancelRefundOrder = vo.CancelRefundOrder{
		// 累计取消单数
		CancelCountTotal: totalData.CancelCount,
		// 累计取消金额
		CancelAmountTotal: totalData.CancelAmount,
		// 累计取消商品数
		CancelProductCountTotal: totalData.CancelProductCount,
		// 累计退款单数
		RefundCountTotal: totalData.RefundCount,
		// 累计退款金额
		RefundAmountTotal: totalData.RefundAmount,
		// 累计退款商品数
		RefundProductCountTotal: totalData.RefundProductCount,
		// 新增取消单数
		CancelCount: curData.CancelCount,
		// 新增取消金额
		CancelAmount: curData.CancelAmount,
		// 新增取消商品数
		CancelProductCount: curData.CancelProductCount,
		// 新增退款单数
		RefundCount: curData.RefundCount,
		// 新增退款金额
		RefundAmount: curData.RefundAmount,
		// 新增退款商品数
		RefundProductCount: curData.RefundProductCount,
		// 新增取消单数  较上一周期百分比
		CancelCountPercent: utils.CalProportion(curData.CancelCount, lastData.CancelCount),
		// 新增取消金额  较上一周期百分比
		CancelAmountPercent: utils.CalProportion(curData.CancelAmount, lastData.CancelAmount),
		// 新增取消商品数  较上一周期百分比
		CancelProductCountPercent: utils.CalProportion(curData.CancelProductCount, lastData.CancelProductCount),
		// 新增退款单数  较上一周期百分比
		RefundCountPercent: utils.CalProportion(curData.RefundCount, lastData.RefundCount),
		// 新增退款金额  较上一周期百分比
		RefundAmountPercent: utils.CalProportion(curData.RefundAmount, lastData.RefundAmount),
		// 新增退款商品数  较上一周期百分比
		RefundProductCountPercent: utils.CalProportion(curData.RefundProductCount, lastData.RefundProductCount),
	}

	// 分销订单数据概览
	out.DisOrderData.SubmitOrder = vo.SubmitOrder{
		// 累计下单客户数
		OrderCustomerCountTotal: disTotalData.OrderCustomerCount,
		// 累计下单单数
		OrderCountTotal: disTotalData.OrderCount,
		// 累计下单金额
		OrderAmountTotal: disTotalData.OrderAmount,
		// 累计下单商品数
		OrderProductCountTotal: disTotalData.OrderProductCount,
		// 累计下单客单价
		OrderCustomerPriceTotal: disTotalData.OrderCustomerPrice,
		// 新增下单客户数
		OrderCustomerCount: disCurData.OrderCustomerCount,
		// 新增下单单数
		OrderCount: disCurData.OrderCount,
		// 新增下单金额
		OrderAmount: disCurData.OrderAmount,
		// 新增下单商品数
		OrderProductCount: disCurData.OrderProductCount,
		// 新增下单客单价
		OrderCustomerPrice: disCurData.OrderCustomerPrice,
		// 新增下单客户数 较上一周期百分比
		OrderCustomerCountPercent: utils.CalProportion(disCurData.OrderCustomerCount, disLastData.OrderCustomerCount),
		// 新增下单单数 较上一周期百分比
		OrderCountPercent: utils.CalProportion(disCurData.OrderCount, disLastData.OrderCount),
		// 新增下单金额 较上一周期百分比
		OrderAmountPercent: utils.CalProportion(disCurData.OrderAmount, disLastData.OrderAmount),
		// 新增下单商品数 较上一周期百分比
		OrderProductCountPercent: utils.CalProportion(disCurData.OrderProductCount, disLastData.OrderProductCount),
		// 新增下单客单价 较上一周期百分比
		OrderCustomerPricePercent: utils.CalProportion(disCurData.OrderCustomerPrice, disLastData.OrderCustomerPrice),
	}
	out.DisOrderData.TransOrder = vo.TransOrder{
		// 累计成交客户数
		TransCustomerCountTotal: disTotalData.TransCustomerCount,
		// 累计成交单数
		TransCountTotal: disTotalData.TransCount,
		// 累计成交金额
		TransAmountTotal: disTotalData.TransAmount,
		// 累计成交商品数
		TransProductCountTotal: disTotalData.TransProductCount,
		// 累计成交客单价
		TransCustomerPriceTotal: disTotalData.TransCustomerPrice,
		// 新增成交客户数
		TransCustomerCount: disCurData.TransCustomerCount,
		// 新增成交单数
		TransCount: disCurData.TransCount,
		// 新增成交金额
		TransAmount: disCurData.TransAmount,
		// 新增成交商品数
		TransProductCount: disCurData.TransProductCount,
		// 新增成交客单价
		TransCustomerPrice: disCurData.TransCustomerPrice,
		// 新增成交客户数 较上一周期百分比
		TransCustomerCountPercent: utils.CalProportion(disCurData.TransCustomerCount, disLastData.TransCustomerCount),
		// 新增成交单数 较上一周期百分比
		TransCountPercent: utils.CalProportion(disCurData.TransCount, disLastData.TransCount),
		// 新增成交金额 较上一周期百分比
		TransAmountPercent: utils.CalProportion(disCurData.TransAmount, disLastData.TransAmount),
		// 新增成交商品数 较上一周期百分比
		TransProductCountPercent: utils.CalProportion(disCurData.TransProductCount, disLastData.TransProductCount),
		// 新增成交客单价 较上一周期百分比
		TransCustomerPricePercent: utils.CalProportion(disCurData.TransCustomerPrice, disLastData.TransCustomerPrice),
	}

	out.DisOrderData.DisSource = vo.DisSource{
		// 分销海报扫码数
		PosterScanCount: curData.PosterScanCount,
		// 分销海报成交单数
		PosterTransCount: curData.PosterTransCount,
		// 分销海报成交金额(分)
		PosterTransAmount: curData.PosterTransAmount,
		// 分销海报扫码数   较上一周期百分比
		PosterScanCountPercent: utils.CalProportion(disCurData.PosterScanCount, disLastData.PosterScanCount),
		// 分销海报成交单数   较上一周期百分比
		PosterTransCountPercent: utils.CalProportion(disCurData.PosterTransCount, disLastData.PosterTransCount),
		// 分销海报成交金额(分)   较上一周期百分比
		PosterTransAmountPercent: utils.CalProportion(disCurData.PosterTransAmount, disLastData.PosterTransAmount),

		// 分销链接点击数
		LinkClickCount: curData.LinkClickCount,
		// 分销链接成交单数
		LinkTransCount: curData.LinkTransCount,
		// 分销链接成交金额
		LinkTransAmount: curData.LinkTransAmount,
		// 分销链接点击数 较上一周期百分比
		LinkClickCountPercent: utils.CalProportion(disCurData.LinkClickCount, disLastData.LinkClickCount),
		// 分销链接成交单数 较上一周期百分比
		LinkTransCountPercent: utils.CalProportion(disCurData.LinkTransCount, disLastData.LinkTransCount),
		// 分销链接成交金额 较上一周期百分比
		LinkTransAmountPercent: utils.CalProportion(disCurData.LinkTransAmount, disLastData.LinkTransAmount),

		// 粉丝关系成交单数
		FanRelationTransCount: curData.FanRelationTransCount,
		// 粉丝关系成交金额
		FanRelationTransAmount: curData.FanRelationTransAmount,
		// 粉丝关系成交单数  较上一周期百分比
		FanRelationTransCountPercent: utils.CalProportion(disCurData.FanRelationTransCount, disLastData.FanRelationTransCount),
		// 粉丝关系成交金额 较上一周期百分比
		FanRelationTransAmountPercent: utils.CalProportion(disCurData.FanRelationTransAmount, disLastData.FanRelationTransAmount),
	}
	out.DisOrderData.DisCommission = vo.DisCommission{
		// // 累计分销佣金
		// CommissionTotal: disTotalData.Commission,
		// // 累计未结佣金
		// UnsettledCommissionTotal: disTotalData.UnsettledCommission,
		// // 累计已结佣金
		// SettledCommissionTotal: disTotalData.SettledCommission,
		// 新增分销佣金
		Commission: disCurData.Commission,
		// 新增未结佣金
		UnsettledCommission: disCurData.UnsettledCommission,
		// 新增已结佣金
		SettledCommission: disCurData.SettledCommission,
		// 新增分销佣金 较上一周期百分比
		CommissionPercent: utils.CalProportion(disCurData.Commission, disLastData.Commission),
		// 新增未结佣金 较上一周期百分比
		UnsettledCommissionPercent: utils.CalProportion(disCurData.UnsettledCommission, disLastData.UnsettledCommission),
		// 新增已结佣金 较上一周期百分比
		SettledCommissionPercent: utils.CalProportion(disCurData.SettledCommission, disLastData.SettledCommission),
	}

	return
}

func OrgStatsDisCenterOverview(curData, lastData, GoodsCurData, GoodsLastData, InsCurData, InsLastData distribution_po.StatsShopDistributorDaily) (out vo.StatsDisCenterOverview) {

	out.GoodsSumIns = vo.GoodsSumIns{
		// 新增成交单数
		TransCount: curData.TransCount,
		// 新增成交金额
		TransAmount: curData.TransAmount,
		// 新增成交客户数
		TransCustomerCount: curData.TransCustomerCount,

		// 新增成交单数 较上一周期百分比
		TransCountPercent: utils.CalProportion(curData.TransCount, lastData.TransCount),
		// 新增成交金额 较上一周期百分比
		TransAmountPercent: utils.CalProportion(curData.TransAmount, lastData.TransAmount),
		// 新增成交客户数 较上一周期百分比
		TransCustomerCountPercent: utils.CalProportion(curData.TransCustomerCount, lastData.TransCustomerCount),

		// 新增分销佣金
		Commission: curData.Commission,
		// 新增未结佣金
		UnsettledCommission: curData.UnsettledCommission,
		// 新增已结佣金
		SettledCommission: curData.SettledCommission,
		// 新增分销佣金 较上一周期百分比
		CommissionPercent: utils.CalProportion(curData.Commission, lastData.Commission),
		// 新增未结佣金 较上一周期百分比
		UnsettledCommissionPercent: utils.CalProportion(curData.UnsettledCommission, lastData.UnsettledCommission),
		// 新增已结佣金 较上一周期百分比
		SettledCommissionPercent: utils.CalProportion(curData.SettledCommission, lastData.SettledCommission),

		// 分销海报成交单数
		PosterTransCount: curData.PosterTransCount,
		// 分销海报成交金额(分)
		PosterTransAmount: curData.PosterTransAmount,
		// 分销海报成交单数   较上一周期百分比
		PosterTransCountPercent: utils.CalProportion(curData.PosterTransCount, lastData.PosterTransCount),
		// 分销海报成交金额(分)   较上一周期百分比
		PosterTransAmountPercent: utils.CalProportion(curData.PosterTransAmount, lastData.PosterTransAmount),

		// 分销链接成交单数
		LinkTransCount: curData.LinkTransCount,
		// 分销链接成交金额
		LinkTransAmount: curData.LinkTransAmount,
		// 分销链接成交单数 较上一周期百分比
		LinkTransCountPercent: utils.CalProportion(curData.LinkTransCount, lastData.LinkTransCount),
		// 分销链接成交金额 较上一周期百分比
		LinkTransAmountPercent: utils.CalProportion(curData.LinkTransAmount, lastData.LinkTransAmount),

		// 粉丝关系成交单数
		FanRelationTransCount: curData.FanRelationTransCount,
		// 粉丝关系成交金额
		FanRelationTransAmount: curData.FanRelationTransAmount,
		// 粉丝关系成交单数  较上一周期百分比
		FanRelationTransCountPercent: utils.CalProportion(curData.FanRelationTransCount, lastData.FanRelationTransCount),
		// 粉丝关系成交金额 较上一周期百分比
		FanRelationTransAmountPercent: utils.CalProportion(curData.FanRelationTransAmount, lastData.FanRelationTransAmount),
	}
	out.GoodsData = vo.StatsShopDistributorDailyForApi{
		// 新增成交客户数
		TransCustomerCount: GoodsCurData.TransCustomerCount,
		// 新增成交单数
		TransCount: GoodsCurData.TransCount,
		// 新增成交金额
		TransAmount: GoodsCurData.TransAmount,
		// 新增成交商品数
		TransProductCount: GoodsCurData.TransProductCount,
		// 新增成交客单价
		TransCustomerPrice: GoodsCurData.TransCustomerPrice,
		// 新增成交客户数 较上一周期百分比
		TransCustomerCountPercent: utils.CalProportion(GoodsCurData.TransCustomerCount, GoodsLastData.TransCustomerCount),
		// 新增成交单数 较上一周期百分比
		TransCountPercent: utils.CalProportion(GoodsCurData.TransCount, GoodsLastData.TransCount),
		// 新增成交金额 较上一周期百分比
		TransAmountPercent: utils.CalProportion(GoodsCurData.TransAmount, GoodsLastData.TransAmount),
		// 新增成交商品数 较上一周期百分比
		TransProductCountPercent: utils.CalProportion(GoodsCurData.TransProductCount, GoodsLastData.TransProductCount),
		// 新增成交客单价 较上一周期百分比
		TransCustomerPricePercent: utils.CalProportion(GoodsCurData.TransCustomerPrice, GoodsLastData.TransCustomerPrice),

		// 新增分销佣金
		Commission: GoodsCurData.Commission,
		// 新增未结佣金
		UnsettledCommission: GoodsCurData.UnsettledCommission,
		// 新增已结佣金
		SettledCommission: GoodsCurData.SettledCommission,
		// 新增分销佣金 较上一周期百分比
		CommissionPercent: utils.CalProportion(GoodsCurData.Commission, GoodsLastData.Commission),
		// 新增未结佣金 较上一周期百分比
		UnsettledCommissionPercent: utils.CalProportion(GoodsCurData.UnsettledCommission, GoodsLastData.UnsettledCommission),
		// 新增已结佣金 较上一周期百分比
		SettledCommissionPercent: utils.CalProportion(GoodsCurData.SettledCommission, GoodsLastData.SettledCommission),

		// 分销海报扫码数
		PosterScanCount: GoodsCurData.PosterScanCount,
		// 分销海报成交单数
		PosterTransCount: GoodsCurData.PosterTransCount,
		// 分销海报成交金额(分)
		PosterTransAmount: GoodsCurData.PosterTransAmount,
		// 分销海报扫码数   较上一周期百分比
		PosterScanCountPercent: utils.CalProportion(GoodsCurData.PosterScanCount, GoodsLastData.PosterScanCount),
		// 分销海报成交单数   较上一周期百分比
		PosterTransCountPercent: utils.CalProportion(GoodsCurData.PosterTransCount, GoodsLastData.PosterTransCount),
		// 分销海报成交金额(分)   较上一周期百分比
		PosterTransAmountPercent: utils.CalProportion(GoodsCurData.PosterTransAmount, GoodsLastData.PosterTransAmount),

		// 分销链接点击数
		LinkClickCount: GoodsCurData.LinkClickCount,
		// 分销链接成交单数
		LinkTransCount: GoodsCurData.LinkTransCount,
		// 分销链接成交金额
		LinkTransAmount: GoodsCurData.LinkTransAmount,
		// 分销链接点击数 较上一周期百分比
		LinkClickCountPercent: utils.CalProportion(GoodsCurData.LinkClickCount, GoodsLastData.LinkClickCount),
		// 分销链接成交单数 较上一周期百分比
		LinkTransCountPercent: utils.CalProportion(GoodsCurData.LinkTransCount, GoodsLastData.LinkTransCount),
		// 分销链接成交金额 较上一周期百分比
		LinkTransAmountPercent: utils.CalProportion(GoodsCurData.LinkTransAmount, GoodsLastData.LinkTransAmount),

		// 粉丝关系成交单数
		FanRelationTransCount: GoodsCurData.FanRelationTransCount,
		// 粉丝关系成交金额
		FanRelationTransAmount: GoodsCurData.FanRelationTransAmount,
		// 粉丝关系成交单数  较上一周期百分比
		FanRelationTransCountPercent: utils.CalProportion(GoodsCurData.FanRelationTransCount, GoodsLastData.FanRelationTransCount),
		// 粉丝关系成交金额 较上一周期百分比
		FanRelationTransAmountPercent: utils.CalProportion(GoodsCurData.FanRelationTransAmount, GoodsLastData.FanRelationTransAmount),
	}

	out.InsData = vo.StatsShopDistributorDailyForApi{
		// 新增成交客户数
		TransCustomerCount: InsCurData.TransCustomerCount,
		// 新增成交单数
		TransCount: InsCurData.TransCount,
		// 新增成交金额
		TransAmount: InsCurData.TransAmount,
		// 新增成交商品数
		TransProductCount: InsCurData.TransProductCount,
		// 新增成交客单价
		TransCustomerPrice: InsCurData.TransCustomerPrice,
		// 新增成交客户数 较上一周期百分比
		TransCustomerCountPercent: utils.CalProportion(InsCurData.TransCustomerCount, InsLastData.TransCustomerCount),
		// 新增成交单数 较上一周期百分比
		TransCountPercent: utils.CalProportion(InsCurData.TransCount, InsLastData.TransCount),
		// 新增成交金额 较上一周期百分比
		TransAmountPercent: utils.CalProportion(InsCurData.TransAmount, InsLastData.TransAmount),
		// 新增成交商品数 较上一周期百分比
		TransProductCountPercent: utils.CalProportion(InsCurData.TransProductCount, InsLastData.TransProductCount),
		// 新增成交客单价 较上一周期百分比
		TransCustomerPricePercent: utils.CalProportion(InsCurData.TransCustomerPrice, InsLastData.TransCustomerPrice),

		// 新增分销佣金
		Commission: InsCurData.Commission,
		// 新增未结佣金
		UnsettledCommission: InsCurData.UnsettledCommission,
		// 新增已结佣金
		SettledCommission: InsCurData.SettledCommission,
		// 新增分销佣金 较上一周期百分比
		CommissionPercent: utils.CalProportion(InsCurData.Commission, InsLastData.Commission),
		// 新增未结佣金 较上一周期百分比
		UnsettledCommissionPercent: utils.CalProportion(InsCurData.UnsettledCommission, InsLastData.UnsettledCommission),
		// 新增已结佣金 较上一周期百分比
		SettledCommissionPercent: utils.CalProportion(InsCurData.SettledCommission, InsLastData.SettledCommission),

		// 分销海报扫码数
		PosterScanCount: InsCurData.PosterScanCount,
		// 分销海报成交单数
		PosterTransCount: InsCurData.PosterTransCount,
		// 分销海报成交金额(分)
		PosterTransAmount: InsCurData.PosterTransAmount,
		// 分销海报扫码数   较上一周期百分比
		PosterScanCountPercent: utils.CalProportion(InsCurData.PosterScanCount, InsLastData.PosterScanCount),
		// 分销海报成交单数   较上一周期百分比
		PosterTransCountPercent: utils.CalProportion(InsCurData.PosterTransCount, InsLastData.PosterTransCount),
		// 分销海报成交金额(分)   较上一周期百分比
		PosterTransAmountPercent: utils.CalProportion(InsCurData.PosterTransAmount, InsLastData.PosterTransAmount),

		// 分销链接点击数
		LinkClickCount: InsCurData.LinkClickCount,
		// 分销链接成交单数
		LinkTransCount: InsCurData.LinkTransCount,
		// 分销链接成交金额
		LinkTransAmount: InsCurData.LinkTransAmount,
		// 分销链接点击数 较上一周期百分比
		LinkClickCountPercent: utils.CalProportion(InsCurData.LinkClickCount, InsLastData.LinkClickCount),
		// 分销链接成交单数 较上一周期百分比
		LinkTransCountPercent: utils.CalProportion(InsCurData.LinkTransCount, InsLastData.LinkTransCount),
		// 分销链接成交金额 较上一周期百分比
		LinkTransAmountPercent: utils.CalProportion(InsCurData.LinkTransAmount, InsLastData.LinkTransAmount),

		// 粉丝关系成交单数
		FanRelationTransCount: InsCurData.FanRelationTransCount,
		// 粉丝关系成交金额
		FanRelationTransAmount: InsCurData.FanRelationTransAmount,
		// 粉丝关系成交单数  较上一周期百分比
		FanRelationTransCountPercent: utils.CalProportion(InsCurData.FanRelationTransCount, InsLastData.FanRelationTransCount),
		// 粉丝关系成交金额 较上一周期百分比
		FanRelationTransAmountPercent: utils.CalProportion(InsCurData.FanRelationTransAmount, InsLastData.FanRelationTransAmount),
	}
	return
}
