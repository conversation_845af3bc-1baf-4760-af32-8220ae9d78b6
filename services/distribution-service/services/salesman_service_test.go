package services

import (
	"eShop/services/common"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	"reflect"
	"testing"

	_ "github.com/go-sql-driver/mysql"
)

func TestDisSalesmanService_GetList(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in distribution_vo.DisSalesmanListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    distribution_vo.DisSalesmanListRes
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "获取业务员列表"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &DisSalesmanService{
				BaseService: tt.fields.BaseService,
			}
			app := distribution_vo.DisSalesmanListReq{}
			app.PageIndex = 1
			app.PageSize = 10
			app.Status = -1

			got, _, err := h.GetList(app)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDisSalesmanService_SaleManAdd(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in distribution_vo.DisSalesman
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    viewmodel.BaseHttpResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "添加业务员"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &DisSalesmanService{
				BaseService: tt.fields.BaseService,
			}

			app := distribution_vo.DisSalesman{}
			app.Mobile = "13126132230"
			app.Name = "周111AAA"
			app.RegionId = 1
			app.ShopId = 1
			app.OrgId = 3
			app.EmployeeNo = "YGBH001"

			err := h.SaleManAdd(app)
			if (err != nil) != tt.wantErr {
				t.Errorf("SaleManAdd() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

		})
	}
}

func TestDisSalesmanService_SaleManEdit(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in distribution_vo.DisSalesman
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    viewmodel.BaseHttpResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "业务员编辑"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &DisSalesmanService{
				BaseService: tt.fields.BaseService,
			}
			app := distribution_vo.DisSalesman{}
			app.Mobile = "138****2233"
			app.Name = "周22222"
			app.RegionId = 1
			app.ShopId = 1
			app.OrgId = 3
			app.Id = "6"
			app.EmployeeNo = "aaa"
			err := h.SaleManEdit(app)
			if (err != nil) != tt.wantErr {
				t.Errorf("SaleManEdit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

		})
	}
}

func TestDisSalesmanService_SaleManStopOrStar(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in distribution_vo.DisSalesmanStopReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    viewmodel.BaseHttpResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "启用停用"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &DisSalesmanService{
				BaseService: tt.fields.BaseService,
			}
			app := distribution_vo.DisSalesmanStopReq{}
			app.Id = "6"
			app.Status = 2
			err := h.SaleManStopOrStar(app)
			if (err != nil) != tt.wantErr {
				t.Errorf("SaleManStopOrStar() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

		})
	}
}

func TestDisSalesmanService_SetSaleManBarCode(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in distribution_vo.DisSalesman
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "业务员生成二维码"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &DisSalesmanService{
				BaseService: tt.fields.BaseService,
			}
			h.SetSaleManBarCode()
		})
	}
}

func TestDisSalesmanService_SetSaleManData(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in distribution_vo.DisSalesman
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "生成业务员数据"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &DisSalesmanService{
				BaseService: tt.fields.BaseService,
			}
			h.SetSaleManData()
		})
	}
}

func TestDisSalesmanService_Get(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in distribution_vo.DisSalesmanStopReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    distribution_vo.DisSalesmanView
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "查询业务员信息"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &DisSalesmanService{
				BaseService: tt.fields.BaseService,
			}
			in := distribution_vo.DisSalesmanStopReq{}
			in.Id = "1779764138497060864"
			got, err := h.Get(in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Get() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDisSalesmanService_SetdistributorInSystem(t *testing.T) {
	tests := []struct {
		name string
		h    *DisSalesmanService
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.h.SetdistributorInSystem()
		})
	}
}
