package services

import (
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"testing"
)

func TestDisGoodsService_CommissionSetting(t *testing.T) {
	type args struct {
		req vo.GoodsCommissionSetReq
	}
	tests := []struct {
		name    string
		s       DisGoodsService
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "aa",
			args: args{
				req: vo.GoodsCommissionSetReq{
					OrgId:               4,
					IsDis:               1,
					GoodsId:             1,
					DisCommisRate:       10,
					IsDefaultCommisRate: 1,
					DisWrite:            " dfdf",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.s.CommissionSetting(tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("DisGoodsService.CommissionSetting() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDisGoodsService_GoodsDisSync(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		GoodsIds []int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				GoodsIds: []int{
					110003,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := DisGoodsService{
				BaseService: tt.fields.BaseService,
			}
			if err := s.GoodsDisSync(tt.args.GoodsIds); (err != nil) != tt.wantErr {
				t.Errorf("GoodsDisSync() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
