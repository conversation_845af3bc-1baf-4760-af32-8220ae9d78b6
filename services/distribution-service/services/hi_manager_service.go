package services

import (
	"context"
	"eShop/infra/cache"
	"eShop/services/common"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	vo "eShop/view-model/distribution-vo"
	"fmt"
	_ "github.com/go-sql-driver/mysql"
	"math/rand"
	"strconv"
	"time"
)

type HiManagerService struct {
	common.BaseService
}

func NewHiManagerSercice(ctx context.Context) HiManagerService {
	return HiManagerService{
		BaseService: common.BaseService{
			Ctx: ctx,
		},
	}
}

func (h HiManagerService) TestDb() {
	h.<PERSON>gin()
	defer h.Close()
	// 将子上下文传入Session
	session := h.Engine.Context(h.Ctx)
	rand.Seed(time.Now().UnixNano())

	// 插入一条数据
	var u vo.DisSalesman
	u.EmployeeNo = strconv.Itoa(rand.Intn(9999))
	time.Now().Unix()
	u.Name = "lisan42"

	_, err := session.InsertOne(&u)
	if err != nil {
		panic(err)
	}
}

func (h HiManagerService) TestTran() {
	h.<PERSON>()
	defer h.Close()
	// 将子上下文传入Session
	session := h.Engine.Context(h.Ctx)
	rand.Seed(time.Now().UnixNano())

	// 插入一条数据
	var u vo.DisSalesman
	u.EmployeeNo = strconv.Itoa(rand.Intn(9999))
	time.Now().Unix()
	u.Name = "lisan42"

	_, err := session.InsertOne(&u)
	if err != nil {
		panic(err)
	}
}

func (h HiManagerService) TestCache() {
	h.Begin()
	defer h.Close()

	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])

	mCache.Save("a", "a", "111", -1)
	mCache.Save("a", "c", "111", -1)
	//var result = mCache.Get("a", "a")
	result := mCache.Get("a", "a", "b", "c")

	mCache.Delete("a", "a", "b", "c")

	if mCache.TryLock("a", "lock-a", time.Duration(10)*time.Second) {
		mCache.Unlock("a", "lock-a")
	}

	fmt.Println(result)
}
