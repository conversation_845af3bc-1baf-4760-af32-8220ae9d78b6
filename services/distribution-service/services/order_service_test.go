package services

import (
	"eShop/infra/log"
	"eShop/services/common"
	"eShop/view-model"
	vo "eShop/view-model/distribution-vo"
	"fmt"
	_ "github.com/go-sql-driver/mysql"
	"reflect"
	"strings"
	"testing"
)

func TestDisOrderService_GetDisOrderList(t *testing.T) {
	type args struct {
		in vo.GetDisOrderListReq
	}
	tests := []struct {
		name      string
		h         *DisOrderService
		args      args
		wantOut   []vo.DisOrderListView
		wantTotal int
		wantErr   bool
	}{
		// TODO: Add test cases.
		{
			name: "分销订单",
			h:    &DisOrderService{},
			args: args{
				in: vo.GetDisOrderListReq{
					BasePageHttpRequest: viewmodel.BasePageHttpRequest{PageIndex: 1, PageSize: 10},
					OrderState:          -1,
					OrgId:               3,
					WhereType:           "",
					Where:               "",
					WhereType2:          "",
					WhereStart:          "",
					WhereEnd:            "",
				},
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, gotTotal, err := tt.h.GetDisOrderList(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisOrderService.GetDisOrderList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DisOrderService.GetDisOrderList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("DisOrderService.GetDisOrderList() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

func TestDisOrderService_GetDisOrderListApi(t *testing.T) {
	type args struct {
		in vo.GetDisOrderListApiReq
	}
	tests := []struct {
		name      string
		h         *DisOrderService
		args      args
		wantOut   []vo.DisOrderData
		wantTotal int
		wantErr   bool
	}{
		// TODO: Add test cases.
		{
			name: "小程序端分销订单列表",
			h:    &DisOrderService{},
			args: args{
				in: vo.GetDisOrderListApiReq{
					BasePageHttpRequest: viewmodel.BasePageHttpRequest{PageIndex: 1, PageSize: 100000},
					OrgId:               3,
					OrderState:          0,
					SearchValue:         "",
					MemberId:            138763,
					//AddTimeStart:        "2020-02-10 01:08:35",
					//AddTimeEnd:          "2020-02-10 15:08:35",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			log.Init()
			gotOut, gotTotal, _, _, err := tt.h.GetDisOrderListApi(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisOrderService.GetDisOrderListApi() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DisOrderService.GetDisOrderListApi() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("DisOrderService.GetDisOrderListApi() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

func TestDisOrderService_GetDisOrderListApi1(t *testing.T) {
	var GoodsImage = "http://file.petrevt.com/files/20201211/de19862ce8eddfd7f1ec0911ab11254b.png"
	if !strings.HasPrefix(GoodsImage, "https://") || !strings.HasPrefix(GoodsImage, "http://") {
		fmt.Println("图片地址不合法")
	} else {
		fmt.Println("图片地址合法")
	}
}

func TestDisOrderService_OrderSetted(t *testing.T) {
	type args struct {
		in vo.OrderSettedReq
	}
	tests := []struct {
		name    string
		h       *DisOrderService
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "百林康源分销单直接结算",
			h:    &DisOrderService{},
			args: args{
				in: vo.OrderSettedReq{
					Data: []vo.SettedData{},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.h.OrderSetted(tt.args.in); (err != nil) != tt.wantErr {
				t.Errorf("DisOrderService.OrderSetted() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDisOrderService_ExportDisOrderList(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in vo.GetDisOrderListReq
	}
	tests := []struct {
		name      string
		fields    fields
		args      args
		wantOut   []vo.DisOrderListView
		wantTotal int
		wantErr   bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				in: vo.GetDisOrderListReq{
					BasePageHttpRequest: viewmodel.BasePageHttpRequest{PageIndex: 1, PageSize: 10},
					OrgId:               4,
					OrderState:          -1,
					LogisticsCode:       "602117428012",
					WhereType:           "",
					WhereType2:          "",
				},
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &DisOrderService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, gotTotal, err := h.ExportDisOrderList(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExportDisOrderList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("ExportDisOrderList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("ExportDisOrderList() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}
