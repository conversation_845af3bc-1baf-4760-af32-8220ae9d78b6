package services

import (
	"eShop/services/common"
	"eShop/view-model"
	"eShop/view-model/distribution-vo"
	"reflect"
	"testing"
)

func TestStatsSkuCityDailyService_GetStatsSkuCityDailyByDate(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req distribution_vo.StatsSkuCityDailyPageReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []distribution_vo.StatsSkuCityDaily
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				req: distribution_vo.StatsSkuCityDailyPageReq{
					//ProductName: "test1",
					//Skuid:       1212,
					StartDate: "2024-06-12",
					EndDate:   "2024-07-11",
					BasePageHttpRequest: viewmodel.BasePageHttpRequest{
						PageIndex: 1,
						PageSize:  10,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := StatsSkuCityDailyService{
				BaseService: tt.fields.BaseService,
			}
			got, _, err := s.GetStatsSkuCityDailyByDate(tt.args.req)
			t.Log(got, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetStatsSkuCityDailyByDate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestStatsSkuCityDailyService_GetStatsSkuCityDailyByDateAndSkuid(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req distribution_vo.StatsSkuCityDailyPageReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []distribution_vo.StatsSkuCityDaily
		want1   int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test2",
			args: args{
				req: distribution_vo.StatsSkuCityDailyPageReq{
					//ProductName: "test1",
					//Skuid:     110002,
					StartDate: "2024-06-12",
					EndDate:   "2024-07-30",
					BasePageHttpRequest: viewmodel.BasePageHttpRequest{
						PageIndex: 1,
						PageSize:  10,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := StatsSkuCityDailyService{
				BaseService: tt.fields.BaseService,
			}
			got, got1, err := s.GetStatsSkuCityDailyBySkuid(tt.args.req)
			t.Log(got, got1, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetStatsSkuCityDailyByDateAndSkuid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetStatsSkuCityDailyByDateAndSkuid() got = %v, want %v", got, tt.want)
			}
		})
	}
}
