package services

import (
	distribution_po "eShop/domain/distribution-po"
	po "eShop/domain/omnibus-po"
	infraEnum "eShop/infra/enum"
	"eShop/infra/log"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
	diswithdrawrecord "eShop/services/distribution-service/enum/dis-withdraw-record"
	vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"math"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/spf13/cast"
	"xorm.io/xorm"
)

type BlkyCodeService struct {
	common.BaseService
}

func (s BlkyCodeService) QueryCode(code string) vo.BlkyQueryCodeData {
	s.Begin()
	defer s.Close()
	session := s.Session

	response := vo.BlkyQueryCodeData{}

	var queryData []vo.QueryData

	err := session.SQL(
		`select kc.sdxtm,kc.swlm,
       case when xd.state=1 then 1 else 0 end as state,
       xd.update_time,
       og.goods_pay_price,
       og.dis_commis_rate,
       og.dis_member_id,
       og.goods_name,
       u.org_id,
       dd.real_name as name,
       ddd.hospital_name,
       ddd.province,
       ddd.city
from blky.xkucun kc
left join blky.xkucun_detail xd  on xd.swlm=kc.swlm
left join upetmart.upet_orders o on o.logistics_code=kc.swlm and o.order_father>0
left join upetmart.upet_order_goods  og on og.order_id=o.order_id
left join eshop.users u on u.member_id=og.dis_member_id and u.org_id=og.store_id
left join eshop.dis_distributor dd on dd.member_id=og.dis_member_id and dd.org_id=u.org_id
left join eshop.dis_distributor_detail ddd on dd.id=ddd.dis_id
where kc.swlm=? or kc.sdxtm=? order by state desc,swlm`, code, code).Find(&queryData)

	if err != nil || len(queryData) == 0 {
		return response
	}

	response.CaseCode = queryData[0].Sdxtm //箱码

	for _, v := range queryData {
		var lc vo.LogisticsCodes
		lc.LogisticsCodes = v.Swlm
		lc.Unused = !v.State

		if v.State { //已使用，则要展示出相应的数据
			lc.Unused = false
			var detail vo.ProductUsedDetail
			detail.AreaName = fmt.Sprintf("%s/%s", v.Province, v.City)
			detail.Commission = int(v.GoodsPayPrice * v.DisCommisRate)
			detail.DoctorName = v.Name
			detail.Time = v.UpdateTime.Format(time.DateTime)
			detail.HospitalName = v.HospitalName
			detail.ProductName = v.GoodsName
			lc.UsedDetail = detail
		}
		response.Data = append(response.Data, lc)
	}
	return response
}

func (s BlkyCodeService) QueryCodeList(in vo.BlkyQueryCodeRequest) vo.BlkyCodeListData {
	s.Begin()
	defer s.Close()
	session := s.Session

	response := vo.BlkyCodeListData{}
	response.Result = "未被扫码登记"

	// 百林康源的物流码是12位或10位
	// 深圳利都的物流码是11位或10位
	if in.OrgType == 1 && in.Code != "" {
		re := regexp.MustCompile(`^\d{12}$|^\d{10}$`)
		if !re.MatchString(in.Code) {
			response.Result = "箱码或物流码ID不存在"
			return response
		}
	} else if in.OrgType == 2 && in.Code != "" {
		re := regexp.MustCompile(`^\d{11}$|^\d{10}$`)
		if !re.MatchString(in.Code) {
			response.Result = "箱码或物流码ID不存在"
			return response
		}
	}

	var queryData []vo.QueryData
	if in.OrgType == 1 {
		err := session.SQL(
			`SELECT kc.sdxtm,
       kc.swlm,
       from_unixtime(o.add_time) as update_time,
       og.goods_pay_price,
       og.dis_commis_rate,
       og.dis_member_id,
       og.goods_name,
       og.shop_id,
       dd.real_name as name,
       dd.mobile,
	   dd.encrypt_mobile,
       ddd.hospital_name,
       ddd.province,
       ddd.city,
       dt.wait_withdraw,
	   s.wait_withdraw as shop_wait_withdraw,
       o.order_sn,
	   e.enterprise_name,
        IF(cps.id > 0, 1, 0) AS commiss_status,
        cps.dis_commis_amount,
		e.province as enterprice_province,
		e.city as enterprice_city
FROM blky.xkucun kc
     LEFT JOIN upetmart.upet_orders o ON o.logistics_code = kc.swlm AND o.order_father > 0 and o.swlm_status=1
     LEFT JOIN upetmart.upet_order_goods og ON og.order_id = o.order_id
     LEFT JOIN eshop.dis_distributor dd ON dd.member_id = og.dis_member_id AND dd.org_id = o.store_id
     LEFT JOIN eshop.dis_distributor_detail ddd ON dd.id = ddd.dis_id
     LEFT JOIN eshop.dis_distributor_total dt ON dt.dis_id = dd.id AND dd.org_id = dt.org_id and dt.shop_id = og.shop_id
	 LEFT JOIN eshop.dis_distributor_swlm_cps cps ON cps.order_sn = o.order_sn
	 left join eshop.shop s on s.org_id= dd.org_id and s.id=og.shop_id
     left join dis_enterprise e on s.enterprise_id = e.scrm_enterprise_id
WHERE (kc.swlm = ? OR kc.sdxtm = ?) AND o.swlm_status=1 AND o.org_type=1`, in.Code, in.Code).Find(&queryData)

		if err != nil {
			log.Errorf("查询百林康源已使用物流码列表失败:%s", err.Error())
			return response
		}
	} else {
		// 查询blky.sync_un_ubarcode,如果type=2,则提示【箱码作废，不支持查询】
		unUbarcode := &distribution_po.SyncUnUbarcode{}
		if has, err := unUbarcode.GetUnUbarcodeByType(session, in.Code, 2); err != nil {
			log.Errorf("查询箱码作废记录失败:%s", err.Error())
			return response
		} else if has {
			response.Result = "箱码作废，不支持查询"
			return response
		}

		// 构建基础SQL查询
		baseSql := `
		SELECT
			kc.source_barcode as sdxtm,
			kc.barcode as swlm,
			from_unixtime(o.add_time) as update_time,
			og.goods_pay_price,
			og.dis_commis_rate,
			og.dis_member_id,
			og.goods_name,
			dd.real_name as name,
			dd.id,
			og.shop_id,
			dd.mobile,
			dd.encrypt_mobile,
			dd.dis_role,
			ddd.hospital_name,
			ddd.province,
			ddd.city,
			 dt.wait_withdraw,
	   s.wait_withdraw as shop_wait_withdraw,
			o.order_sn,
			IF(cps.id > 0, 1, 0) AS commiss_status,
			cps.dis_commis_amount,
			e.enterprise_name,
			e.province as enterprice_province,
			e.city as enterprice_city
		FROM blky.sync_outbound_log kc
			left JOIN upetmart.upet_orders o ON o.logistics_code = kc.barcode AND o.order_father > 0
			left JOIN upetmart.upet_order_goods og ON og.order_id = o.order_id
			LEFT JOIN eshop.dis_distributor_total dt ON og.dis_member_id = dt.member_id AND og.store_id = dt.org_id and og.shop_id = dt.shop_id
			LEFT JOIN eshop.dis_distributor dd ON dt.dis_id = dd.id
			LEFT JOIN eshop.dis_distributor_detail ddd ON dd.id = ddd.dis_id
			left join eshop.shop s on s.org_id= dt.org_id and s.id=dt.shop_id
			LEFT JOIN eshop.dis_distributor_swlm_cps cps ON cps.order_sn = o.order_sn
			left join eshop.dis_enterprise e on e.scrm_enterprise_id = s.enterprise_id`

		var err error
		if in.Code != "" {
			// 如果传入了code，添加条件查询
			baseSql += ` WHERE (kc.barcode = ? OR kc.source_barcode = ?) AND o.swlm_status=1 and o.org_type=2 group by kc.barcode,o.order_sn`
			err = session.SQL(baseSql, in.Code, in.Code).Find(&queryData)
		} else {
			// 如果没有传入code，只添加状态条件
			baseSql += ` WHERE o.swlm_status=1 and o.org_type=2 group by kc.barcode,o.order_sn`
			err = session.SQL(baseSql).Find(&queryData)
		}

		if err != nil {
			log.Errorf("查询宠利扫已使用物流码列表失败:%s", err.Error())
			return response
		}
	}

	if len(queryData) > 0 {
		// 获取 提现税率
		rate := utils.WithdrawTaxRate(enum.BLKYOrgId)
		response.IsExist = true
		response.Result = "已被扫码登记"
		for _, v := range queryData {
			var detail vo.ProductUsedDetail

			//提现过一次，可提现设置为0
			if v.DisCommisAmount > 0 {
				detail.Commission = 0
			} else {
				detail.Commission = cast.ToInt(math.Round(v.GoodsPayPrice * v.DisCommisRate))
			}
			detail.AfterTaxCommission = utils.GetAfterTaxAmount(detail.Commission, rate)
			if v.ShopId > 0 {
				detail.WaitWithdraw = v.ShopWaitWithdraw
				detail.AfterTaxWaitWithdraw = utils.GetAfterTaxAmount(v.ShopWaitWithdraw, rate)
				detail.AreaName = fmt.Sprintf("%s/%s", v.Province, v.City)
			} else {
				detail.WaitWithdraw = v.WaitWithdraw
				detail.AfterTaxWaitWithdraw = utils.GetAfterTaxAmount(v.WaitWithdraw, rate)
				detail.AreaName = fmt.Sprintf("%s/%s", v.EnterpriceProvince, v.EnterpriceCity)
			}

			detail.DoctorName = v.Name
			detail.Time = v.UpdateTime.Format(time.DateTime)
			detail.HospitalName = v.HospitalName
			detail.ProductName = v.GoodsName
			detail.SwlmStatus = v.SwlmStatus
			detail.Mobile = v.Mobile
			detail.EncryptMobile = v.EncryptMobile

			detail.Swlm = v.Swlm
			detail.OrderSn = v.OrderSn
			detail.CommissStatus = v.CommissStatus
			detail.EnterpriseName = v.EnterpriseName
			if detail.WaitWithdraw == 0 || detail.Commission == 0 {
				detail.CommissStatus = 1
			}
			response.UseData = append(response.UseData, detail)
		}
	}
	var xkucun []distribution_po.Xkucun
	if in.OrgType == 1 {
		// 使用xkucun表查询未使用物流码
		if err := session.SQL(`
		SELECT
			xk.swlm,xk.sfwm
		FROM
			blky.xkucun xk
			LEFT JOIN
			blky.xkucun_detail xkd ON xk.swlm = xkd.swlm
		WHERE
			(xkd.swlm IS NULL OR xkd.state = 0)
		AND (xk.swlm = ? OR xk.sdxtm = ?)`, in.Code, in.Code).Find(&xkucun); err != nil {
			return response
		} else {
			if len(xkucun) > 0 {
				response.IsExist = true
				for _, v := range xkucun {
					var xkucunDetail vo.Xkucun
					xkucunDetail.Swlm = v.Swlm
					response.UnuseData = append(response.UnuseData, xkucunDetail)
				}
			}
		}
	} else {
		// 使用xlogistics_code表查询未使用物流码
		if err := session.SQL(`
		SELECT
			DISTINCT xk.barcode as swlm,xk.source_barcode as sfwm
		FROM
			blky.sync_outbound_log xk
			LEFT JOIN
			blky.xlogistics_detail xkd ON xk.barcode = xkd.swlm
		WHERE
			(xkd.swlm IS NULL OR xkd.state = 0)
		AND (xk.source_barcode = ? OR xk.barcode = ?) group by xk.barcode
		`, in.Code, in.Code).Find(&xkucun); err != nil {
			return response
		} else {
			if len(xkucun) > 0 {
				response.IsExist = true
				for _, v := range xkucun {
					var xkucunDetail vo.Xkucun
					xkucunDetail.Swlm = v.Swlm
					response.UnuseData = append(response.UnuseData, xkucunDetail)
				}
			}
		}
	}

	if len(queryData) > 0 && len(xkucun) > 0 {
		response.Result = "部分被扫码登记"
	} else if len(queryData) == 0 && len(xkucun) == 0 {
		response.Result = "箱码或物流码ID不存在"
	}

	return response
}

// 百林康源分销员医生角色时分销的订单 扣除佣金并恢复物流码
func (s BlkyCodeService) DeductCommission(in vo.CommissionWithdrawReq) (Description string, err error) {
	logfiex := fmt.Sprintf("扣除佣金,参数:%s", utils.InterfaceToJSON(in))
	log.Info(logfiex)
	s.Begin()
	defer s.Close()
	session := s.Session

	var (
		cpsStatus        = distribution_po.StatusDeducted
		cpsIsSelfBalance = distribution_po.IsSelfBalanceDefault
	)
	// 1. 加redis锁10s
	lockKey := fmt.Sprintf("deduct_blky_code_lock_%s", in.Code)
	if !cache.RedisLock(lockKey, 10) {
		return "", errors.New("系统繁忙，请稍后再试")
	}
	if in.OrgType != 1 && in.OrgType != 2 {
		return "", errors.New("所属企业错误")
	}
	if in.Code == "" {
		return "", errors.New("物流码不能为空")
	}
	if in.OrderSn == "" {
		return "", errors.New("订单号不能为空")
	}

	// 判断DisDistributorSwlmCps是否有记录，有则不能再进行扣除
	var cps distribution_po.DisDistributorSwlmCps
	has, err := session.Table("eshop.dis_distributor_swlm_cps").Where("swlm=? and order_sn=?", in.Code, in.OrderSn).Get(&cps)
	if err != nil {
		return "", err
	}
	if has {
		return "", errors.New("该物流码已进行佣金扣除，请勿重复操作")
	}

	// 2. 查询出该物流码对应的订单 upetmart.upet_orders,查询出该订单的佣金upetmart.upet_order_goods
	var w vo.CommissionWithdraw
	if has, err := session.SQL(`
		SELECT
			ROUND(og.goods_pay_price * og.dis_commis_rate, 0) AS commission,o.logistics_code,d.id,d.wait_withdraw,s.wait_withdraw as shop_wait_withdraw,og.shop_id,
			d.dis_id,d.org_id,dd.real_name as name,dd.member_id,dd.mobile,dd.encrypt_mobile,o.order_sn,o.swlm_status
		FROM upetmart.upet_orders o
			INNER JOIN upetmart.upet_order_goods og ON o.order_id = og.order_id
			LEFT JOIN eshop.dis_distributor_total d ON d.member_id = og.dis_member_id AND d.org_id = o.store_id and d.shop_id = og.shop_id
			LEFT JOIN eshop.dis_distributor dd ON dd.id = d.dis_id AND dd.org_id = o.store_id
			LEFT JOIN eshop.shop s on s.org_id = o.store_id and s.id = og.shop_id
		WHERE o.store_id = 4
		AND o.order_father > 0
		AND o.swlm_status = 1
		AND o.logistics_code = ? 
		AND o.order_sn =?`, in.Code, in.OrderSn).Get(&w); err != nil {
		log.Errorf("查询佣金失败，错误为:%s", err.Error())
		return "", err
	} else if !has {
		return "", errors.New("物流码不存在或状态异常")
	}

	// 百林康源 分销员是医生角色，走这里， 如果是分销员入驻了企业， 则不走这个函数。
	// if w.ShopId != 0 {
	// 	return "", fmt.Errorf("宠利扫佣金扣除失败，请稍后再试")
	// }

	// 宠利扫 分销员入驻了企业
	if w.ShopId > 0 {
		// 查询该订单是否有提现中和提现成功的记录
		withdrawOrder, err := new(distribution_po.DisWithdrawOrder).GetSuccessWithdrawOrder(session, w.ShopId, []int64{cast.ToInt64(w.OrderSn)})
		if err != nil {
			return "", err
		}
		if len(withdrawOrder) == 0 {
			cpsStatus = distribution_po.StatusDeducted
			cpsIsSelfBalance = distribution_po.IsSelfBalance
		} else {
			cpsStatus = distribution_po.StatusNotDeduct
			cpsIsSelfBalance = distribution_po.IsNotSelfBalance
			// 判断该企业待提现金额是否大于登记佣金
			if w.Commission == 0 || w.ShopWaitWithdraw == 0 {
				return "", errors.New("佣金金额或提现佣金为0，不能扣除")
			}
			if w.Commission > w.ShopWaitWithdraw {
				return "", fmt.Errorf("待提现金额小于登记佣金，无法进行扣除")
			}
		}
	} else {
		if w.Commission == 0 || w.WaitWithdraw == 0 {
			return "", errors.New("佣金金额或提现佣金为0，不能扣除")
		}

		if w.Commission > w.WaitWithdraw {
			return "", fmt.Errorf("待提现金额小于登记佣金，无法进行扣除")
		}
	}

	session.Begin()
	//扣除佣金, 添加eshop.dis_withdraw_record记录
	insertSli := &distribution_po.DisWithdrawRecord{
		OrgId:         w.OrgId,
		ShopId:        w.ShopId,
		DisId:         w.DisId, //  计算出具体提到谁的身上
		WithdrawDisId: 0,
		Type:          diswithdrawrecord.TypeDeductCommission,
		WaitWithdraw:  -w.Commission,
	}

	if _, err = session.Insert(insertSli); err != nil {
		session.Rollback()
		log.Errorf("%s-添加DisWithdrawRecord失败，错误为:%s", logfiex, err.Error())
		return "", err
	}
	// 添加物流码佣金扣除记录
	cpsLog := &distribution_po.DisDistributorSwlmCps{
		DisMemberID:     w.MemberId,
		Swlm:            w.LogisticsCode,
		OrderSn:         w.OrderSn,
		DisCommisAmount: w.Commission,
		Status:          cpsStatus,
		IsSelfBalance:   cpsIsSelfBalance,
		CreateTime:      time.Now(),
		UpdateTime:      time.Now(),
	}
	if _, err = session.Insert(cpsLog); err != nil {
		session.Rollback()
		log.Errorf("%s-添加DisDistributorSwlmCps失败，错误为:%s", logfiex, err.Error())
		return "", err
	}
	isRestore := false
	//恢复物流码
	_, err = RestoreCode(session, vo.BlkyQueryCodeRequest{
		Code:    in.Code,
		OrgType: in.OrgType,
	})
	if err != nil && err.Error() != "物流码已恢复，请勿重复操作" {
		session.Rollback()
		log.Errorf("%s-恢复物流码失败，错误为:%s", logfiex, err.Error())
		return "", errors.New("恢复物流码失败")
	}
	if err == nil || err.Error() == "物流码已恢复，请勿重复操作" {
		isRestore = true
	}

	session.Commit()

	needUpdateMap := make(map[string]string)
	needUpdateMap[fmt.Sprintf("%d:%d:%d", w.OrgId, w.ShopId, w.DisId)] = ""

	go func() {
		new(DiscommissionService).WithdrawCommUpdate(needUpdateMap, enum.WithdrawFlagShop)
	}()

	Description = utils.JsonEncode(&vo.CommissionWithdrawLog{
		DisId:         w.DisId,
		Commission:    w.Commission,
		WaitWithdraw:  w.WaitWithdraw,
		Name:          w.Name,
		OrderSn:       w.OrderSn,
		Mobile:        w.Mobile,
		EncryptMobile: w.EncryptMobile,
		LogisticsCode: w.LogisticsCode,
		IsRestore:     isRestore,
	})
	return Description, nil
}

// 恢复物流码
func RestoreCode(session *xorm.Session, in vo.BlkyQueryCodeRequest) (w vo.CommissionWithdraw, err error) {
	// 1. 加redis锁10s
	lockKey := fmt.Sprintf("restore_blky_code_lock_%s", in.Code)
	if !cache.RedisLock(lockKey, 10) {
		err = errors.New("系统繁忙，请稍后再试")
		return
	}

	// 查询
	if _, err = session.SQL(`
		SELECT 
			o.logistics_code,dd.mobile,dd.encrypt_mobile,o.order_sn,o.swlm_status,dd.real_name as name
		FROM upetmart.upet_orders o
			INNER JOIN upetmart.upet_order_goods og ON o.order_id = og.order_id
			LEFT JOIN eshop.dis_distributor dd ON og.dis_member_id = dd.member_id and dd.org_id = o.store_id
			LEFT JOIN eshop.dis_distributor_total d ON dd.id = d.dis_id
		WHERE o.store_id = 4
		AND o.order_father > 0
		AND o.swlm_status=1
		AND o.logistics_code = ?`, in.Code).Get(&w); err != nil {
		log.Errorf("查询佣金失败，错误为:%s", err.Error())
		return
	}

	if w.SwlmStatus == 0 {
		err = errors.New("物流码已恢复，请勿重复操作")
		return
	}

	//恢复物流码
	if _, err = session.Exec(`update upetmart.upet_orders set swlm_status=0,order_state=0 where logistics_code=?`, in.Code); err != nil {
		log.Errorf("物流码恢复-更新订单失败，错误为:%s", err.Error())
		return
	}

	if in.OrgType == 1 {
		if _, err = session.Exec(`update blky.xkucun_detail set state=0 where swlm=?`, in.Code); err != nil {
			log.Errorf("物流码恢复-更新记录失败，错误为:%s", err.Error())
			return
		}
	} else {
		if _, err = session.Exec(`update blky.xlogistics_detail set state=0 where swlm=?`, in.Code); err != nil {
			log.Errorf("物流码恢复-更新记录失败，错误为:%s", err.Error())
			return
		}
	}

	//将结算订单设置成已取消
	if _, err = session.Exec(`update eshop.dis_settlement set status=3 where order_no=?`, w.OrderSn); err != nil {
		log.Errorf("物流码恢复-更新结算订单失败，错误为:%s", err.Error())
		return
	}

	return
}

// RestoreCode 物流码恢复
func (s BlkyCodeService) RestoreCode(in vo.BlkyQueryCodeRequest) (Description string, err error) {
	s.Begin()
	defer s.Close()
	session := s.Session

	w, err := RestoreCode(session, in)
	if err != nil {
		return "", err
	}
	Description = utils.JsonEncode(&vo.CommissionWithdrawLog{
		Name:          w.Name,
		OrderSn:       w.OrderSn,
		Mobile:        w.Mobile,
		EncryptMobile: w.EncryptMobile,
		LogisticsCode: w.LogisticsCode,
	})
	return Description, nil
}

// 物流码批量导入
func (s BlkyCodeService) BatchSwlmImport(in vo.SwlmImportReq) (err error) {
	s.Begin()
	defer s.Close()
	session := s.Session
	// 判断blky.xcode_detail有没有数据，没有就添加，有则更新
	var xcodeDetail distribution_po.XcodeDetail
	has, err := session.Table("blky.xcode_detail").Where("swlm=? and org_type=?", in.Code, in.OrgType).Get(&xcodeDetail)
	if err != nil {
		return err
	}
	if !has {
		xcodeDetail.Swlm = in.Code
		xcodeDetail.Sdxtm = in.Sdxtm
		xcodeDetail.OrgType = in.OrgType
		if in.CommissionRate == 0 {
			xcodeDetail.DisCommisRate = 0
		} else {
			xcodeDetail.DisCommisRate = utils.Yuan2Fen(in.CommissionRate)
		}
		xcodeDetail.CreateTime = time.Now()
		xcodeDetail.UpdateTime = time.Now()
		if _, err := session.Table("blky.xcode_detail").Insert(&xcodeDetail); err != nil {
			return err
		}
	} else {
		xcodeDetail.UpdateTime = time.Now()
		if in.CommissionRate == 0 {
			xcodeDetail.DisCommisRate = 0
		} else {
			xcodeDetail.DisCommisRate = utils.Yuan2Fen(in.CommissionRate)
		}
		if _, err := session.Table("blky.xcode_detail").Cols("dis_commis_rate,update_time").Where("id=?", xcodeDetail.Id).Update(&xcodeDetail); err != nil {
			return err
		}
	}
	return nil
}

// 删除结算管理记录
func (s BlkyCodeService) DelTestData(in vo.DelDataReq) error {
	s.Begin()
	defer s.Close()
	session := s.Session
	switch in.Type {
	case 1:
		if _, err := session.Table("eshop.dis_distributor").Where("id=? and org_id =4", cast.ToInt(in.FromId)).Update(map[string]interface{}{"hide_state": 1}); err != nil {
			return err
		}
	case 2:
		if _, err := session.Table("upetmart.upet_orders").Where("order_sn=? and store_id =4", in.FromId).Update(map[string]interface{}{"hide_state": 1}); err != nil {
			return err
		}
	case 3:
		if _, err := session.Table("eshop.dis_settlement").Where("id=? and org_id =4", cast.ToInt(in.FromId)).Update(map[string]interface{}{"hide_state": 1}); err != nil {
			return err
		}
	case 4:
		if _, err := session.Table("eshop.dis_withdraw").Where("id=? and org_id =4", cast.ToInt(in.FromId)).Update(map[string]interface{}{"hide_state": 1}); err != nil {
			return err
		}
	}

	return nil
}

// 同步贵族商品码数据
func (s BlkyCodeService) SyncCodeDate(in vo.SyncCodeDateReq, r *http.Request) error {
	var (
		key = "w0BAQEFAASCAIBADANBgkqhkiG9" //用来签名
	)
	s.Begin()
	defer s.Close()
	session := s.Session

	nowUnix := time.Now().Unix()
	if cast.ToInt(in.Timestamp) < int(nowUnix-300) || cast.ToInt(in.Timestamp) > int(nowUnix+300) {
		log.Errorf("同步贵族商品码数据-时间戳错误:%s,%d", utils.InterfaceToJSON(in), nowUnix)
		return errors.New("时间戳错误")
	}

	if in.BatchNum == "" {
		log.Errorf("同步贵族商品码数据-batch_num不能为空:%s", utils.InterfaceToJSON(in))
		return errors.New("batch_num不能为空")
	}

	if cast.ToInt(in.FileType) != 1 && cast.ToInt(in.FileType) != 2 {
		log.Errorf("同步贵族商品码数据-file_type错误:%s", utils.InterfaceToJSON(in))
		return errors.New("file_type错误")
	}

	if in.FileUrl == "" {
		log.Errorf("同步贵族商品码数据-文件流错误:%s", utils.InterfaceToJSON(in))
		return errors.New("文件流错误")
	}

	//验证签名
	if in.Sign != strings.ToUpper(utils.GetMd5String(fmt.Sprintf("key=%s&timestamp=%s&batch_num=%s&file_type=%s", key, in.Timestamp, in.BatchNum, in.FileType))) {
		log.Errorf("同步贵族商品码数据-签名错误:%s", utils.InterfaceToJSON(in))
		return errors.New("sign签名错误")
	}

	if isExist, err := session.Table("task_list_async").Where("task_content = ? and task_status in (1,2) and key_str = ?", infraEnum.SyncXkucunCodeDateTaskContent, in.BatchNum).Exist(); err != nil {
		return err
	} else if isExist {
		return errors.New("batch_num批次号已存在")
	}

	item := po.TaskListAsync{
		TaskContent:      infraEnum.SyncXkucunCodeDateTaskContent,
		TaskStatus:       0,
		OperationFileUrl: utils.JsonEncode(in),
		ExtendedData:     infraEnum.SyncTaskContentMap[infraEnum.SyncXkucunCodeDateTaskContent],
		CreateTime:       time.Now(),
		UpdateTime:       time.Now(),
	}

	if _, err := session.Insert(item); err != nil {
		log.Errorf("同步贵族商品码数据-保存任务失败:%s,%s", utils.InterfaceToJSON(in), err.Error())
		return errors.New("保存任务失败")
	}

	return nil
}
