package services

import (
	distribution_po "eShop/domain/distribution-po"
	"eShop/infra/log"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
	distribution_vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"time"

	"github.com/spf13/cast"
)

type DisShopService struct {
	common.BaseService
}

func (s DisShopService) DisShopList(req distribution_vo.DisShopListReq) ([]distribution_vo.ShopExt, int, error) {
	s.Begin()
	defer s.Close()

	session := s.Session
	var list []distribution_vo.ShopExt

	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}
	if req.OrgId == enum.BLKYOrgId {
		session.Table("shop").<PERSON><PERSON>("s").
			Join("LEFT", "dis_enterprise de", "de.scrm_enterprise_id = s.enterprise_id and de.org_id = ?", req.OrgId).
			Join("LEFT", "scrm_enterprise en", "en.id = s.enterprise_id").
			Join("LEFT", "dis_distributor dd", "s.id=dd.shop_id AND dd.dis_role =1").
			Join("LEFT", "scrm_salesperson ssp", "s.registered_salesperson=ssp.id").
			Join("LEFT", "(SELECT e.id,sp.id AS salesperson_id,GROUP_CONCAT(sp.name) AS 'salesperson_names' "+
				"FROM scrm_enterprise e LEFT JOIN scrm_enterprise_salesperson_bind esb ON e.id=esb.enterprise_id LEFT JOIN scrm_salesperson sp "+
				"ON esb.salesperson_id=sp.id GROUP BY e.id) se", "de.scrm_enterprise_id = se.id").
			Join("LEFT", "(SELECT shop_id,COUNT(1) AS dis_num,SUM(total_customer) AS total_customer "+
				"FROM dis_distributor GROUP BY shop_id) dis", "s.id = dis.shop_id").
			//查询dis_distributor_fans有效期内的粉丝数量
			Join("LEFT", "(SELECT shop_id,COUNT(1) AS effective_dis_num FROM dis_distributor_fans "+
				"WHERE expire_time > NOW() GROUP BY shop_id) dis_fans", "s.id = dis_fans.shop_id").
			Select("s.*,(s.`settled_commission`+s.ins_settled_commission) AS settled_commission_total," +
				"(s.unsettled_commission+s.ins_unsettled_commission) AS unsettled_commission_total," +
				"(s.`settled_commission`+s.ins_settled_commission+s.unsettled_commission+s.ins_unsettled_commission) as commission_total," +
				"de.id as dis_enterprise_id,de.enterprise_name,en.enterprise_status,de.province,de.city,de.district,dd.real_name AS distributor_name,de.data_source," +
				"dd.mobile AS distributor_mobile,dd.encrypt_mobile,dd.source_type,dd.dis_role,se.salesperson_names,IFNULL(dis_num,0) AS dis_num," +
				"dis.total_customer,ssp.name AS reg_salesperson_name,IFNULL(dis_fans.effective_dis_num,0) AS effective_dis_num")

	} else {
		session.Table("shop").Alias("s").
			Join("LEFT", "scrm_enterprise en", "en.id=s.enterprise_id").
			Join("LEFT", "dis_distributor dd", "s.id=dd.shop_id AND dd.dis_role =1").
			Join("LEFT", "scrm_salesperson ssp", "s.registered_salesperson=ssp.id").
			Join("LEFT", "(SELECT e.id,sp.id AS salesperson_id,GROUP_CONCAT(sp.name) AS 'salesperson_names' "+
				"FROM scrm_enterprise e LEFT JOIN scrm_enterprise_salesperson_bind esb ON e.id=esb.enterprise_id LEFT JOIN scrm_salesperson sp "+
				"ON esb.salesperson_id=sp.id GROUP BY e.id) se", "s.enterprise_id = se.id").
			Join("LEFT", "(SELECT shop_id,COUNT(1) AS dis_num,SUM(total_customer) AS total_customer "+
				"FROM dis_distributor GROUP BY shop_id) dis", "s.id = dis.shop_id").
			//查询dis_distributor_fans有效期内的粉丝数量
			Join("LEFT", "(SELECT shop_id,COUNT(1) AS effective_dis_num FROM dis_distributor_fans "+
				"WHERE expire_time > NOW() GROUP BY shop_id) dis_fans", "s.id = dis_fans.shop_id").
			Select("s.*,(s.`settled_commission`+s.ins_settled_commission) AS settled_commission_total," +
				"(s.unsettled_commission+s.ins_unsettled_commission) AS unsettled_commission_total," +
				"(s.`settled_commission`+s.ins_settled_commission+s.unsettled_commission+s.ins_unsettled_commission) as commission_total," +
				"en.enterprise_name,en.enterprise_type,en.enterprise_status,en.province,en.city,en.district,dd.name AS distributor_name," +
				"dd.mobile AS distributor_mobile,dd.encrypt_mobile,dd.source_type,dd.dis_role,se.salesperson_names,IFNULL(dis_num,0) AS dis_num," +
				"dis.total_customer,ssp.name AS reg_salesperson_name,IFNULL(dis_fans.effective_dis_num,0) AS effective_dis_num")
	}

	if req.OrgId > 0 {
		session.And("s.org_id = ?", req.OrgId)
	}
	if req.OrgId == enum.BLKYOrgId {
		session.And("s.enterprise_id >0")
	}
	if req.Status > 0 {
		if req.OrgId == enum.BLKYOrgId {
			// 启用
			if req.Status == 1 {
				session.And(" (de.scrm_enterprise_id = 0 or  en.enterprise_status = 1", req.Status)
			}
			// 禁用
			if req.Status == 2 {
				session.And(" (de.scrm_enterprise_id =0 or  en.enterprise_status = 0)", req.Status)
			}
		} else {
			if req.Status == 1 {
				session.And("en.enterprise_status = ?", 1)
			}
			if req.Status == 2 {
				session.And("en.enterprise_status = ?", 0)
			}
		}

	}
	if req.IsSettedShop > 0 {
		session.And("s.is_setted_shop = ?", req.IsSettedShop)
	}

	//企业id/企业名称
	if req.EnterpriseName != "" {
		if req.OrgId == enum.BLKYOrgId {
			session.And("de.enterprise_name = ? or de.scrm_enterprise_id = ?", req.EnterpriseName, cast.ToInt64(req.EnterpriseName))
		} else {
			session.And("s.enterprise_id in (select id from scrm_enterprise where id = ? or enterprise_name like '%"+req.EnterpriseName+"%')", req.EnterpriseName)
		}
	}

	//查询条件的类型（1-老板Id 2-老板姓名、3-老板手机号、4-所属业务员、5-所属业务员Id、6-所属业务员手机号）
	if req.WhereType > 0 && req.Where != "" {
		switch req.WhereType {
		case 1:
			session.And("dd.id = ?", req.Where)
		case 2:
			session.And("dd.name LIKE '%" + req.Where + "%'")
		case 3:
			session.And("dd.encrypt_mobile = ?", utils.MobileEncrypt(req.Where))
		case 4: //用子查询
			session.And("s.enterprise_id in (select enterprise_id from scrm_enterprise_salesperson_bind where salesperson_id in (select id from scrm_salesperson where name like '%" + req.Where + "%'))")
		case 5: //用子查询
			session.And("s.enterprise_id in (select enterprise_id from scrm_enterprise_salesperson_bind where salesperson_id = ?)", req.Where)
		case 6: //用子查询
			session.And("s.enterprise_id in (select enterprise_id from scrm_enterprise_salesperson_bind where salesperson_id in (select id from scrm_salesperson where phone = ?))", req.Where)
		}
	}

	total, err := session.OrderBy("s.id desc").Limit(req.PageSize, (req.PageIndex-1)*req.PageSize).FindAndCount(&list)
	//查询总数
	if err != nil {
		log.Error("查询企业店铺列表失败：err=", err.Error())
		return nil, 0, err
	}
	for k, v := range list {
		if req.OrgId == enum.BLKYOrgId {
			if v.DataSource == distribution_po.DisEnterpriseDataSourceSelf {
				list[k].EnterpriseStatus = 1
			}

		}
	}

	return list, cast.ToInt(total), nil
}

// 设置店铺成功
func (s DisShopService) GetShopAdd(req distribution_vo.DisShopUpdateReq) error {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()

	reqitem := distribution_vo.DisShopReq{}
	reqitem.ShopId = req.Id
	//先查询店铺名称是否已经存在。存在的话不允许设置

	var info distribution_vo.Shop

	info, err := s.GetShopDetail(reqitem)
	if err != nil {
		return errors.New("设置店铺信息失败：err=" + err.Error())
	}
	//如果没有设置过店铺，需要默认插入所有的分销商品到店铺
	var goodIds []int

	if info.IsSettedShop == 2 {
		session.SQL("select goods_id from upetmart.upet_goods where store_id = 3 and goods_state =1 and goods_verify=1 and is_dis =1")
		err = session.Find(&goodIds)
		if err != nil {
			return errors.New("设置店铺信息失败：err=" + err.Error())
		}
	}

	//添加商品记录，同步到es
	err = SyncEsGoods(session, goodIds, req.Id, enum.SyncEsDisGoodsEnum)
	if err != nil {
		return errors.New(err.Error())
	}
	now := time.Now().Format(utils.DateTimeLayout)
	_, err = session.Exec("update shop set shop_name=?,head_image=?,welcome=?,is_setted_shop=1,is_setted_time=? where id=?", req.ShopName, req.HeadImage, req.Welcome, now, req.Id)

	if err != nil {
		session.Rollback()
		return errors.New("设置店铺信息失败：err=" + err.Error())
	}
	session.Commit()
	return nil
}

// 获取店铺详情
func (s DisShopService) GetShopDetail(req distribution_vo.DisShopReq) (distribution_vo.Shop, error) {
	s.Begin()
	defer s.Close()
	session := s.Session
	var info distribution_vo.Shop

	if req.ShopId > 0 {
		_, err := session.Table("eshop.shop").Where("id=?", req.ShopId).Get(&info)
		if err != nil {
			return info, errors.New("查询店铺信息失败：err=" + err.Error())
		}
		if info.Id > 0 {
			return info, nil
		}
	}

	return info, nil
}

// 获取saas店铺详情
func (s DisShopService) GetSaasShopDetail(req distribution_vo.DisSaasShopReq) (distribution_vo.ShopExt, error) {
	s.Begin()
	defer s.Close()
	session := s.Session
	var info distribution_vo.ShopExt
	if req.SaasShopId == "" {
		return info, errors.New("saas店铺id不能为空")
	}
	session.Table("eshop.shop").Alias("s").
		Select("s.*,ss.name as salesperson_name,en.enterprise_name,en.enterprise_status,en.enterprise_type,dd.id_card,dd.idcard_front,"+
			"dd.idcard_reverse,dd.dis_role,dd.social_credit_code,dd.social_code_image,dd.name,dd.mobile,dd.encrypt_mobile").
		Join("INNER", "scrm_enterprise en", "en.id=s.enterprise_id").
		Join("LEFT", "eshop.scrm_salesperson ss", "s.registered_salesperson=ss.id").
		Join("LEFT", "dis_distributor dd", "s.id=dd.shop_id and s.org_id = dd.org_id").
		Where("saas_shop_id=?", req.SaasShopId)
	if req.SourceType != 1 {
		session.Where("dd.encrypt_mobile=?", utils.MobileEncrypt(req.Mobile))
	}
	_, err := session.Get(&info)
	if err != nil {
		return info, errors.New("查询店铺信息失败：err=" + err.Error())
	}

	return info, nil
}
func (s DisShopService) GetDisShop(req distribution_vo.EnterpriseReq) ([]distribution_vo.ScrmEnterprise, int, error) {
	s.Begin()
	defer s.Close()
	session := s.Session
	var list []distribution_vo.ScrmEnterprise
	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}
	total, err := session.Table("scrm_enterprise en").
		Join("left", "shop s", "en.id=s.enterprise_id").
		Where("en.enterprise_status=1 and en.enterprise_name like '%"+req.EnterpriseName+"%'").And("en.social_credit_code!='' or en.id_card_no!='' ").
		Limit(req.PageSize, (req.PageIndex-1)*req.PageSize).OrderBy("en.id desc").
		Select("en.*,s.id shop_id").
		FindAndCount(&list)
	if err != nil {
		return nil, 0, errors.New("查询企业列表失败：err=" + err.Error())
	}
	return list, cast.ToInt(total), nil
}

// 企业列表接口
func (s DisShopService) GetBLKYDisShop(req distribution_vo.EnterpriseReq) ([]distribution_vo.ScrmEnterprise, int, error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	var list []distribution_vo.ScrmEnterprise
	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}

	// 构建企业名称查询条件
	enterpriseNameCond := ""
	if len(req.EnterpriseName) > 0 {
		enterpriseNameCond = fmt.Sprintf(" AND enterprise_name LIKE '%%%s%%' ", req.EnterpriseName)
	}

	// 使用原生SQL实现UNION ALL
	sql := fmt.Sprintf(`
        SELECT * FROM (
            SELECT en.id, en.enterprise_name, en.social_credit_code, en.province, en.city, en.district, en.address,2 as data_source
            FROM scrm_enterprise en 
            WHERE en.enterprise_status = 1 AND (en.social_credit_code != '' OR en.id_card_no != '') %s
            UNION ALL
            SELECT de.scrm_enterprise_id as id, de.enterprise_name, de.social_credit_code, de.province, de.city, de.district, de.address,1 as data_source
            FROM dis_enterprise de
            WHERE de.data_source = 1 %s
        ) t ORDER BY data_source asc,id DESC LIMIT ?, ?
    `, enterpriseNameCond, enterpriseNameCond)

	// 计算总数的SQL
	countSql := fmt.Sprintf(`
        SELECT COUNT(*) FROM (
            SELECT en.id FROM scrm_enterprise en 
            WHERE en.enterprise_status = 1 AND (en.social_credit_code != '' OR en.id_card_no != '') %s
            UNION ALL
            SELECT de.scrm_enterprise_id as id FROM dis_enterprise de
            WHERE de.data_source = 1 %s
        ) t
    `, enterpriseNameCond, enterpriseNameCond)

	// 查询总数
	var total int
	_, err := session.SQL(countSql).Get(&total)
	if err != nil {
		return nil, 0, errors.New("查询企业列表总数失败：err=" + err.Error())
	}

	// 查询数据列表
	err = session.SQL(sql, (req.PageIndex-1)*req.PageSize, req.PageSize).Find(&list)
	if err != nil {
		return nil, 0, errors.New("查询企业列表失败：err=" + err.Error())
	}

	return list, total, nil
}

// 企业列表接口
func (s DisShopService) GetApiShopList(req distribution_vo.DisShopListReq) ([]distribution_vo.ShopExt, int, error) {
	s.Begin()
	defer s.Close()

	session := s.Session
	var list []distribution_vo.ShopExt

	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}

	session.Table("shop").Alias("s").
		Join("INNER", "scrm_enterprise en", "en.id=s.enterprise_id").
		Join("LEFT", "dis_distributor dd", "s.id=dd.shop_id AND dd.dis_role =1").
		Join("LEFT", "scrm_salesperson ssp", "s.registered_salesperson=ssp.id").
		Join("LEFT", "(SELECT e.id,sp.id AS salesperson_id,GROUP_CONCAT(sp.name) AS 'salesperson_names' FROM scrm_enterprise e LEFT JOIN scrm_enterprise_salesperson_bind esb ON e.id=esb.enterprise_id LEFT JOIN scrm_salesperson sp ON esb.salesperson_id=sp.id GROUP BY e.id) se", "s.enterprise_id = se.id").
		Join("LEFT", "(SELECT shop_id,COUNT(1) AS dis_num,SUM(total_customer) AS total_customer FROM dis_distributor GROUP BY shop_id) dis", "s.id = dis.shop_id").
		//查询dis_distributor_fans有效期内的粉丝数量
		Join("LEFT", "(SELECT shop_id,COUNT(1) AS effective_dis_num FROM dis_distributor_fans WHERE expire_time > NOW() GROUP BY shop_id) dis_fans", "s.id = dis_fans.shop_id").
		Select("s.*,en.enterprise_name,en.enterprise_type,en.enterprise_status,en.province,en.city,en.district,dd.name AS distributor_name,dd.mobile AS distributor_mobile,dd.encrypt_mobile,se.salesperson_names,IFNULL(dis_num,0) AS dis_num,dis.total_customer,ssp.name AS reg_salesperson_name,IFNULL(dis_fans.effective_dis_num,0) AS effective_dis_num")
	if req.OrgId > 0 {
		session.And("s.org_id = ?", req.OrgId)
	}
	if req.Status > 0 {
		session.And("en.enterprise_status = ?", req.Status)
	}
	if req.IsSettedShop > 0 {
		session.And("s.is_setted_shop = ?", req.IsSettedShop)
	}

	//企业id/企业名称
	if req.EnterpriseName != "" {
		session.And("s.enterprise_id in (select id from scrm_enterprise where id = ? or enterprise_name like '%"+req.EnterpriseName+"%')", req.EnterpriseName)
	}

	//查询条件的类型（1-老板Id 2-老板姓名、3-老板手机号、4-所属业务员、5-所属业务员Id、6-所属业务员手机号）
	if req.WhereType > 0 && req.Where != "" {
		switch req.WhereType {
		case 1:
			session.And("dd.id = ?", req.Where)
		case 2:
			session.And("dd.name LIKE '%" + req.Where + "%'")
		case 3:
			session.And("dd.encrypt_mobile = ?", utils.MobileEncrypt(req.Where))
		case 4: //用子查询
			session.And("s.enterprise_id in (select enterprise_id from scrm_enterprise_salesperson_bind where salesperson_id in (select id from scrm_salesperson where name like '%" + req.Where + "%'))")
		case 5: //用子查询
			session.And("s.enterprise_id in (select enterprise_id from scrm_enterprise_salesperson_bind where salesperson_id = ?)", req.Where)
		case 6: //用子查询
			session.And("s.enterprise_id in (select enterprise_id from scrm_enterprise_salesperson_bind where salesperson_id in (select id from scrm_salesperson where phone = ?))", req.Where)
		}
	}

	total, err := session.OrderBy("s.id desc").Limit(req.PageSize, (req.PageIndex-1)*req.PageSize).FindAndCount(&list)
	//查询总数
	if err != nil {
		log.Error("查询企业店铺列表失败：err=", err.Error())
		return nil, 0, err
	}

	return list, cast.ToInt(total), nil
}

func (s DisShopService) SyncEsShopGoods(shopId int) error {
	s.Begin()
	defer s.Close()
	session := s.Session
	// redis 限制一分钟只能操作一次
	// 1. 加redis锁10s
	redisConn := cache.GetRedisConn()
	lockKey := fmt.Sprintf("lock:SyncEsShopGoods:%d", shopId)
	lock, err := redisConn.SetNX(lockKey, 1, 60*time.Second).Result()
	if err != nil {
		return err
	}
	if !lock {
		return errors.New("系统繁忙，请1分钟后再试")
	}

	var goodIds []int
	if err := session.SQL(`
SELECT e.goods_id from upetmart.upet_goods_eshop e left join upetmart.upet_goods g on e.goods_id = g.goods_id where g.store_id =3 and  g.goods_verify=1 and g.goods_state=1 and g.goods_storage > 0 and e.shop_id=?;
`, shopId).Find(&goodIds); err != nil {
		return errors.New("同步商品数据：err=" + err.Error())
	}
	//添加商品记录，同步到es
	err = SyncEsGoods(session, goodIds, shopId, enum.SyncEsGoodsShopEnum)
	if err != nil {
		return errors.New(err.Error())
	}
	session.Commit()
	return nil
}
