package services

import (
	"eShop/domain/distribution-po"
	"eShop/infra/log"
	glog "eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/view-model/distribution-vo"
	"errors"

	"strings"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
)

type StatsSalespersonService struct {
	common.BaseService
}

func (s StatsSalespersonService) StatsSalespersonPage(req distribution_vo.StatsSalespersonPageReq) ([]distribution_vo.StatsSalespersonPageData, int, error) {
	data := make([]distribution_vo.StatsSalespersonPageData, 0)

	// 根据时间和筛选条件，进行分页查询
	s.Begin()
	defer s.Close()
	session := s.Session
	session.Select("ssd.salesman_id,ssd.salesman_name,ssd.organization,ss.phone,"+
		"ss.phone,"+
		"SUM(service_enterprise_count) AS service_enterprise_count,"+
		"SUM(shop_count) AS shop_count,"+
		"SUM(service_distributor_count) AS service_distributor_count,"+
		"SUM(product_trans_count + insurance_trans_count) AS trans_count,"+
		"SUM(product_trans_amount + insurance_trans_amount) AS trans_amount,"+
		"SUM(product_commission + insurance_commission) AS commission,"+
		"SUM(product_enterprise_count) AS product_enterprise_count,"+
		"SUM(product_trans_count) AS product_trans_count,"+
		"SUM(product_trans_amount) AS product_trans_amount,"+
		"SUM(product_commission) AS product_commission,"+
		"SUM(insurance_enterprise_count) AS insurance_enterprise_count,"+
		"SUM(insurance_trans_count) AS insurance_trans_count,"+
		"SUM(insurance_trans_amount) AS insurance_trans_amount,"+
		"SUM(insurance_commission) AS insurance_commission").
		Table("stats_salesperson_daily ssd").
		Join("LEFT", "scrm_salesperson ss", "ssd.salesman_id=ss.id")

	if len(req.SalesmanName) > 0 {
		session.And("ss.name like ? OR ss.id =? OR ss.phone=?", "%"+req.SalesmanName+"%", req.SalesmanName, req.SalesmanName)
	}
	if len(req.Organization) > 0 {
		session.And("ssd.organization like ?", "%"+req.Organization+"%")
	}
	if len(req.StartDate) > 0 {
		session.And("ssd.stat_date = ? ", req.StartDate)
	}
	if len(req.EndDate) > 0 {
		session.And("ssd.end_date = ? ", req.EndDate)
	}

	total, err := session.GroupBy("ssd.salesman_id").Limit(req.PageSize, (req.PageIndex-1)*req.PageSize).FindAndCount(&data)
	if err != nil {
		log.Errorf("分页查询业务员统计数据失败：e=%v", err)
		return nil, 0, err
	}

	// 开启一组协程完成以下遍历操作
	return data, cast.ToInt(total), nil
}

func (s StatsSalespersonService) StatsSalespersonSummary(req distribution_vo.GetStatsSalespersonReq) (distribution_vo.StatsSalespersonSummaryData, error) {
	var data distribution_vo.StatsSalespersonSummaryData
	s.Begin()
	defer s.Close()
	session := s.Session

	// 查询统计数据
	_, err := session.Select("ds.total_pay_sales AS total_sales,ds.total_order_pay_num AS total_order_num,ds.service_ent_num,ds.service_dis_num,ds.tuoke_dis_num,"+
		"COUNT(DISTINCT(IF(s.is_setted_shop=1, s.id, NULL))) AS shop_num,COUNT(DISTINCT(IF(dd.dis_role=1,dd.id,NULL))) AS boss_count,"+
		"SUM(s.settled_commission + s.unsettled_commission + s.ins_settled_commission + s.ins_unsettled_commission) AS total_commission").
		Table("dis_salesperson ds").
		Join("LEFT", "scrm_enterprise_salesperson_bind sesb", "sesb.salesperson_id=ds.salesperson_id").
		Join("LEFT", "shop s", "s.enterprise_id=sesb.enterprise_id").
		Join("LEFT", "dis_distributor dd", "dd.shop_id=s.id AND dd.org_id=s.org_id AND dd.dis_role=1").
		Where("ds.salesperson_id=? AND s.org_id=?", req.SalesmanId, req.OrgId).GroupBy("ds.salesperson_id").
		Get(&data)

	if err != nil {
		log.Errorf("StatsSalespersonSummary，查询业务员统计数据失败：e=%v", err)
		return data, err
	}

	return data, nil
}

func (s StatsSalespersonService) StatsDisGraph(req distribution_vo.GetStatsSalespersonReq) ([]distribution_vo.StatsDisGraphData, error) {
	data := make([]distribution_vo.StatsDisGraphData, 0)
	s.Begin()
	defer s.Close()
	session := s.Session

	selectSql := ""
	if req.GraphType == 0 {
		log.Errorf("StatsDisGraph,参数异常，reqGraphType不能为0")
		return data, errors.New("参数异常，reqGraphType不能为0")
	}

	// 曲线图数据类型：1-分销单数，2-分销金额，3-企业数，4-分销员数
	switch req.GraphType {
	case 1:
		selectSql = "stat_date,(product_trans_count + insurance_trans_count) AS num"
	case 2:
		selectSql = "stat_date,(product_trans_amount + insurance_trans_amount) AS num"
	case 3:
		selectSql = "stat_date,(product_enterprise_count + insurance_enterprise_count) AS num"
	case 4:
		selectSql = "stat_date,service_distributor_count AS num"
	}

	session.Select(selectSql).
		Table("stats_salesperson_daily").
		Where("salesman_id=? AND stat_date=end_date", req.SalesmanId)
	if len(req.StartDate) > 0 {
		startTime, err := time.Parse(utils.DateLayout2, req.StartDate)
		if err != nil {
			log.Errorf("StatsDisGraph，时间格式转换失败：e=%v", err)
		}
		session.And("stat_date >= ?", startTime.Format(utils.DateLayout))
	}
	if len(req.EndDate) > 0 {
		endTime, err := time.Parse(utils.DateLayout2, req.EndDate)
		if err != nil {
			log.Errorf("StatsDisGraph，时间格式转换失败：e=%v", err)
		}
		session.And("stat_date <= ?", endTime)
	}
	err := session.Find(&data)
	if err != nil {
		log.Errorf("StatsDisGraph，查询业务员统计数据-曲线图失败：e=%v", err)
		return data, err
	}

	return data, nil
}

func (s StatsSalespersonService) StatsDisTrend(req distribution_vo.GetStatsSalespersonReq) (distribution_vo.StatsDisTrendData, error) {
	data := distribution_vo.StatsDisTrendData{}

	// 两个时间范围
	salesmanId := req.SalesmanId
	curStartDate := strings.ReplaceAll(req.StartDate, "/", "-")
	curEndDate := strings.ReplaceAll(req.EndDate, "/", "-")

	// 查询当前周期的统计数据
	curData, err := s.GetDisTrendByStatData(salesmanId, curStartDate, curEndDate)
	if err != nil {
		log.Errorf("StatsDisTrend，查询业务员统计数据-趋势数据salesmanId=%d,startDate=%s,endDate=%s，失败：e=%v", salesmanId, curStartDate, curEndDate, err)
		return data, err
	}

	// 查询上一个周期的统计数据
	preData := distribution_vo.OrderDisStatDto{}
	if len(curStartDate) > 0 {
		preStartTime, preEndTime, _ := utils.GetLastCycleDate(curStartDate, curEndDate)
		preStartDate := preStartTime.Format("2006-01-02")
		preEndDate := preEndTime.Format("2006-01-02")
		preData, err = s.GetDisTrendByStatData(salesmanId, preStartTime.Format("2006-01-02"), preEndTime.Format("2006-01-02"))
		if err != nil {
			log.Errorf("StatsDisTrend，查询业务员上周期统计数据-趋势数据salesmanId=%d,startDate=%s,endDate=%s，失败：e=%v", salesmanId, preStartDate, preEndDate, err)
			return data, err
		}
	}

	// 组装返回结构
	data = distribution_vo.StatsDisTrendData{
		TotalDisStat: distribution_vo.TotalOrderDisStat{
			ServiceEntCount:      curData.ServiceEnterpriseCount,
			ServiceEntCountRatio: utils.CalProportion(curData.ServiceEnterpriseCount, preData.ServiceEnterpriseCount),
			ShopCount:            curData.ShopCount,
			ShopCountRatio:       utils.CalProportion(curData.ShopCount, preData.ShopCount),
			ServiceDisCount:      curData.ServiceDistributorCount,
			ServiceDisCountRatio: utils.CalProportion(curData.ServiceDistributorCount, preData.ServiceDistributorCount),
			TransCount:           curData.TransCount,
			TransCountRatio:      utils.CalProportion(curData.TransCount, preData.TransCount),
			TransAmount:          curData.TransAmount,
			TransAmountRatio:     utils.CalProportion(curData.TransAmount, preData.TransAmount),
			Commission:           curData.Commission,
			CommissionRatio:      utils.CalProportion(curData.Commission, preData.Commission),
		},
		ProductDisStat: distribution_vo.OrderDisStat{
			TransCount:         curData.ProductTransCount,
			TransCountRatio:    utils.CalProportion(curData.ProductTransCount, preData.ProductTransCount),
			TransAmount:        curData.ProductTransAmount,
			TransAmountRatio:   utils.CalProportion(curData.ProductTransAmount, preData.ProductTransAmount),
			TransEntCount:      curData.ProductEnterpriseCount,
			TransEntCountRatio: utils.CalProportion(curData.ProductEnterpriseCount, preData.ProductEnterpriseCount),
			Commission:         curData.ProductCommission,
			CommissionRatio:    utils.CalProportion(curData.ProductCommission, preData.ProductCommission),
		},
		InsureDisStat: distribution_vo.OrderDisStat{
			TransCount:         curData.InsuranceTransCount,
			TransCountRatio:    utils.CalProportion(curData.InsuranceTransCount, preData.InsuranceTransCount),
			TransAmount:        curData.InsuranceTransAmount,
			TransAmountRatio:   utils.CalProportion(curData.InsuranceTransAmount, preData.InsuranceTransAmount),
			TransEntCount:      curData.InsuranceEnterpriseCount,
			TransEntCountRatio: utils.CalProportion(curData.InsuranceEnterpriseCount, preData.InsuranceEnterpriseCount),
			Commission:         curData.InsuranceCommission,
			CommissionRatio:    utils.CalProportion(curData.InsuranceCommission, preData.InsuranceCommission),
		},
	}

	return data, nil
}

func (s StatsSalespersonService) GetDisTrendByStatData(salesmanId int, startDate, endDate string) (distribution_vo.OrderDisStatDto, error) {
	data := distribution_vo.OrderDisStatDto{}

	s.Begin()
	defer s.Close()
	session := s.Session

	session.Select("SUM(service_enterprise_count) AS service_enterprise_count,"+
		"SUM(shop_count) AS shop_count,"+
		"SUM(service_distributor_count) AS service_distributor_count,"+
		"SUM(product_trans_count + insurance_trans_count) AS trans_count,"+
		"SUM(product_trans_amount + insurance_trans_amount) AS trans_amount,"+
		"SUM(product_enterprise_count + insurance_enterprise_count) AS trans_enterprise_count,"+
		"SUM(product_commission + insurance_commission) AS commission,"+
		"SUM(product_trans_count) AS product_trans_count,"+
		"SUM(product_trans_amount) AS product_trans_amount,"+
		"SUM(product_enterprise_count) AS product_enterprise_count,"+
		"SUM(product_commission) AS product_commission,"+
		"SUM(insurance_trans_count) AS insurance_trans_count,"+
		"SUM(insurance_trans_amount) AS insurance_trans_amount,"+
		"SUM(insurance_enterprise_count) AS insurance_enterprise_count,"+
		"SUM(insurance_commission) AS insurance_commission").
		Table("stats_salesperson_daily").
		Where("salesman_id=?", salesmanId)
	if len(startDate) > 0 {
		session.And("stat_date = ?", startDate)
	}
	if len(endDate) > 0 {
		session.And("end_date = ?", endDate)
	}
	_, err := session.Get(&data)
	if err != nil {
		log.Errorf("StatsDisTrend，查询业务员统计数据-周期统计数据失败：e=%v", err)
		return data, err
	}
	return data, nil
}

func (s StatsSalespersonService) InsertAndGet(salespersonId int64, daily distribution_po.StatsSalespersonDaily) (distribution_po.StatsSalespersonDaily, error) {
	existDaily := distribution_po.StatsSalespersonDaily{}
	statDate := daily.StatDate
	endDate := daily.EndDate
	endTime, err := time.Parse(utils.DateLayout, endDate)
	if err != nil {
		glog.Info("StatsSalespersonService InsertAndGet，参数异常")
		return existDaily, nil
	}
	realEndDate := endTime.AddDate(0, 0, 1).Format(utils.DateLayout)
	if len(statDate) == 0 || len(endDate) == 0 {
		glog.Info("StatsSalespersonService InsertAndGet，参数异常")
		return existDaily, nil
	}

	s.Begin()
	defer s.Close()
	session := s.Session

	_, err = session.Table("stats_salesperson_daily").Where("salesman_id=? AND stat_date=? AND end_date=?", salespersonId, statDate, endDate).Get(&existDaily)
	if err != nil {
		glog.Error("StatsSalespersonService InsertAndGet，查询异常", err)
		return existDaily, err
	}

	// 如果不存在，则需要统计以下字段（服务企业数、分销店铺数、服务分销员数、老板数），并创建一条新的记录
	if existDaily.Id == 0 {
		// 服务企业数
		var serviceEntCount int
		_, err = session.Select("COUNT(DISTINCT(s.enterprise_id)) AS service_enterprise_count").
			Table("scrm_enterprise_salesperson_bind sesb").
			Join("LEFT", "shop s", "s.enterprise_id=sesb.enterprise_id").
			Where("sesb.salesperson_id=? AND s.create_time >= ? AND s.create_time < ?", salespersonId, statDate, realEndDate).Get(&serviceEntCount)
		if err != nil {
			glog.Error("StatsSalespersonService InsertAndGet，查询服务企业数异常", err)
			return existDaily, err
		}

		// 分销店铺数
		var shopCount int
		_, err = session.Select("COUNT(DISTINCT(s.id)) AS shop_count").
			Table("scrm_enterprise_salesperson_bind sesb").
			Join("LEFT", "shop s", "s.enterprise_id=sesb.enterprise_id").
			Where("sesb.salesperson_id=? AND s.is_setted_time >= ? AND s.is_setted_time < ?", salespersonId, statDate, realEndDate).Get(&shopCount)
		if err != nil {
			glog.Error("StatsSalespersonService InsertAndGet，查询分销店铺数异常", err)
			return existDaily, err
		}

		// 服务分销员数、老板数
		_, err = session.Select("COUNT(DISTINCT(dd.id)) AS service_distributor_count,COUNT(DISTINCT(IF(dd.dis_role=1,dd.id,NULL))) AS boss_count").
			Table("scrm_enterprise_salesperson_bind sesb").
			Join("LEFT", "shop s", "s.enterprise_id=sesb.enterprise_id").
			Join("LEFT", "dis_distributor dd", "dd.shop_id=s.id").
			Where("sesb.salesperson_id=? AND dd.create_time >= ? AND dd.create_time < ?", salespersonId, statDate, realEndDate).Get(&existDaily)
		if err != nil {
			glog.Error("StatsSalespersonService InsertAndGet，查询服务分销员数、老板数异常", err)
			return existDaily, err
		}

		existDaily.ServiceEnterpriseCount = serviceEntCount
		existDaily.ShopCount = shopCount
		existDaily.StatDate = statDate
		existDaily.EndDate = endDate
		existDaily.SalesmanId = daily.SalesmanId
		existDaily.SalesmanName = daily.SalesmanName
		existDaily.Organization = daily.Organization
		session.Insert(&existDaily)
	}

	return existDaily, nil
}

func (s StatsSalespersonService) StatsDisGoods(req distribution_vo.StatsDisGoodsReq) (out []distribution_vo.StatsDisCenterGoods, total int, err error) {
	s.Begin()
	defer s.Close()
	session := s.Session

	out = make([]distribution_vo.StatsDisCenterGoods, 0)
	var startDate, endDate time.Time
	if (len(req.StartDate) > 0 && len(req.EndDate) == 0) || (len(req.StartDate) == 0 && len(req.EndDate) > 0) {
		log.Error("StatsDisGoods 开始日期和结束日期要么都为空，要么都不为空")
		err = errors.New("开始日期和结束日期要么都为空，要么都不为空")
		return
	}
	if len(req.StartDate) > 0 {
		startDate, err = time.ParseInLocation(utils.DateLayout2, req.StartDate, time.Local)
		if err != nil {
			log.Error("StatsDisGoods 开始日期格式错误err=", err.Error())
			err = errors.New("开始日期格式错误")
			return
		}
	}
	if len(req.EndDate) > 0 {
		endDate, err = time.ParseInLocation(utils.DateLayout2, req.EndDate, time.Local)
		if err != nil {
			log.Error("StatsDisGoods 结束日期格式错误err=", err.Error())
			err = errors.New("结束日期格式错误")
			return
		}
	}
	if req.PageIndex == 0 {
		req.PageIndex = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	// 根据业务员id查询到分销员id列表
	salesmanId := req.SalesmanId
	var disMemberIds = make([]int, 0)
	err = session.Select("DISTINCT dd.member_id").
		Table("scrm_enterprise_salesperson_bind sesb").
		Join("LEFT", "shop s", "s.enterprise_id=sesb.enterprise_id").
		Join("LEFT", "dis_distributor dd", "dd.shop_id=s.id").
		Where("sesb.salesperson_id=? AND dd.member_id IS NOT NULL", salesmanId).Find(&disMemberIds)
	if err != nil {
		log.Error("StatsDisGoods 查询分销员id列表失败err=", err.Error())
		err = errors.New("查询分销员id列表失败")
		return
	}
	if len(disMemberIds) == 0 {
		log.Info("StatsDisGoods 查询分销员id列表为空")
		return out, 0, nil
	}

	w := map[string]interface{}{"pageIndex": req.PageIndex, "pageSize": req.PageSize, "groupBy": "goods_id", "isPayed": 1, "isDis": 1}
	w["disMemberIds"] = disMemberIds
	if len(req.GoodsName) > 0 {
		w["goodsName"] = req.GoodsName
	}
	if !startDate.IsZero() {
		w["addTimeStart"] = startDate.Unix()
	}
	if !endDate.IsZero() {
		endDatestr := endDate.Format(utils.DateLayout) + " 23:59:59"
		endDate2, _ := time.ParseInLocation(utils.DateTimeLayout, endDatestr, time.Local)
		w["addTimeEnd"] = endDate2.Unix()
	}

	// 排序:1-分销件数升序 2-分销件数降序 3-分销金额升序 4-分销金额降序 5-分销佣金升序 6-分销佣金降序
	switch req.OrderBy {
	case 1:
		w["orderBy"] = "trans_count asc"
	case 2:
		w["orderBy"] = "trans_count desc"
	case 3:
		w["orderBy"] = "trans_amount asc"
	case 4:
		w["orderBy"] = "trans_amount desc"
	case 5:
		w["orderBy"] = "commission asc"
	case 6:
		w["orderBy"] = "commission desc"

	}

	log.Info("StatsDisGoods 查询数据条件为：", utils.InterfaceToJSON(w))
	data, total, err := distribution_po.StatsOrderGoods(s.Engine, w)
	if err != nil {
		log.Error("StatsDisGoods 查询数据出现错误：", err.Error())
		err = errors.New("查询数据出现错误")

	}
	for _, v := range data {
		GoodsImage := v.GoodsImage
		if len(v.GoodsImage) > 0 && !strings.HasPrefix(v.GoodsImage, "http://") && !strings.HasPrefix(v.GoodsImage, "https://") {
			GoodsImage = config.GetString("bbc_img_path") + v.GoodsImage
		}

		d := distribution_vo.StatsDisCenterGoods{
			// 商品Id
			GoodsId: v.GoodsId,
			// 商品名称
			GoodsName: v.GoodsName,
			// 商品图片
			GoodsImage: GoodsImage,
			// 分销成交件数
			TransCount: v.TransCount,
			// 分销成交金额(单位分)
			TransAmount: cast.ToInt(v.TransAmount),
			// 分销佣金(单位分)
			Commission: cast.ToInt(v.Commission),
		}
		out = append(out, d)
	}
	return

}
