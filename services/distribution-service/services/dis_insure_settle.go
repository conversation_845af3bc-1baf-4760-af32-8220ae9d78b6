package services

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"fmt"
	"time"

	"github.com/spf13/cast"
)

type DisInsureSettleService struct {
	common.BaseService
}

func (s DisInsureSettleService) InsureSettlePage(req vo.InsureSettlePageReq) ([]vo.InsureSettlePageData, int, error) {
	s.<PERSON>gin()
	defer s.Close()

	session := s.Session
	var list []vo.InsureSettlePageData

	session.Select("dis.id,dis.state,dis.settle_time,dis.order_no,dis.policy_no,dis.dis_id,dis.dis_amount," +
		"pipi.insure_begin_date,pipi.insure_end_date," +
		"poi.order_status,poi.order_create_time AS create_time,poi.dis_rate," +
		"(pipi.real_premium_amt * 100) AS pay_price,ppi.product_name," +
		"IF(dd.org_id = 4, dd.real_name, dd.name) as dis_name," +
		"se.enterprise_name,se.code")
	session.Table("dis_insurance_settle dis").
		Join("LEFT", "dis_distributor dd", "dd.id=dis.dis_id").
		Join("LEFT", "insurance_business.pi_order_info poi", "poi.order_no=dis.order_no").
		Join("LEFT", "insurance_business.pi_insure_policy_info pipi", "pipi.policy_no=dis.policy_no").
		Join("LEFT", "insurance_business.pi_product_info ppi", "ppi.product_code=dis.product_code").
		Join("LEFT", "shop s", "s.id=dis.shop_id").
		Join("LEFT", "scrm_enterprise se", "se.id=s.enterprise_id")
	session.And("dis.org_id = ?", req.OrgId)
	if req.State > 0 {
		session.And("dis.state = ?", req.State)
	}
	if len(req.Where) > 0 {
		//查询条件的类型（id=结算编号，order_no=订单号，policy_no=保单号，product_name=保险名称，dis_name=分销员名称，dis_mobile=分销员手机号，dis_id=分销员Id，enterprise_name=线下企业）
		switch req.WhereType {
		case "":
		case "id":
			session.And("dis.id = ?", req.Where)
		case "order_no":
			session.And("dis.order_no = ?", req.Where)
		case "policy_no":
			session.And("dis.policy_no = ?", req.Where)
		case "product_name":
			session.And("ppi.product_name LIKE ?", "%"+req.Where+"%")
		case "dis_name":
			session.And("dd.name LIKE ?", "%"+req.Where+"%")
		case "dis_mobile":
			session.And("dd.encrypt_mobile = ?", utils.MobileEncrypt(req.Where))
		case "dis_id":
			session.And("dis.dis_id = ?", req.Where)
		case "enterprise_name":
			session.And("se.enterprise_name LIKE ?", "%"+req.Where+"%")
		}
	}
	if len(req.TimeWhereType) > 0 {
		// settle_time=结算时间，create_time=下单时间，insure_begin_time=保险起期，insure_end_time=保险止期
		//查询条件的类型（）
		switch req.TimeWhereType {
		case "":
		case "settle_time":
			if len(req.TimeBegin) > 0 {
				session.And("dis.settle_time >= ?", req.TimeBegin)
			}
			if len(req.TimeEnd) > 0 {
				session.And("dis.settle_time <= ?", req.TimeEnd)
			}
		case "create_time":
			if len(req.TimeBegin) > 0 {
				session.And("poi.order_create_time >= ?", req.TimeBegin)
			}
			if len(req.TimeEnd) > 0 {
				session.And("poi.order_create_time <= ?", req.TimeEnd)
			}
		case "insure_begin_time":
			if len(req.TimeBegin) > 0 {
				session.And("pipi.insure_begin_date >= ?", req.TimeBegin)
			}
			if len(req.TimeEnd) > 0 {
				session.And("pipi.insure_begin_date <= ?", req.TimeEnd)
			}
		case "insure_end_time":
			if len(req.TimeBegin) > 0 {
				session.And("pipi.insure_end_date >= ?", req.TimeBegin)
			}
			if len(req.TimeEnd) > 0 {
				session.And("pipi.insure_end_date <= ?", req.TimeEnd)
			}
		}
	}

	limit := req.PageSize
	start := (req.PageIndex - 1) * req.PageSize
	total, err := session.OrderBy("dis.id DESC").Limit(limit, start).FindAndCount(&list)
	if err != nil {
		log.Error("查询保险分销结算失败：err=", err.Error())
		return nil, 0, err
	}

	if len(list) > 0 {
		for i, e := range list {
			// 处理支付金额、退款金额、处理分销金额
			orderStatus := e.OrderStatus
			// “已退保、已退款” 则 退款金额=支付金额，分销佣金为0
			if orderStatus == 6 || orderStatus == 7 || orderStatus == 20 {
				list[i].RefundPrice = e.PayPrice
			}
		}
	}

	return list, cast.ToInt(total), nil
}

func (s DisInsureSettleService) InsureSettleApiPage(req vo.InsureSettlePageApiReq) ([]vo.InsureSettlePageApiData, int, int, error) {
	s.Begin()
	defer s.Close()

	session := s.Session
	var list []vo.InsureSettlePageApiData
	session.Select("poi.id,poi.order_no,poi.dis_id,d.name AS dis_name," +
		"pipi.insure_begin_date,pipi.insure_end_date,ppi.product_price AS product_price,ppi.product_name,ppi.dis_rate,ppi.commodity_image_url AS product_image_url," +
		"poi.order_status,UNIX_TIMESTAMP(poi.order_create_time) AS create_time,dis.state AS settle_state," +
		"ROUND(pipi.real_premium_amt * poi.dis_rate / 100, 2) AS dis_amount")
	session.Table("insurance_business.pi_order_info poi").
		Join("LEFT", "insurance_business.pi_insure_policy_info pipi", "pipi.order_no=poi.order_no").
		Join("LEFT", "insurance_business.pi_product_info ppi", "ppi.product_code=poi.product_code").
		Join("LEFT", "dis_insurance_settle dis", "dis.order_no=poi.order_no").
		Join("LEFT", "eshop.dis_distributor d", "d.id=poi.dis_id")
	session.And("poi.shop_id = ?", req.ShopId)
	if req.DisId > 0 {
		session.And("poi.dis_id = ?", req.DisId)
	}
	if req.SettleState > 0 {
		session.And("dis.state = ?", req.SettleState)
	}
	if req.State > 0 {
		//1-待支付, 3-投保成功, 4-投保中, 5-退款处理中, 6-已退款, 7-已取消, 11-保障中, 12-已过期, 19-投保失败, 20-已退保
		orderStatus := ""
		if req.State == 1 {
			//待支付、投保中、投保成功 "1,3,4"
			orderStatus = "1,3,4"
			session.And("poi.order_status=1 OR" +
				" (poi.order_status=3 AND pipi.insure_begin_date>=SYSDATE()) OR" +
				" poi.order_status=4")
		} else if req.State == 2 {
			//已退款、已取消、投保失败、已退保
			orderStatus = "6,7,19,20"
			session.And(fmt.Sprintf("poi.order_status IN (%s)", orderStatus))
		} else if req.State == 3 {
			//保障中、已过期
			orderStatus = "11,12"
			session.And("(poi.order_status=3 AND pipi.insure_begin_date<=SYSDATE() AND pipi.insure_end_date>=SYSDATE()) OR" +
				" (poi.order_status = 3 and SYSDATE()>=pipi.insure_end_date)")
		}
	}
	if len(req.Where) > 0 {
		//用户手机号、订单号、保险名称
		session.And("poi.user_mobile LIKE ? OR poi.order_no = ? OR ppi.product_name LIKE ?", "%"+req.Where+"%", req.Where, "%"+req.Where+"%")
	}
	if len(req.AddTimeStart) > 0 {
		session.And("poi.order_create_time >= ?", req.AddTimeStart)
	}
	if len(req.AddTimeEnd) > 0 {
		session.And("poi.order_create_time <= ?", req.AddTimeEnd)
	}

	limit := req.PageSize
	start := (req.PageIndex - 1) * req.PageSize
	total, err := session.OrderBy("poi.create_time DESC").Limit(limit, start).FindAndCount(&list)

	if err != nil {
		log.Error("C端查询保险分销结算失败：err=", err.Error())
		return nil, 0, 0, err
	}

	if len(list) > 0 {
		for ind, _ := range list {
			ele := list[ind]
			if ele.OrderStatus == 3 {
				result := timeInRange(time.Now(), ele.InsureBeginDate, ele.InsureEndDate)
				if result == 0 {
					list[ind].OrderStatus = 11
				} else if result == 1 {
					list[ind].OrderStatus = 12
				}
			}
		}
	}
	// 查询总销售额
	type SumStruct struct {
		ProductPrice float64
	}
	ss := new(SumStruct)
	session2 := s.Engine.NewSession()
	defer session2.Close()
	session2.Select("SUM(ppi.product_price) AS product_price")
	session2.Table("insurance_business.pi_order_info poi").
		Join("LEFT", "insurance_business.pi_insure_policy_info pipi", "pipi.order_no=poi.order_no").
		Join("LEFT", "insurance_business.pi_product_info ppi", "ppi.product_code=poi.product_code").
		Join("LEFT", "dis_insurance_settle dis", "dis.order_no=poi.order_no")
	session2.And("poi.shop_id = ?", req.ShopId)
	if req.DisId > 0 {
		session2.And("poi.dis_id = ?", req.DisId)
	}
	if req.SettleState > 0 {
		session2.And("dis.state = ?", req.SettleState)
	}
	if req.State > 0 {
		orderStatus := ""
		if req.State == 1 {
			orderStatus = "1,3,4"
			session2.And("poi.order_status=1 OR" +
				" (poi.order_status=3 AND pipi.insure_begin_date>=SYSDATE()) OR" +
				" poi.order_status=4")
		} else if req.State == 2 {
			orderStatus = "6,7,19,20"
			session2.And(fmt.Sprintf("poi.order_status IN (%s)", orderStatus))
		} else if req.State == 3 {
			orderStatus = "11,12"
			session2.And("(poi.order_status=3 AND pipi.insure_begin_date<=SYSDATE() AND pipi.insure_end_date>=SYSDATE()) OR" +
				" (poi.order_status = 3 and SYSDATE()>=pipi.insure_end_date)")
		}
	}
	if len(req.Where) > 0 {
		session2.And("poi.user_mobile LIKE ? OR poi.order_no = ? OR ppi.product_name LIKE ?", "%"+req.Where+"%", req.Where, "%"+req.Where+"%")
	}
	if len(req.AddTimeStart) > 0 {
		session2.And("poi.order_create_time >= ?", req.AddTimeStart)
	}

	if len(req.AddTimeEnd) > 0 {
		session2.And("poi.order_create_time <= ?", req.AddTimeEnd)

	}
	totalSales, err := session2.Sum(ss, "product_price")
	if err != nil {
		log.Error("查询保险分销结算失败：err=", err.Error())
		return nil, 0, 0, err
	}

	return list, cast.ToInt(total), cast.ToInt(totalSales * 100), nil
}

func timeInRange(now time.Time, startTime, endTime string) int {
	// Parse the time layout
	layout := "2006-01-02 15:04:05"

	// Parse the start time
	parsedStartTime, err := time.Parse(layout, startTime)
	if err != nil {
		log.Fatalf("Error parsing start time: %v", err)
	}

	// Parse the end time
	parsedEndTime, err := time.Parse(layout, endTime)
	if err != nil {
		log.Fatalf("Error parsing end time: %v", err)
	}

	// Check if the current time is before the start time
	if now.Before(parsedStartTime) {
		return -1
	}

	// Check if the current time is after the end time
	if now.After(parsedEndTime) {
		return 1
	}

	// If the current time is neither before the start time nor after the end time, it's within the range
	return 0
}
