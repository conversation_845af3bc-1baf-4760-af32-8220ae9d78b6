package services

import (
	distribution_po "eShop/domain/distribution-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/services/common"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	distribution_vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"time"

	"github.com/spf13/cast"
)

type DisCommisSettingService struct {
	common.BaseService
}

func (s *DisCommisSettingService) GetDisCommisSettingPageList(req distribution_vo.GetDisCommisSettingPageListReq) (out []distribution_vo.DisCommisSettingInfo, total int64, err error) {

	s.<PERSON>gin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	out = make([]distribution_vo.DisCommisSettingInfo, 0)

	data, total, err := new(distribution_po.DisCommisSetting).FindPage(session, distribution_po.FindDisCommisSettingPageListReq{
		OrgId:     req.OrgId,
		PageIndex: req.PageIndex,
		PageSize:  req.PageSize,
	})
	if err != nil {
		log.Error("获取佣金设置列表失败：err=", err.Error())
		err = errors.New("获取佣金设置列表失败")
		return out, 0, err
	}

	for _, v := range data {
		out = append(out, distribution_vo.DisCommisSettingInfo{
			Id:                v.Id,
			CustomerChannelId: v.CustomerChannelId,
			ChannelPath:       v.ChannelPath,
			DisCommisRate:     v.DisCommisRate,
			IsDefault:         v.IsDefault,
			UpdateTime:        v.UpdateTime.Format("2006-01-02 15:04:05"),
		})
	}
	return out, total, nil
}

// 获取销售渠道全局佣金设置
func (s *DisCommisSettingService) GetCustomerChannelGlobalSetting(orgId int) (rate string) {
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	rat := mCache.Get(string(cache_source.EShop), fmt.Sprintf(cachekey.CustomerChannelCommisRate, orgId))
	if len(rat) > 0 {
		rate = rat[0].(string)
	}
	return
}

// 设置销售渠道全局佣金设置
func (s *DisCommisSettingService) CustomerChannelGlobalSetting(orgId int, rate float64) (oldRate, newRate float64, err error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	oldRate = cast.ToFloat64(s.GetCustomerChannelGlobalSetting(orgId))
	if oldRate != rate {
		var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
		mCache.Save(string(cache_source.EShop), fmt.Sprintf(cachekey.CustomerChannelCommisRate, orgId), rate, 0)

		// 更新佣金设置
		disCommisSetting := new(distribution_po.DisCommisSetting)
		disCommisSetting.DisCommisRate = cast.ToFloat64(rate)

		_, err = session.Table("eshop.dis_commis_setting").Where("org_id = ?", orgId).Where("is_default = ?", 1).Cols("dis_commis_rate").Update(disCommisSetting)
		if err != nil {
			mCache.Save(string(cache_source.EShop), fmt.Sprintf(cachekey.CustomerChannelCommisRate, orgId), oldRate, 0)
			log.Error("设置销售渠道全局佣金设置失败：err=", err.Error())
			err = errors.New("设置销售渠道全局佣金设置失败")
			return
		}
		newRate = rate
	} else {
		err = errors.New("佣金比例未发生变化")
		return
	}
	return

}
func (s *DisCommisSettingService) AddDisCommisSetting(req distribution_vo.AddDisCommisSettingReq) (data distribution_vo.AddDisCommisSettingData, err error) {

	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	disCustomerChannel := new(distribution_po.DisCustomerChannel)
	exists, err := session.Table("eshop.dis_customer_channel").Where("id = ?", req.ChannelCustomerId).Get(disCustomerChannel)
	if err != nil {
		log.Error("获取销售渠道失败：err=", err.Error())
		err = errors.New("获取销售渠道失败")
		return
	}
	if !exists {
		err = errors.New("销售渠道不存在")
		return
	}

	disSetting := new(distribution_po.DisCommisSetting)
	exists, err = session.Table("eshop.dis_commis_setting").Where("customer_channel_id = ?", req.ChannelCustomerId).Get(disSetting)
	if err != nil {
		log.Error("获取佣金设置失败：err=", err.Error())
		err = errors.New("获取佣金设置失败")
		return
	}
	if req.IsDefault == 1 {
		req.DisCommisRate = cast.ToFloat64(s.GetCustomerChannelGlobalSetting(req.OrgId))
	}

	disSetting.OrgId = req.OrgId
	disSetting.CustomerChannelId = req.ChannelCustomerId
	disSetting.DisCommisRate = req.DisCommisRate
	disSetting.IsDefault = req.IsDefault
	if exists {
		disSetting.IsDel = 0
		disSetting.DelDate = time.Time{}
		_, err = session.Table("eshop.dis_commis_setting").Cols("dis_commis_rate", "is_default", "is_del", "del_date").Where("customer_channel_id = ?", req.ChannelCustomerId).Update(disSetting)
	} else {

		_, err = session.Table("eshop.dis_commis_setting").Insert(disSetting)
	}

	if err != nil {
		log.Error("新增佣金设置失败：err=", err.Error())
		err = errors.New("新增佣金设置失败")
		return
	}

	data.DisCommisSettingId = disSetting.Id
	data.ChannelPath = disCustomerChannel.ChannelPath
	data.DisCommisRate = disSetting.DisCommisRate
	data.IsDefault = disSetting.IsDefault
	return

}

func (s *DisCommisSettingService) EditDisCommisSetting(req distribution_vo.EditDisCommisSettingReq) (data distribution_vo.EditDisCommisSettingData, err error) {

	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	if req.DisCommisSettingId <= 0 {
		err = errors.New("佣金设置id不能为0")
		return
	}
	disCommisSetting, err := new(distribution_po.DisCommisSetting).Get(session, req.DisCommisSettingId)
	if err != nil {
		log.Error("编辑佣金设置失败：err=", err.Error())
		return
	}
	if req.IsDefault == 1 {
		req.DisCommisRate = cast.ToFloat64(s.GetCustomerChannelGlobalSetting(req.OrgId))
	}
	data.OldDisCommisRate = disCommisSetting.DisCommisRate
	data.OldIsDefault = disCommisSetting.IsDefault

	disCommisSetting.DisCommisRate = req.DisCommisRate
	disCommisSetting.IsDefault = req.IsDefault
	_, err = session.Table("eshop.dis_commis_setting").Cols("dis_commis_rate", "is_default").Where("id = ?", req.DisCommisSettingId).Update(disCommisSetting)
	if err != nil {
		log.Error("编辑佣金设置失败：err=", err.Error())
		err = errors.New("编辑佣金设置失败")
		return
	}

	data.DisCommisSettingId = disCommisSetting.Id
	data.ChannelPath = disCommisSetting.ChannelPath
	data.DisCommisRate = disCommisSetting.DisCommisRate
	data.IsDefault = disCommisSetting.IsDefault

	return
}

func (s *DisCommisSettingService) DeleteDisCommisSetting(req distribution_vo.DeleteDisCommisSettingReq) (channelPath string, err error) {

	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	if req.DisCommisSettingId <= 0 {
		err = errors.New("佣金设置id不能为0")
		return
	}

	disCommisSetting, err := new(distribution_po.DisCommisSetting).GetOneAnyway(session, req.DisCommisSettingId)
	if err != nil {
		log.Error("删除佣金设置失败：err=", err.Error())
		return
	}

	disCommisSetting.IsDel = 1
	disCommisSetting.DelDate = time.Now()
	_, err = session.Table("eshop.dis_commis_setting").Cols("is_del", "del_date").Where("id = ?", req.DisCommisSettingId).Update(disCommisSetting.DisCommisSetting)
	if err != nil {
		log.Error("删除佣金设置失败：err=", err.Error())
		err = errors.New("删除佣金设置失败")
		return
	}
	channelPath = disCommisSetting.ChannelPath
	return
}

func (s *DisCommisSettingService) GetCustomerChannelList(req distribution_vo.GetCustomerChannelListReq) (data []distribution_vo.CustomerChannelListData, err error) {

	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	data = make([]distribution_vo.CustomerChannelListData, 0)
	list := make([]distribution_vo.CustomerChannelListData, 0)
	err = session.Table("eshop.dis_customer_channel").Find(&list)
	if err != nil {
		log.Error("获取销售渠道列表失败：err=", err.Error())
		err = errors.New("获取销售渠道列表失败")
		return
	}

	parentMap := make(map[int][]distribution_vo.CustomerChannelListData)
	for _, v := range list {
		if v.Parent == 0 {

			data = append(data, v)
		} else {
			parentMap[v.Parent] = append(parentMap[v.Parent], v)
		}
	}

	for i, v := range data {
		data[i].Children = append(data[i].Children, parentMap[v.Id]...)
		if v.ChannelName == "总仓->供应链内部" {
			data = append(data, v)
		}
	}

	return data, nil
}
