package services

import (
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
)

type StatsDistributorService struct {
	common.BaseService
}

func (s StatsDistributorService) StatsDoctor() vo.StatsDoctor {
	resp := vo.StatsDoctor{}

	s.Begin()
	defer s.Close()
	session := s.Session

	_, err := session.SQL(`select count(*) as all_user,
       sum(case when dd.dis_role=3 and dd.approve_state=2 then 1 else 0 end) as real_id
from dis_distributor dd  join eshop.users u on dd.member_id=u.member_id
where u.org_id=4 and u.member_id not in(5287,5418,2911555,5751419,1674684,3417,5256299,2488615,1,3882056,2775288,4063004,3335661,166,339600,6647583,5797986)
and dd.create_time>='2024/09/05 00:00:00'`).Get(&resp)
	if err != nil {
		return vo.StatsDoctor{}
	}

	return resp
}
