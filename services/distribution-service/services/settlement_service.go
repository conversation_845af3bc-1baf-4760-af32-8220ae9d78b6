package services

import (
	"database/sql"
	distribution_po "eShop/domain/distribution-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"

	dissettlement "eShop/services/distribution-service/enum/dis-settlement"
	diswithdrawrecord "eShop/services/distribution-service/enum/dis-withdraw-record"
	upetorders "eShop/services/distribution-service/enum/upet-orders"
	vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"runtime"
	"strings"
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
)

type DisSettlementService struct {
	common.BaseService
}

func (h *DisSettlementService) GetDisSettlementList(in vo.GetDisSettlementListReq) (out []vo.DisSettlementView, total int, err error) {
	log.Infof("获取结算列表-入参为%s", utils.InterfaceToJSON(in))
	h.Begin()
	defer h.Close()

	// 将子上下文传入Session
	session := h.Session

	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	if in.Status != 0 {
		session.And("a.status=?", in.Status)
	}
	if in.ShopId != 0 {
		session.And("a.shop_id=?", in.ShopId)
	}

	if in.WhereType != "" && in.Where != "" {
		whereTypeMap := map[string]string{
			"settlement_no": "a.settlement_no = ?",
			"order_no":      "a.order_no = ?",
			"goods_name":    "a.goods_name like ?",
			//"distributor_name":   "c.name like ? ",
			"distributor_mobile": "c.encrypt_mobile = ?",
			"distributor_id":     "a.distributor_id=?",
			"enterprise_name":    "e.enterprise_name like ?",
		}
		if in.OrgId == enum.BLKYOrgId {
			whereTypeMap["enterprise_name"] = "de.enterprise_name like ?"
		}
		if v, ok := whereTypeMap[in.WhereType]; ok {
			if in.WhereType == "distributor_mobile" {
				session.And(v, utils.MobileEncrypt(in.Where))
			} else if in.WhereType == "goods_name" || in.WhereType == "enterprise_name" {
				session.And(v, "%"+in.Where+"%")
			} else {
				session.And(v, in.Where)
			}
		}
		if in.WhereType == "distributor_name" {
			if in.OrgId == 4 {
				session.And("c.real_name like ?", "%"+in.Where+"%")
			} else {
				session.And("c.name like ?", "%"+in.Where+"%")
			}
		}
	}

	if in.WhereType2 != "" && (in.WhereStart != "" || in.WhereEnd != "") {
		if in.WhereType2 == "settlement_time" || in.WhereType2 == "order_finish_time" {
			if in.WhereStart != "" {
				session.And("a."+in.WhereType2+">=?", in.WhereStart)
			}
			if in.WhereEnd != "" {
				session.And("a."+in.WhereType2+"<=?", in.WhereEnd)
			}
		}
	}

	s := `a.*,
	CASE a.org_id
           WHEN 1 THEN '阿闻'
           WHEN 2 THEN '极宠家'
           WHEN 3 THEN '润合云店'
           WHEN 4 THEN '百林康源'
           ELSE null END AS shop_name,
	IF(c.org_id = 4, c.real_name, c.name) as distributor_name,
	CAST(e.id AS CHAR(25)) as enterprise_id,
	e.enterprise_name,
	de.enterprise_name as dis_enterprise_name,
	cps.dis_commis_amount,
	c.dis_role,
	(case
		when a.status = 1 then '待结算'
		when a.status = 2 then '已结算'
		when a.status = 3 then '已取消'
		else ''
	end ) as status_text`
	out = make([]vo.DisSettlementView, 0)
	count, err := session.Table("dis_settlement").Alias("a").
		Join("left", "shop b", "b.id=a.shop_id").
		Join("left", "eshop.scrm_enterprise e", "e.id=b.enterprise_id").
		Join("left", "eshop.dis_enterprise de", "de.scrm_enterprise_id = b.enterprise_id and de.org_id = ?", in.OrgId).
		Join("left", "dis_distributor c", "c.id = a.distributor_id ").
		Join("left", "eshop.dis_distributor_swlm_cps cps", "cps.order_sn = a.order_no").
		And("a.org_id=? and a.hide_state=0", in.OrgId).
		Select(s).
		OrderBy("a.order_finish_time desc").
		Limit((in.PageSize), int(in.PageSize*(in.PageIndex-1))).FindAndCount(&out)
	if err != nil {
		log.Errorf("获取结算列表失败-错误为%s", err.Error())
		return nil, 0, errors.New("获取结算列表失败" + err.Error())
	}

	for _, v := range out {
		if in.OrgId == enum.BLKYOrgId {
			v.EnterpriseName = v.DisEnterpriseName
		}
	}
	total = cast.ToInt(count)

	return out, total, nil
}

// 订单完成时， 写入一条待结算数据（该定时器每天凌点20分钟执行）t 示例： 2020-01-01 2024-03-20
func (h *DisSettlementService) InsertSettlementData() {
	logPrefix := "写入待结算数据"
	log.Infof("%s 定时任务开始-%s", logPrefix, time.Now().Format(utils.DateTimeLayout))
	defer func() {
		if err := recover(); err != nil {
			// 记录 panic 信息
			fmt.Println("定时器cron-job异常信息捕获:", logPrefix, err)
			log.Error("定时器cron-job异常信息捕获：", logPrefix, err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER PWERROR]%s %v %s\n", logPrefix, err, stack[:length])

		}
	}()

	var needUpdateMap = make(map[string]string)

	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	setNxReslt := mCache.TryLock(string(cache_source.EShop), cachekey.InsertSettlementLock, time.Minute*120)
	if !setNxReslt {
		log.Errorf("%s-设置redis锁(%s)失败", logPrefix, cachekey.InsertSettlementLock)
		return
	}
	defer mCache.Delete(string(cache_source.EShop), cachekey.InsertSettlementLock)

	// 第二步：获取订单状态为已完成的分销订单（订单完成时间是：定时器执行的前一天的零点到23:59:59）
	h.Begin()
	defer h.Close()
	oneDayAgo := time.Now().AddDate(0, 0, -1).Format(utils.DateLayout)
	start, _ := time.ParseInLocation(utils.DateTimeLayout, oneDayAgo+" 00:00:00", time.Local)
	end, _ := time.ParseInLocation(utils.DateTimeLayout, oneDayAgo+" 23:59:59", time.Local)

	type upetOrderStru struct {
		StoreId      int
		OrderId      int
		AddTime      int
		OrderSn      int
		FinnshedTime int
	}
	var upetOrdersData = make([]upetOrderStru, 0)
	if err := h.Session.Table("upetmart.upet_orders").
		Where("is_dis=1").
		Where("store_id=?", enum.OrgId).
		Where("order_state=?", upetorders.StateSuccess).
		In("refund_state", []int{0, 1}).
		Where("finnshed_time>=?", start.Unix()).
		Where("finnshed_time<=?", end.Unix()).
		Where("order_demolition=0").Find(&upetOrdersData); err != nil {
		log.Errorf("%s-获取分销订单失败-错误为%s", logPrefix, err)
		return
	}
	if len(upetOrdersData) == 0 {
		return
	}

	orderIdSli := make([]int, len(upetOrdersData))
	upetordersMap := make(map[int]upetOrderStru)
	for k, v := range upetOrdersData {
		orderIdSli[k] = v.OrderId
		upetordersMap[v.OrderId] = v
	}
	orderIdSliSli := lo.Chunk(orderIdSli, 100)
	for _, ids := range orderIdSliSli {
		//第三步：获取分销订单商品信息
		var orderGoodsData = make([]struct {
			RecId         int
			StoreId       int
			ShopId        int
			OrderId       int
			GoodsId       int
			GoodsName     string
			GoodsPayPrice float64
			DisCommisRate float64
			DisMemberId   int
			DisId         int
		}, 0)
		if err := h.Session.Table("upetmart.upet_order_goods").Alias("a").
			Select("a.rec_id,a.store_id,a.shop_id,a.order_id,a.goods_id,a.goods_name,a.goods_pay_price,a.dis_commis_rate,a.dis_member_id,b.id as dis_id").
			Join("inner", "eshop.dis_distributor b", "a.dis_member_id=b.member_id and a.store_id=b.org_id").
			In("a.order_id", ids).
			Where("a.is_dis=1").
			Where("a.goods_pay_price>0").
			Where("a.dis_pay_state=0").
			Find(&orderGoodsData); err != nil {
			log.Errorf("%s-获取分销订单商品失败-错误为%s", logPrefix, err.Error())
			return
		}

		var settlementSli = make([]*distribution_po.DisSettlement, len(orderGoodsData))
		var recIdSli = make([]int, len(orderGoodsData))

		// dis_distributor_total.unsettled_commission 查询 dis_settlement 条件：org_id=3 && shop_id && distributor_id 求和commission
		// shop.unsettled_commission 查询 dis_settlement 条件：org_id=3 && shop_id 求和commission
		// 组织待结算数据

		for k, v := range orderGoodsData {
			comm := decimal.NewFromFloat(v.GoodsPayPrice).Mul(decimal.NewFromFloat(v.DisCommisRate)).Round(0).IntPart() //金额单位是分， 直接四舍五入，然后取整
			settlementNo := utils.GenerateNo(enum.SettlementNoPrefix)
			settlementSli[k] = &distribution_po.DisSettlement{
				OrgId:           upetordersMap[v.OrderId].StoreId,
				ShopId:          v.ShopId,
				Status:          dissettlement.StatusUnSett,
				SettlementNo:    settlementNo,
				OrderNo:         cast.ToString(upetordersMap[v.OrderId].OrderSn),
				OrderTime:       time.Unix(int64(upetordersMap[v.OrderId].AddTime), 0),
				OrderFinishTime: time.Unix(int64(upetordersMap[v.OrderId].FinnshedTime), 0),
				GoodsId:         v.GoodsId,
				GoodsName:       v.GoodsName,
				PayAmount:       utils.Yuan2Fen(v.GoodsPayPrice),
				CommissionRate:  v.DisCommisRate,
				Commission:      cast.ToInt(comm),
				DistributorId:   v.DisId,
				CreateTime:      time.Now(),
				UpdateTime:      time.Now(),
			}
			recIdSli[k] = v.RecId
			needUpdateMap[fmt.Sprintf("%d:%d:%d", upetordersMap[v.OrderId].StoreId, v.ShopId, v.DisId)] = "unsettled_commission"

		}

		log.Infof("结算结算========%s", utils.InterfaceToJSON(settlementSli))

		//  第四步：批量插入待结算数据，以及批量更新电商订单商品的结算标识(upetmart.upet_order_goods.dis_pay_state=1 )
		if len(settlementSli) > 0 {
			session := h.Engine.NewSession()
			session.Begin()
			if _, err := session.Insert(&settlementSli); err != nil {
				log.Errorf("%s-插入待结算数据失败-错误为%s", logPrefix, err.Error())
				session.Rollback()
				return
			}
			if _, err := session.Table("upetmart.upet_order_goods").Cols("dis_pay_state").In("rec_id", recIdSli).Update(map[string]interface{}{"dis_pay_state": 1}); err != nil {
				log.Errorf("%s-更新订单商品表分销结算状态为已结失败-recId=%s-错误为%s", logPrefix, utils.InterfaceToJSON(recIdSli), err.Error())
				session.Rollback()
				return
			}
			session.Commit()
		}

	}

	//  将分销订单商品的退款金额写入dis_settlement表
	upetRefundReturnData := make([]struct {
		RefundId      int
		OrderId       int
		OrderSn       string
		GoodsId       int
		RefundAmount  float64
		DisPayState   int
		DisCommisRate float64
	}, 0)
	if err := h.Session.Table("upetmart.upet_refund_return").Alias("a").
		Select("a.refund_id,a.order_id,a.order_sn,a.goods_id,a.refund_amount,a.dis_pay_state,a.dis_commis_rate").
		Where("a.dis_pay_state=0").
		Where("a.is_dis=1").
		Where("a.seller_state=2"). //商家处理状态:1为待审核,2为同意,3为不同意
		Where("a.refund_state=3"). //管理员处理状态:1为处理中,2为待处理,3为已完成
		Where("a.store_id=?", enum.OrgId).
		Where("a.goods_id!=1001"). //排除退运费
		Find(&upetRefundReturnData); err != nil {
		log.Errorf("%s-获取未结算的退款数据失败-错误为%s", logPrefix, err.Error())
		return
	}

	for _, v := range upetRefundReturnData {
		//获取分销员id
		disInfo := struct {
			DisMemberId int
			DisId       int
			OrgId       int
			ShopId      int
		}{}
		if exist, err := h.Session.Table("upetmart.upet_order_goods").Alias("a").
			Join("inner", "eshop.dis_distributor b", "a.dis_member_id=b.member_id").
			Select("a.dis_member_id,b.id as dis_id,b.org_id,a.shop_id").
			Where("a.order_id=?", v.OrderId).
			Where("a.is_dis=1").Get(&disInfo); err != nil {
			log.Errorf("%s-获取该订单的分销员id失败-orderId=%d-错误为%s", logPrefix, v.OrderId, err.Error())
			continue
		} else if !exist {
			log.Errorf("%s-未找到该订单的分销员id-orderId=%d", logPrefix, v.OrderId)
			continue
		}
		session := h.Engine.NewSession()
		session.Begin()
		var err error
		var rst sql.Result

		if v.GoodsId == 0 { //代表该订单全部退款了
			log.Errorf("%s-存在商品goods_id=0的情况了，%s", logPrefix, utils.InterfaceToJSON(v))
		}
		updateSql := "update eshop.dis_settlement set refund_amount=pay_amount,commission=0 where order_no=? and goods_id=? and status=? and org_id=? and distributor_id=? and shop_id=?"
		rst, err = session.Exec(updateSql, v.OrderSn, v.GoodsId, dissettlement.StatusUnSett, disInfo.OrgId, disInfo.DisId, disInfo.ShopId)

		if err != nil {
			log.Errorf("%s-更新退款金额失败-orgId=%d-shopId=%d-disId=%d-错误为%s", logPrefix, disInfo.OrgId, disInfo.ShopId, disInfo.DisId, err.Error())
			session.Rollback()
			continue
		}
		rowaffected, _ := rst.RowsAffected()
		if rowaffected == 0 {
			log.Errorf("%s-更新退款金额失败-orgId=%d-shopId=%d-disId=%d-影响条数为0", logPrefix, disInfo.OrgId, disInfo.ShopId, disInfo.DisId)
			session.Rollback()
			continue
		}

		if _, err := session.Table("upetmart.upet_refund_return").Cols("dis_pay_state").Where("refund_id=?", v.RefundId).Update(map[string]interface{}{"dis_pay_state": 1}); err != nil {
			log.Errorf("%s-更新退货退款表的分销结算状态为已结失败-refund_id=%d-错误为%s", logPrefix, v.RefundId, err.Error())
			session.Rollback()
			continue
		}

		session.Commit()
		needUpdateMap[fmt.Sprintf("%d:%d:%d", disInfo.OrgId, disInfo.ShopId, disInfo.DisId)] = "unsettled_commission"

	}

	// 第五步： 将新的待结算数据统计到分销员统计表（dis_settlement.unsettled_commission）和分销店铺(shop.unsettled_commission)
	go h.SettCommUpdate(needUpdateMap)

}

func (h *DisSettlementService) ChangeSettlementStatus() {
	// 佣金结算，将待结算状态 改为 已结算状态
	logPrefix := "佣金结算定时任务"
	log.Infof("%s 开始-%s", logPrefix, time.Now().Format(utils.DateTimeLayout))
	defer func() {
		if err := recover(); err != nil {
			// 记录 panic 信息
			fmt.Println("定时器cron-job异常信息捕获:", logPrefix, err)
			log.Error("定时器cron-job异常信息捕获：", logPrefix, err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER PWERROR]%s %v %s\n", logPrefix, err, stack[:length])

		}
	}()

	//需要统计的待结算数据写到dis_distributor_total.unsettled_commission字段map[key]value里的key是orgId:shopId:distributorId,value是指要更新的列（多个用逗号隔开）
	//需要统计的待结算数据写到shop.unsettled_commission里 map[key]value里的key是orgId:shopId:distributorId,value是指要更新的列（多个用逗号隔开）
	var needUpdateMap = make(map[string]string)

	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	setNxReslt := mCache.TryLock(string(cache_source.EShop), cachekey.ChangeSettlemenStatustLock, time.Minute*60)
	if !setNxReslt {
		log.Errorf("%s-设置redis(%s)锁失败", logPrefix, cachekey.ChangeSettlemenStatustLock)

		return
	}
	defer mCache.Delete(string(cache_source.EShop), cachekey.ChangeSettlemenStatustLock)

	//第二步 先查出待结算的列表
	h.Begin()
	defer h.Close()
	t := time.Now().AddDate(0, 0, -15).Format(utils.DateLayout) + " 00:00:00"
	t2, _ := time.ParseInLocation(utils.DateTimeLayout, t, time.Local)
	data := make([]distribution_po.DisSettlement, 0)
	if err := h.Session.Table("dis_settlement").
		Where("status=?", dissettlement.StatusUnSett).
		Where("order_finish_time<=?", t2.Format(utils.DateTimeLayout)).Find(&data); err != nil {
		log.Errorf("%s-获取完成时间已经超过15天的待结算记录列表失败-错误为%s", logPrefix, err.Error())
		return
	}

	orderSnSli := make([]string, 0)
	for _, v := range data {
		orderSnSli = append(orderSnSli, v.OrderNo)
	}
	orderSnSliSli := lo.Chunk(orderSnSli, 100)
	var OrderGoodsCannotSettMap = make(map[string]int) //不能结算的map[order_sn:goods_id] =1 不能结算
	var OrderGoodsRefundAmtMap = make(map[string]int)  // 结算时，该商品的退款金额map[order_sn:goods_id] =退款金额
	for _, sns := range orderSnSliSli {
		// 第三步：查出所有待结算的分销订单的退款信息
		//  `refund_state` tinyint unsigned DEFAULT '1' COMMENT '申请状态:1为处理中,2为待管理员处理,3为已完成,4用户取消,默认为1',
		selectStr := "goods_id,goods_num,order_id,order_sn,refund_sn,refund_amount,refund_state"
		var refundReturn = make([]struct {
			GoodsId      int
			GoodsNum     int
			OrderId      int
			OrderSn      string
			RefundSn     string
			RefundAmount float64
			RefundState  int
		}, 0)
		if err := h.Session.Table("upetmart.upet_refund_return").Alias("a").
			Select(selectStr).
			In("order_sn", sns).
			Where("dis_pay_state=0").
			Find(&refundReturn); err != nil {
			log.Errorf("%s-获取待结算的订单的退货退款信息失败-错误为%s", logPrefix, err.Error())

			return
		}

		for _, v := range refundReturn {
			if v.RefundState == 1 || v.RefundState == 2 { //排除不能结算的情况: 目前只考虑实物订单 upet_refund_return.refund_state为1或者2时（1为处理中,2为待管理员处理）
				OrderGoodsCannotSettMap[fmt.Sprintf("%s:%d", v.OrderSn, v.GoodsId)] = 1 //注意： 这里有可能有OrderGoodsCannotSettMap[order_sn:0], 因为全部退款，就会有这种情况
			}
			if v.RefundState == 3 {
				OrderGoodsRefundAmtMap[fmt.Sprintf("%s:%d", v.OrderSn, v.GoodsId)] = utils.Yuan2Fen(v.RefundAmount)
			}

		}
	}
	log.Infof("%s-不能结算的订单有%s-已经发生退款的订单有%s", logPrefix, utils.InterfaceToJSON(OrderGoodsCannotSettMap), utils.InterfaceToJSON(OrderGoodsRefundAmtMap))
	for _, v := range data {
		if _, ok := OrderGoodsCannotSettMap[fmt.Sprintf("%s:%d", v.OrderNo, v.GoodsId)]; ok {
			log.Infof("%s-有正在处理的退款订单暂不可结算-orgId=%d-shopId=%d-disId=%d", logPrefix, v.OrgId, v.ShopId, v.DistributorId)
			continue
		}

		var newRefundAmount = OrderGoodsRefundAmtMap[fmt.Sprintf("%s:%d", v.OrderNo, v.GoodsId)] //这里是本次从退款表里 获得的退款金额

		// 备注：佣金率是：5%，则存的是5
		refund := v.RefundAmount
		if newRefundAmount > 0 {
			refund = newRefundAmount
		}
		decimalValue := decimal.NewFromFloat(cast.ToFloat64(v.PayAmount)).Sub(decimal.NewFromFloat(cast.ToFloat64(refund)))
		decimalValue = decimalValue.Mul(decimal.NewFromFloat(v.CommissionRate)).Mul(decimal.NewFromFloat(0.01))
		comm := decimalValue.Round(0).IntPart()

		tmp := struct {
			Commission     int
			Status         int
			SettlementTime time.Time
			RefundAmount   int
		}{
			Commission:     int(comm),
			Status:         dissettlement.StatusSetted,
			SettlementTime: time.Now(),
		}
		cols := []string{"commission", "settlement_time", "status"}
		if newRefundAmount > 0 {
			tmp.RefundAmount = newRefundAmount
			cols = append(cols, "refund_amount")
		}

		session := h.Engine.NewSession()
		session.Begin()

		if _, err := session.Table("dis_settlement").Cols(cols...).Where("id=?", v.Id).Where("status=?", dissettlement.StatusUnSett).Update(&tmp); err != nil {
			log.Errorf("%s-修改结算信息失败-orgId=%d-shopId=%d-disId=%d-错误为%s", logPrefix, v.OrgId, v.ShopId, v.DistributorId, err.Error())
			session.Rollback()
			return
		}
		if tmp.Commission > 0 { //佣金大于0时， 插入表dis_withdraw_record
			var insertRecord = distribution_po.DisWithdrawRecord{
				OrgId:           v.OrgId,
				ShopId:          v.ShopId,
				DisId:           v.DistributorId,
				WithdrawDisId:   0,
				ThirdId:         cast.ToInt64(v.Id),
				CreateTime:      time.Now(),
				UpdateTime:      time.Now(),
				Type:            diswithdrawrecord.TypeSetted,
				WaitWithdraw:    tmp.Commission,
				WithdrawApply:   0,
				WithdrawSuccess: 0,
			}

			if _, err := session.Table("eshop.dis_withdraw_record").Insert(&insertRecord); err != nil {
				log.Errorf("%s-插入提现记录失败-错误为-%s", logPrefix, err.Error())
				session.Rollback()
				return
			}
		}
		session.Commit()

		needUpdateMap[fmt.Sprintf("%d:%d:%d", v.OrgId, v.ShopId, v.DistributorId)] = "unsettled_commission,settled_commission"

	}
	// 修改可提现金额wait_withdraw
	go func() {
		h.SettCommUpdate(needUpdateMap)
		s := DiscommissionService{}
		s.WithdrawCommUpdate(needUpdateMap, enum.WithdrawFlagShop)
	}()

}

// 分销员所有店铺的待结算和结算数据写入（dis_distributor.unsettled_commission和dis_distributor.settled_commission）
// 分销员在指定分销店铺里的待结算和结算数据写入（dis_distributor_total.unsettled_commission和settled_commission）
// 分销店铺的待结算和结算数据写入（shop.unsettled_commission和settled_commission）
//
// 结算和待结算数据的更新 map[key]value 里的key是orgId:shopId:distributorId ; value是是指要更新的列（多个用逗号隔开 unsettled_commission,settled_commission ）
func (h *DisSettlementService) SettCommUpdate(needUpdateMap map[string]string, orgId ...int) {
	logPrefix := "待结算和结算数据的写入"
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(needUpdateMap), "orgId:", orgId)
	h.Begin()
	defer h.Close()
	disMap := map[string]int{}  //map[key]的key值为：unsettled_commission:orgId:disId
	shopMap := map[string]int{} //map[key]的key值为：unsettled_commission:orgId:shopId
	if len(needUpdateMap) == 0 && len(orgId) > 0 {
		data := make([]*distribution_po.DisDistributorTotal, 0)
		if err := h.Session.Table("eshop.dis_distributor_total").Where("org_id=?", orgId[0]).Find(&data); err != nil {
			log.Errorf("%s,获取dis_distributor_total数据失败：%s", logPrefix, err.Error())
			return
		}
		for _, v := range data {

			needUpdateMap[fmt.Sprintf("%d:%d:%d", v.OrgId, v.ShopId, v.DisId)] = "unsettled_commission,settled_commission"

		}
	}

	for k, v := range needUpdateMap {

		ksli := strings.Split(k, ":")
		vsli := strings.Split(v, ",")
		for _, vv := range vsli {
			var err error
			var comm int
			if vv != "unsettled_commission" && vv != "settled_commission" {
				log.Errorf("%s-待结算和结算数据的写入-错误为%s", logPrefix, "参数错误")
				return
			}
			status := dissettlement.StatusUnSett
			if vv == "settled_commission" {
				status = dissettlement.StatusSetted
			}

			if _, ok := disMap[fmt.Sprintf("%s:%s:%s", vv, ksli[0], ksli[2])]; !ok {
				disMap[fmt.Sprintf("%s:%s:%s", vv, ksli[0], ksli[2])] = 1
				//第一步：分销员维度 - 待结算和已结算佣金统计 dis_distributor
				comm, err := h.Session.Table("eshop.dis_settlement").SumInt(&distribution_po.DisSettlement{
					OrgId:         cast.ToInt(ksli[0]),
					DistributorId: cast.ToInt(ksli[2]),
					Status:        status,
				}, "commission")
				if err != nil {
					log.Errorf("%s-获取分销员维度-结算佣金失败-错误为%s", logPrefix, err.Error())
					return
				}
				updateData := &distribution_po.DisDistributor{UnsettledCommission: cast.ToInt(comm)}
				if vv == "settled_commission" {
					updateData = &distribution_po.DisDistributor{SettledCommission: cast.ToInt(comm)}
				}
				if _, err := h.Session.Table("eshop.dis_distributor").Where("id=?", ksli[2]).Where("org_id=?", ksli[0]).Cols(vv).Update(updateData); err != nil {
					log.Errorf("%s-更新分销员维度-结算佣金失败-错误为%s", logPrefix, err.Error())
					return
				}

			}

			//第二步：分销员在指定分销店铺维度 - 待结算和结算佣金统计 dis_distributor_total
			_, err = h.Session.Table("eshop.dis_settlement").Select("sum(commission) as commission").Where("org_id=?", cast.ToInt(ksli[0])).
				Where("shop_id=?", cast.ToInt(ksli[1])).Where("distributor_id=?", cast.ToInt(ksli[2])).Where("status=?", status).Get(&comm)
			if err != nil {
				log.Errorf("%s-分销员在指定分销店铺维度-错误为%s", logPrefix, err.Error())
				return
			}
			updateData := &distribution_po.DisDistributorTotal{UnsettledCommission: cast.ToInt(comm)}
			if vv == "settled_commission" {
				updateData = &distribution_po.DisDistributorTotal{SettledCommission: cast.ToInt(comm)}
			}
			if _, err := h.Session.Table("eshop.dis_distributor_total").Where("org_id=?", ksli[0]).Where("shop_id=?", ksli[1]).Where("dis_id=?", ksli[2]).Cols(vv).Update(updateData); err != nil {
				log.Errorf("%s-分销员在指定分销店铺维度-错误为%s", logPrefix, err.Error())
				return
			}

			if _, ok := shopMap[fmt.Sprintf("%s:%s:%s", vv, ksli[0], ksli[1])]; !ok {
				shopMap[fmt.Sprintf("%s:%s:%s", vv, ksli[0], ksli[1])] = 1
				//第三步:分销店铺维度 - 待结算和已结算佣金统计 shop
				comm := 0
				_, err = h.Session.Table("eshop.dis_settlement").Select("sum(commission) as commission").Where("org_id=?", cast.ToInt(ksli[0])).
					Where("shop_id=?", cast.ToInt(ksli[1])).Where("status=?", status).Get(&comm)
				if err != nil {
					log.Errorf("%s-分销店铺维度 - 结算佣金失败-错误为%s", logPrefix, err.Error())
					return
				}
				updateData := &distribution_po.Shop{UnsettledCommission: cast.ToInt(comm)}
				if vv == "settled_commission" {
					updateData = &distribution_po.Shop{SettledCommission: cast.ToInt(comm)}
				}
				if _, err := h.Session.Table("shop").Where("org_id=?", ksli[0]).Where("id=?", ksli[1]).Cols(vv).Update(updateData); err != nil {
					log.Errorf("%s-分销店铺维度-更新分销店铺结算佣金失败-错误为%s", logPrefix, err.Error())
					return
				}
			}
		}
	}
}
