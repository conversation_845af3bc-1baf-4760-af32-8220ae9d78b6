package services

import (
	"eShop/infra/log"
	"eShop/services/common"
	distribution_vo2 "eShop/view-model/distribution-vo"
	"reflect"
	"testing"

	_ "github.com/go-sql-driver/mysql"
)

func TestDisShopService_GetShopAdd(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req distribution_vo2.DisShopUpdateReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "修改或添加店铺"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := DisShopService{
				BaseService: tt.fields.BaseService,
			}

			req := distribution_vo2.DisShopUpdateReq{}
			req.Id = 3
			req.ShopName = "周翔的店铺"
			req.Welcome = "欢迎来到小小店"
			req.HeadImage = "www.baidu.com"
			if err := s.GetShop<PERSON>dd(req); (err != nil) != tt.wantErr {
				t.<PERSON>rf("GetShopAdd() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDisShopService_GetShopDetail(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req distribution_vo2.DisShopReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    distribution_vo2.Shop
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "获取店铺信息"},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := DisShopService{
				BaseService: tt.fields.BaseService,
			}
			req := distribution_vo2.DisShopReq{}
			req.ShopId = 3
			got, err := s.GetShopDetail(req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetShopDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetShopDetail() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDisShopService_DisShopList(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req distribution_vo2.DisShopListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []distribution_vo2.Shop
		want1   int
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "获取店铺列表",
			args: args{
				req: distribution_vo2.DisShopListReq{
					OrgId: 3,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := DisShopService{
				BaseService: tt.fields.BaseService,
			}
			tt.args.req.WhereType = 5
			tt.args.req.Where = "1779765859801673728"
			tt.args.req.EnterpriseName = ""
			got, got1, err := s.DisShopList(tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisShopList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DisShopList() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("DisShopList() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestDisShopService_GetDisShop(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req distribution_vo2.EnterpriseReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []distribution_vo2.EnterpriseRes
		want1   int
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "获取企业信息",
			args: args{
				req: distribution_vo2.EnterpriseReq{
					EnterpriseName: "",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := DisShopService{
				BaseService: tt.fields.BaseService,
			}
			got, got1, err := s.GetDisShop(tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDisShop() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetDisShop() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("DisShopList() got1 = %v, want %v", got1, tt.want1)
			}

		})
	}
}
