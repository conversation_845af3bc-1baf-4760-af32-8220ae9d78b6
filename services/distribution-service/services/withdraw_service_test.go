package services

import (
	"eShop/infra/cache"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"eShop/view-model"
	vo "eShop/view-model/distribution-vo"
	"reflect"
	"testing"
)

func TestDisWithdrawService_GetList(t *testing.T) {
	type args struct {
		in vo.GetDisWithdrawListReq
	}
	tests := []struct {
		name    string
		h       *DisWithdrawService
		args    args
		wantOut vo.GetDisWithdrawListRes
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "提现列表",
			h:    &DisWithdrawService{},
			args: args{
				in: vo.GetDisWithdrawListReq{
					OrgId:               3,
					BasePageHttpRequest: viewmodel.BasePageHttpRequest{PageIndex: 1, PageSize: 3},
					Status:              1,
					WhereType2:          "",
					Where2:              "",
					WhereType:           "create_time",
					WhereStart:          "2023-10-12",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, _, err := tt.h.GetDisWithdrawList(tt.args.in)
			t.Logf("gotout=%v", gotOut)
			t.Logf("err=%v", err)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("DisWithdrawService.GetList() error = %v, wantErr %v", err, tt.wantErr)
			// 	return
			// }
			// if !reflect.DeepEqual(gotOut, tt.wantOut) {
			// 	t.Errorf("DisWithdrawService.GetList() = %v, want %v", gotOut, tt.wantOut)
			// }
		})
	}
}

func TestDisWithdrawService_GetDisWithdrawDetail(t *testing.T) {
	type args struct {
		in vo.GetDisWithdrawDetailReq
	}
	tests := []struct {
		name    string
		h       *DisWithdrawService
		args    args
		wantOut vo.DisWithdrawView
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "提现详情",
			h:    &DisWithdrawService{},
			args: args{in: vo.GetDisWithdrawDetailReq{
				DisWithdrawId: 1,
				OrgId:         3,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.h.GetDisWithdrawDetail(tt.args.in)
			t.Logf("gotOut:%v", gotOut)
			t.Logf("err:%v", err)
		})
	}
}

func TestDisWithdrawService_DisWithdrawCheck(t *testing.T) {
	type args struct {
		in vo.DisWithdrawCheckReq
	}
	tests := []struct {
		name    string
		h       *DisWithdrawService
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "提现审核",
			h:    &DisWithdrawService{},
			args: args{
				in: vo.DisWithdrawCheckReq{
					DisWithdrawId: 2,
					CheckType:     1,
					RejectReason:  "444",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache.CacheSources[cache_source.EShop] = "redis://qlwhIPO82@#KDFQAwe@**********:6979?ssl=true&db=0" //config.Get("redis.Addr")
			if err := tt.h.DisWithdrawCheck(tt.args.in); err != nil {
				t.Errorf("DisWithdrawService.DisWithdrawCheck() error = %v", err)
			}
		})
	}
}

func TestDisWithdrawService_GetSimpleWithList(t *testing.T) {
	type args struct {
		in vo.GetSimpleWithListReq
	}
	tests := []struct {
		name      string
		h         *DisWithdrawService
		args      args
		wantOut   []vo.DisWithdrawView
		wantTotal int
		wantErr   bool
	}{
		// TODO: Add test cases.
		{
			name: "提现列表",
			h:    &DisWithdrawService{},
			args: args{
				in: vo.GetSimpleWithListReq{
					OrgId:               3,
					BasePageHttpRequest: viewmodel.BasePageHttpRequest{PageIndex: 1, PageSize: 3},
					ShopId:              1,
					DistributorId:       36,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, gotTotal, err := tt.h.GetSimpleWithList(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisWithdrawService.GetSimpleWithList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DisWithdrawService.GetSimpleWithList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("DisWithdrawService.GetSimpleWithList() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}
