package services

import (
	distribution_po "eShop/domain/distribution-po"
	"eShop/infra/log"
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"errors"
)

type DisWithdrawOrderService struct {
	common.BaseService
}

// GetWithdrawOrderList 获取可提现订单列表
func (h *DisWithdrawOrderService) GetWithdrawOrderList(in vo.GetWithdrawOrderListReq) (out []vo.WithdrawOrderItem, allWithdrawOrderItem []vo.WithdrawOrderItem, shopId, disid, total, amount, maxOrderId int, err error) {
	h.<PERSON>gin()
	defer h.Close()
	session := h.Engine.NewSession()
	defer session.Close()

	// 获取分销员信息
	d, err := new(distribution_po.DisDistributor).GetDisInfoByMemberIdOrScrmUserId(session, distribution_po.GetDisInfoReq{
		MemberId:   in.MemberId,
		ScrmUserId: in.ScrmUserId,
		OrgId:      in.OrgId,
	})
	if err != nil {
		log.Errorf("获取分销员信息失败: %s", err.Error())
		err = errors.New("获取分销员信息失败")
		return
	}
	if d.ShopId == 0 {
		log.Errorf("分销员信息中店铺id为0")
		err = errors.New("分销员信息中店铺id为0")
		return
	}
	shopId = d.ShopId
	disid = d.Id

	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	withdrawOrder := &distribution_po.DisWithdrawOrder{}
	if out, total, _, _, err = withdrawOrder.GetWithdrawOrderList(session, distribution_po.GetWithdrawOrderListReq{
		OrgId:     in.OrgId,
		PageIndex: in.PageIndex,
		PageSize:  in.PageSize,
		ShopId:    d.ShopId,
	}); err != nil {
		log.Errorf("获取可提现订单列表失败: %s", err.Error())
		err = errors.New("获取可提现订单列表失败")
		return
	}

	// 获取总金额
	if allWithdrawOrderItem, _, amount, maxOrderId, err = withdrawOrder.GetWithdrawOrderList(session, distribution_po.GetWithdrawOrderListReq{
		OrgId:  in.OrgId,
		ShopId: d.ShopId,
	}); err != nil {
		log.Errorf("获取可提现订单列表失败: %s", err.Error())
		err = errors.New("获取可提现订单列表失败")
		return
	}

	return
}

// GetRefundOrderList 获取退款单列表
func (h *DisWithdrawOrderService) GetRefundOrderList(in vo.GetRefundOrderListReq) (out []vo.DistributorSwlmCpsListRes, shopId, disId int, err error) {
	h.Begin()
	defer h.Close()
	session := h.Engine.NewSession()
	defer session.Close()

	// 获取分销员信息
	d, err := new(distribution_po.DisDistributor).GetDisInfoByMemberIdOrScrmUserId(session, distribution_po.GetDisInfoReq{
		MemberId:   in.MemberId,
		ScrmUserId: in.ScrmUserId,
		OrgId:      in.OrgId,
	})
	if err != nil {
		log.Errorf("获取分销员信息失败: %s", err.Error())
		err = errors.New("获取分销员信息失败")
		return
	}
	if d.ShopId == 0 {
		log.Errorf("分销员信息中店铺id为0")
		err = errors.New("分销员信息中店铺id为0")
		return
	}
	shopId = d.ShopId
	disId = d.Id
	out, err = new(distribution_po.DisDistributorSwlmCps).DistributorSwlmCpsList(h.Session, distribution_po.DistributorSwlmCpsListReq{
		ShopId: d.ShopId,
		Status: distribution_po.StatusNotDeduct,
	})
	if err != nil {
		log.Errorf("获取退款单列表失败: %s", err.Error())
		err = errors.New("获取退款单列表失败")
		return
	}

	return
}
