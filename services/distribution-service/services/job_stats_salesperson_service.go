package services

import (
	po "eShop/domain/distribution-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	vo "eShop/view-model/distribution-vo"
	"fmt"
	"sync"
	"time"
	"xorm.io/xorm"
)

type JobStatsSalespersonService struct {
	common.BaseService
}

type JobStatsSalespersonServiceData struct {
	Engine         *xorm.Engine
	Data           *po.StatsSalespersonDaily
	StartTimestamp int64
	EndTimestamp   int64
}

func (s JobStatsSalespersonService) StatsSalespersonOrderDaliyDataRun(startTime, endTime string) {
	startDay, err := time.Parse(utils.DateLayout, startTime)
	if err != nil {
		return
	}
	endDay, err := time.Parse(utils.DateLayout, endTime)
	if err != nil {
		return
	}
	for i := startDay; i.Before(endDay) || i.Equal(endDay); i = i.AddDate(0, 0, 1) {
		// 跑一天的数据
		fmt.Println("处理日期范围-业务员-周:", i.Format("2006-01-02"), "-", i.Format("2006-01-02"))
		s.StatsSalespersonOrderDailyData(vo.StatsShopDistributorDailyReq{
			StartDate: i.Format(time.DateOnly),
			EndDate:   i.Format(time.DateOnly),
		})

		// 检测是不是周末，跑一周的数据
		weekday := i.Weekday()
		if weekday == time.Sunday {
			startDate := i.AddDate(0, 0, -6)
			fmt.Println("处理日期范围-业务员-周:", startDate.Format("2006-01-02"), "-", i.Format("2006-01-02"))
			s.StatsSalespersonOrderDailyData(vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}

		// 检测是不是月末，跑一月的数据
		tomorrow := i.AddDate(0, 0, 1)
		if tomorrow.Day() == 1 {
			startDate := tomorrow.AddDate(0, -1, 0)
			fmt.Println("处理日期范围-业务员-月:", startDate.Format("2006-01-02"), "-", i.Format("2006-01-02"))
			s.StatsSalespersonOrderDailyData(vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}

		// 检测是不是年末，跑一年的数据
		if tomorrow.Month() == 1 && tomorrow.Day() == 1 {
			startDate := tomorrow.AddDate(-1, 0, 0)
			fmt.Println("处理日期范围-业务员-年:", startDate.Format("2006-01-02"), "-", i.Format("2006-01-02"))
			s.StatsSalespersonOrderDailyData(vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}
	}
}

// 跑历史数据每周的统计记录
func (s JobStatsSalespersonService) StatsSalespersonOrderWeeklyDataRun() {
	now := time.Now()
	startDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local)
	endDate := now

	for {
		if startDate.After(endDate) {
			break
		}
		// 计算本周的结束日期（周日）
		dayOfWeek := int(startDate.Weekday())
		weekEndDate := startDate.AddDate(0, 0, 7-dayOfWeek)
		// 如果本周结束日期超过了当前时间，则将结束日期设置为当前时间
		if weekEndDate.After(endDate) {
			weekEndDate = endDate
		}
		fmt.Println("处理日期范围:", startDate.Format("2006-01-02"), "-", weekEndDate.Format("2006-01-02"))

		s.StatsSalespersonOrderDailyData(vo.StatsShopDistributorDailyReq{
			StartDate: startDate.Format("2006-01-02"),
			EndDate:   weekEndDate.Format("2006-01-02"),
		})
		// 移动到下一周的开始日期（周一）
		startDate = weekEndDate.AddDate(0, 0, 1)
	}
}

func (s *JobStatsSalespersonService) StatsSalespersonOrderDaliyData() {
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsSalespersonOrderDataLock, "daily")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)

	now := time.Now()
	startDate := now.AddDate(0, 0, -1)
	s.StatsSalespersonOrderDailyData(vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   startDate.Format("2006-01-02"),
	})
}

func (s *JobStatsSalespersonService) StatsSalespersonOrderWeeklyData() {
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsSalespersonOrderDataLock, "weekly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)

	now := time.Now()
	startDate := now.AddDate(0, 0, -int(now.Weekday())-6)
	endDate := startDate.AddDate(0, 0, 6)
	s.StatsSalespersonOrderDailyData(vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

func (s *JobStatsSalespersonService) StatsSalespersonOrderMontylyData() {
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsSalespersonOrderDataLock, "monthly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)
	now := time.Now()
	startDate := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
	endDate := startDate.AddDate(0, 1, -1) // 加一个月，再减去一天
	s.StatsSalespersonOrderDailyData(vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

func (s *JobStatsSalespersonService) StatsSalespersonOrderYearlyData() {
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsSalespersonOrderDataLock, "yearly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)

	now := time.Now()
	startDate := time.Date(now.Year()-1, 1, 1, 0, 0, 0, 0, time.Local)
	endDate := time.Date(now.Year()-1, 12, 31, 0, 0, 0, 0, time.Local)
	s.StatsSalespersonOrderDailyData(vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

// 商品分销订单概览
// 1 累计下单指标
// 2 累计成交指标
// 3 分销新增来源数据
// 4 查询出的结果更新到StatsShopDistributorDaily表
// StatsSalespersonOrderDailyData 定时任务-统计商品分销订单每天的数据
func (s *JobStatsSalespersonService) StatsSalespersonOrderDailyData(req ...vo.StatsShopDistributorDailyReq) {
	log.Infof("StatsSalespersonOrderDailyData cron begin:%v", req)
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("StatsSalespersonOrderDailyData panic error:%v", err) // 这里的err其实就是panic传入的内容，55
		}
	}()
	s.Begin()
	defer s.Close()
	//日期字符串StartDate 2024-06-01 转成时间戳
	var StartDate, EndDate string
	if len(req) > 0 {
		StartDate = req[0].StartDate
		EndDate = req[0].EndDate
	}
	startTimestamp := utils.Date2Timestamp(StartDate)
	endTimestamp := utils.AddDate2Timestamp(EndDate)
	serv := &JobStatsSalespersonServiceData{
		Engine:         s.Engine,
		StartTimestamp: startTimestamp,
		EndTimestamp:   endTimestamp,
	}

	var salesperson = make([]po.StatsSalespersonDaily, 0)
	if err := s.Engine.SQL(`
SELECT
    s.id AS salesman_id,
    s.name AS salesman_name,
    s.org_name AS organization,
    (
        SELECT COUNT(DISTINCT sh.id)
        FROM eshop.scrm_enterprise_salesperson_bind b
        join  eshop.shop sh ON b.enterprise_id = sh.enterprise_id
        WHERE DATE(sh.create_time) >= ? and DATE(sh.create_time) <= ?
        AND b.salesperson_id = s.id
    ) AS service_enterprise_count, -- 服务企业数
    
    (
        SELECT COUNT(DISTINCT sh.id)
        FROM eshop.scrm_enterprise_salesperson_bind b
        join  eshop.shop sh ON b.enterprise_id = sh.enterprise_id
        WHERE DATE(sh.is_setted_time) >= ? and DATE(sh.is_setted_time) <= ?
        AND sh.is_setted_shop =1
        AND b.salesperson_id = s.id
    ) AS shop_count, -- 分销店铺数
            
      (SELECT COUNT(DISTINCT d.id)
        FROM eshop.dis_distributor d
             left join eshop.shop s on d.shop_id = s.id
             left join eshop.scrm_enterprise_salesperson_bind b on b.enterprise_id = s.enterprise_id
        WHERE DATE(d.create_time) >= ? and DATE(d.create_time) <= ? 
        and b.salesperson_id = osp.salesperson_id
        ) AS service_distributor_count, -- 分销员数

       (SELECT COUNT(DISTINCT d.id)
        FROM eshop.dis_distributor d
             left join eshop.shop s on d.shop_id = s.id
             left join eshop.scrm_enterprise_salesperson_bind b on b.enterprise_id = s.enterprise_id
        WHERE DATE(d.create_time) >= ? and DATE(d.create_time) <= ? 
        and d.dis_role = 1 and b.salesperson_id = osp.salesperson_id
        ) AS boss_count -- 老板数                                                     
FROM upetmart.upet_orders_salesperson osp
     JOIN upetmart.upet_orders o ON osp.order_id = o.order_id
     JOIN eshop.scrm_salesperson s ON osp.salesperson_id = s.id
WHERE o.add_time BETWEEN ? AND ?
GROUP BY osp.salesperson_id;
`, StartDate, EndDate, StartDate, EndDate, StartDate, EndDate, StartDate, EndDate, startTimestamp, endTimestamp).Find(&salesperson); err != nil {
		log.Errorf("StatsSalespersonOrderDailyData 查询分销员id失败 error:%v", err)
	}

	// 遍历分销员的数据统计
	for _, v := range salesperson {
		serv.Data = &po.StatsSalespersonDaily{}
		//判断表是否已存在
		_, err := serv.Engine.Table("eshop.stats_salesperson_daily").
			Where("stat_date = ? and end_date = ? and salesman_id = ?", StartDate, EndDate, v.SalesmanId).Get(serv.Data)
		if err != nil {
			log.Errorf("StatsSalespersonOrderDailyData 查统计表数据失败 error:%v", err)
		}
		wg := sync.WaitGroup{}
		wg.Add(2)
		// 累计分销下单指标
		go func(salesmanId int) {
			defer wg.Done()
			serv.StatsSalespersonOrderDailyData1(salesmanId)
		}(v.SalesmanId)

		// 累计商品成交企业数
		go func(salesmanId int) {
			defer wg.Done()
			serv.StatsSalespersonOrderDailyData2(salesmanId)
		}(v.SalesmanId)

		wg.Wait()

		//公共数据
		serv.Data.StatDate = StartDate
		serv.Data.EndDate = EndDate
		serv.Data.SalesmanId = v.SalesmanId
		serv.Data.SalesmanName = v.SalesmanName
		serv.Data.Organization = v.Organization
		serv.Data.ServiceEnterpriseCount = v.ServiceEnterpriseCount
		serv.Data.ShopCount = v.ShopCount
		serv.Data.ServiceDistributorCount = v.ServiceDistributorCount
		serv.Data.BossCount = v.BossCount

		if serv.Data.Id > 0 {
			if _, err = serv.Engine.Table("eshop.stats_salesperson_daily").
				ID(serv.Data.Id).Update(serv.Data); err != nil {
				log.Errorf("StatsSalespersonOrderDailyData 更新数据失败 error:%s", err.Error())
			}
		} else {
			_, err = serv.Engine.Table("eshop.stats_salesperson_daily").Insert(serv.Data)
			if err != nil {
				log.Errorf("StatsSalespersonOrderDailyData 添加数据失败 error:%v", err)
			}
		}
	}
}

// StatsSalespersonOrderDailyData1 累计分销下单指标
func (s *JobStatsSalespersonServiceData) StatsSalespersonOrderDailyData1(salesmanId int) {
	var (
		OrderIds           string
		ProductTransCount  int
		ProductTransAmount int
		ProductCommission  int
	)
	session := s.Engine
	_, err := session.SQL(`
SELECT 
    group_concat(DISTINCT o.order_id) as order_ids,
    COUNT(DISTINCT o.order_id) AS product_trans_count,
       ROUND(SUM(COALESCE(og.goods_pay_price,0) * og.dis_commis_rate)) AS product_commission
FROM upetmart.upet_orders_salesperson osp
     LEFT JOIN upetmart.upet_orders o ON osp.order_id = o.order_id
     LEFT JOIN upetmart.upet_order_goods og ON o.order_id = og.order_id
WHERE osp.salesperson_id = ?
  AND o.is_dis = 1
  AND o.order_father > 0
  AND o.add_time BETWEEN ? AND ?
  AND o.payment_time > 0;
`, salesmanId, s.StartTimestamp, s.EndTimestamp).
		Get(&OrderIds, &ProductTransCount, &ProductCommission)
	if err != nil {
		log.Errorf("StatsSalespersonOrderDailyData 累计分销下单指标 error:%v", err)
	}

	// 单独统计：分销商品成交金额
	session2 := s.Engine
	_, err = session2.SQL(`
SELECT ROUND(SUM(COALESCE(order_amount, 0) * 100)) AS product_trans_amount
FROM upetmart.upet_orders_salesperson osp
     left JOIN upetmart.upet_orders o ON osp.order_id = o.order_id
WHERE osp.salesperson_id = ?
  AND o.is_dis = 1
  AND o.order_father > 0
  AND o.payment_time > 0
  AND o.add_time BETWEEN ? AND ?;
`, salesmanId, s.StartTimestamp, s.EndTimestamp).Get(&ProductTransAmount)
	if err != nil {
		log.Errorf("StatsSalespersonOrderDailyData 累计分销下单商品成交指标 error:%v", err)
	}

	s.Data.ProductTransCount = ProductTransCount
	s.Data.ProductTransAmount = ProductTransAmount
	s.Data.ProductCommission = ProductCommission
}

// StatsSalespersonOrderDailyData1 累计分销下单指标 product_enterprise_count
func (s *JobStatsSalespersonServiceData) StatsSalespersonOrderDailyData2(salesmanId int) {
	var (
		ProductEnterpriseCount int
	)
	session := s.Engine
	_, err := session.SQL(`
SELECT COUNT(DISTINCT og.shop_id)
FROM upetmart.upet_orders_salesperson osp
     LEFT JOIN upetmart.upet_orders o ON osp.order_id = o.order_id
     LEFT JOIN upetmart.upet_order_goods og ON osp.order_id = og.order_id
WHERE o.add_time BETWEEN ? AND ?
  AND o.order_father > 0
  AND osp.salesperson_id = ?
  AND og.shop_id > 0;
`, s.StartTimestamp, s.EndTimestamp, salesmanId).
		Get(&ProductEnterpriseCount)
	if err != nil {
		log.Errorf("StatsSalespersonOrderDailyData 累计商品成交企业数 error:%v", err)
	}
	s.Data.ProductEnterpriseCount = ProductEnterpriseCount

}
