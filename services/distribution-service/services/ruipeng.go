package services

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	disdatareference "eShop/services/distribution-service/enum/dis-data-reference"
	"eShop/view-model"
	vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"strings"
)

type RPService struct {
	common.BaseService
}

// 获取瑞鹏体制内外的医院列表
func (s RPService) AllHospitalList(req vo.AllHospitalListReq) (out []vo.Hospital, total int, err error) {
	s.<PERSON>gin()
	defer s.Close()
	log.Info("获取瑞鹏医院列表,入参：", utils.InterfaceToJSON(req))
	session := s.Session
	if req.City == "" {
		err = errors.New("请选择城市")
		return
	}
	req.City = strings.TrimRight(req.City, "市")
	out = make([]vo.Hospital, 0)

	//sql := fmt.Sprintf("((select hospital_name from scrm_organization_db.t_scrm_hospital_info where hospital_status=0 and hospital_city like %s) union (select shop hospital_name from eshop.scrm_leads_autonavi where city like %s)) aa", "'%"+req.City+"%'", "'%"+req.City+"%'")

	sql := `select hospital_name  from  ((
				select
					hospital_name
				from
					scrm_organization_db.t_scrm_hospital_info
				where
					hospital_status = 0 
					and hospital_city REGEXP ?)
				union (
				select
				shop hospital_name
				from
				eshop.scrm_leads_autonavi
				where
				city REGEXP ?)) aa`
	if err = session.SQL(sql, req.City, req.City).Find(&out); err != nil {
		log.Error("获取医院列表失败，err=", err.Error())
		return nil, 0, errors.New("获取医院列表失败")
	}

	return

}

// 获取瑞鹏体制内的医院列表
func (s RPService) RPHospitalList(req vo.RPHospitalListReq) (out []vo.Hospital, err error) {
	s.Begin()
	defer s.Close()
	log.Info("获取瑞鹏医院列表,入参：", utils.InterfaceToJSON(req))
	session := s.Session
	if req.City == "" {
		return nil, errors.New("请选择城市")
	}
	req.City = strings.TrimRight(req.City, "市")
	out = make([]vo.Hospital, 0)

	if err = session.Table("scrm_organization_db.t_scrm_hospital_info").
		Select("hospital_name").Where(fmt.Sprintf("hospital_city REGEXP %q", req.City)).Where("hospital_status=0").Where("hospital_name NOT LIKE '%测试%'").Find(&out); err != nil {
		log.Error("获取医院列表失败，err=", err.Error())
		return nil, errors.New("获取医院列表失败")
	}
	return out, nil

}

// 获取字典数据
func (s RPService) DictData() (out map[string][]viewmodel.DisDataReference, err error) {
	s.Begin()
	defer s.Close()

	data := make([]viewmodel.DisDataReference, 0)
	if err = s.Session.Table("eshop.dis_data_reference").Find(&data); err != nil {
		log.Error("获取字典数据失败，err=", err.Error())
		return nil, errors.New("获取字典数据失败")
	}
	out = make(map[string][]viewmodel.DisDataReference)
	for _, v := range data {
		if _, ok := out[disdatareference.TypeMap[v.Type]]; !ok {
			out[disdatareference.TypeMap[v.Type]] = make([]viewmodel.DisDataReference, 0)
		}

		out[disdatareference.TypeMap[v.Type]] = append(out[disdatareference.TypeMap[v.Type]], v)
	}
	return out, nil

}
