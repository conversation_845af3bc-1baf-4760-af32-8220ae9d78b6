package services

import (
	vo "eShop/view-model/distribution-vo"
	"testing"
)

func TestRPService_RPHospitalList(t *testing.T) {
	type args struct {
		req vo.RPHospitalListReq
	}
	tests := []struct {
		name    string
		s       RPService
		args    args
		wantOut []vo.Hospital
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "获取医院列表",
			s:    RPService{},
			args: args{
				req: vo.RPHospitalListReq{
					City: "深圳市",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.s.RPHospitalList(tt.args.req)
			if err != nil {
				t.Errorf("RPService.RPHospitalList() error = %v", err)
			}
			t.Logf("%+v", gotOut)

		})
	}
}
