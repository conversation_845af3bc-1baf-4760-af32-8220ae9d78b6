package services

import (
	"eShop/infra/cache"
	"eShop/infra/config"
	"eShop/infra/log"
	"eShop/infra/utils"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	vo "eShop/view-model/distribution-vo"
	"fmt"
	"reflect"
	"testing"

	"github.com/spf13/cast"
)

func TestDisSettlementService_GetDisSettlementList(t *testing.T) {
	type args struct {
		in vo.GetDisSettlementListReq
	}
	tests := []struct {
		name      string
		h         *DisSettlementService
		args      args
		wantOut   []vo.DisSettlementView
		wantTotal int
		wantErr   bool
	}{
		// TODO: Add test cases.
		{

			name: "结算列表",
			h:    &DisSettlementService{},
			args: args{
				in: vo.GetDisSettlementListReq{
					//BasePageHttpRequest: vo.BasePageHttpRequest{PageIndex: 1, PageSize: 2},
					OrgId:      3,
					Status:     1,
					ShopId:     1,
					WhereType:  "distributor_mobile",
					Where:      "188",
					WhereType2: "settlement_time",
					WhereStart: "2022-03-13",
					WhereEnd:   "2024-04-14",
				},
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, gotTotal, err := tt.h.GetDisSettlementList(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisSettlementService.GetDisSettlementList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DisSettlementService.GetDisSettlementList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("DisSettlementService.GetDisSettlementList() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}
func init1() {
	//cache.CacheSources[cache_source.EShop] = "redis://qlwhIPO82@#KDFQAwe@**********:6979?ssl=true&db=0" //config.Get("redis.Addr")
	redisAddr := fmt.Sprintf("redis://%s@%s?ssl=true&db=%d", config.Get("redis.Password"), config.Get("redis.Addr"), cast.ToInt(config.Get("redis.DB")))
	cache.CacheSources[cache_source.EShop] = cache.Address(redisAddr)
	log.Init()
	//初始化
	utils.InitClient()
}
func TestDisSettlementService_InsertSettlementData(t *testing.T) {
	tests := []struct {
		name string
		h    *DisSettlementService
	}{
		// TODO: Add test cases.
		{
			name: "写入待结算数据",
			h:    &DisSettlementService{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.h.InsertSettlementData()
		})
	}
}

func TestDisSettlementService_ChangeSettlementStatus(t *testing.T) {
	tests := []struct {
		name string
		h    *DisSettlementService
	}{
		// TODO: Add test cases.
		{
			name: "待结算变成已结算",
			h:    &DisSettlementService{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.h.ChangeSettlementStatus()
		})
	}
}

func TestDisSettlementService_SettCommUpdate(t *testing.T) {
	type args struct {
		needUpdateMap map[string]string
	}
	tests := []struct {
		name string
		h    *DisSettlementService
		args args
	}{
		// TODO: Add test cases.
		{
			name: "结算佣金更新",
			h:    &DisSettlementService{},
			args: args{
				needUpdateMap: map[string]string{
					"3:1:42": "unsettled_commission,settled_commission",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.h.Begin()
			defer tt.h.Close()
			tt.h.SettCommUpdate(tt.args.needUpdateMap)
		})
	}
}
