package services

import (
	po "eShop/domain/upetmart-po"
	"eShop/infra/log"
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"fmt"
	"time"
)

// VoucherService 优惠券服务
type VoucherService struct {
	common.BaseService
}

// NewVoucherService 创建优惠券服务实例
func NewVoucherService() *VoucherService {
	return &VoucherService{}
}

// GetVoucherList 获取优惠券列表
func (s *VoucherService) GetVoucherList(req vo.VoucherPageReq) ([]vo.VoucherItem, int64, error) {
	s.<PERSON>gin()
	defer s.Close()

	// 参数验证
	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}
	var tempList []po.VoucherExt
	session := s.Engine.Table("upetmart.upet_voucher").Alias("v").
		Join("INNER", "upetmart.upet_voucher_template vt", "v.voucher_t_id = vt.voucher_t_id").
		Select("v.voucher_id,v.voucher_t_id, v.voucher_title, v.voucher_code, v.voucher_type, v.voucher_price, v.voucher_active_date, v.voucher_start_date, v.voucher_end_date, v.voucher_state, v.voucher_active_date, vt.voucher_t_gettype, vt.voucher_days").
		Where("v.voucher_store_id = ? and v.voucher_owner_id =?", req.OrgId, req.MemberId)

	// 添加查询条件
	if req.VoucherTitle != "" {
		session.Where("v.voucher_title LIKE ?", "%"+req.VoucherTitle+"%")
	}
	if req.VoucherCode != "" {
		session.Where("v.voucher_code = ?", req.VoucherCode)
	}
	if req.VoucherState > 0 {
		session.Where("v.voucher_state = ?", req.VoucherState)
	}

	// 查询数据
	total, err := session.
		OrderBy("v.voucher_id DESC").
		Limit(req.PageSize, (req.PageIndex-1)*req.PageSize).
		FindAndCount(&tempList)
	if err != nil {
		log.Errorf("查询优惠券列表失败: %v", err)
		return nil, 0, fmt.Errorf("查询优惠券列表失败: %v", err)
	}

	vouchers := make([]vo.VoucherItem, len(tempList))

	for i := range tempList {
		vouchers[i] = vo.VoucherItem{
			VoucherId:         tempList[i].VoucherId,
			VoucherTId:        tempList[i].VoucherTId,
			VoucherTitle:      tempList[i].VoucherTitle,
			VoucherCode:       tempList[i].VoucherCode,
			VoucherType:       tempList[i].VoucherType,
			VoucherPrice:      float64(tempList[i].VoucherPrice),
			VoucherStartDate:  time.Unix(tempList[i].VoucherStartDate, 0).Format("2006-01-02 15:04:05"),
			VoucherEndDate:    time.Unix(tempList[i].VoucherEndDate, 0).Format("2006-01-02 15:04:05"),
			VoucherState:      tempList[i].VoucherState,
			VoucherActiveDate: time.Unix(tempList[i].VoucherActiveDate, 0).Format("2006-01-02 15:04:05"),
			VoucherTGettype:   tempList[i].VoucherTGettype,
			VoucherDays:       tempList[i].VoucherDays,
			GettypeText:       GetVoucherGettypeText(tempList[i].VoucherTGettype),
		}
	}

	return vouchers, total, nil
}

// GetVoucherGettypeText 获取优惠券领取方式文本
func GetVoucherGettypeText(gettype int) string {
	switch gettype {
	case 1:
		return "积分兑换"
	case 2:
		return "卡密兑换"
	case 3:
		return "免费领取"
	case 4:
		return "自动派发"
	case 5:
		return "支付有礼"
	default:
		return "未知方式"
	}
}
