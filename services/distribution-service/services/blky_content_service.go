package services

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"fmt"
	"strings"
)

type BlkyContentService struct {
	common.BaseService
}

// 百林康源 获取文章列表
func (s BlkyContentService) ArticleList(req vo.ArticleListReq) (data []vo.ContentInfo, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("百林康源获取文章列表,入参：%s", utils.InterfaceToJSON(req))
	log.Info(logPrefix)
	if req.Tags != "" {
		tags := strings.Split(req.Tags, ",")
		for _, tag := range tags {
			contentInfo := vo.ContentInfo{
				Id:        0,
				Title:     tag,
				Type:      0,
				Childrens: make([]vo.ContentInfo, 0),
				UpdatedAt: "",
			}
			articles := make([]vo.ContentInfo, 0)
			goods := make([]vo.ContentInfo, 0)

			query := s.Engine.Table("dc_content.article").Select("id,title,1 as type,updated_at,cover_url img").Where("org_id = ? and status = 1 ", req.OrgId).OrderBy("last_publish_time desc,id desc")

			if req.Keyword != "" {
				query.Where("title like ? or content like ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
			}
			query.Where("tag_json regexp ?", fmt.Sprintf("name\":\"疾病部位\",\"tags\":\".*%s", tag))
			if err = query.Find(&articles); err != nil {
				return
			}

			session.Table("upetmart.upet_goods").Alias("a").Select("distinct a.goods_id as id ,a.goods_name as title,2 as type , goods_image as img,a.goods_jingle as content").
				Join("left", "upetmart.upet_tags_goods b", "a.store_id=b.store_id and a.goods_id=b.tag_goods_id").
				Join("left", "upetmart.upet_tags c", "b.tag_id=c.tag_id").
				Where("a.g_search_status=0").Where("a.goods_state=1").Where("a.goods_verify=1").Where("a.store_id=4").
				Where("c.tag_name=?", tag)
			if req.Keyword != "" {
				session.Where("a.goods_name like ?", "%"+req.Keyword+"%")
			}

			if err := session.OrderBy("a.goods_addtime desc").Find(&goods); err != nil {
				log.Error(logPrefix, "获取商品列表失败，err=", err.Error())
			}
			contentInfo.Childrens = append(contentInfo.Childrens, goods...)
			contentInfo.Childrens = append(contentInfo.Childrens, articles...)
			data = append(data, contentInfo)
		}
	}

	return
}

func (s BlkyContentService) SearchArticleList(req vo.SearchArticleListReq) (data []vo.SearchContentInfo, total int, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	var countNum int64
	logPrefix := fmt.Sprintf("百林康源搜索文章列表,入参：%s", utils.InterfaceToJSON(req))
	log.Info(logPrefix)
	if req.ArticleType == 1 {
		data = make([]vo.SearchContentInfo, 0)
		articles := make([]vo.SearchContentInfo, 0)
		goods := make([]vo.SearchContentInfo, 0)
		query := s.Engine.Table("dc_content.article").Select("id,title,1 as type,updated_at,cover_url img").Where("org_id = ? and status = 1 ", req.OrgId).OrderBy("last_publish_time desc,id desc")
		if req.Keyword != "" {
			query.Where("title like ? or content like ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
		}
		if countNum, err = query.FindAndCount(&articles); err != nil {
			log.Error(logPrefix, "搜索文章列表失败，err=", err.Error())
			return
		}

		tags := []string{"肠胃", "皮肤", "泌尿", "肾脏", "肝胆", "心脏", "胰腺", "关节", "骨骼", "呼吸系统", "繁育", "过敏", "免疫", "综合营养"}
		session.Table("upetmart.upet_goods").Alias("a").Select("distinct a.goods_id as id ,a.goods_name as title,2 as type , a.goods_image as img,a.goods_jingle as content").
			Join("left", "upetmart.upet_tags_goods b", "a.store_id=b.store_id and a.goods_id=b.tag_goods_id").
			Join("left", "upetmart.upet_tags c", "b.tag_id=c.tag_id").
			Where("a.g_search_status=0").Where("a.goods_state=1").Where("a.goods_verify=1").Where("a.store_id=4").
			In("c.tag_name", tags)

		if req.Keyword != "" {
			session.Where("a.goods_name like ?", "%"+req.Keyword+"%")
		}

		if err = session.OrderBy("a.goods_addtime desc").Find(&goods); err != nil {
			log.Error(logPrefix, "获取商品列表失败，err=", err.Error())
		}

		total = int(countNum) + len(goods)
		data = append(data, goods...)
		data = append(data, articles...)
		return
	} else {
		query := s.Engine.Table("dc_content.article").Select("id,title,updated_at,cover_url img,(select SUM(CASE type WHEN 1 THEN 1 ELSE 0 END) from dc_content.article_visit_record where content_id = dc_content.article.id) page_view").Where("org_id = ? and status = 1 ", req.OrgId).Limit(5).OrderBy("page_view desc,last_publish_time desc")
		if err = query.Find(&data); err != nil {
			return
		}
	}

	return
}
