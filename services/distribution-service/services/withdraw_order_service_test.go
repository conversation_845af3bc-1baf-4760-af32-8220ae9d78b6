package services

import (
	_ "github.com/go-sql-driver/mysql"
)

// func TestDisWithdrawOrderService_GetWithdrawOrderList(t *testing.T) {
// 	// 初始化日志
// 	log.Init()

// 	type fields struct {
// 		BaseService common.BaseService
// 	}
// 	type args struct {
// 		in vo.GetWithdrawOrderListReq
// 	}
// 	tests := []struct {
// 		name      string
// 		fields    fields
// 		args      args
// 		wantOut   []vo.WithdrawOrderItem
// 		wantTotal int
// 		wantErr   bool
// 	}{
// 		{
// 			name: "test_get_withdraw_order_list",
// 			fields: fields{
// 				BaseService: common.BaseService{},
// 			},
// 			args: args{
// 				in: vo.GetWithdrawOrderListReq{
// 					PageIndex: 1,
// 					PageSize:  10,
// 					OrgId:     4,
// 				},
// 			},
// 			wantErr: false,
// 		},
// 	}

// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			s := DisWithdrawOrderService{
// 				BaseService: tt.fields.BaseService,
// 			}

// 			gotOut, gotTotal, err := s.GetWithdrawOrderList(tt.args.in)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("DisWithdrawOrderService.GetWithdrawOrderList() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if !reflect.DeepEqual(gotOut, tt.wantOut) {
// 				t.Logf("DisWithdrawOrderService.GetWithdrawOrderList() gotOut = %v", gotOut)
// 			}
// 			if gotTotal != tt.wantTotal {
// 				t.Logf("DisWithdrawOrderService.GetWithdrawOrderList() gotTotal = %v", gotTotal)
// 			}
// 		})
// 	}
// }

// func TestDisWithdrawOrderService_GetRefundOrderList(t *testing.T) {
// 	// 初始化日志
// 	log.Init()

// 	type fields struct {
// 		BaseService common.BaseService
// 	}
// 	type args struct {
// 		in vo.GetRefundOrderListReq
// 	}
// 	tests := []struct {
// 		name      string
// 		fields    fields
// 		args      args
// 		wantOut   []vo.DistributorSwlmCpsListRes
// 		wantErr   bool
// 	}{
// 		{
// 			name: "test_get_refund_order_list",
// 			fields: fields{
// 				BaseService: common.BaseService{},
// 			},
// 			args: args{
// 				in: vo.GetRefundOrderListReq{
// 					PageIndex: 1,
// 					PageSize:  10,
// 					OrgId:     4,
// 				},
// 			},
// 			wantErr: false,
// 		},
// 	}

// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			s := DisWithdrawOrderService{
// 				BaseService: tt.fields.BaseService,
// 			}

// 			gotOut, gotTotal, err := s.GetRefundOrderList(tt.args.in)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("DisWithdrawOrderService.GetRefundOrderList() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if !reflect.DeepEqual(gotOut, tt.wantOut) {
// 				t.Logf("DisWithdrawOrderService.GetRefundOrderList() gotOut = %v", gotOut)
// 			}
// 			if gotTotal != tt.wantTotal {
// 				t.Logf("DisWithdrawOrderService.GetRefundOrderList() gotTotal = %v", gotTotal)
// 			}
// 		})
// 	}
// }
