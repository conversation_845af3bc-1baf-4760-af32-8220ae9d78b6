package services

import (
	"eShop/services/common"
	"eShop/view-model/distribution-vo"
	"testing"
)

// import (
//
//	distribution_vo "eShop/contracts/view-model/distribution-vo"
//	vo "eShop/contracts/view-model/distribution-vo"
//	"eShop/services/common"
//	"eShop/services/distribution-service/enum"
//	//disdistributor "eShop/services/distribution-service/enum/dis_distributor"
//	"reflect"
//	"testing"
//
//	_ "github.com/go-sql-driver/mysql"
//
// )

func TestDistributorManageService_DistributorPage(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req distribution_vo.DistributorPageReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want1   int
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "分销员列表"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := DistributorManageService{
				BaseService: tt.fields.BaseService,
			}
			in := distribution_vo.DistributorPageReq{}
			in.OrgId = 3
			in.WhereType = "name"
			in.Where = ""
			in.ApproveState = 0
			in.PageSize = 10
			in.PageIndex = 1
			s.DistributorPage(in)

		})
	}
}

//	func TestDistributorManageService_DistributorChange(t *testing.T) {
//		type fields struct {
//			BaseService common.BaseService
//		}
//		type args struct {
//			req distribution_vo.DisChangeReq
//		}
//		tests := []struct {
//			name    string
//			fields  fields
//			args    args
//			wantErr bool
//		}{
//			// TODO: Add test cases.
//			{name: "分销员更换企业"},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				s := DistributorManageService{
//					BaseService: tt.fields.BaseService,
//				}
//				req := distribution_vo.DisChangeReq{}
//				req.OrgId = 3
//				req.Id = 6
//				req.EnterpriseId = "1779800169170644992"
//				if err := s.DistributorChange(req); (err != nil) != tt.wantErr {
//					t.Errorf("DistributorChange() error = %v, wantErr %v", err, tt.wantErr)
//				}
//			})
//		}
//	}
//
//	func TestDistributorManageService_DistributorDetail(t *testing.T) {
//		type fields struct {
//			BaseService common.BaseService
//		}
//		type args struct {
//			req distribution_vo.DisIdReq
//		}
//		tests := []struct {
//			name    string
//			fields  fields
//			args    args
//			want    distribution_vo.DistributorDetail
//			wantErr bool
//		}{
//			// TODO: Add test cases.
//			{name: "分销员详情"},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				s := DistributorManageService{
//					BaseService: tt.fields.BaseService,
//				}
//				req := distribution_vo.DisIdReq{}
//				req.Id = 6
//				got, err := s.DistributorDetail(req)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("DistributorDetail() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if !reflect.DeepEqual(got, tt.want) {
//					t.Errorf("DistributorDetail() got = %v, want %v", got, tt.want)
//				}
//			})
//		}
//	}
//
//	func TestDistributorManageService_DistributorGet(t *testing.T) {
//		type fields struct {
//			BaseService common.BaseService
//		}
//		type args struct {
//			req distribution_vo.DistributorReq
//		}
//		tests := []struct {
//			name    string
//			fields  fields
//			args    args
//			want    distribution_vo.DistributorPageData
//			wantErr bool
//		}{
//			// TODO: Add test cases.
//			{name: "前端获取分销员信息"},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				s := DistributorManageService{
//					BaseService: tt.fields.BaseService,
//				}
//				req := distribution_vo.DistributorReq{}
//				req.OrgId = 3
//				req.ScrmUserId = "86448243dec543d2a0e465c0f65c6850"
//				got, err := s.DistributorGet(req)
//				if (err != nil) != tt.wantErr {
//					t.Errorf("DistributorGet() error = %v, wantErr %v", err, tt.wantErr)
//					return
//				}
//				if !reflect.DeepEqual(got, tt.want) {
//					t.Errorf("DistributorGet() got = %v, want %v", got, tt.want)
//				}
//			})
//		}
//	}
func TestDistributorManageService_DistributorInsert(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req distribution_vo.DisDistributorAddReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "分销员入住"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := DistributorManageService{
				BaseService: tt.fields.BaseService,
			}
			req := distribution_vo.DisDistributorAddReq{}
			req.OrgId = 3
			req.IdCard = "370783199507122757"
			req.TuokeSalespersonId = "1779765859801673728"
			req.DisRole = 1
			req.MemberId = 10000083
			req.Name = "zyw新分销员"
			req.HeadImage = "头图"
			req.IdcardFront = "身份证正面"
			req.IdcardReverse = "身份证反面"
			req.SocialCodeImage = "营业执照"
			req.SocialCreditCode = "统一社会信用代码"
			req.Mobile = "13126142230"
			req.EnterpriseId = "1782302822421278722"

			if err := s.DistributorInsert(req); (err != nil) != tt.wantErr {
				t.Errorf("DistributorInsert() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

//
//func TestDistributorManageService_SaleManCenterDetails(t *testing.T) {
//	type fields struct {
//		BaseService common.BaseService
//	}
//	type args struct {
//		in distribution_vo.SalesAchievementReq
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{name: "获取店铺下的分销员"},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			s := DistributorManageService{
//				BaseService: tt.fields.BaseService,
//			}
//			in := distribution_vo.SalesAchievementReq{}
//			in.OrgId = 3
//			in.ShopId = 3
//			s.SaleManCenterDetails(in)
//
//		})
//	}
//}
//
//func TestDistributorManageService_BLKYDistributorInsert(t *testing.T) {
//	type args struct {
//		req vo.DisDistributorAddReq
//	}
//	tests := []struct {
//		name    string
//		s       DistributorManageService
//		args    args
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		// {
//		// 	name: "百林康源",
//		// 	s:    DistributorManageService{},
//		// 	args: args{
//		// 		req: vo.DisDistributorAddReq{
//		// 			OrgId:           enum.BLKYOrgId,
//		// 			MemberId:        138718,
//		// 			Mobile:          "18870070534",
//		// 			DisRole:         disdistributor.DisRoleDoctor,
//		// 			HeadImage:       "",
//		// 			Name:            "饶翠",
//		// 			SocialCodeImage: "",
//		// 			HospitalName:    "顽皮医院", //医院名称（百林康源）
//		// 			Province:        "广东省",  //省份（百林康源）
//		// 			City:            "深圳市",  //城市（百林康源）
//		// 			Professional:    "助理",   //身份、职称（百林康源）
//		// 			Specialize:      "内科",   //擅长（百林康源）
//
//		// 		},
//		// 	},
//		// },
//		// {
//		// 	name: "百林康源",
//		// 	s:    DistributorManageService{},
//		// 	args: args{
//		// 		req: vo.DisDistributorAddReq{
//		// 			OrgId:           enum.BLKYOrgId,
//		// 			MemberId:        2,
//		// 			Mobile:          "15118811943",
//		// 			DisRole:         disdistributor.DisRoleDoctor,
//		// 			HeadImage:       "",
//		// 			Name:            "卓宝宝",
//		// 			SocialCodeImage: "",
//		// 			HospitalName:    "其他",   //医院名称（百林康源）
//		// 			Province:        "广东省",  //省份（百林康源）
//		// 			City:            "深圳市",  //城市（百林康源）
//		// 			Professional:    "院长",   //身份、职称（百林康源）
//		// 			Specialize:      "中兽医科", //擅长（百林康源）
//
//		// 		},
//		// 	},
//		// },
//		// {
//		// 	name: "百林康源",
//		// 	s:    DistributorManageService{},
//		// 	args: args{
//		// 		req: vo.DisDistributorAddReq{
//		// 			OrgId:           enum.BLKYOrgId,
//		// 			MemberId:        103855,
//		// 			Mobile:          "13510676311",
//		// 			DisRole:         disdistributor.DisRoleDoctor,
//		// 			HeadImage:       "",
//		// 			Name:            "许成鹏",
//		// 			SocialCodeImage: "",
//		// 			HospitalName:    "其他",   //医院名称（百林康源）
//		// 			Province:        "广东省",  //省份（百林康源）
//		// 			City:            "深圳市",  //城市（百林康源）
//		// 			Professional:    "院长",   //身份、职称（百林康源）
//		// 			Specialize:      "中兽医科", //擅长（百林康源）
//
//		// 		},
//		// 	},
//		// },
//		{
//			name: "百林康源",
//			s:    DistributorManageService{},
//			args: args{
//				req: vo.DisDistributorAddReq{
//					OrgId:           enum.BLKYOrgId,
//					MemberId:        34157,
//					Mobile:          "15361625050",
//					DisRole:         disdistributor.DisRoleDoctor,
//					HeadImage:       "",
//					Name:            "李晓明",
//					SocialCodeImage: "",
//					HospitalName:    "其他",   //医院名称（百林康源）
//					Province:        "广东省",  //省份（百林康源）
//					City:            "深圳市",  //城市（百林康源）
//					Professional:    "院长",   //身份、职称（百林康源）
//					Specialize:      "中兽医科", //擅长（百林康源）
//
//				},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if err := tt.s.BLKYDistributorInsert(tt.args.req); err != nil {
//				t.Errorf("DistributorManageService.BLKYDistributorInsert() error = %v, ", err)
//			}
//		})
//	}
//}
//
//func TestDistributorManageService_BLKYDistributorGet(t *testing.T) {
//	type args struct {
//		req vo.DistributorReq
//	}
//	tests := []struct {
//		name    string
//		s       DistributorManageService
//		args    args
//		want    vo.DistributorPageData
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{
//			name: "获取百林康源分销员信息",
//			s:    DistributorManageService{},
//			args: args{
//				req: vo.DistributorReq{
//					MemberId: 138718,
//					OrgId:    4,
//				},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := tt.s.BLKYDistributorGet(tt.args.req)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("DistributorManageService.BLKYDistributorGet() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("DistributorManageService.BLKYDistributorGet() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestDistributorManageService_Approve(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req distribution_vo.DisApproveReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				req: distribution_vo.DisApproveReq{
					Id:           30,
					ApproveState: 3,
					Reason:       "test nopass",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := DistributorManageService{
				BaseService: tt.fields.BaseService,
			}
			if err := s.Approve(tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("Approve() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
