package services

import (
	"eShop/view-model"
	"eShop/view-model/distribution-vo"
	"reflect"
	"testing"
)

func TestDisOrderService_GetDisFansList(t *testing.T) {
	type args struct {
		in distribution_vo.GetDisFansListReq
	}
	tests := []struct {
		name      string
		h         *DisFansService
		args      args
		wantOut   []distribution_vo.DisDistributorFansView
		wantTotal int
		wantErr   bool
	}{
		// TODO: Add test cases.
		{
			name: "分销客户",
			h:    &DisFansService{},
			args: args{
				in: distribution_vo.GetDisFansListReq{
					BasePageHttpRequest: viewmodel.BasePageHttpRequest{PageIndex: 1, PageSize: 2},
					IsValid:             1,
					Mobile:              "15118811943",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, gotTotal, err := tt.h.GetDisFansList(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisOrderService.GetDisFansList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DisOrderService.GetDisFansList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("DisOrderService.GetDisFansList() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

func TestDisFansService_EditDisFansProtectSetting(t *testing.T) {
	type args struct {
		in distribution_vo.DistributorFansProtect
	}
	tests := []struct {
		name    string
		h       *DisFansService
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "aaa",
			h:    &DisFansService{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.h.EditDisFansProtectSetting(tt.args.in); (err != nil) != tt.wantErr {
				t.Errorf("DisFansService.EditDisFansProtectSetting() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
