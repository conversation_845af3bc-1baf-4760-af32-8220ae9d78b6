package voucher

import (
	"context"
	po "eShop/domain/inventory-po/voucher"
	jwt "eShop/infra/jwtauth"
	"eShop/infra/pkg/util/cache"
	"eShop/services/common"
	vo "eShop/view-model/inventory-vo/voucher"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// VoucherService 单据服务
type VoucherService struct {
	common.BaseService
}

// NewVoucherService 创建服务实例的工厂方法
func NewVoucherService() *VoucherService {
	return &VoucherService{}
}

// Query 查询单据 适用于除盘点以外的单据
func (s VoucherService) Query(ctx context.Context, cmd vo.VoucherQueryParams) (page []vo.VoucherPageResponse, total int, err error) {
	s.Begin()
	session := s.Session
	defer func() {
		if r := recover(); r != nil {
			return
		}
		session.Close()
		s.Close()
	}()

	var params []interface{}

	var countSql = `select count(DISTINCT iv.id) as total`
	var selectSql = `
	select
	iv.id,
	civ.id AS child_id,
	civ.voucher_no AS child_voucher_no,
	iv.voucher_no,
	iv.chain_id,
	iv.store_id,
	s.name as store_name,
	iv.warehouse_id,
	w.name as warehouse_name,
	iv.supplier_id,
	ins.name as supplier_name,
	iv.name,
	iv.voucher_type,
	iv.status,
	count(distinct vd.sku_id) as   expect_sku_num,
	sum(vd.quantity) as  expect_sku_count,
	sum(vd.amount) as  expect_total_amount,
	count(distinct if(vd.actual_num>0,vd.sku_id,null)) as   actual_sku_num,
	sum(vd.actual_num) as  actual_sku_count,
	sum(vd.actual_amount) as  actual_total_amount,
	count(distinct  if (vd.actual_num=vd.quantity,null,vd.sku_id)) as wait_sku_num,
    sum(vd.quantity-vd.actual_num) as wait_sku_count,
    sum(vd.amount-vd.actual_amount) as wait_total_amount,
	iv.source_type,
	iv.profit_status,
	iv.change_num,
	iv.change_amount,
	iv.remark,
	iv.purchase_time,
	iv.delivery_time,
	iv.return_time,
	iv.is_deleted,
	iv.operator,
	iv.voucher_time,
	iv.created_time,
	iv.updated_time 
	`

	var fromSql = `
	from
	eshop.inventory_voucher  iv 
	LEFT JOIN eshop.inventory_voucher civ ON civ.pid=iv.id
	left join eshop.inventory_voucher_detail vd on iv.id=vd.voucher_id 
	left join eshop.inventory_suppliers ins on iv.supplier_id =ins.id
	left join datacenter.store s on s.finance_code=iv.store_id
	left join dc_dispatch.warehouse w  on iv.warehouse_id=w.id
	LEFT JOIN eshop.pro_sku ps ON vd.sku_id=ps.id
	LEFT JOIN eshop.pro_product pp ON vd.product_id=pp.id
	where iv.is_deleted =0 
	`

	var wherSql = ""

	if cmd.VoucherNo != "" {
		wherSql = wherSql + ` and iv.voucher_no = ?`
		params = append(params, cmd.VoucherNo)
	}
	if cmd.WarehouseId > 0 {
		wherSql = wherSql + ` and iv.warehouse_id = ?`
		params = append(params, cmd.WarehouseId)
	}
	if cmd.SupplierInfo != "" {
		wherSql = wherSql + ` and (iv.supplier_id = ? or ins.name like ?) `
		params = append(params, cmd.SupplierInfo, "%"+cmd.SupplierInfo+"%")
	}
	if cmd.Status > 0 {
		wherSql = wherSql + ` and iv.status = ?`
		params = append(params, cmd.Status)
	}
	if cmd.SourceType > 0 {
		wherSql = wherSql + ` and iv.source_type = ?`
		params = append(params, cmd.SourceType)
	}
	if cmd.VoucherType > 0 {
		var typeValue = 0
		//如果是采购单或者采购入库单，则单据以采购单为主体
		if cmd.VoucherType == 1 || cmd.VoucherType == 4 {
			typeValue = 1
		}
		//如果是采购退货单或者采购退货出库单，则单据以采购退货单为主体
		if cmd.VoucherType == 2 || cmd.VoucherType == 5 {
			typeValue = 2
		}
		wherSql = wherSql + ` and iv.voucher_type = ?`
		params = append(params, typeValue)
	}
	if cmd.SkuInfo != "" {
		wherSql = wherSql + ` AND (ps.bar_code=? OR ps.id=? OR pp.id=? OR pp.name LIKE ? OR pp.short_name LIKE ? )`
		params = append(params, cmd.SkuInfo, cmd.SkuInfo, cmd.SkuInfo, "%"+cmd.SkuInfo+"%", "%"+cmd.SkuInfo+"%")
	}

	if cmd.VoucherTimeStart != "" && cmd.VoucherTimeEnd != "" {
		if cmd.VoucherTimeType == 1 {
			wherSql = wherSql + ` and iv.voucher_time between ? and ?`
			params = append(params, cmd.VoucherTimeStart, cmd.VoucherTimeEnd)
		}
		if cmd.VoucherTimeType == 2 {
			wherSql = wherSql + ` and iv.purchase_time between ? and ?`
			params = append(params, cmd.VoucherTimeStart, cmd.VoucherTimeEnd)
		}
		if cmd.VoucherTimeType == 3 {
			wherSql = wherSql + ` and iv.delivery_time between ? and ?`
			params = append(params, cmd.VoucherTimeStart, cmd.VoucherTimeEnd)
		}
		if cmd.VoucherTimeType == 4 {
			wherSql = wherSql + ` and iv.return_time between ? and ?`
			params = append(params, cmd.VoucherTimeStart, cmd.VoucherTimeEnd)
		}
	}

	var num int64
	session.SQL(countSql+fromSql+wherSql, params...).Get(&num)

	offset := (cmd.PageIndex - 1) * cmd.PageSize
	wherSql = wherSql + ` group by iv.id  order by iv.id desc`
	wherSql = wherSql + ` limit ?,?`
	params = append(params, offset, cmd.BasePageHttpRequest.PageSize)

	err = session.SQL(selectSql+fromSql+wherSql, params...).Find(&page)
	if err != nil {
		panic(err)
	}

	//下面这一段主要是通过上面分页的数据来获得单据商品的图片等相关信息
	var voucherIds []string
	if len(page) > 0 {
		for _, v := range page {
			voucherIds = append(voucherIds, strconv.Itoa(v.Id))
		}
		var idStr = strings.Join(voucherIds, ",")
		var details []vo.VoucherPageProductDetail

		err = session.SQL(`
	SELECT
    iv.id as voucher_id,
    vd.sku_id,
    vd.product_id,
    pp.name AS product_name,
    pp.pic as product_img,
    vd.quantity AS  expect_quantity
FROM
    eshop.inventory_voucher  iv
        LEFT JOIN eshop.inventory_voucher_detail vd ON iv.id=vd.voucher_id
        LEFT JOIN eshop.pro_sku ps ON vd.sku_id=ps.id
        LEFT JOIN eshop.pro_product pp ON vd.product_id=pp.id
WHERE iv.id IN (` + idStr + `)`).Find(&details)
		if err != nil {
			panic(err)
		}

		var productMap = make(map[int][]vo.VoucherPageProductDetail)
		for _, v := range details {
			productMap[v.VoucherId] = append(productMap[v.VoucherId], v)
		}

		 for i := range page {
		// 	createdTime, _ := time.Parse(utils.DateTimeLayout, page[i].CreatedTime)
		// 	updatedTime, _ := time.Parse(utils.DateTimeLayout, page[i].UpdatedTime)
		// 	voucherTime, _ := time.Parse(utils.DateTimeLayout, page[i].VoucherTime)
			
		// 	// 将UTC时间转换为本地时区
		// 	loc, _ := time.LoadLocation("Asia/Shanghai")
		// 	createdTime = createdTime.In(loc)
		// 	updatedTime = updatedTime.In(loc)
		// 	voucherTime = voucherTime.In(loc)
			
		// 	page[i].CreatedTime = createdTime.Format(utils.DateTimeLayout)
		// 	page[i].UpdatedTime = updatedTime.Format(utils.DateTimeLayout)
		// 	page[i].VoucherTime = voucherTime.Format(utils.DateTimeLayout)
		 	page[i].Details = productMap[page[i].Id]
		 }
	}

	return page, int(num), nil
}

// Save 保存单据 适用于除盘点以外的单据
func (s VoucherService) Save(ctx context.Context, cmd vo.VoucherSaveCommand) (int, error) {
	s.Begin()
	session := s.Engine.NewSession()
	session.Begin()
	defer func() {
		if r := recover(); r != nil {
			session.Rollback()
		}
		session.Close()
		s.Close()
	}()

	voucherNo, err := s.GenerateVoucherNo(cmd.VoucherType)
	if err != nil {
		return -1, err
	}

	vourcher := po.Voucher{
		ChainId:      jwt.CtxGet[int64](ctx, "ChainId"),
		StoreId:      jwt.CtxGet[string](ctx, "TenantId"),
		WarehouseId:  cmd.WarehouseId,
		SupplierId:   cmd.SupplierId,
		VoucherNo:    voucherNo,
		Name:         cmd.Name,
		VoucherType:  vo.VoucherTypeMap[cmd.VoucherType],
		Status:       1,
		ChangeNum:    0,
		ChangeAmount: 0,
		ProfitStatus: 0,
		Remark:       "",
		IsDeleted:    0,
		Operator:     jwt.CtxGet[string](ctx, "UserName"),
		VoucherTime:  time.Now(),
		CreatedTime:  time.Now(),
		UpdatedTime:  time.Now(),
	}
	voucherId, err := vourcher.Insert(ctx, session)
	if err != nil {
		panic(err)
	}

	for _, detail := range cmd.Details {
		voucherDetail := po.VoucherDetail{
			VoucherId: voucherId,
			SkuId:     detail.SkuId,
			Quantity:  detail.Quantity,
			Price:     detail.Price,
			Amount:    detail.Amount,
		}
		err := voucherDetail.Insert(ctx, session)
		if err != nil {
			panic(err)
		}
	}

	session.Commit()

	return voucherId, nil
}

// Detail2 获取单据详情 适用于除盘点以外的单据,这个方法名要改。待重构
func (s VoucherService) Detail2(ctx context.Context, id int) (vo.VoucherWithDetailResponse, error) {
	s.Begin()
	session := s.Session
	defer func() {
		if r := recover(); r != nil {
			return
		}
		session.Close()
		s.Close()
	}()
	var response vo.VoucherWithDetailResponse

	count, err := session.SQL(`
			select
			iv.id,
			iv.chain_id,
			iv.store_id,
			s.name as store_name,
			iv.warehouse_id,
			w.name as warehouse_name,
			iv.supplier_id,
			ins.name as supplier_name,
			iv.name,
			iv.voucher_no,
			iv.voucher_type,
			iv.status,
			iv.source_type,
			iv.profit_status,
			iv.change_num,
			iv.change_amount,
			iv.remark,
			iv.purchase_time,
			iv.delivery_time,
			iv.return_time,
			iv.is_deleted,
			iv.operator,
			iv.voucher_time,
			iv.created_time,
			iv.updated_time,
			count(distinct vd.sku_id) as   purchase_sku_num,
			sum(vd.quantity) as  purchase_sku_count,
			sum(vd.amount) as  purchase_total_amount,
			count(distinct vd.sku_id) as   actual_sku_num,
			sum(vd.actual_num) as  actual_sku_count,
			sum(vd.actual_amount) as  actual_total_amount
		from
			eshop.inventory_voucher  iv 
			left join eshop.inventory_voucher_detail vd on iv.id=vd.voucher_id 
			left join eshop.inventory_suppliers ins on iv.supplier_id =ins.id
			left join datacenter.store s on s.id=iv.store_id
			left join dc_dispatch.warehouse w  on iv.warehouse_id=w.id
			where iv.is_deleted =0 and iv.id=?
			group by iv.id`, id).FindAndCount(&response)
	if err != nil {
		panic(err)
	}
	if count == 0 {
		return vo.VoucherWithDetailResponse{}, errors.New("单据不存在")
	}
	return response, nil
}

// Create 创建订单（✅）
func (s VoucherService) Create(ctx context.Context, cmd vo.VoucherCreateCommand) (int, error) {
	s.Begin()
	session := s.Session
	defer s.Close()

	voucherNo, err := s.GenerateVoucherNo(cmd.VoucherType)

	voucher := &po.Voucher{
		ChainId:      jwt.CtxGet[int64](ctx, "ChainId"),
		StoreId:      jwt.CtxGet[string](ctx, "TenantId"),
		WarehouseId:  cmd.WarehouseId,
		VoucherNo:    voucherNo,
		Name:         cmd.Name,
		Status:       1,
		ChangeNum:    0,
		ChangeAmount: 0,
		ProfitStatus: 0,
		Remark:       "",
		IsDeleted:    0,
		Operator:     jwt.CtxGet[string](ctx, "UserName"),
		VoucherTime:  time.Now(),
		CreatedTime:  time.Now(),
		UpdatedTime:  time.Now(),
	}

	id, err := voucher.Insert(ctx, session)
	if err != nil {
		return 0, err
	}
	return id, nil
}

// Operate 保存库存状态单据（✅）
func (s VoucherService) Operate(ctx context.Context, cmd vo.VoucherUpdateCommand) error {
	s.Begin()
	session := s.Session
	defer s.Close()

	var voucher po.Voucher
	voucher, err := voucher.GetById(ctx, session, cmd.Id)
	if err != nil {
		return err
	}
	voucher.Remark = cmd.Remark
	voucher.Status = cmd.VoucherOperateType
	voucher.UpdatedTime = time.Now()
	err = voucher.Update(ctx, session)
	if err != nil {
		return err
	}

	return nil
}

// Cancel 取消单据（✅）
func (s VoucherService) Cancel(ctx context.Context, id int) error {
	s.Begin()
	session := s.Session
	defer s.Close()
	var voucher po.Voucher
	voucher, err := voucher.GetById(ctx, session, id)
	if err != nil {
		return err
	}
	voucher.Cancel(ctx, session)
	err = voucher.Update(ctx, session)
	if err != nil {
		return err
	}
	return nil
}

// ManualComplete 手动完成单据（✅）
func (s VoucherService) ManualComplete(ctx context.Context, id int) error {
	s.Begin()
	session := s.Session
	defer s.Close()
	var voucher po.Voucher
	voucher, err := voucher.GetById(ctx, session, id)
	if err != nil {
		return err
	}
	voucher.ManualComplete(ctx, session)
	err = voucher.Update(ctx, session)
	if err != nil {
		return err
	}
	return nil
}

// BatchCreate 用于选择商品加入单据 ✅
func (s VoucherService) BatchCreate(ctx context.Context, cmd vo.VoucherDetailBatchCreateCommand) error {
	s.Begin()
	session := s.Session
	defer s.Close()

	var voucher po.Voucher

	voucher, err := voucher.GetById(ctx, session, cmd.Id)
	if err != nil {
		return err
	}

	for _, detail := range cmd.Details {
		var voucherDetail po.VoucherDetail
		voucherDetail.VoucherId = voucher.Id
		voucherDetail.SkuId = detail.SkuId
		voucherDetail.Quantity = detail.Quantity
		voucherDetail.Remark = detail.Remark
		voucherDetail.ChainId = voucher.ChainId
		voucherDetail.StoreId = voucher.StoreId
		voucherDetail.WarehouseId = voucher.WarehouseId
		voucherDetail.CreatedTime = time.Now()
		voucherDetail.UpdatedTime = time.Now()
		//下面几个值待完善
		voucherDetail.IsDeleted = 0
		voucherDetail.ProfitStatus = 0
		voucherDetail.TotalNumBefore = 0
		voucherDetail.TotalNumAfter = 0
		voucherDetail.AvgCostPrice = 0
		voucherDetail.Reason = ""
		voucherDetail.Amount = 0
		voucherDetail.ActualNum = 0
		voucherDetail.ActualAmount = 0
		err := voucherDetail.Insert(ctx, session)
		if err != nil {
			return err
		}
	}

	return nil
}

// GenerateVoucherNo	根据单据类型生成单据号
func (s VoucherService) GenerateVoucherNo(voucherType vo.VoucherType) (string, error) {
	redisConn := cache.GetRedisConn()
	date := time.Now().Format("20060102")
	key := fmt.Sprintf("eShop:voucherNo:%s", date)
	val, err := redisConn.Exists(key).Result()
	if err == nil && val <= 0 {
		redisConn.Set(key, 1, 24*time.Hour)
	}
	if err == nil && val > 0 {
		val, err = redisConn.Incr(key).Result()
	}

	return fmt.Sprintf("%s%s%08d", voucherType, date, val), nil
}
