package voucher

import (
	"context"
	inventoryPO "eShop/domain/inventory-po/inventory"
	po "eShop/domain/inventory-po/voucher"
	"eShop/infra/errors"
	jwt "eShop/infra/jwtauth"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/inventory-service/iobound"
	baseVO "eShop/view-model/inventory-vo"
	ioboundVO "eShop/view-model/inventory-vo/iobound"
	vo "eShop/view-model/inventory-vo/voucher"
	"github.com/samber/lo"
	"strconv"
	"strings"
	"time"
	"xorm.io/xorm"
)

// StocktakingService 单据服务
type StocktakingService struct {
	common.BaseService
}

// NewStocktakingService 创建服务实例的工厂方法
func NewStocktakingService() *StocktakingService {
	return &StocktakingService{}
}

func (s StocktakingService) Page(ctx context.Context, cmd vo.VoucherPageRequest) ([]vo.VoucherResponse, int64, error) {
	s.Begin()
	defer s.Close()
	session := s.Session

	vouchers, total, err := new(po.Voucher).Page(ctx, session, cmd)
	if err != nil {
		return nil, 0, err
	}

	return vouchers, total, nil
}

func (s StocktakingService) Detail(ctx context.Context, cmd baseVO.IdRequest) (vo.VoucherResponse, error) {
	s.Begin()
	defer s.Close()
	session := s.Session

	record, err := new(po.Voucher).Detail(ctx, session, cmd.Id)
	if err != nil {
		return record, err
	}

	return record, nil
}

func (s StocktakingService) Summary(ctx context.Context, cmd vo.VoucherPageRequest) (int, error) {
	s.Begin()
	defer s.Close()
	session := s.Session

	changeAmount, err := new(po.Voucher).Summary(ctx, session, cmd)
	if err != nil {
		return 0, err
	}

	return changeAmount, nil
}

func (s StocktakingService) Create(ctx context.Context, cmd vo.VoucherCreateCommand) (int, error) {
	s.Begin()
	defer s.Close()
	session := s.Session

	voucher := &po.Voucher{
		ChainId:      cmd.ChainId,
		StoreId:      cmd.StoreId,
		WarehouseId:  cmd.WarehouseId,
		Name:         cmd.Name,
		VoucherNo:    utils.GenInventorySN("PD"),
		VoucherType:  3,
		Status:       2, // 待处理
		ProfitStatus: 3, // 平账
		ChangeNum:    0,
		ChangeAmount: 0,
		Remark:       "",
		Operator:     cmd.Operator,
		VoucherTime:  time.Now(),
		CreatedTime:  time.Now(),
		UpdatedTime:  time.Now(),
	}

	id, err := voucher.Insert(ctx, session)
	if err != nil {
		return 0, err
	}

	return id, nil
}

func (s StocktakingService) Operate(ctx context.Context, cmd vo.VoucherUpdateCommand) (vo.StocktakingOperateResponse, error) {
	s.Begin()
	defer s.Close()
	session := s.Session

	var response vo.StocktakingOperateResponse

	// 检查重复skuId
	voucher, err := checkStocktaking(ctx, session, cmd.Id)
	if err != nil {
		return response, err
	}
	voucher.Remark = cmd.Remark

	// 判断操作类型：1-取消, 2-保存草稿, 3-完成盘点, 4-清空
	switch cmd.VoucherOperateType {
	case 1:
		err = cancel(ctx, session, voucher)
	case 2:
		err = saveDraft(ctx, session, voucher)
	case 3:
		response, err = finishTaking(ctx, session, voucher, cmd)
	case 4:
		err = clean(ctx, session, voucher)
	default:
		return response, errors.NewBadRequest("不支持的操作类型！")
	}

	if err != nil {
		return response, err
	}

	return response, nil
}

func finishTaking(ctx context.Context, session *xorm.Session, voucher po.Voucher, cmd vo.VoucherUpdateCommand) (vo.StocktakingOperateResponse, error) {
	var response vo.StocktakingOperateResponse

	voucher.Status = 4
	voucher.UpdatedTime = time.Now()

	count, err := new(po.VoucherDetail).CountInit(ctx, session, voucher.Id)
	if err != nil {
		return response, err
	}
	if count > 0 {
		return response, errors.NewBadRequest("有尚未填写盘点实存，无法完成盘点")
	}
	stocktakingMode := "NOT_BLIND"

	// 传入参数：isCheckChange=1时，则检测库存是否已经发生变动，如果库存已经发生变动，则返回盘点失败，并告知哪些库存存在变动
	operateResult := checkChangeNum(ctx, session, cmd.IsCheckChange, voucher.Id, stocktakingMode)
	if len(operateResult.OperateResult) > 0 {
		return operateResult, nil
	}

	// 根据voucher.Id查询库存详情列表
	details, err := new(po.VoucherDetail).ListByVoucherId(ctx, session, voucher.Id)
	if len(details) == 0 {
		return response, errors.NewBadRequest("请先添加商品再点击完成盘点")
	}

	// 获取到库存详情列表中的skuId集合
	skuIds := lo.Uniq(lo.Filter(lo.Map(details, func(detail po.VoucherDetail, _ int) int {
		return detail.SkuId
	}), func(skuId int, _ int) bool {
		return skuId > 0
	}))
	if len(skuIds) == 0 {
		return response, errors.NewBadRequest("盘点商品为空")
	}
	if len(skuIds) != len(details) {
		return response, errors.NewBadRequest("请勿对同一商品重复盘点")
	}

	// 根据SkuIds查询库存信息
	inventories, err := new(inventoryPO.Inventory).ListBySkuIds(ctx, session, voucher.WarehouseId, skuIds)
	if len(inventories) == 0 {
		return response, errors.NewBadRequest("商品库存为空")
	}

	// 将inventories根据skuId转为Map
	inventoryMap := make(map[int]inventoryPO.Inventory)
	for _, inventory := range inventories {
		inventoryMap[inventory.SkuId] = inventory
	}

	// 检查商品是否在商品库存表里面
	var notExistProduct []string
	for _, detail := range details {
		if _, ok := inventoryMap[detail.SkuId]; !ok {
			notExistProduct = append(notExistProduct, strconv.Itoa(detail.ProductId))
		}
	}
	if len(notExistProduct) > 0 {
		return response, errors.NewBadRequest("商品Id：" + strings.Join(notExistProduct, ",") + "在商品库存中无数据，请先初始化库存或移除该商品再盘点")
	}

	// 不检查变动且直接更新为最新库存
	if cmd.IsCheckChange == 0 && cmd.IsUseLatestNum == 1 {
		err = new(po.VoucherDetail).UpdateSnapShot(ctx, session, voucher.Id)
		details, err = new(po.VoucherDetail).ListByVoucherId(ctx, session, voucher.Id)
	}

	// 调盘点
	saveVO, err := packStocktakingSaveVO(voucher, details)
	if err != nil {
		return response, err
	}
	err = doTaking(ctx, session, &voucher, saveVO, inventoryMap)
	if err != nil {
		return response, err
	}

	// 更新
	err = voucher.Update(ctx, session)
	if err != nil {
		return response, err
	}
	response.OperateResult = "OK"
	response.StocktakingModel = stocktakingMode
	return response, nil
}

func doTaking(ctx context.Context, session *xorm.Session, voucher *po.Voucher, saveVO vo.StocktakingSaveVO, inventoryMap map[int]inventoryPO.Inventory) error {
	err := checkTaking(inventoryMap, saveVO.Details)
	if err != nil {
		return err
	}

	profit := ioboundVO.IoBoundCreateCommand{
		ChainId:     jwt.CtxGet[int64](ctx, "ChainId"),
		StoreId:     jwt.CtxGet[string](ctx, "TenantId"),
		WarehouseId: voucher.WarehouseId,
		ItemType:    7,
		ItemRefNo:   voucher.VoucherNo,
		ItemRefType: 4,
		Operator:    jwt.CtxGet[string](ctx, "UserName"),
	}

	loss := ioboundVO.IoBoundCreateCommand{
		ChainId:     jwt.CtxGet[int64](ctx, "ChainId"),
		StoreId:     jwt.CtxGet[string](ctx, "TenantId"),
		WarehouseId: voucher.WarehouseId,
		ItemType:    8,
		ItemRefNo:   voucher.VoucherNo,
		ItemRefType: 4,
		Operator:    jwt.CtxGet[string](ctx, "UserName"),
	}

	takingDetail, err := calcTakingProfit(ctx, voucher, inventoryMap, &profit, &loss, saveVO.Details)
	if err != nil {
		return err
	}

	if len(profit.Details) > 0 {
		_, err = iobound.NewIoBoundService().Create(ctx, session, profit)
	}
	if len(loss.Details) > 0 {
		_, err = iobound.NewIoBoundService().Create(ctx, session, loss)
	}

	err = new(po.VoucherDetail).BatchUpdateById(ctx, session, takingDetail)
	if err != nil {
		return err
	}
	return nil
}

func calcTakingProfit(ctx context.Context, voucher *po.Voucher, inventoryMap map[int]inventoryPO.Inventory, profit *ioboundVO.IoBoundCreateCommand,
	loss *ioboundVO.IoBoundCreateCommand, details []vo.StocktakingDetailSaveVO) ([]po.VoucherDetail, error) {

	result := make([]po.VoucherDetail, 0)
	changeNum := 0
	changeAmount := 0

	for _, detail := range details {
		inventory, exists := inventoryMap[detail.SkuId]
		if !exists {
			return nil, errors.NewBadRequest("商品ID: " + strconv.Itoa(detail.ProductId) + " 不存在")
		}

		// 计算变更数量
		currentChangeNum := detail.ChangeNum
		// 计算金额
		productChangeAmount := inventory.AvgCostPrice * currentChangeNum
		// 可用库存变更
		//availableNumAfter := inventory.AvailableNum + currentChangeNum
		// 金额变更
		//totalAmountAfter := inventory.TotalAmount + productChangeAmount
		// 计算盈利状态
		profitStatus := calcProfitType(currentChangeNum)
		// 变更金额相加
		changeAmount += productChangeAmount
		changeNum += currentChangeNum

		takingDetail := genTakingDetail(ctx, voucher, detail, inventory, currentChangeNum,
			productChangeAmount /*, availableNumAfter, totalAmountAfter*/, profitStatus)
		if takingDetail.ProfitStatus == 1 { // Assuming PROFIT is represented by 1
			profit.Details = append(profit.Details, ioboundVO.IoBoundDetailCreateCommand{
				SkuId:           inventory.SkuId,
				ProductName:     inventory.ProductName,
				ItemDetailRefId: strconv.Itoa(takingDetail.Id),
				IoCount:         takingDetail.ChangeNum,
				IoPrice:         inventory.AvgCostPrice,
			})
		} else if takingDetail.ProfitStatus == 2 { // Assuming LOSS is represented by 2
			loss.Details = append(loss.Details, ioboundVO.IoBoundDetailCreateCommand{
				SkuId:           inventory.SkuId,
				ProductName:     inventory.ProductName,
				ItemDetailRefId: strconv.Itoa(takingDetail.Id),
				IoPrice:         inventory.AvgCostPrice,
				IoCount:         takingDetail.ChangeNum,
			})
			if takingDetail.ChangeNum < 0 {
				loss.Details[len(loss.Details)-1].IoCount = -takingDetail.ChangeNum
			}
		}
		result = append(result, takingDetail)
	}

	voucher.ChangeNum = changeNum
	voucher.ChangeAmount = changeAmount
	voucher.ProfitStatus = calcProfitType(changeNum)

	return result, nil
}

func genTakingDetail(ctx context.Context, voucher *po.Voucher, detail vo.StocktakingDetailSaveVO, inventory inventoryPO.Inventory, currentChangeNum int,
	productChangeAmount int /*, availableNumAfter int, totalAmountAfter int*/, profitStatus int) po.VoucherDetail {

	return po.VoucherDetail{
		Id:               detail.Id,
		ChainId:          jwt.CtxGet[int64](ctx, "ChainId"),
		StoreId:          jwt.CtxGet[string](ctx, "TenantId"),
		WarehouseId:      voucher.WarehouseId,
		VoucherId:        voucher.Id,
		ProductId:        inventory.ProductId,
		SkuId:            inventory.SkuId,
		TotalNum:         inventory.TotalNum,
		TotalNumSnapshot: detail.TotalNumSnapshot,
		AvgCostPrice:     inventory.AvgCostPrice,
		ActualNum:        detail.ActualNum,
		ChangeNum:        currentChangeNum,
		ChangeAmount:     productChangeAmount,
		ProfitStatus:     profitStatus,
		TotalNumAfter:    inventory.TotalNum + currentChangeNum,
		Remark:           detail.Remark,
		IsDeleted:        0,
		UpdatedTime:      time.Now(),
	}
}

func calcProfitType(changeNum int) int {
	var profitStatus int
	if changeNum == 0 {
		profitStatus = 3
	} else if changeNum > 0 {
		profitStatus = 1
	} else {
		profitStatus = 2
	}

	return profitStatus
}

func checkTaking(inventoryMap map[int]inventoryPO.Inventory, details []vo.StocktakingDetailSaveVO) error {
	var notFoundProducts []string
	var lessProducts []string
	var lessZeroNumProducts []string

	for _, detail := range details {
		inventory, exists := inventoryMap[detail.SkuId]
		if !exists {
			notFoundProducts = append(notFoundProducts, strconv.Itoa(detail.ProductId))
			continue
		}
		// 冻结库存比盘点后库存还要大
		realTakingNum := inventory.TotalNum + detail.ChangeNum
		if realTakingNum < 0 {
			lessZeroNumProducts = append(lessZeroNumProducts, strconv.Itoa(detail.ProductId))
		}
		if inventory.FreezeNum > realTakingNum {
			lessProducts = append(lessProducts, "商品ID: "+strconv.Itoa(detail.ProductId)+" 盘点后数量【"+strconv.Itoa(realTakingNum)+"】少于冻结库存数量【"+strconv.Itoa(inventory.FreezeNum)+"】")
		}
	}

	if len(notFoundProducts) > 0 {
		return errors.NewBadRequest("商品ID: " + strings.Join(notFoundProducts, ",") + " 不存在")
	}
	if len(lessZeroNumProducts) > 0 {
		return errors.NewBadRequest("商品ID: " + strings.Join(lessZeroNumProducts, ",") + " 盘点后数量小于0")
	}
	if len(lessProducts) > 0 {
		return errors.NewBadRequest(strings.Join(lessProducts, "\n"))
	}

	return nil
}

func packStocktakingSaveVO(voucher po.Voucher, details []po.VoucherDetail) (vo.StocktakingSaveVO, error) {
	var saveDetails []vo.StocktakingDetailSaveVO
	if len(details) > 0 {
		for _, detail := range details {
			saveDetail := vo.StocktakingDetailSaveVO{
				Id:               detail.Id,
				ProductId:        detail.ProductId,
				SkuId:            detail.SkuId,
				TotalNumSnapshot: detail.TotalNumSnapshot,
				ActualNum:        detail.ActualNum,
				ChangeNum:        detail.ActualNum - detail.TotalNumSnapshot,
				Remark:           detail.Remark,
			}
			saveDetails = append(saveDetails, saveDetail)
		}
	}

	return vo.StocktakingSaveVO{
		VoucherId: voucher.Id,
		Details:   saveDetails,
	}, nil
}

func checkChangeNum(ctx context.Context, session *xorm.Session, isCheckChange int, voucherId int, stocktakingMode string) vo.StocktakingOperateResponse {
	var response vo.StocktakingOperateResponse
	if isCheckChange == 0 {
		// 检查库存是否已经发生变动，如果库存已经发生变动，则返回盘点失败，并告知哪些库存存在变动
		return response
	}

	// 根据voucherId查询到总库存变更的记录列表
	resultVOs, err := new(po.VoucherDetail).ListChangeNumDetail(ctx, session, voucherId)
	if err != nil || len(resultVOs) == 0 {
		return response
	}

	response.OperateResult = "NUM_CHANGE"
	response.StocktakingModel = stocktakingMode
	response.ChangeNumDetails = resultVOs
	return response
}

func cancel(ctx context.Context, session *xorm.Session, voucher po.Voucher) error {
	// 更新
	updEntity := &po.Voucher{
		Id:          voucher.Id,
		Status:      6, // 已取消
		Remark:      voucher.Remark,
		UpdatedTime: time.Now(),
	}

	err := updEntity.Update(ctx, session)
	if err != nil {
		return err
	}
	return nil
}

func saveDraft(ctx context.Context, session *xorm.Session, voucher po.Voucher) error {
	// 更新
	updEntity := &po.Voucher{
		Id:          voucher.Id,
		Status:      1, // 草稿
		Remark:      voucher.Remark,
		UpdatedTime: time.Now(),
	}

	err := updEntity.Update(ctx, session)
	if err != nil {
		return err
	}
	return nil
}

func clean(ctx context.Context, session *xorm.Session, voucher po.Voucher) error {
	// 清除明细
	err := new(po.VoucherDetail).DeleteByVoucherId(ctx, session, voucher.Id)
	if err != nil {
		return err
	}
	return nil
}

func checkStocktaking(ctx context.Context, session *xorm.Session, id int) (po.Voucher, error) {
	voucher, err := new(po.Voucher).GetById(ctx, session, id)
	if err != nil || voucher.Id == 0 {
		return voucher, errors.NewBadRequest("该盘点Id找不到对应的盘点单")
	}
	if voucher.Status != 1 && voucher.Status != 2 {
		return voucher, errors.NewBadRequest("仅未完成盘点支持操作")
	}

	return voucher, nil
}

func (s StocktakingService) BatchCreate(ctx context.Context, cmd vo.VoucherDetailBatchCreateCommand) error {
	s.Begin()
	defer s.Close()
	session := s.Session

	// 检查重复skuId
	inventoryMap, err := checkRepeatProduct(ctx, session, cmd)
	if err != nil {
		return err
	}

	// 拼装库存详情列表
	stocktakingDetails, err := packStocktakingDetails(inventoryMap, cmd)
	if err != nil {
		return err
	}

	err = new(po.VoucherDetail).BatchCreate(ctx, session, stocktakingDetails)
	if err != nil {
		return err
	}

	return nil
}

func checkRepeatProduct(ctx context.Context, session *xorm.Session, cmd vo.VoucherDetailBatchCreateCommand) (map[int]inventoryPO.Inventory, error) {
	// 从cmd.Detail中获取到去重的skuId切片
	skuIds := lo.Uniq(lo.Filter(lo.Map(cmd.Details, func(detail vo.VoucherDetail, _ int) int {
		return detail.SkuId
	}), func(skuId int, _ int) bool {
		return skuId > 0
	}))

	if len(skuIds) == 0 {
		return nil, errors.NewBadRequest("盘点商品不能为空")
	}
	if len(skuIds) != len(cmd.Details) {
		return nil, errors.NewBadRequest("同一商品重复录入")
	}

	voucherId := cmd.Id

	// 根据id查询到inventory_voucher记录
	voucher, err := new(po.Voucher).GetById(ctx, session, voucherId)

	// 根据voucherId查询是否已经存在的skuId记录
	count, err := new(po.VoucherDetail).CountBySkuId(ctx, session, voucherId, skuIds)
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.NewBadRequest("同一商品重复录入")
	}

	inventories, err := new(inventoryPO.Inventory).ListBySkuIds(ctx, session, voucher.WarehouseId, skuIds)
	if err != nil {
		return nil, err
	}

	return lo.KeyBy(inventories, func(inventory inventoryPO.Inventory) int {
		return inventory.SkuId
	}), nil
}

func packStocktakingDetails(inventoryMap map[int]inventoryPO.Inventory, cmd vo.VoucherDetailBatchCreateCommand) ([]po.VoucherDetail, error) {
	var stocktakingDetails []po.VoucherDetail

	var errList []string
	for _, detail := range cmd.Details {
		inventory, ok := inventoryMap[detail.SkuId]
		if !ok {
			errList = append(errList, detail.ProductName)
		}

		stocktakingDetail := po.VoucherDetail{
			ChainId:          inventory.ChainId,
			StoreId:          inventory.StoreId,
			WarehouseId:      inventory.WarehouseId,
			VoucherId:        cmd.Id,
			ProductId:        inventory.ProductId,
			SkuId:            detail.SkuId,
			Price:            0,
			Quantity:         0,
			TotalNum:         0,
			TotalNumSnapshot: inventory.TotalNum,
			Amount:           0,
			ActualNum:        -1,
			ActualAmount:     0,
			TotalNumBefore:   0,
			TotalNumAfter:    0,
			ProfitStatus:     3,
			ChangeNum:        0,
			ChangeAmount:     0,
			AvgCostPrice:     0,
			Reason:           "",
			Remark:           detail.Remark,
			IsDeleted:        0,
			CreatedTime:      time.Now(),
			UpdatedTime:      time.Now(),
		}
		stocktakingDetails = append(stocktakingDetails, stocktakingDetail)
	}

	if len(errList) > 0 {
		return nil, errors.NewBadRequest("商品:" + strings.Join(errList, ",") + "不存在")
	}

	return stocktakingDetails, nil
}

func (s StocktakingService) DetailUpdate(ctx context.Context, cmd vo.VoucherDetailUpdateCommand) error {
	s.Begin()
	defer s.Close()
	session := s.Session

	if cmd.Id == 0 {
		return errors.NewBadRequest("盘点明细Id不能为空")
	}
	detail, err := new(po.VoucherDetail).GetById(ctx, session, cmd.Id)
	if err != nil || detail.Id == 0 {
		return errors.NewBadRequest("该盘点明细Id找不到对应的盘点明细记录")
	}

	detail.ActualNum = cmd.ActualNum
	if len(cmd.Remark) > 0 {
		detail.Remark = cmd.Remark
	}
	detail.UpdatedTime = time.Now()
	err = detail.Update(ctx, session)
	if err != nil {
		return err
	}

	return nil
}

func (s StocktakingService) PageForStatistics(ctx context.Context, cmd vo.VoucherDetailPageRequest) (vo.DetailStatisticsResponse, error) {
	s.Begin()
	defer s.Close()
	session := s.Session

	response, err := new(po.VoucherDetail).PageForStatistics(ctx, session, cmd.VoucherId)
	if err != nil {
		return vo.DetailStatisticsResponse{}, err
	}

	return response, nil
}

func (s StocktakingService) DetailPage(ctx context.Context, cmd vo.VoucherDetailPageRequest) ([]vo.VoucherDetailResponse, int64, error) {
	s.Begin()
	defer s.Close()
	session := s.Session

	records, total, err := new(po.VoucherDetail).Page(ctx, session, cmd)
	if err != nil {
		return nil, 0, err
	}

	if len(records) > 0 {
		storeId := jwt.CtxGet[string](ctx, "TenantId")
		for i, record := range records {
			records[i].ProductSku, err = new(inventoryPO.Inventory).GetProductBySkuId(ctx, session, storeId, record.SkuId)
		}
	}

	return records, total, nil
}

func (s StocktakingService) DetailDetail(ctx context.Context, cmd baseVO.IdRequest) (vo.VoucherDetailResponse, error) {
	s.Begin()
	defer s.Close()
	session := s.Session

	record, err := new(po.VoucherDetail).Detail(ctx, session, cmd)
	if err != nil {
		return record, err
	}

	if record.SkuId > 0 {
		storeId := jwt.CtxGet[string](ctx, "TenantId")
		record.ProductSku, err = new(inventoryPO.Inventory).GetProductBySkuId(ctx, session, storeId, record.SkuId)
	}

	return record, nil
}

func (s StocktakingService) DetailDelete(ctx context.Context, id int) error {
	s.Begin()
	defer s.Close()
	session := s.Session

	err := new(po.VoucherDetail).DetailDelete(ctx, session, id)
	if err != nil {
		return err
	}

	return nil
}
