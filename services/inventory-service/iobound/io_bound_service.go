package iobound

import (
	"bytes"
	"context"
	inventoryPO "eShop/domain/inventory-po/inventory"
	po "eShop/domain/inventory-po/iobound"
	warehousePO "eShop/domain/inventory-po/warehouse"
	"eShop/infra/converter"
	"eShop/infra/errors"
	jwt "eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	inventorySvc "eShop/services/inventory-service/inventory"
	baseVO "eShop/view-model/inventory-vo"
	inventoryVO "eShop/view-model/inventory-vo/inventory"
	vo "eShop/view-model/inventory-vo/iobound"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/golang/glog"

	"github.com/samber/lo"
	"xorm.io/xorm"
)

// IoBoundService 库存服务
type IoBoundService struct {
	common.BaseService
}

// NewIoBoundService 创建库存服务
func NewIoBoundService() *IoBoundService {
	return &IoBoundService{}
}

// InitInventory 初始化库存（商品下发时，初始化库存相关记录，其他场景请勿调用）
func (s IoBoundService) InitInventory(ctx context.Context, session *xorm.Session, cmd inventoryVO.InventoryInitCommand) ([]inventoryPO.Inventory, error) {
	// 打印请求参数cmd，json格式化
	log.Infof("IoBoundService InitInventory cmd: %v", utils.JsonEncode(cmd))

	// 传入的session为空，则初始化一个session
	if session == nil {
		s.Begin()
		defer s.Close()
		session = s.Engine.NewSession()
		session.Begin()
		defer session.Close()
		defer session.Commit()
	}

	inventories, err := new(inventoryPO.Inventory).BatchCreate(ctx, session, cmd)
	if err != nil || len(inventories) == 0 {
		session.Rollback()
		return nil, err
	}

	// inventories转Map
	inventoriesMap := make(map[int]inventoryPO.Inventory)
	for _, inventory := range inventories {
		inventoriesMap[inventory.SkuId] = inventory
	}

	var details []vo.IoBoundDetailCreateCommand
	for _, detail := range cmd.Details {
		// 如果库存inventoriesMap没有detail.skuId，则跳过
		if _, ok := inventoriesMap[detail.SkuId]; !ok {
			continue
		}

		details = append(details, vo.IoBoundDetailCreateCommand{
			ItemDetailRefId: "0",
			SkuId:           detail.SkuId,
			ProductName:     inventoriesMap[detail.SkuId].ProductName,
			IoPrice:         detail.AvgCostPrice,
			IoCount:         0,
		})
	}

	if len(details) > 0 {
		saveVO := vo.IoBoundCreateCommand{
			ChainId:     cmd.ChainId,
			StoreId:     cmd.StoreId,
			WarehouseId: cmd.WarehouseId,
			ItemType:    11,
			ItemRefId:   0,
			ItemRefNo:   "",
			ItemRefType: 1,
			Remark:      "初始库存",
			Details:     details,
			Operator:    cmd.Operator,
		}

		_, err = s.Create(ctx, session, saveVO)
		if err != nil {
			session.Rollback()
			return nil, err
		}
	}

	return inventories, nil
}

// Create 创建库存
func (s IoBoundService) Create(ctx context.Context, session *xorm.Session, cmd vo.IoBoundCreateCommand) (po.IoBound, error) {
	// 打印请求参数cmd，json格式化
	log.Infof("IoBoundService Create cmd: %v", utils.JsonEncode(cmd))

	if cmd.ChainId == 0 || len(cmd.StoreId) == 0 {
		return po.IoBound{}, errors.NewBadRequest("检测到参数异常，连锁id和店铺id不能为空")
	}

	// 传入的session为空，则初始化一个session
	if session == nil {
		s.Begin()
		defer s.Close()
		session = s.Engine.NewSession()
		session.Begin()
		defer session.Close()
		defer session.Commit()
	}

	// 判断ctx是否是emptyCtx，如果是则设置上下文
	ctx = jwt.CtxSet(ctx, &jwt.XCShopPayload{
		ChainId:  strconv.FormatInt(cmd.ChainId, 10),
		TenantId: cmd.StoreId,
		UserName: cmd.Operator,
	})

	// 从cmd中获取到details的skuIds，过滤掉0并去重
	skuIds := lo.Uniq(lo.Filter(lo.Map(cmd.Details, func(detail vo.IoBoundDetailCreateCommand, _ int) int {
		return detail.SkuId
	}), func(skuId int, _ int) bool {
		return skuId != 0
	}))

	// skuIds为空则直接返回错误信息
	if len(skuIds) == 0 {
		return po.IoBound{}, errors.NewBadRequest("出入库商品为空")
	}

	// 根据skuIds查询库存
	inventories, err := new(inventoryPO.Inventory).ListBySkuIds(ctx, session, cmd.WarehouseId, skuIds)
	if err != nil {
		return po.IoBound{}, err
	}

	// 将查询到的库存转换为skuId:库存的map
	inventoryMap := make(map[int]inventoryPO.Inventory)
	for _, inventory := range inventories {
		inventoryMap[inventory.SkuId] = inventory
	}

	// 检查商品是否存在
	if err = checkIoBound(inventoryMap, cmd.Details); err != nil {
		return po.IoBound{}, err
	}
	// 检查商品出入库价格
	if err = checkIoPrice(cmd.ItemType, cmd.Details); err != nil {
		return po.IoBound{}, err
	}

	// 拼装出入库记录
	ioBound := packIoBound(ctx, cmd)

	// 组装库存变更对象
	inventoryChange := packInventoryChange(ctx, ioBound)

	// 拼装出入库详情列表
	ioBoundDetails, err := packIoBoundDetails(ctx, inventoryMap, ioBound, inventoryChange, cmd.Details)
	if err != nil {
		return po.IoBound{}, err
	}

	// 库存变更
	inventoryService := inventorySvc.NewInventoryService()
	_, err = inventoryService.InventoryManage(ctx, session, inventoryChange)
	if err != nil {
		session.Rollback()
		return po.IoBound{}, err
	}

	// 插入出入库记录
	ioBoundId, err := ioBound.Create(ctx, session)
	if err != nil {
		session.Rollback()
		return po.IoBound{}, err
	}

	// ioBoundDetails中的BoundId赋值
	for i := range ioBoundDetails {
		ioBoundDetails[i].BoundId = ioBoundId
	}

	// 批量创建出入库详情
	ioBoundDetail := &po.IoBoundDetail{}
	err = ioBoundDetail.BatchCreate(ctx, session, ioBoundDetails)
	if err != nil {
		session.Rollback()
		return po.IoBound{}, err
	}
	//如果是出库，计算库存是否少于配置的值，是的话就发送消息通知
	if 2-(inventoryChange.ItemType&1) == 2 && inventoryChange.ItemType != 4 {
		// 只对本次涉及的仓库和sku做预警
		skuIds := lo.Map(cmd.Details, func(detail vo.IoBoundDetailCreateCommand, _ int) int {
			return detail.SkuId
		})

		// 直接查询数据库获取库存信息
		inventories, err := new(inventoryPO.Inventory).ListBySkuIds(ctx, session, cmd.WarehouseId, skuIds)
		// 查询预警阈值
		var warningQty int
		_, err = session.SQL("SELECT parameter FROM eshop_saas.sys_switch_setting WHERE CODE='INVENTORY_WARNING_CONFIG' AND option_value='INVENTORY_QTY' and tenant_id=?", cmd.StoreId).Get(&warningQty)
		if err != nil || warningQty == 0 {
			warningQty = 5 // 默认5
		}
		if err == nil {
			// 获取仓库信息
			warehouse, err := new(warehousePO.Warehouse).GetByID(ctx, session, cmd.WarehouseId)
			if err == nil {
				CategoryName := "门店仓"
				if warehouse.Category == 4 {
					CategoryName = "加盟仓"
				}
				for _, inventory := range inventories {
					if inventory.AvailableNum < warningQty { // 低于1就预警，可根据实际调整
						msg := inventoryVO.OrderMessage{
							MessageType: 6,
							Msg:         fmt.Sprintf("商品[%s]（SKU:%d）在仓库[%s](%s)库存仅剩%d，已低于预警值！", inventory.ProductName, inventory.SkuId, warehouse.WarehouseName, CategoryName, inventory.AvailableNum),
							WarehouseId: inventory.WarehouseId,
							SkuId:       inventory.SkuId,
							FinanceCode: inventory.StoreId,
							BarCode:     inventory.BarCode,
						}
						msgContent, _ := json.Marshal(msg)
						pushReq := inventoryVO.MessageCreateRequest{
							ShopId:      inventory.StoreId,
							MemberMain:  "", // 可补充负责人
							Content:     string(msgContent),
							MessageType: 6,
						}
						s.PushMessage(pushReq)
					}
				}
			}
		}
	}
	return *ioBound, nil
}

// Freeze 锁库【多次调用则不断添加商品锁库】
func (s IoBoundService) Freeze(ctx context.Context, session *xorm.Session, cmd *vo.OutBoundCommand) (po.IoBound, error) {
	// 打印请求参数cmd，json格式化
	log.Infof("IoBoundService Freeze cmd: %v", utils.JsonEncode(cmd))

	// 传入的session为空，则初始化一个session
	if session == nil {
		s.Begin()
		defer s.Close()
		session = s.Engine.NewSession()
		session.Begin()
		defer session.Close()
		defer session.Commit()
	}

	// 设置上下文
	ctx = jwt.CtxSet(ctx, &jwt.XCShopPayload{
		ChainId:  strconv.FormatInt(cmd.ChainId, 10),
		TenantId: cmd.StoreId,
		UserName: cmd.Operator,
	})

	// 仓库id为空时，则需要根据店铺id、skuId、渠道id查询对应的仓库id
	skuId := cmd.Details[0].SkuId
	storeId := cmd.StoreId
	if cmd.WarehouseId == 0 {
		warehouseId, err := new(warehousePO.Warehouse).GetWarehouseIdBySkuId(ctx, session, storeId, skuId, cmd.ChannelId)
		if err != nil {
			session.Rollback()
			return po.IoBound{}, err
		}
		cmd.WarehouseId = warehouseId
	}

	// 1. 查询是否存在锁库数据
	ioBound := new(po.IoBound).GetFreezeItem(ctx, session, cmd.RefId, cmd.RefType)
	// 2. 将传入的数据转换为出库详情
	details, err := converter.ConvertSlice[vo.IoBoundDetailCreateCommand](cmd.Details)
	if err != nil {
		session.Rollback()
		return po.IoBound{}, err
	}

	if ioBound.Id == 0 {
		// 如果锁库数据不存在, 则添加一条锁库数据
		return s.Create(ctx, session, vo.IoBoundCreateCommand{
			ChainId:     cmd.ChainId,
			StoreId:     cmd.StoreId,
			WarehouseId: cmd.WarehouseId,
			ItemType:    po.BoundItemType_Freeze,
			ItemRefId:   cmd.RefId,
			ItemRefNo:   cmd.RefNo,
			ItemRefType: cmd.RefType,
			Remark:      cmd.Remark,
			Details:     details,
			Operator:    cmd.Operator,
		})
	} else {
		// 如果锁库数据存在, 则表示已经锁库
		// s.InOutMore(ctx, &ioBound, details)
		return ioBound, nil
	}
}

func (s IoBoundService) UnFreeze(ctx context.Context, session *xorm.Session, cmd vo.UnFreezeCommand) (bool, error) {
	// 打印请求参数cmd，json格式化
	log.Infof("IoBoundService UnFreeze cmd: %v", utils.JsonEncode(cmd))

	// 传入的session为空，则初始化一个session
	if session == nil {
		s.Begin()
		defer s.Close()
		session = s.Engine.NewSession()
		session.Begin()
		defer session.Close()
		defer session.Commit()
	}

	// 设置上下文
	ctx = jwt.CtxSet(ctx, &jwt.XCShopPayload{
		ChainId:  strconv.FormatInt(cmd.ChainId, 10),
		TenantId: cmd.StoreId,
		UserName: cmd.Operator,
	})

	// 1. 查询是否存在锁库数据
	ioBound := new(po.IoBound).GetFreezeItem(ctx, s.Engine.NewSession(), cmd.RefId, cmd.RefType)
	if ioBound.Id == 0 {
		return false, errors.NewBadRequest("锁库记录不存在，执行解锁库存失败")
	}

	if cmd.WarehouseId == 0 {
		cmd.WarehouseId = ioBound.WarehouseId
	}

	// 2. 查询到所有锁定的详情
	ioBoundDetails, err := new(po.IoBoundDetail).ListByBoundId(ctx, s.Engine.NewSession(), ioBound.Id)
	if err != nil {
		return false, errors.NewBadRequest("锁库详情记录查询异常，执行解锁库存失败")
	}

	var details []vo.IoBoundDetailCreateCommand
	for _, detail := range ioBoundDetails {
		if len(cmd.DetailMap) > 0 {
			if _, ok := cmd.DetailMap[detail.ItemDetailRefId]; ok {
				// 校验数量
				if cmd.DetailMap[detail.ItemDetailRefId] > detail.IoCount {
					return false, errors.NewBadRequest(fmt.Sprintf("商品%s解锁数量超过锁定数量", detail.ProductName))
				}
				details = append(details, vo.IoBoundDetailCreateCommand{
					SkuId:           detail.SkuId,
					ProductName:     detail.ProductName,
					ItemDetailRefId: detail.ItemDetailRefId,
					IoCount:         cmd.DetailMap[detail.ItemDetailRefId],
					IoPrice:         detail.IoPrice,
				})
			}
		} else {
			details = append(details, vo.IoBoundDetailCreateCommand{
				SkuId:           detail.SkuId,
				ProductName:     detail.ProductName,
				ItemDetailRefId: detail.ItemDetailRefId,
				IoCount:         cmd.DetailMap[detail.ItemDetailRefId],
				IoPrice:         detail.IoPrice,
			})
		}
	}

	// 4. 执行解冻
	_, err = s.Create(ctx, session, vo.IoBoundCreateCommand{
		ChainId:     cmd.ChainId,
		StoreId:     cmd.StoreId,
		WarehouseId: cmd.WarehouseId,
		ItemType:    po.BoundItemType_UnFreeze,
		ItemRefId:   ioBound.ItemRefId,
		ItemRefNo:   ioBound.ItemRefNo,
		ItemRefType: ioBound.ItemRefType,
		Remark:      cmd.Remark,
		Details:     details,
		Operator:    cmd.Operator,
	})
	if err != nil {
		session.Rollback()
		return false, err
	}

	return true, nil
}

func (s IoBoundService) OrderOut(ctx context.Context, session *xorm.Session, cmd vo.OutBoundCommand) (po.IoBound, error) {
	// 打印请求参数cmd，json格式化
	log.Infof("IoBoundService OrderOut cmd: %s", utils.JsonEncode(cmd))

	// 传入的session为空，则初始化一个session
	if session == nil {
		s.Begin()
		defer s.Close()
		session = s.Engine.NewSession()
		session.Begin()
		defer session.Close()
		defer session.Commit()
	}

	// 设置上下文
	ctx = jwt.CtxSet(ctx, &jwt.XCShopPayload{
		ChainId:  strconv.FormatInt(cmd.ChainId, 10),
		TenantId: cmd.StoreId,
		UserName: cmd.Operator,
	})

	// 先执行锁库
	_, err := s.Freeze(ctx, session, &cmd)
	if err != nil {
		session.Rollback()
		return po.IoBound{}, err
	}

	// 再执行出库
	details, err := converter.ConvertSlice[vo.IoBoundDetailCreateCommand](cmd.Details)
	if err != nil {
		session.Rollback()
		return po.IoBound{}, err
	}
	ioBound, err := s.Create(ctx, session, vo.IoBoundCreateCommand{
		ChainId:     cmd.ChainId,
		StoreId:     cmd.StoreId,
		WarehouseId: cmd.WarehouseId,
		ItemType:    po.BoundItemType_OrderOut,
		ItemRefId:   cmd.RefId,
		ItemRefNo:   cmd.RefNo,
		ItemRefType: cmd.RefType,
		Remark:      cmd.Remark,
		Details:     details,
		Operator:    cmd.Operator,
	})
	if err != nil {
		session.Rollback()
		return po.IoBound{}, err
	}

	return ioBound, nil
}

func (s IoBoundService) RefundIn(ctx context.Context, session *xorm.Session, cmd vo.InBoundCommand) (po.IoBound, error) {
	// 打印请求参数cmd，json格式化
	log.Infof("IoBoundService RefundIn cmd: %v", utils.JsonEncode(cmd))

	// 传入的session为空，则初始化一个session
	if session == nil {
		s.Begin()
		defer s.Close()
		session = s.Engine.NewSession()
		session.Begin()
		defer session.Close()
		defer session.Commit()
	}

	// 设置上下文
	ctx = jwt.CtxSet(ctx, &jwt.XCShopPayload{
		ChainId:  strconv.FormatInt(cmd.ChainId, 10),
		TenantId: cmd.StoreId,
		UserName: cmd.Operator,
	})

	// cmd.Details中的OutRefDetailId去重，并转为切片
	outRefDetailIds := lo.Uniq(lo.Map(cmd.Details, func(detail vo.InBoundDetailCommand, _ int) string {
		return detail.ItemDetailRefId
	}))

	ioBoundDetails, err := new(po.IoBoundDetail).ListByRefDetailIds(ctx, session, po.BoundItemType_OrderOut, outRefDetailIds)
	if err != nil {
		session.Rollback()
		return po.IoBound{}, err
	}
	if len(ioBoundDetails) == 0 {
		return po.IoBound{}, errors.NewBadRequest(fmt.Sprintf("没有找到相应的销售出库明细记录：refDetailIds=%s", utils.JsonEncode(outRefDetailIds)))
	}

	// 将ioBoundDetails转为map[int]po.IoBoundDetail
	detailMap := make(map[int]po.IoBoundDetail)
	for _, detail := range ioBoundDetails {
		detailMap[detail.SkuId] = detail
	}

	details := make([]vo.IoBoundDetailCreateCommand, 0)
	for _, detail := range cmd.Details {
		ioBoundDetail := detailMap[detail.SkuId]
		details = append(details, vo.IoBoundDetailCreateCommand{
			SkuId:           detail.SkuId,
			ProductName:     detail.ProductName,
			ItemDetailRefId: detail.ItemDetailRefId,
			IoCount:         detail.IoCount,
			IoPrice:         ioBoundDetail.AvgCostPrice,
			RealIoAmount:    ioBoundDetail.IoPrice * detail.IoCount,
		})
	}

	// 直接调用接口退货入库
	ioBound, err := s.Create(ctx, session, vo.IoBoundCreateCommand{
		ChainId:     cmd.ChainId,
		StoreId:     cmd.StoreId,
		WarehouseId: cmd.WarehouseId,
		ItemType:    po.BoundItemType_RefundIn,
		ItemRefId:   cmd.RefId,
		ItemRefNo:   cmd.RefNo,
		ItemRefType: cmd.RefType,
		Remark:      cmd.Remark,
		Details:     details,
		Operator:    cmd.Operator,
	})
	if err != nil {
		session.Rollback()
		return po.IoBound{}, err
	}

	return ioBound, nil
}

// 组装库存变更对象
func packInventoryChange(ctx context.Context, ioBound *po.IoBound) *inventoryVO.InventoryChange {
	inventoryChange := &inventoryVO.InventoryChange{
		WarehouseId:         ioBound.WarehouseId,
		ItemType:            ioBound.ItemType,
		ItemRefId:           ioBound.ItemRefId,
		ItemRefNo:           ioBound.ItemRefNo,
		ItemRefType:         ioBound.ItemRefType,
		Operator:            jwt.CtxGet[string](ctx, "UserName"),
		SkuInventoryChanges: make([]inventoryVO.SkuInventoryChanges, 0),
		Remark:              ioBound.Remark,
	}
	return inventoryChange
}

func packIoBoundDetails(ctx context.Context, inventoryMap map[int]inventoryPO.Inventory, ioBound *po.IoBound, inventoryChange *inventoryVO.InventoryChange, details []vo.IoBoundDetailCreateCommand) ([]po.IoBoundDetail, error) {
	var ioBoundDetails []po.IoBoundDetail

	// 初始化变更数量、变更金额、单价
	changeNum := ioBound.TotalNum
	changeAmount := ioBound.TotalAmount
	sellAmount := ioBound.SellAmount

	// 准备一个Map，用于合并多个detail记录，但是skuId相同的商品
	skuMap := make(map[int]inventoryVO.SkuInventoryChanges)

	// 遍历details，将每个detail的skuId和数量、金额、单价、销售金额添加到skuMap中
	for _, detail := range details {
		inventory := inventoryMap[detail.SkuId]

		// 计算出入库单价
		ioBoundDetail := &po.IoBoundDetail{}
		sellPrice, err := ioBoundDetail.CalcSellPrice(inventory, detail.IoPrice, ioBound.ItemType)
		if err != nil {
			return nil, err
		}

		// 计算出入库单价
		price, err := ioBoundDetail.CalcIoBoundPrice(inventory, detail, ioBound.ItemType)
		if err != nil {
			return nil, err
		}

		change := inventoryVO.SkuInventoryChanges{
			ChangeNum:   detail.IoCount,
			Price:       price,
			ProductName: detail.ProductName,
			SkuId:       detail.SkuId,
		}

		inventoryChangeCalc, err := ioBoundDetail.CalcInventoryChange(ioBound.ItemType, inventory, change)
		if err != nil {
			return nil, err
		}

		// 计算变更数量, 变更金额, 销售金额
		changeNum += inventoryChangeCalc.ChangeNum
		changeAmount += inventoryChangeCalc.ChangeAmount
		sellAmount += sellPrice * detail.IoCount
		ioBoundDetails = append(ioBoundDetails, genIoBoundDetail(ctx, ioBound, detail, inventory, inventoryChangeCalc))

		// 多个商品同时出库场景时, 合并出库数量
		if dto, ok := skuMap[detail.SkuId]; !ok {
			// 出库时, 对于库存流水价格为库存成本价
			dto = inventoryVO.SkuInventoryChanges{
				SkuId:       detail.SkuId,
				ProductName: detail.ProductName,
				Price:       price,
				ChangeNum:   detail.IoCount,
			}
			skuMap[detail.SkuId] = dto
		} else {
			dto.ChangeNum += detail.IoCount
			skuMap[detail.SkuId] = dto
		}
	}

	ioBound.TotalNum = changeNum
	ioBound.TotalAmount = changeAmount
	ioBound.SellAmount = sellAmount
	inventoryChange.SkuInventoryChanges = lo.Values(skuMap)

	return ioBoundDetails, nil
}

func genIoBoundDetail(ctx context.Context, ioBound *po.IoBound, detail vo.IoBoundDetailCreateCommand, inventory inventoryPO.Inventory, inventoryChangeCalc inventoryVO.InventoryChangeCalc) po.IoBoundDetail {
	ioAmount := detail.IoPrice * detail.IoCount
	realIoAmount := ioAmount
	if detail.RealIoAmount > 0 {
		realIoAmount = detail.RealIoAmount
	}
	return po.IoBoundDetail{
		ChainId:             jwt.CtxGet[int64](ctx, "ChainId"),
		StoreId:             jwt.CtxGet[string](ctx, "TenantId"),
		WarehouseId:         ioBound.WarehouseId,
		BoundId:             ioBound.Id,
		BoundNo:             ioBound.BoundNo,
		BoundType:           ioBound.BoundType,
		ItemType:            ioBound.ItemType,
		ItemDetailRefId:     detail.ItemDetailRefId,
		ProductId:           inventory.ProductId,
		SkuId:               inventory.SkuId,
		ProductName:         inventory.ProductName,
		ProductType:         inventory.ProductType,
		BarCode:             inventory.BarCode,
		ProductCategoryPath: inventory.ProductCategoryPath,
		AvgCostPrice:        inventory.AvgCostPrice,
		IoPrice:             detail.IoPrice,
		IoCount:             detail.IoCount,
		IoAmount:            ioAmount,
		RealIoAmount:        realIoAmount,
		TotalNumAfter:       inventoryChangeCalc.TotalNumAfter,
		FreezeNumAfter:      inventoryChangeCalc.FreezeNumAfter,
		AvailableNumAfter:   inventoryChangeCalc.AvailableNumAfter,
		TotalAmountAfter:    inventoryChangeCalc.TotalAmountAfter,
		Operator:            jwt.CtxGet[string](ctx, "UserName"),
		IsDeleted:           false,
		CreatedTime:         time.Now(),
		UpdatedTime:         time.Now(),
	}
}

// 组装出入库对象
func packIoBound(ctx context.Context, cmd vo.IoBoundCreateCommand) *po.IoBound {
	occurrenceTime := cmd.OccurrenceTime
	if time.Time.IsZero(occurrenceTime) {
		occurrenceTime = time.Now()
	}

	boundType := 2 - (cmd.ItemType & 1)
	prefix := "CK"
	if boundType == 1 {
		prefix = "RK"
	}
	return &po.IoBound{
		ChainId:        jwt.CtxGet[int64](ctx, "ChainId"),
		StoreId:        jwt.CtxGet[string](ctx, "TenantId"),
		WarehouseId:    cmd.WarehouseId,
		BoundNo:        utils.GenInventorySN(prefix),
		BoundType:      boundType,
		ItemType:       cmd.ItemType,
		ItemRefId:      cmd.ItemRefId,
		ItemRefNo:      cmd.ItemRefNo,
		ItemRefType:    cmd.ItemRefType,
		TotalNum:       0,
		TotalAmount:    0,
		SellAmount:     0,
		Remark:         cmd.Remark,
		Operator:       jwt.CtxGet[string](ctx, "UserName"),
		OccurrenceTime: occurrenceTime,
		CreatedTime:    time.Now(),
		UpdatedTime:    time.Now(),
	}
}

// 检查出入库商品是否存在
func checkIoBound(inventoryMap map[int]inventoryPO.Inventory, details []vo.IoBoundDetailCreateCommand) error {
	// 检查details中是否存在inventoryMap中没有有的skuId
	var notExists []string
	for _, detail := range details {
		if _, ok := inventoryMap[detail.SkuId]; !ok {
			notExists = append(notExists, detail.ProductName)
		}
	}
	if len(notExists) > 0 {
		return errors.NewBadRequest(fmt.Sprintf("商品%s的库存记录不存在", strings.Join(notExists, ",")))
	}

	return nil
}

func checkIoPrice(itemType int, details []vo.IoBoundDetailCreateCommand) error {
	var inPriceError []string
	var OutPriceError []string
	for _, detail := range details {
		// itemType如果是采购入库，则出入库价格必须大于0
		if itemType == 1 && detail.IoPrice <= 0 {
			inPriceError = append(inPriceError, detail.ProductName)
		}
		// itemType如果是采购退货出库或者冻结库存，则出入库价格必须小于0
		if (itemType == 2 || itemType == 4) && detail.IoPrice < 0 {
			OutPriceError = append(OutPriceError, detail.ProductName)
		}
	}

	if len(inPriceError) > 0 {
		return errors.NewBadRequest(fmt.Sprintf("采购入库商品:%s价格未设置或小于0.01元", strings.Join(inPriceError, ",")))
	}

	if len(OutPriceError) > 0 {
		return errors.NewBadRequest(fmt.Sprintf("销售出库或锁定商品:%s必须设置出库单价且价格必须大于等于0.00元", strings.Join(OutPriceError, ",")))
	}

	return nil
}

// 查询库存列表
func (s IoBoundService) Page(ctx context.Context, req vo.IoBoundPageRequest) ([]vo.IoBoundResponse, int64, error) {
	s.Begin()
	session := s.Session
	defer s.Close()

	ioBounds, total, err := new(po.IoBound).Page(ctx, session, req)
	if err != nil {
		return nil, 0, err
	}

	return ioBounds, total, nil
}

// 查询库存详情
func (s IoBoundService) Detail(ctx context.Context, req baseVO.IdRequest) (vo.IoBoundResponse, error) {
	s.Begin()
	defer s.Close()

	ioBound, err := new(po.IoBound).GetById(ctx, s.Engine.NewSession(), req.Id)
	if err != nil || ioBound.Id == 0 {
		return vo.IoBoundResponse{}, errors.NewBadRequest("出入库记录不存在")
	}

	ioBoundResponse, err := converter.Convert[vo.IoBoundResponse](ioBound)
	if err != nil {
		return vo.IoBoundResponse{}, errors.NewBadRequest("出入库记录转换失败")
	}

	// 补充仓库信息
	if ioBound.WarehouseId > 0 {
		warehouseVO, err := new(warehousePO.Warehouse).GetByID(ctx, s.Engine.NewSession(), ioBound.WarehouseId)
		if err != nil {
			return vo.IoBoundResponse{}, errors.NewBadRequest("仓库信息获取失败")
		}
		ioBoundResponse.WarehouseName = warehouseVO.WarehouseName
	}
	return ioBoundResponse, nil
}

func (s IoBoundService) DetailPage(ctx context.Context, req vo.IoBoundDetailPageRequest) ([]vo.IoBoundDetailResponse, int64, error) {
	s.Begin()
	session := s.Session
	defer s.Close()

	ioBoundDetails, total, err := new(po.IoBoundDetail).Page(ctx, s.Engine.NewSession(), req)
	if err != nil {
		return nil, 0, err
	}

	if len(ioBoundDetails) > 0 {
		storeId := jwt.CtxGet[string](ctx, "TenantId")
		for i, ioBoundDetail := range ioBoundDetails {
			ioBoundDetails[i].ProductSku, err = new(inventoryPO.Inventory).GetProductBySkuId(ctx, session, storeId, ioBoundDetail.SkuId)
			if err != nil {
				return nil, 0, err
			}
		}
	}
	return ioBoundDetails, total, nil
}

func (s IoBoundService) DetailSummary(ctx context.Context, req vo.IoBoundDetailPageRequest) (int, error) {
	s.Begin()
	defer s.Close()

	summary, err := new(po.IoBoundDetail).Summary(ctx, s.Session, req)
	if err != nil {
		return 0, err
	}
	return summary, nil
}

// inOutMore 出入库更多商品
// Deprecated: 此方法已废弃，不建议使用
func (s IoBoundService) InOutMore(ctx context.Context, ioBound *po.IoBound, details []vo.IoBoundDetailCreateCommand) error {
	s.Begin()
	defer s.Close()

	if ioBound == nil || ioBound.Id == 0 {
		return errors.NewBadRequest("出入库信息为空")
	}

	// 从cmd中获取到details的skuIds，过滤掉0并去重
	skuIds := lo.Uniq(lo.Filter(lo.Map(details, func(detail vo.IoBoundDetailCreateCommand, _ int) int {
		return detail.SkuId
	}), func(skuId int, _ int) bool {
		return skuId != 0
	}))

	// skuIds为空则直接返回错误信息
	if len(skuIds) == 0 {
		return errors.NewBadRequest("出入库商品为空")
	}

	session := s.Engine.NewSession()
	session.Begin()

	// 根据skuIds查询库存，并转为skuId:库存的map
	inventoryMap := make(map[int]inventoryPO.Inventory)

	// 使用充血模型，创建库存模型，调用模型内查询方法
	inventories, err := new(inventoryPO.Inventory).ListBySkuIds(ctx, session, ioBound.WarehouseId, skuIds)
	if err != nil {
		return err
	}

	// 将查询到的库存转换为skuId:库存的map
	for _, inventory := range inventories {
		inventoryMap[int(inventory.SkuId)] = inventory
	}

	// 检查商品是否存在
	if err = checkIoBound(inventoryMap, details); err != nil {
		return err
	}
	// 检查商品出入库价格
	if err = checkIoPrice(ioBound.ItemType, details); err != nil {
		return err
	}

	// 组装库存变更对象
	inventoryChange := packInventoryChange(ctx, ioBound)

	// 拼装出入库详情列表
	ioBoundDetails, err := packIoBoundDetails(ctx, inventoryMap, ioBound, inventoryChange, details)
	if err != nil {
		return err
	}

	// 库存变更
	inventoryService := inventorySvc.NewInventoryService()
	_, err = inventoryService.InventoryManage(ctx, session, inventoryChange)
	if err != nil {
		session.Rollback()
		return err
	}

	// 插入出入库记录
	err = ioBound.UpdateById(ctx, session)
	if err != nil {
		session.Rollback()
		return err
	}

	// 批量创建出入库详情
	ioBoundDetail := &po.IoBoundDetail{}
	err = ioBoundDetail.BatchCreate(ctx, session, ioBoundDetails)
	if err != nil {
		session.Rollback()
		return err
	}

	session.Commit()
	return nil
}

func (s *IoBoundService) PushMessage(model inventoryVO.MessageCreateRequest) error {
	//model := inventoryVO.MessageCreateRequest{}
	//model.OrderSn = orderSn
	str, _ := json.Marshal(model)
	url := "http://127.0.0.1:11001/boss/datacenter/message/messagecreate"
	ret, err := HttpPostTo(url, str)
	glog.Info("ESHOP 发预计通知", string(ret), err)
	return nil
}

func HttpPostTo(url string, bytesData []byte) ([]byte, error) {
	//跳过证书验证
	//tr := &http.Transport{
	//	TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	//	DialContext: (&net.Dialer{
	//		Timeout: 10 * time.Second,
	//	}).DialContext,
	//}

	reader := bytes.NewReader(bytesData)
	request, err := http.NewRequest("POST", url, reader)
	if err != nil {
		return nil, err
	}
	//request.Header.Set("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
	request.Header.Set("Content-Type", "application/json;charset=utf-8")

	//client := &http.Client{Transport: tr}
	resp, err := utils.HttpTransportClientTimeout.Do(request)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != 200 {
		return nil, errors.New(resp.Status)
	}
	respBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	} else {
	}
	return respBytes, nil
}
