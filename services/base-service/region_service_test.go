package base_service

import (
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"reflect"
	"testing"
)

func TestRegionService_RegionList(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	tests := []struct {
		name    string
		fields  fields
		want    []vo.BaseOrganization
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "查询区域"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := RegionService{
				BaseService: tt.fields.BaseService,
			}
			got, err := s.RegionList()
			if (err != nil) != tt.wantErr {
				t.Errorf("RegionList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.E<PERSON>rf("RegionList() got = %v, want %v", got, tt.want)
			}
		})
	}
}
