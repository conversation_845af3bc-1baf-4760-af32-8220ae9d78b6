package base_service

import (
	po "eShop/domain/distribution-po"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"fmt"
	"net/http"
	"time"

	"github.com/spf13/cast"
)

type OperateLogService struct {
	common.BaseService
}

// 业务模块
const (
	ModuleDistributor      = iota + 1 // 分销员
	ModuleSalesman                    // 业务员
	ModuleDisGoods                    // 分销商品
	ModuleShop                        // 线下企业模块
	ModuleCode                        // 百林康源-物流码
	ModuleDisCommisSetting            // 客户渠道佣金设置

)

// 分销员操作类型：1-清退，2-变更，3-启用，4-更换绑定的医院操作，5-审核
const (
	DistributorUnbind = iota + 1 // 清退操作
	DistributorChange            // 变更操作
	DistributorAble              // 启用操作
	ChangeHospital               // 更换绑定的医院操作
	Approve                      // 审核
	ChangeSalesperson            // 变更注册业务员
)

// 业务员操作
const (
	// 编辑操作
	SalesmanEdit = iota + 1
	SalesmanStart
	SalesmanStop
)

// 线下企业
const (
	//角色更换
	ShopRoleChange = iota + 1
)

// 分销商品操作类型：1-佣金设置
const (
	DisGoodsCommisSet = iota + 1 // 佣金设置
)

// 物流码
const (
	Code1Type = iota + 1 // 查询物流码
	Code2Type            //扣除佣金
	Code3Type            //物流码恢复
	Code4Type            //佣金扣除并恢复物流码
)

const (
	DisCommisSettingCreate = iota + 1 // 新增客户渠道佣金设置
	DisCommisSettingEdit              // 编辑客户渠道佣金设置
	DisCommisSettingDelete            // 删除客户渠道佣金设置
)

var ModuleDisCommisSettingMap = map[int]string{
	DisCommisSettingCreate: "创建渠道佣金设置",
	DisCommisSettingEdit:   "变更渠道佣金设置",
	DisCommisSettingDelete: "删除渠道佣金设置",
}

// Add 添加操作日志记录
func (s OperateLogService) Add(r *http.Request, req vo.OperateLogReq) error {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("写入获取登录信息失败: err", err.Error())
		return err
	}

	// operateLog 校验
	if req.ModuleType <= 0 {
		return fmt.Errorf("写入操作日志失败，业务模块id不能为空：ModuleType=%d", req.ModuleType)
	}
	if req.Type <= 0 {
		return fmt.Errorf("写入操作日志失败，操作类型不能为空：Type=%d", req.Type)
	}
	if req.FromId == "" {
		return fmt.Errorf("写入操作日志失败，业务数据id不能为空：From_id=%s", req.FromId)
	}

	s.Begin()
	defer s.Close()
	session := s.Session

	operateLog := &po.OperateLog{
		ModuleType:  req.ModuleType,
		Type:        req.Type,
		FromId:      req.FromId,
		Description: req.Description,
	}

	// 组装operateLog对象
	operateLog.UserNo = jwtInfo.UserNo
	operateLog.UserName = jwtInfo.Name
	operateLog.CreateTime = time.Now()

	_, err = session.Insert(operateLog)
	if err != nil {
		log.Error("写入操作日志失败：err=", err.Error())
		return err
	}

	return nil
}

func (s OperateLogService) OperateLogPage(req vo.OperateLogPageReq) ([]vo.OperateLogPageData, int, error) {
	s.Begin()
	defer s.Close()

	session := s.Session
	var list []vo.OperateLogPageData

	session.Table("operate_log")
	if req.ModuleType > 0 {
		session.And("module_type = ?", req.ModuleType)
	}
	if req.FromId != "" {
		session.And("from_id = ?", cast.ToInt64(req.FromId))
	}
	if req.IsExport == 1 {
		req.PageSize = 10000
	}
	limit := req.PageSize
	start := (req.PageIndex - 1) * req.PageSize
	total, err := session.OrderBy("id DESC").Limit(limit, start).FindAndCount(&list)
	if err != nil {
		log.Error("查询分销员列表失败：err=", err.Error())
		return nil, 0, err
	}

	return list, cast.ToInt(total), nil
}
