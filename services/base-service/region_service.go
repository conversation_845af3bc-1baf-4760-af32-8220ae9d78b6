package base_service

import (
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"os"
	"strings"
)

type RegionService struct {
	common.BaseService
}

func (s RegionService) RegionList() ([]vo.BaseOrganization, error) {
	s.<PERSON>gin()
	defer s.Close()

	session := s.Session

	var BaseOrganization []vo.BaseOrganization

	IsDebug := false
	//	var err error
	//	err = GlobalEngine.Find(&CodeInfo, t)
	env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	if env == "staging" || env == "uat" || env == "" {
		IsDebug = true
	}
	sess := session.Table("dc_oms.base_organization")
	//如果不是测试等环境，就需要加上条件查询数据
	if !IsDebug {
		sess.Where("parent_id=118 or parent_id=119")
	}

	err := sess.Find(&BaseOrganization)
	if err != nil {
		return nil, err
	}
	return BaseOrganization, err
}
