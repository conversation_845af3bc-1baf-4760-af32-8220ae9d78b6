package base_service

import (
	"crypto/tls"
	po "eShop/domain/distribution-po"
	"eShop/infra/jwtauth"
	proproductstoreinfo "eShop/services/product-service/enum/pro-product-store-info"
	viewmodel "eShop/view-model"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/spf13/cast"
	"github.com/streadway/amqp"
	"github.com/xuri/excelize/v2"

	tasklist "eShop/infra/enum"
	"eShop/infra/log"
	mq "eShop/infra/mq"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
	"eShop/services/distribution-service/export"
	inventoryExport "eShop/services/inventory-service/export"
	marketing_export "eShop/services/marketing-service/export"
	export3 "eShop/services/omnibus-service/export"
	points_export "eShop/services/points-service/export"
	export2 "eShop/services/product-service/export"
)

type TaskService struct {
	common.BaseService
}

func (h TaskService) TaskInit() {

	go func() {
		for {
			mq.Consume(tasklist.TaskQueue, tasklist.TaskQueue, "eshop", h.EshopTask)
		}
	}()
}

/*
*
mq消息发布测试：
{"id":447,"task_content":20,"task_status":1,"task_detail":"","operation_file_url":"https://rpets-saas-cos-pre.rvet.cn/530219708465609002/product/2024/09/04/a59cc11f034c4d6ba730b879ab1738fe.xlsx","request_header":"","resulte_file_url":"","create_id":"530541534660097044","create_time":"2024-09-04T16:49:53.85765+08:00","create_name":"","create_mobile":"","create_ip":"127.0.0.1","ip_location":"","success_num":0,"fail_num":0,"extended_data":"","context_data":"530219708465609002","org_id":6,"do_count":0,"err_mes":"","update_time":"2024-09-04T16:49:53.85765+08:00","operation_type":1}
*/
func (h TaskService) EshopTask(d amqp.Delivery) (response string, err error) {
	logPrefix := "mq In..EshopTask"
	log.Info(logPrefix)
	model := new(po.TaskList)
	if err := json.Unmarshal(d.Body, model); err != nil {
		log.Error(logPrefix, err)
		return err.Error(), nil
	}
	logPrefix = fmt.Sprintf("%s, %s, %d", logPrefix, model.CreateName, model.Id)
	log.Info(logPrefix, ", 异步任务开始")

	//文件下载链接
	var url string
	var success_num int
	var fail_num int
	var err_mes string
	var task_detail string

	defer func() {
		model.TaskStatus = tasklist.TaskStatusFinished
		model.ResulteFileUrl = url
		model.SuccessNum = success_num
		model.FailNum = fail_num
		model.TaskDetail = task_detail
		model.ErrMes = err_mes
		if err != nil {
			log.Error(logPrefix, "更新任务失败", err.Error())
			model.TaskStatus = 4
		}

		h.UpdateTask(model)

		d.Ack(false)
	}()

	//修改任务状态
	h.StartTask(model)
	oet := NewOrderExportTask(*model)
	switch model.OperationType {

	case 1: //导入
		out, err := h.ImportFunc(model.OperationFileUrl, model.OrgId, oet.OperationFunc, model.TaskContent)
		if err != nil {
			log.Error(model.CreateIp, ", 导入文件失败, ", err)
			err = errors.New("导入文件失败, " + err.Error())
			task_detail = "导入失败" + err.Error()
			err_mes = err.Error()
			return "", err
		}
		url = out.QiniuUrl
		success_num = int(out.SuccessNum)
		fail_num = int(out.FailNum)
		task_detail = "操作成功:" + cast.ToString(success_num) + " 操作失败:" + cast.ToString(fail_num)

	case 0: //导出
		success_num, _, err = oet.DataExport(model.OperationFileUrl)
		if err != nil {
			log.Error(logPrefix, ", 文件数据导出失败, ", err)
			err = errors.New("dataExport失败, " + err.Error())
			task_detail = "导出失败" + err.Error()
			err_mes = err.Error()
			return "", err
		} else {
			task_detail = "操作成功:" + cast.ToString(success_num) + " 操作失败:0"
			log.Info(logPrefix, ", 文件上传开始")
			//上传至oss生成下载链接
			url, err = oet.GenerateDownUrl()
			if err != nil {
				log.Error(logPrefix, ", 生成下载链接失败, ", err)
				err = errors.New("生成下载链接失败, " + err.Error())
				err_mes = err.Error()
				return "", err
			}

			log.Info(logPrefix, ", 任务结束, ", url)
		}
	}

	return "success", nil
}

type TaskInterface interface {
	//导出方法 成功数量，失败数量，错误
	DataExport(string) (int, int, error)
	//设置表头
	SetSheetName(args ...interface{})
	//导出文件
	GenerateDownUrl() (string, error)
	//导入方法
	//DataImport(string, int) (*vo.ImportResult, error)
	//导入的方法
	OperationFunc(row []string, org_id int) string
}

func NewOrderExportTask(model po.TaskList) (oet TaskInterface) {
	switch model.TaskContent {
	//业务员
	case tasklist.TaskContentSalesmanImport, tasklist.TaskContentSalesmanExport:
		oet = &export.DisSalesmanTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentDisOrderExport:
		oet = &export.DisOrderTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentDisSettlementExport:
		oet = &export.DisSettlementTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentDisWithdrawExport, tasklist.TaskContentDisWithdrawImport, tasklist.TaskContentDisWithdrawOrderExport:
		oet = &export.DisWithdrawTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentDistributorExport:
		oet = &export.DisDistributorTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentDisGoodsExport, tasklist.TaskContentDisGoodsImport:
		oet = &export.DisGoodsTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentDisInsureSettleExport:
		oet = &export.DisInsureSettleTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentShopExport:
		oet = &export.ShopTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentBlkyProductExport:
		oet = &export.BlkyProductTask{
			SheetName: fmt.Sprintf("百林康源商品物流码导出(%s%d).txt", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000),
		}
	case tasklist.TaskContentSzldProductExport:
		oet = &export.BlkyProductTask{
			SheetName: fmt.Sprintf("深圳利都商品物流码导出(%s%d).txt", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000),
		}
	case tasklist.StatsShopExport:
		oet = &export.StatsShopTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.StatsSalespersonExport:
		oet = &export.StatsSalespersonTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentStatsSkuExport:
		oet = &export.StatsSkuTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentStatsCitySkuExport:
		oet = &export.StatsSkuCityTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentStoreProductExport:

		var JwtInfo jwtauth.XCShopPayload
		err := json.Unmarshal([]byte(model.RequestHeader), &JwtInfo)
		if err != nil {
			log.Error("导出门店商品解析JWT失败", model)
		}

		oet = export2.SaasStoreProductExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
			JwtInfo:   &JwtInfo,
		}

	case tasklist.TaskContentChainProductExport, tasklist.TaskContentChainProductImport:
		var JwtInfo jwtauth.XCShopPayload
		err := json.Unmarshal([]byte(model.RequestHeader), &JwtInfo)
		if err != nil {
			log.Error("导出门店商品解析JWT失败", model)
		}
		oet = export2.SaasChainProductExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
			ChainId:   cast.ToInt64(model.ContextData),
			JwtInfo:   &JwtInfo,
		}
	case tasklist.TaskContentProductBatchUp:
		oet = export2.SaasStoreProductExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
			StoreId:   model.ContextData,
			Type:      proproductstoreinfo.ProductTypeUp,
		}
	case tasklist.TaskContentProductBatchDown:
		oet = export2.SaasStoreProductExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
			StoreId:   model.ContextData,
			Type:      proproductstoreinfo.ProductTypeDown,
		}
	case tasklist.TaskContentProductBatchStock:
		oet = export2.SaasStoreProductExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
			StoreId:   model.ContextData,
			Type:      proproductstoreinfo.ProductTypeStock,
		}
	case tasklist.TaskContentProductBatchDel:
		oet = export2.SaasStoreProductExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
			StoreId:   model.ContextData,
			Type:      proproductstoreinfo.ProductTypeDel,
		}
	case tasklist.TaskContentProductBatchPush:
		oet = export2.SaasStoreProductExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
			StoreId:   model.ContextData,
			Type:      proproductstoreinfo.ProductTypeLaunch,
		}
	case tasklist.TaskContentProductBatchPrice:
		oet = export2.SaasStoreProductExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
			StoreId:   model.ContextData,
			Type:      proproductstoreinfo.ProductTypePrice,
		}
	case tasklist.TaskContentDisOrderDetailExport:
		oet = &export.DisOrderDetailTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentStoreServiceImport:
		oet = &export2.StoreServiceImport{
			SheetName:     "Sheet1",
			F:             excelize.NewFile(),
			RequestHeader: model.RequestHeader,
		}
	case tasklist.TaskContentStoreALIVEImport:
		oet = &export2.StoreAliveImport{
			SheetName:     "Sheet1",
			F:             excelize.NewFile(),
			RequestHeader: model.RequestHeader,
		}
	case tasklist.TaskContentInventoryLocationImport, tasklist.TaskContentInventoryLocationExport:
		oet = &inventoryExport.InventoryLocationTask{
			F:           excelize.NewFile(),
			SheetName:   "Sheet1",
			ContextData: model.ContextData,
		}
	case tasklist.TaskContentClsVerifyDetailExport:
		oet = &export.ClsVerifyDetailTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentUserListExport:
		oet = &export.UserTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentEmployeePerformanceExport:
		oet = &export3.PerformanceExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.ClsPointOrderImport, tasklist.ClsPointOrderExport:
		oet = &points_export.ClsPointsOrderTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.ClsPointsRuleImport:
		oet = &points_export.ClsPointsRuleTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.ClsPointsDailyStatExport:
		oet = &points_export.ClsPointsDailyStatsTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.ClsPointsFlowExport:
		oet = &points_export.ClsPointsFlowTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentPetInviteExport:
		oet = &marketing_export.PetInviteTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentPetContestantExport:
		oet = &marketing_export.PetContestantTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentPetStatExport:
		oet = &marketing_export.PetStatTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case tasklist.TaskContentPetPrizeExport:
		oet = &marketing_export.PetPrizeTask{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	}

	return
}

// 更新任务状态为 进行中
func (h TaskService) StartTask(task *po.TaskList) {
	log.Info("任务详情", task)
	h.Begin()
	defer h.Close()

	db := h.Engine
	db.Exec("update task_list set task_status=2 where id =?", task.Id)
}

func (h TaskService) UpdateTask(task *po.TaskList) {
	logPrefix := fmt.Sprintf("更新任务详情, %d", task.Id)
	log.Info(logPrefix, task)
	h.Begin()
	defer h.Close()

	db := h.Engine
	db.Where("id =?", task.Id).Update(task)
}

type OperationFunc func(row []string, org_id int) string

func (h TaskService) ImportFunc(url string, org_id int, operation OperationFunc, isSaasProduct int) (*viewmodel.ImportResult, error) {
	out := new(viewmodel.ImportResult)
	out.Code = 400
	var err error
	req := new(http.Request)
	resp := new(http.Response)
	if org_id == enum.SaasOrgId {
		resp, err = http.Get(url)

	} else {
		// 下载excel
		req, err = http.NewRequest("POST", url, nil)
		if err != nil {
			out.Message = err.Error()
			return out, nil
		}
		client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
		resp, err = client.Do(req)

	}

	if err != nil {
		out.Message = err.Error()
		return out, err
	}
	defer resp.Body.Close()
	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		out.Message = err.Error()
		return out, err
	}

	var handleErrList [][]string
	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		out.Message = err.Error()
		return out, err
	}
	var oneRow, twoRow []string

	for i, row := range rows {
		if i == 0 {
			if i == 0 {
				oneRow = row
			}
			continue
		}
		if i == 1 && (isSaasProduct == 19 || isSaasProduct == 28) {
			twoRow = row
			continue
		}
		//如果数据比表头少的话，加空数据对齐列
		rowleng := len(row)
		if len(oneRow) > rowleng {
			for x := 0; x < len(oneRow)-rowleng; x++ {
				row = append(row, "")
			}
		}
		errstr := operation(row, org_id)
		if len(errstr) == 0 {
			out.SuccessNum = out.SuccessNum + 1
		} else {
			out.FailNum = out.FailNum + 1
			msgs := append(row, errstr)
			handleErrList = append(handleErrList, msgs)
		}
	}
	if len(handleErrList) > 0 {
		// 将处理失败的商品信息导入excel上传至七牛云
		oneRow = append(oneRow, "失败原因")
		errList := append([][]string{}, oneRow)
		if isSaasProduct == 19 {
			errList = append(errList, twoRow)
		}
		errList = append(errList, handleErrList...)

		url, err = utils.ExportHandleErr(errList)
		if err != nil {
			out.Message = "错误信息上传失败; err: " + err.Error()
			return out, nil
		}
		out.QiniuUrl = url
	}
	out.Code = 200
	return out, nil
}
