package services

import (
	"eShop/infra/log"
	"eShop/view-model/external-vo/dto"
	"testing"
)

func TestOrderPick_PushOrderPick(t *testing.T) {
	log.Init()

	type args struct {
		in dto.OrderPick
	}
	tests := []struct {
		name    string
		s       *OrderPick
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "测试通知饿了么拣货完成",
			s:    &OrderPick{},
			args: args{
				in: dto.OrderPick{
					ChannelId:  2,
					OldOrderSn: "3301482642835059681",
					AppChannel: 12,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.s.PushOrderPick(tt.args.in); (err != nil) != tt.wantErr {
				t.Errorf("OrderPick.PushOrderPick() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
