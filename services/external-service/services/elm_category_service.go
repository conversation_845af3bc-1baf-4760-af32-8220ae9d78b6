package services

import (
	product_po "eShop/domain/product-po"
	"eShop/infra/config"
	glog "eShop/infra/log"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/proto"
	"eShop/services/common"
	pro_category "eShop/services/product-service/enum/pro-category"
	"eShop/view-model/external-vo/dto"
	vo "eShop/view-model/product-vo"
	"encoding/json"
	"errors"
	"strings"
	"time"

	"github.com/spf13/cast"
)

type ElmProductService struct {
	common.BaseService
}

// v6.4.0
// 删除饿了么店铺内分类
// 接口限流20次/source/秒
// sku.shop.category.delete
func (s *ElmProductService) DelElmShopCategory(params *vo.DelElmShopCategoryRequest) (*proto.DelElmShopCategoryResponse, error) {

	//glog.Info("limit :", ELERateLimitedDelElmShopCategory.Limit())
	//reserve := ELERateLimitedDelElmShopCategory.Reserve()
	reserve := common.GetLimiter("ele.limit.sku.shop.category.delete", 0).Reserve()
	time.Sleep(reserve.Delay())

	res := new(proto.DelElmShopCategoryResponse)
	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId
	//bodys["category_id"] = params.CategoryId
	bodys["shop_custom_id"] = params.ShopCustomId
	bodys["category_name"] = params.CategoryName
	result, _ := ElmAPI("sku.shop.category.delete", bodys, params.AppChannel)

	var datas dto.ResponseDatas
	if err := json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	if datas.Body.Errno == 0 && datas.Body.Error == "success" {
		res.Code = 200
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}
	return res, nil
}

// NewElmShopCategory
// 新增修改饿了么店铺内分类
// sku.shop.category.create 创建
// sku.shop.category.update 修改
func (s *ElmProductService) NewElmShopCategory(params *vo.NewElmShopCategoryRequest, syncType int) (*proto.NewElmShopCategoryResponse, error) {
	reserve := common.GetLimiter("ele.limit.sku.shop.category.update", 0).Reserve()
	time.Sleep(reserve.Delay())

	res := new(proto.NewElmShopCategoryResponse)
	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId
	//bodys["parent_category_id"] = params.ParentCategoryId
	bodys["shop_custom_id"] = params.ShopCustomId
	bodys["name"] = params.Name
	bodys["rank"] = cast.ToString(pro_category.EleMeCategorySort - cast.ToInt(params.Rank))
	if syncType == pro_category.SyncCategoryUpdate {
		//bodys["category_id"] = params.CategoryId

		//glog.Info("NewElmShopCategory-request, body", utils.JsonEncode(bodys), "：参数", params)
		result, _ := ElmAPI("sku.shop.category.update", bodys, params.AppChannel)
		//glog.Info("NewElmShopCategory-return-origin", params, bodys, result)

		var datas dto.ResponseDatas
		if err := json.Unmarshal(result, &datas); err != nil {
			glog.Error("饿了么接口,返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
		}
		//glog.Info("NewElmShopCategory-return", params, bodys, utils.JsonEncode(datas))
		if datas.Body.Errno == 0 {
			//不需要返回分类id，直接用商家自定义分类id
			//var data proto.NewElmShopCategory
			//data.CategoryId = bodys["category_id"].(string)
			//data.CategoryName = bodys["name"].(string)
			res.Code = 200
			//res.Data = append(res.Data, &data)
		} else {
			res.Code = 400
			res.Error = datas.Body.Error
		}
	} else {
		if cast.ToInt(params.ShopCustomParentId) > 0 {
			bodys["shop_custom_parent_id"] = params.ShopCustomParentId
		}

		//glog.Info("NewElmShopCategory-request, body", utils.JsonEncode(bodys), "：参数", params)
		result, _ := ElmAPI("sku.shop.category.create", bodys, params.AppChannel)
		//glog.Info("NewElmShopCategory-return-origin", params, bodys, result)

		var datas dto.ResponseData
		if err := json.Unmarshal(result, &datas); err != nil {
			glog.Error("饿了么接口,返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
		}
		//glog.Info("NewElmShopCategory-return", params, bodys, utils.JsonEncode(datas))
		if datas.Body.Errno == 0 && datas.Body.Error == "success" {
			var returnData = datas.Body.Data
			var data proto.NewElmShopCategory
			data.CategoryId = returnData["category_id"].(string)
			data.CategoryName = returnData["category_name"].(string)
			res.Code = 200
			res.Data = append(res.Data, &data)
		} else {
			res.Code = 400
			res.Error = datas.Body.Error
		}
	}
	return res, nil
}

func (s *ElmProductService) UpdateElmShopCategory(params *vo.UpdateElmShopCategoryRequest) (res *proto.NewElmShopCategoryResponse, err error) {
	reserve := common.GetLimiter("ele.limit.sku.shop.category.map", 0).Reserve()
	time.Sleep(reserve.Delay())

	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId
	bodys["custom_sku_id"] = params.CustomSkuId
	bodys["shop_custom_id"] = params.ShopCustomId
	bodys["category_name"] = params.Name
	bodys["rank"] = cast.ToString(pro_category.EleMeCategorySort - cast.ToInt(params.Rank))

	result, _ := ElmAPI("sku.shop.category.map", bodys, params.AppChannel)

	var datas dto.ResponseDatas
	if err = json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	res = new(proto.NewElmShopCategoryResponse)
	if datas.Body.Errno == 0 {
		res.Code = 200
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}

	return
}

// UpdateElmSkuStock 批量更新饿了么商品库存 单次最多支持100个商品，用分号隔开 001:200;002:200;
// todo 将err返回
// sku.stock.update.batch 接口限流200次/source/秒
func (s *ElmProductService) UpdateElmSkuStock(params *proto.UpdateElmSkuStockRequest) (*proto.UpdateElmSkuStockResponse, error) {
	//res := new(dto.BaseResponse)
	res := &proto.UpdateElmSkuStockResponse{}

	//reserve := ELERateLimitedSkuStockUpdateBatch.Reserve()
	reserve := common.GetLimiter("ele.limit.sku.stock.update.batch", 0).Reserve()
	time.Sleep(reserve.Delay())

	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId
	bodys["skuid_stocks"] = params.SkuidStocks
	bodys["upc_stocks"] = params.UpcStocks
	bodys["custom_sku_id"] = params.CustomSkuId
	result, _ := ElmAPI("sku.stock.update.batch", bodys, int32(params.AppChannel))

	var datas dto.ResponseBoolData
	if err := json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	glog.Info("饿了么商品更新库存接口响应：", string(result))
	if datas.Body.Errno == 0 {
		res.Code = 200
		res.Message = ""
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}
	return res, nil
}

// OfflineElmShopSku 批量设置饿了么商品下架 单次最多支持100个商品，用逗号隔开
// sku.offline  启用 接口限流80次/source/秒
func (s *ElmProductService) OfflineElmShopSku(params *proto.UpdateElmShopSkuPriceRequest) (*proto.ELMBaseResponse, error) {
	res := new(proto.ELMBaseResponse)

	//reserve := ELERateLimitedSkuOffline.Reserve()
	reserve := common.GetLimiter("ele.limit.sku.offline", 0).Reserve()
	time.Sleep(reserve.Delay())

	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId
	bodys["sku_id"] = params.Skuid
	bodys["upc"] = params.Upc
	bodys["custom_sku_id"] = params.CustomSkuId
	result, _ := ElmAPI("sku.offline", bodys, params.AppChannel)

	var datas dto.ResponseBoolData
	if err := json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	glog.Info("饿么商品下架接口响应：", string(result))
	if datas.Body.Errno == 0 {
		if len(datas.Body.Error) > 0 && datas.Body.Error != "success" {
			res.Code = 400
			res.Error = datas.Body.Error
		} else {
			res.Code = 200
		}
	} else {
		if datas.Body.Errno == 20502 || datas.Body.Error == "gw.QosAPIFrequencyLimit" {
			datas.Body.Error = "饿了么接口请求频率过高,请您稍后重试"
		}
		res.Code = 400
		res.Error = datas.Body.Error
	}
	return res, nil
}

// OfflineElmShopSkuOne 批量设置饿了么商品下架 单次最多支持100个商品，用逗号隔开
// sku.offline.one
func (s *ElmProductService) OfflineElmShopSkuOne(params *proto.UpdateElmShopSkuPriceRequest) (*proto.ELMBaseResponse, error) {
	res := new(proto.ELMBaseResponse)
	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId
	bodys["sku_id"] = params.Skuid
	bodys["upc"] = params.Upc
	bodys["custom_sku_id"] = params.CustomSkuId
	result, _ := ElmAPI("sku.offline.one", bodys, params.AppChannel)

	var datas dto.ResponseBoolData
	if err := json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	glog.Info("饿了么商品下架接口响应：", string(result))
	if datas.Body.Errno == 0 {
		if len(datas.Body.Error) > 0 && datas.Body.Error != "success" {
			res.Code = 400
			res.Error = datas.Body.Error
		} else {
			res.Code = 200
		}
	} else {
		if datas.Body.Errno == 20502 || datas.Body.Error == "gw.QosAPIFrequencyLimit" {
			datas.Body.Error = "饿了么接口请求频率过高,请您稍后重试"
		}
		res.Code = 400
		res.Error = datas.Body.Error
		res.Message = datas.Body.Error
	}
	return res, nil
}

// OnlineElmShopSku 批量设置饿了么商品上架 单次最多支持100个商品，用逗号隔开
// sku.online  切仓启用 接口限流50次/source/秒
func (s *ElmProductService) OnlineElmShopSku(params *proto.UpdateElmShopSkuPriceRequest) (*proto.ELMBaseResponse, error) {
	res := new(proto.ELMBaseResponse)

	//reserve := ELERateLimitedSkuOnline.Reserve()
	reserve := common.GetLimiter("ele.limit.sku.online", 0).Reserve()
	time.Sleep(reserve.Delay())

	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId
	bodys["sku_id"] = params.Skuid
	bodys["upc"] = params.Upc
	bodys["custom_sku_id"] = params.CustomSkuId
	result, _ := ElmAPI("sku.online", bodys, params.AppChannel)

	var datas dto.ResponseBoolData
	if err := json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	glog.Info("饿了么商品上架接口响应：", string(result))
	if datas.Body.Errno == 0 {
		if len(datas.Body.Error) > 0 && datas.Body.Error != "success" {
			res.Code = 400
			res.Error = datas.Body.Error
		} else {
			res.Code = 200
		}
	} else {
		// 饿了么系统繁忙,Errno = 20502 , Error = "gw.QosAPIFrequencyLimit"
		if datas.Body.Errno == 20502 || datas.Body.Error == "gw.QosAPIFrequencyLimit" {
			datas.Body.Error = "饿了么接口请求频率过高,请您稍后重试"
		}
		res.Code = 400
		res.Error = datas.Body.Error
	}
	return res, nil
}

//OnlineElmShopSkuOne 上架单个饿了么商品
//sku.online.one

func (s *ElmProductService) OnlineElmShopSkuOne(params *proto.UpdateElmShopSkuPriceRequest) (*proto.ELMBaseResponse, error) {
	res := new(proto.ELMBaseResponse)
	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId
	//bodys["sku_id"] = params.Skuid
	//bodys["upc"] = params.Upc
	bodys["custom_sku_id"] = params.CustomSkuId

	result, _ := ElmAPI("sku.online.one", bodys, params.AppChannel)

	var datas dto.ResponseInterfaceData
	if err := json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	glog.Info("饿了么商品上架接口响应：", string(result))
	if datas.Body.Errno == 0 {
		if len(datas.Body.Error) > 0 && datas.Body.Error != "success" {
			res.Code = 400
			res.Error = datas.Body.Error
			res.Message = datas.Body.Error
		} else {
			res.Code = 200
		}
	} else {
		// 饿了么系统繁忙,Errno = 20502 , Error = "gw.QosAPIFrequencyLimit"
		if datas.Body.Errno == 20502 || datas.Body.Error == "gw.QosAPIFrequencyLimit" {
			datas.Body.Error = "饿了么接口请求频率过高,请您稍后重试"
		}
		res.Code = 400
		res.Error = datas.Body.Error
		res.Message = datas.Body.Error
	}
	return res, nil
}

// UpdateElmShopSku
// 更新、新增饿了么商品信息
// sku.create 创建
// sku.update 更新
func (s *ElmProductService) UpdateElmShopSku(params *proto.UpdateElmShopSkuRequest) (*proto.ELMBaseResponse, error) {
	res := new(proto.ELMBaseResponse)
	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId
	//bodys["sku_id"] = params.SkuId
	bodys["custom_sku_id"] = params.CustomSkuId
	if params.UpdateField == "" {
		bodys["upc"] = params.Upc
		//bodys["shelvesNuml"] = params.Upc
		bodys["name"] = params.Name
		//bodys["shelf_number"] = params.ShelfNumber
		if params.BrandId > 0 {
			bodys["brand_id"] = params.BrandId
			bodys["brand_name"] = params.BrandName
		} else if params.SkuId != "" {
			bodys["brand_id"] = ""
			bodys["brand_name"] = ""
		}

		if params.CategoryName != "" {
			bodys["category_name"] = params.CategoryName
		} else if params.SkuId != "" {
			bodys["category_id"] = ""
			bodys["category_name"] = ""
		}
		if params.Desc != "" {
			bodys["desc"] = params.Desc
		} else if params.SkuId != "" {
			bodys["desc"] = ""
		}

		bodys["cat3_id"] = params.Cat3Id
		bodys["sale_price"] = params.SalePrice
		bodys["weight"] = params.Weight
		bodys["summary"] = params.Summary
		bodys["preparation_time"] = params.PreparationTime
		bodys["process_type"] = params.ProcessType
		//bodys["preminus_weight"] = params.PreminusWeight
		//bodys["minimum"] = params.Minimum
		//bodys["sale_step"] = params.SaleStep
		//bodys["sale_unit"] = params.SaleUnit
		bodys["weight_flag"] = 2 // 默认为非称重品
		bodys["status"] = params.Status
		if len(params.SkuId) == 0 || params.LeftNum > 0 {
			bodys["left_num"] = params.LeftNum
		}
		var photos []*proto.SkuPhotos
		for _, v := range params.Photos {
			var photo proto.SkuPhotos
			photo.IsMaster = v.IsMaster
			photo.Url = v.Url
			photos = append(photos, &photo)
		}
		bodys["photos"] = photos
		var process_details []*proto.ProcessDetail
		for _, v := range params.ProcessDetail {
			var process_detail proto.ProcessDetail
			process_detail.Type = v.Type
			process_detail.Time = v.Time
			process_details = append(process_details, &process_detail)
		}
		bodys["process_detail"] = process_details
		var sku_propertys []*proto.SkuProperty
		for _, v := range params.SkuProperty {
			var sku_property proto.SkuProperty
			sku_property.Name = v.Name
			var sku_details []*proto.SkuPropertyDetail
			for _, val := range v.Detail {
				var sku_detail proto.SkuPropertyDetail
				sku_detail.K = val.K
				sku_details = append(sku_details, &sku_detail)
			}
			sku_property.Detail = sku_details
			sku_propertys = append(sku_propertys, &sku_property)
		}
		bodys["sku_property"] = sku_propertys
		bodys["cat_property"] = params.CatProperty
		bodys["sku_spec"] = params.SkuSpec
	} else {
		updateFieldList := strings.Split(params.UpdateField, ",")
		for _, v := range updateFieldList {
			switch v {
			case "name":
				bodys["name"] = params.Name
			case "selling_point":
				bodys["summary"] = params.Summary
			case "pic":
				var photos []*proto.SkuPhotos
				for _, v := range params.Photos {
					var photo proto.SkuPhotos
					photo.IsMaster = v.IsMaster
					photo.Url = v.Url
					photos = append(photos, &photo)
				}
				bodys["photos"] = photos
			case "bar_code":
				bodys["upc_update"] = params.Upc
			case "market_price":
				bodys["sale_price"] = params.SalePrice
			case "content_pc":
				bodys["desc"] = params.Desc
			case "weight_for_unit":
				bodys["weight"] = params.Weight
			case "category_id":
				if params.CategoryId <= 0 {
					bodys["category_name"] = params.CategoryName
				} else {
					bodys["category_id"] = params.CategoryId
				}
			case "cat3_id":
				bodys["cat3_id"] = params.Cat3Id
			case "stock":
				bodys["left_num"] = params.LeftNum
			case "status":
				bodys["status"] = params.Status
			case "cat_property":
				bodys["cat_property"] = params.CatProperty
			case "sku_spec":
				bodys["sku_spec"] = params.SkuSpec
			}
		}
	}
	var cmd string
	if params.SkuId == "" {
		cmd = "sku.create"
	} else {
		cmd = "sku.update"
	}
	result, _ := ElmAPI(cmd, bodys, params.AppChannel)

	var datas dto.ResponseData
	if err := json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口,返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	glog.Info("饿了么接口,更新商品接口响应：", string(result))
	if datas.Body.Errno == 0 {
		if len(datas.Body.Error) > 0 && datas.Body.Error != "success" {
			res.Code = 400
			res.Error = datas.Body.Error
		} else {
			res.Code = 200
			if res.Data == nil {
				res.Data = &proto.Body{}
			}
			if res.Data.Data == nil {
				res.Data.Data = make([]*proto.ResultListVo, 0)
			}
			for key, value := range datas.Body.Data {
				switch key {
				case "sku_id":
					resData := &proto.ResultListVo{
						SkuId: cast.ToString(value),
					}
					res.Data.Data = append(res.Data.Data, resData)
				}
			}

		}
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}
	return res, nil
}

// UploadPicture 公共上传图片
// picture.upload
func (s *ElmProductService) UploadPicture(params *proto.UploadPictureRequest) (*proto.DelElmShopCategoryResponse, error) {
	res := new(proto.DelElmShopCategoryResponse)
	bodys := make(map[string]interface{})
	bodys["data"] = params.Data
	bodys["url"] = params.Url
	bodys["type"] = params.Type

	redisConn := cache.GetRedisConn()
	get := redisConn.HGet("eleMePic", params.Url)
	pic, err := get.Result()
	if err == nil && len(pic) > 0 {
		res.Code = 200
		res.Data = pic
		return res, nil
	}
	//rate := LimiterImportantRate(limiterElePictureUploadProductRate, ElePictureUpload)
	//if !rate {
	//	glog.Error(" 饿了么公共上传图片限流： ", rate)
	//	res.Code = 400
	//	return res, errors.New("饿了么公共上传图片限流,稍后再试")
	//}
	reserve := common.GetLimiter("ele.retail.picture.upload", 10).Reserve()
	time.Sleep(reserve.Delay())

	result, _ := ElmAPI("picture.upload", bodys, params.AppChannel)

	var datas dto.ResponseData
	if err := json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	glog.Info("饿了么上传图片响应参数：", datas)
	if datas.Body.Errno == 0 {
		var returnData = datas.Body.Data
		res.Code = 200
		if _, ok := returnData["url"].(string); ok {
			res.Data = returnData["url"].(string)
			redisConn.HSet("eleMePic", params.Url, res.Data)
		}
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}
	return res, nil
}

// UploadPictureRTF 公共上传富文本
// sku.uploadrtf
func (s *ElmProductService) UploadPictureRTF(params *proto.UploadPictureRequest) (*proto.DelElmShopCategoryResponse, error) {
	res := new(proto.DelElmShopCategoryResponse)
	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId
	bodys["rtf_detail"] = params.Data

	//限流
	//rate := LimiterImportantRate(limiterEleEleSkuUpoadRtfRate, EleSkuUpoadRtf)
	//if !rate {
	//	glog.Error(" 饿了么公共上传富文本限流： ", rate)
	//	res.Code = 400
	//	return res, errors.New(" 饿了么公共上传富文本限流,稍后再试")
	//}
	reserve := common.GetLimiter("me.ele.retail.sku.uploadrtf", 200).Reserve()
	time.Sleep(reserve.Delay())

	result, _ := ElmAPI("sku.uploadrtf", bodys, params.AppChannel)

	var datas dto.ResponseData
	if err := json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	glog.Info("饿了么上传富文本响应参数：", datas)
	if datas.Body.Errno == 0 {
		var returnData = datas.Body.Data
		res.Code = 200
		if _, ok := returnData["url"].(string); ok {
			res.Data = returnData["url"].(string)
		}
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}
	return res, nil
}

// SkuPriceUpdateOne
// 单个修改商品价格
// sku.price.update.one
func (s *ElmProductService) SkuPriceUpdateOne(in *proto.SkuPriceUpdateOneRequest) (*proto.ELMBaseResponse, error) {

	glog.Info("饿了么价格请求参数：", in)
	res := new(proto.ELMBaseResponse)
	cmd := "sku.price.update.one"
	bodys := make(map[string]interface{}, 5)
	bodys["shop_id"] = in.ShopId
	bodys["custom_sku_id"] = in.SkuidPrice
	result, err := ElmAPI(cmd, bodys, in.AppChannel)
	if err != nil {
		res.Code = 400
		res.Message = "价格同步请求饿了么接口失败"
		res.Error = err.Error()
		glog.Error("价格同步请求饿了么接口失败", err)
		return res, err
	}
	var datas dto.ResponseBoolData
	if err = json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	glog.Info("饿了么价格响应参数：", datas)
	if datas.Body.Errno == 0 {
		if len(datas.Body.Error) > 0 && datas.Body.Error != "success" {
			res.Code = 400
			res.Error = datas.Body.Error
		} else {
			res.Code = 200
		}
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}
	return res, nil
}

// GetElmProductList 获取饿了么商品列表
// sku.list
func (s *ElmProductService) GetElmProductList(params *proto.ElmGetProductListRequest) (*proto.ElmGetProductListResponse, error) {
	glog.Info("获取饿了么商品列表请求参数: ", params)

	//reserve := ELERateLimitedSkuList.Reserve()
	reserve := common.GetLimiter("ele.limit.sku.list", 0).Reserve()
	time.Sleep(reserve.Delay())

	out := new(proto.ElmGetProductListResponse)
	bodys := make(map[string]interface{})

	bodys["shop_id"] = params.ShopId
	bodys["page"] = params.Page
	bodys["pagesize"] = params.Pagesize
	if len(params.Upc) > 0 {
		bodys["upc"] = params.Upc
	}
	if len(params.CustomSkuId) > 0 {
		bodys["custom_sku_id"] = params.CustomSkuId
	}
	if params.SkuIdOffset == -1 {
		bodys["sku_id_offset"] = 0
	}
	if params.SkuIdOffset > 0 {
		bodys["sku_id_offset"] = params.SkuIdOffset
	}
	//bodys["sku_id"] = params.SkuId
	//bodys["upc_type"] = params.UpcType
	//bodys["get_uncate"] = params.GetUncate
	//bodys["delete"] = params.Delete
	//bodys["enabled"] = params.Enabled
	//bodys["start_time"] = params.StartTime
	//bodys["end_time"] = params.EndTime
	//bodys["sku_id_offset"] = params.SkuIdOffset
	bodys["include_cate_info"] = params.IncludeCateInfo
	//bodys["platform_shop_id"] = params.PlatformShopId

	result, _ := ElmAPI("sku.list", bodys, params.AppChannel)

	var resp dto.ResponseSkuListData
	if err := json.Unmarshal(result, &resp); err != nil {
		out.Code = 400
		out.Message = "饿了么接口返回数据反解析失败"
		out.Error = err.Error()
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
		return out, err
	}

	glog.Info("饿了么商品列表接口响应：", string(result))
	if errnoString, ok := resp.Body.Errno.(string); ok {
		if errnoString != "0" {
			out.Code = 400
			out.Error = resp.Body.Error
		}
	} else if errnoint, ok := resp.Body.Errno.(int); ok {
		if errnoint != 0 {
			out.Code = 400
			out.Error = resp.Body.Error
		}
	} else {
		out.Code = 200
		out.Error = resp.Body.Error

		var data = &proto.ElmProductListDto{
			Page:        resp.Body.Data.Page,
			Pages:       resp.Body.Data.Pages,
			Total:       resp.Body.Data.Total,
			SkuIdOffset: resp.Body.Data.SkuIdOffset,
		}

		data.List = append(data.List, resp.Body.Data.List...)
		out.Data = data
	}
	return out, nil
}

//-------------------以下为暂时没用的方法--------------

// UpdateElmShopSkuPrice 批量修改饿了么商品价格 单次最多支持100个商品，用分号隔开 001:10;002:20;
// 暂时没用
func (s *ElmProductService) UpdateElmShopSkuPrice(params *proto.UpdateElmShopSkuPriceRequest) (*proto.ELMBaseResponse, error) {
	res := new(proto.ELMBaseResponse)
	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId
	bodys["skuid_price"] = params.Skuid
	bodys["upc_price"] = params.Upc
	bodys["custom_sku_id"] = params.CustomSkuId
	result, err := ElmAPI("sku.price.update.batch", bodys, params.AppChannel)
	if err != nil {
		res.Code = 400
		res.Message = "请求饿了么出错"
		res.Error = err.Error()
		return res, err
	}
	var datas dto.ResponseDatas
	if err = json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	glog.Info("饿了么商品修改价格接口响应：", string(result))
	if datas.Body.Errno == 0 {
		res.Code = 200
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}
	return res, nil
}

//DeleteElmShopSku 删除饿了么商品信息/eleme只有批量的没有个的删除接口
/*
https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.delete-3?aopApiCategory=item_all&type=api_menu 接口限流20次/source/秒
	批量删除商品，单次最多支持100个商品。 由于删除为异步操作，不可删除后立刻重新创建同条码商品。
	建议可以修改原商品的场景尽量用修改而不是删除。 如果删除后不需要重新创建的场景，建议用删除接口。
*/
func (s *ElmProductService) DeleteElmShopSku(params *proto.UpdateElmShopSkuPriceRequest) (*proto.ELMBaseResponse, error) {
	glog.Info("DeleteElmShopSku入参： ", utils.JsonEncode(params))

	res := new(proto.ELMBaseResponse)
	////新限流
	//rate := LimiterImportantRate(limiterEleDeleteProductRate, EleSkuDelete)
	//if !rate {
	//	glog.Error(" 删除饿了么商品限流： ", rate)
	//	res.Code = 400
	//	return res, errors.New("删除饿了么商品限流")
	//}
	reserve := common.GetLimiter("ele.sku.delete", 20).Reserve()
	time.Sleep(reserve.Delay())

	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId
	if len(params.CustomSkuId) > 0 {
		bodys["custom_sku_id"] = params.CustomSkuId
	}
	if len(params.Skuid) > 0 {
		bodys["sku_id"] = params.Skuid
	}
	result, err := ElmAPI("sku.delete", bodys, params.AppChannel)
	if err != nil {
		res.Code = 400
		res.Message = "请求饿了么出错"
		res.Error = err.Error()
		return res, err
	}
	var datas dto.EleDeleteResponse
	if err := json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	if datas.Body.Errno == 0 {
		res.Code = 200
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}
	return res, nil
}

// GetElmShopBrand 获取饿了么品牌列表
// 暂时没用
func (s *ElmProductService) GetElmShopBrand(params *proto.GetElmShopBrandRequest) (*proto.GetElmShopBrandResponse, error) {
	res := new(proto.GetElmShopBrandResponse)
	bodys := make(map[string]interface{})
	bodys["cat3_id"] = params.Cat3Id
	bodys["keyword"] = params.Keyword
	bodys["page"] = params.Page
	result, err := ElmAPI("sku.brand.list", bodys, params.AppChannel)
	if err != nil {
		res.Code = 400
		res.Message = "请求饿了么出错"
		res.Error = err.Error()
		return res, nil
	}
	var datas dto.ResponseSkuBrandData
	if err = json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	glog.Info("饿了么商品获取品牌接口响应：", string(result))
	if datas.Body.Errno == 0 {
		res.Code = 200
		var reurnData proto.ShopBrandData
		reurnData.Count = datas.Body.Data.Count
		reurnData.PageNum = datas.Body.Data.PageNum
		reurnData.MaxPageNum = datas.Body.Data.MaxPageNum
		var shopBrandList []*proto.ShopBrandList
		if len(datas.Body.Data.Detail) > 0 {
			for _, v := range datas.Body.Data.Detail {
				var shopBrand proto.ShopBrandList
				shopBrand.BrandId = v.BrandId
				shopBrand.BrandName = v.BrandName
				shopBrandList = append(shopBrandList, &shopBrand)
			}
			reurnData.Detail = shopBrandList
		}
		res.Data = append(res.Data, &reurnData)
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}
	return res, nil
}

// BatchUpdateElmShopSku
// 批量更新饿了么商品信息
func (s *ElmProductService) BatchUpdateElmShopSku(params *proto.BatchUpdateElmShopSkuRequest) (*proto.ELMBaseResponse, error) {

	glog.Info("批量更新饿了么商品信息入参：", utils.JsonEncode(params))

	//r := ELERateLimitedBatchSkuUpdate.Reserve()
	reserve := common.GetLimiter("ele.limit.sku.batch.update", 0).Reserve()
	time.Sleep(reserve.Delay())

	res := new(proto.ELMBaseResponse)
	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId

	bodys["update_list"] = params.UpdateList

	var cmd string = "batch.sku.update"

	result, _ := ElmAPI(cmd, bodys, params.AppChannel)

	var datas dto.ResponseData
	if err := json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}
	glog.Info("更新饿了么商品接口响应：", string(result))

	if len(datas.Body.Data) > 0 {
		if data, ok := datas.Body.Data["result_list"]; ok {
			marshal, err := json.Marshal(data)
			if err != nil {
				glog.Error("解析ele批量更新数据异常", err.Error())
			}
			outData := &proto.Body{
				Errno: datas.Body.Errno,
				Error: datas.Body.Error,
				Data:  []*proto.ResultListVo{},
			}

			err = json.Unmarshal(marshal, &outData.Data)
			if err != nil {
				glog.Error("解析ele批量更新数据返回异常", err.Error())
			}
			res.Data = outData
		}

	}
	if datas.Body.Errno == 0 {
		if len(datas.Body.Error) > 0 && datas.Body.Error != "success" {
			res.Code = 400
			res.Error = datas.Body.Error
		} else {
			res.Code = 200
		}
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}
	glog.Info("更新饿了么商品接口返回：", utils.JsonEncode(res))
	return res, nil
}

//https://open-retail.ele.me/#/apidoc/me.ele.retail:batch.sku.update-3?aopApiCategory=item_all&type=api_menu
/*
接口限流5次/source/秒
*/
func (s *ElmProductService) BatchCreateElmShopSku(params *proto.BatchCreateElmShopSkuRequest) (*proto.ELMBaseResponse, error) {

	glog.Info("批量创建饿了么商品信息入参：", utils.JsonEncode(params))

	//r := ELERateLimitedBatchSkuUCreate.Reserve()
	reserve := common.GetLimiter("ele.limit.sku.batch.create", 0).Reserve()
	time.Sleep(reserve.Delay())

	res := new(proto.ELMBaseResponse)
	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId

	bodys["create_list"] = params.UpdateList

	var cmd string = "batch.sku.create"

	result, _ := ElmAPI(cmd, bodys, params.AppChannel)

	var datas dto.ResponseData
	if err := json.Unmarshal(result, &datas); err != nil {
		glog.Error("批量创建饿了么商品信息", utils.RunFuncName(), err.Error(), result)
	}
	glog.Info("批量创建饿了么商品信息返回：", string(result))

	if len(datas.Body.Data) > 0 {
		if data, ok := datas.Body.Data["result_list"]; ok {
			marshal, err := json.Marshal(data)
			if err != nil {
				glog.Error("解析ele批量创建数据异常", err.Error())
			}
			outData := &proto.Body{
				Errno: datas.Body.Errno,
				Error: datas.Body.Error,
				Data:  []*proto.ResultListVo{},
			}

			err = json.Unmarshal(marshal, &outData.Data)
			if err != nil {
				glog.Error("解析ele批量创建数据返回异常", err.Error())
			}
			res.Data = outData
		}

	}
	if datas.Body.Errno == 0 {
		if len(datas.Body.Error) > 0 && datas.Body.Error != "success" {
			res.Code = 400
			res.Error = datas.Body.Error
		} else {
			res.Code = 200
		}
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}
	glog.Info("批量创建饿了么商品接口返回：", utils.JsonEncode(res))
	return res, nil
}

// GetElmShopCategory 获取饿了么店铺分类列表
// sku.shop.category.get 接口限流20次/source/秒
func (s *ElmProductService) GetElmShopCategory(params *product_po.GetElmShopCategoryRequest) (*product_po.GetElmShopCategoryResponse, error) {
	// 使用限流器
	reserve := common.GetLimiter("ele.limit.sku.shop.category.get", 0).Reserve()
	time.Sleep(reserve.Delay())

	res := new(product_po.GetElmShopCategoryResponse)
	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId

	result, _ := ElmAPI("sku.shop.category.get", bodys, params.AppChannel)

	var datas dto.ResponseData
	if err := json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
		res.Code = 400
		res.Error = err.Error()
		return res, err
	}

	glog.Info("饿了么获取店铺分类列表接口响应：", string(result))

	if datas.Body.Errno == 0 {
		res.Code = 200
		if categoryList, ok := datas.Body.Data["categorys"].([]interface{}); ok {
			for _, item := range categoryList {
				if category, ok := item.(map[string]interface{}); ok {
					categoryItem := &product_po.ShopCategory{
						CategoryId:   cast.ToString(category["category_id"]),
						CategoryName: cast.ToString(category["name"]),
						ShopCustomId: cast.ToString(category["shop_custom_id"]),
						Rank:         cast.ToString(category["rank"]),
					}
					res.Data = append(res.Data, categoryItem)
				}
			}
		}
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}

	return res, nil
}

// ElmClose 设置饿了么商户营业状态休息
func (s *ElmProductService) ElmClose(params *proto.ElmBusStatusSetRequest) (*proto.ElmBusStatusSetResponse, error) {
	resp := new(proto.ElmBusStatusSetResponse)
	bodys := make(map[string]interface{})
	bodys["shop_id"] = params.ShopId
	bodys["baidu_shop_id"] = params.ShopId
	appChannel, _ := GetAppChannelByFinanceCodeStr(params.FinanceCode)
	result, _ := ElmAPI("shop.close", bodys, appChannel.AppChannel)

	var ret dto.AutoGenerated1
	//	ret.Body = new(proto.ElmBusStatusSetResponse)
	if err := json.Unmarshal(result, &ret); err != nil {
		glog.Error("设置饿了么商户营业状态休息-接口返回数据反解析失败", err.Error(), result)
		return resp, errors.New("设置饿了么商户营业状态休息-接口返回数据反解析失败")
	}
	//if resp, ok := ret.Body.(*proto.ElmBusStatusSetResponse); ok {
	resp.Errno = cast.ToInt32(ret.Body.Errno)
	resp.Error = ret.Body.Error
	if resp.Errno == 0 || resp.Error == "商户都已经停止营业" {
		return resp, nil
	}

	glog.Error("设置饿了么商户营业状态休息-接口返回数据返回业务失败：error=" + resp.Error)
	return resp, errors.New("设置饿了么商户营业状态休息失败")
	//}
}

// GetElmCategoryPropertyList 获取饿了么商品属性列表
func (s *ElmProductService) GetElmCategoryPropertyList(req *dto.ElmCategoryPropertyListRequest) (*dto.ElmCategoryPropertyListResponse, error) {
	s.Begin()
	defer s.Close()
	result := &dto.ElmCategoryPropertyListResponse{
		Code: 200,
	}
	ChannelStoreId, AppChannel, state := common.GetChannelInfoByStoreId(s.Engine, config.Get("elm_store_id"), Channel_ELM)
	if state == false {
		return result, errors.New("门店信息未设置")
	}
	if ChannelStoreId == "" {
		return result, errors.New("门店信息未设置")
	}
	// 构建请求参数
	bodys := make(map[string]interface{})
	bodys["shop_id"] = ChannelStoreId
	if req.CategoryId != "" {
		bodys["cat3_id"] = req.CategoryId
	}
	// 调用饿了么API
	resultBytes, err := ElmAPI("sku.category.property.list", bodys, int32(AppChannel))
	if err != nil {
		result.Code = 400
		result.Message = "请求饿了么接口失败:" + err.Error()
		return result, err
	}

	// 解析返回结果
	var resp dto.ResponsePropertyListData
	if err = json.Unmarshal(resultBytes, &resp); err != nil {
		result.Code = 400
		result.Message = "解析饿了么返回数据失败:" + err.Error()
		return result, err
	}

	// 处理错误情况
	if resp.Body.Errno != 0 {
		result.Code = 400
		result.Message = resp.Body.Error
		return result, errors.New(resp.Body.Error)
	}

	// 转换属性数据
	result.Data = make([]dto.PropertyInfo, 0)
	for _, prop := range resp.Body.Data {
		if prop.PropertyName == "规格" {
			continue
		}
		// 只处理枚举属性且有属性值的情况
		//if !prop.InputProp && len(prop.PropertyValues) > 0 {
		propertyInfo := dto.PropertyInfo{
			PropertyId:   prop.PropertyId,
			PropertyName: prop.PropertyName,
			Required:     prop.Required,
			EnumProp:     prop.EnumProp,
			SortOrder:    prop.SortOrder,
			MajorProp:    prop.MajorProp,
			InputProp:    prop.InputProp,
			CategoryId:   prop.CategoryId,
			SaleProp:     prop.SaleProp,
			MultiSelect:  prop.MultiSelect,
			Values:       make([]dto.PropertyValue, 0),
		}

		// 转换属性值列表
		for _, val := range prop.PropertyValues {
			propertyInfo.Values = append(propertyInfo.Values, dto.PropertyValue{
				ValueId:   val.ValueId,
				ValueName: val.ValueData,
			})
		}

		result.Data = append(result.Data, propertyInfo)
	}
	//}

	return result, nil
}

// UpdateSkuStock 更新商品库存
// 接口文档: https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.spec.stock.update-3
func (s *ElmProductService) UpdateSkuSpecStock(in *proto.UpdateSkuStockRequest) (*proto.ELMBaseResponse, error) {
	glog.Info("饿了么库存更新请求参数：", in)
	res := new(proto.ELMBaseResponse)

	// 构建请求参数
	bodys := make(map[string]interface{}, 3)
	bodys["shop_id"] = in.ShopId
	bodys["sku_stock"] = 0
	bodys["custom_sku_id"] = in.CustomSkuId
	bodys["spec_stock_list"] = in.SpecStockList

	// 调用饿了么开放平台接口
	result, err := ElmAPI("sku.spec.stock.update", bodys, in.AppChannel)
	if err != nil {
		glog.Error("库存同步请求饿了么接口失败", err)
		res.Code = 400
		res.Message = "库存同步请求饿了么接口失败"
		res.Error = err.Error()
		return res, err
	}

	var datas dto.ResponseBoolData
	if err = json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}

	glog.Info("饿了么库存更新响应参数：", datas)
	if datas.Body.Errno == 0 {
		if len(datas.Body.Error) > 0 && datas.Body.Error != "success" {
			res.Code = 400
			res.Error = datas.Body.Error
		} else {
			res.Code = 200
		}
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}

	return res, nil
}

// UpdateSkuPrice 更新商品价格
// 接口文档: https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.spec.update.price-3
func (s *ElmProductService) UpdateSpecSkuPrice(in *proto.UpdateSkuPriceRequest) (*proto.ELMBaseResponse, error) {
	glog.Info("饿了么价格更新请求参数：", in)
	res := new(proto.ELMBaseResponse)

	// 构建请求参数
	bodys := make(map[string]interface{}, 3)
	bodys["shop_id"] = in.ShopId
	bodys["custom_sku_id"] = in.CustomSkuId // 格式: "sku_id:销售价格,市场价格"
	bodys["sku_spec_price_list"] = in.SkuSpecPriceList

	// 调用饿了么开放平台接口
	result, err := ElmAPI("sku.spec.update.price", bodys, in.AppChannel)
	if err != nil {
		glog.Error("价格同步请求饿了么接口失败", err)
		res.Code = 400
		res.Message = "价格同步请求饿了么接口失败"
		res.Error = err.Error()
		return res, err
	}

	var datas dto.ResponseBoolData
	if err = json.Unmarshal(result, &datas); err != nil {
		glog.Error("饿了么接口返回数据反解析失败", utils.RunFuncName(), err.Error(), result)
	}

	glog.Info("饿了么价格更新响应参数：", datas)
	if datas.Body.Errno == 0 {
		if len(datas.Body.Error) > 0 && datas.Body.Error != "success" {
			res.Code = 400
			res.Error = datas.Body.Error
		} else {
			res.Code = 200
		}
	} else {
		res.Code = 400
		res.Error = datas.Body.Error
	}

	return res, nil
}
