package services

import (
	blky_po "eShop/domain/blky-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	external_vo "eShop/view-model/external-vo"
	"fmt"
	"time"
)

const batchSize = 200 // 每批次处理的记录数

// SjanService 世纪安诺服务
type SjanService struct {
	common.BaseService
}

// AddProducts 批量添加产品信息
// 参数 products: 产品信息数组
// 返回值：处理结果错误信息
func (s *SjanService) AddProducts(products []external_vo.ProductInfo) error {
	s.<PERSON>gin()
	defer s.Close()

	// 检查记录数据
	if len(products) == 0 {
		return fmt.Errorf("产品信息数据不能为空")
	}

	successCount := 0
	failedCount := 0

	// 批量处理相关变量
	const batchSize = 200                            // 每批次处理的记录数
	newProducts := make([]interface{}, 0, batchSize) // 待插入的新产品集合

	// 处理每一条产品记录
	for i, product := range products {
		// 检查必填字段
		if product.PNo == "" || product.Pname == "" {
			failedCount++
			log.Errorf("产品数据缺少必填字段: %+v", product)
			continue
		}

		// 检查产品编号是否已存在
		existProduct := &blky_po.SjanProduct{}
		has, err := s.Session.Where("p_no = ?", product.PNo).Get(existProduct)
		if err != nil {
			failedCount++
			log.Errorf("查询产品失败: %s, 错误: %v", product.PNo, err)
			continue
		}

		if has {
			// 产品已存在，执行更新
			existProduct.Pname = product.Pname
			existProduct.Ptype = product.Ptype
			existProduct.UnitNum = product.UnitNum
			existProduct.UpdateTime = time.Now()

			_, err := s.Session.ID(existProduct.Id).Update(existProduct)
			if err != nil {
				failedCount++
				log.Errorf("更新产品信息失败: %s, 错误: %v", product.PNo, err)
				continue
			}
			log.Infof("更新产品信息成功: %s", product.PNo)
			successCount++
		} else {
			// 产品不存在，加入批量插入队列
			sjanProduct := &blky_po.SjanProduct{
				PNo:        product.PNo,
				Pname:      product.Pname,
				Ptype:      product.Ptype,
				UnitNum:    product.UnitNum,
				CreateTime: time.Now(),
				UpdateTime: time.Now(),
			}

			newProducts = append(newProducts, sjanProduct)

			// 当累计到批处理大小或处理到最后一条记录时，执行批量插入
			if len(newProducts) >= batchSize || i == len(products)-1 {
				if len(newProducts) > 0 {
					affected, err := s.Session.Insert(newProducts...)
					if err != nil {
						// 批量插入失败，记录错误但继续处理
						log.Errorf("批量添加产品信息失败，批次大小: %d, 错误: %v", len(newProducts), err)
						failedCount += len(newProducts)
					} else {
						// 批量插入成功
						successCount += int(affected)
						log.Infof("批量添加产品信息成功，本批次处理: %d 条", affected)
					}

					// 清空待处理队列，准备下一批次
					newProducts = make([]interface{}, 0, batchSize)
				}
			}
		}
	}

	// 提交事务
	if successCount > 0 {
		s.Session.Commit()
		log.Infof("批量处理产品信息完成，成功：%d，失败：%d", successCount, failedCount)
		return nil
	} else {
		log.Errorf("批量处理产品信息失败，所有记录处理失败")
		return fmt.Errorf("添加产品信息失败：所有记录处理失败")
	}
}

// AddAgents 添加代理商信息
// 参数 agents: 代理商信息数组
// 返回值：错误信息(包含成功和失败数量)
func (s *SjanService) AddAgents(agents []external_vo.AgentInfo) error {
	s.Begin()
	defer s.Close()

	// 检查记录数据
	if len(agents) == 0 {
		return fmt.Errorf("代理商信息数据不能为空")
	}

	successCount := 0
	failedCount := 0

	// 批量处理相关变量
	newAgents := make([]interface{}, 0, batchSize) // 待插入的新代理商集合

	// 处理每一条代理商记录
	for i, agent := range agents {
		// 检查必填字段
		if agent.CuNo == "" || agent.CuName == "" {
			failedCount++
			log.Errorf("代理商数据缺少必填字段: %+v", agent)
			continue
		}

		// 检查代理商编号是否已存在
		existAgent := &blky_po.SjanAgent{}
		has, err := s.Session.Where("cu_no = ?", agent.CuNo).Get(existAgent)
		if err != nil {
			failedCount++
			log.Errorf("查询代理商失败: %s, 错误: %v", agent.CuNo, err)
			continue
		}

		if has {
			// 代理商已存在，执行更新
			existAgent.CuName = agent.CuName
			existAgent.Ctype = agent.Ctype
			existAgent.Phone = agent.Phone
			existAgent.Province = agent.Province
			existAgent.City = agent.City
			existAgent.Address = agent.Address
			existAgent.UpdateTime = time.Now()

			_, err := s.Session.ID(existAgent.Id).Update(existAgent)
			if err != nil {
				failedCount++
				log.Errorf("更新代理商信息失败: %s, 错误: %v", agent.CuNo, err)
				continue
			}
			successCount++
		} else {
			// 代理商不存在，加入批量插入队列
			sjanAgent := &blky_po.SjanAgent{
				CuNo:       agent.CuNo,
				CuName:     agent.CuName,
				Ctype:      agent.Ctype,
				Phone:      agent.Phone,
				Province:   agent.Province,
				City:       agent.City,
				Address:    agent.Address,
				CreateTime: time.Now(),
				UpdateTime: time.Now(),
			}

			newAgents = append(newAgents, sjanAgent)

			// 当累计到批处理大小或处理到最后一条记录时，执行批量插入
			if len(newAgents) >= batchSize || i == len(agents)-1 {
				if len(newAgents) > 0 {
					affected, err := s.Session.Insert(newAgents...)
					if err != nil {
						// 批量插入失败，记录错误但继续处理
						log.Errorf("批量添加代理商信息失败，批次大小: %d, 错误: %v", len(newAgents), err)
						failedCount += len(newAgents)
					} else {
						// 批量插入成功
						successCount += int(affected)
						log.Infof("批量添加代理商信息成功，本批次处理: %d 条", affected)
					}
					newAgents = make([]interface{}, 0, batchSize)
				}
			}
		}
	}

	// 提交事务
	if successCount > 0 {
		s.Session.Commit()
		log.Infof("批量处理代理商信息完成，成功：%d，失败：%d", successCount, failedCount)
		if failedCount > 0 {
			return fmt.Errorf("添加代理商信息部分失败：成功 %d 条，失败 %d 条", successCount, failedCount)
		}
		return nil
	} else {
		log.Errorf("批量处理代理商信息失败，所有记录处理失败")
		return fmt.Errorf("添加代理商信息失败：所有记录处理失败,条数：%d", failedCount)
	}
}

// AddPackagingTask 添加包装任务及明细
// 参数 task: 包装任务请求
func (s *SjanService) AddPackagingTask(task external_vo.PackagingTaskRequest) error {
	s.Begin()
	defer s.Close()

	// 检查必填字段
	if task.BillNo == "" || task.PNo == "" || task.Pname == "" {
		return fmt.Errorf("缺少必要参数：任务单号、产品编号和产品名称为必填")
	}

	// 检查数据
	// if len(task.Data) == 0 {
	// 	return fmt.Errorf("包装任务明细数据不能为空")
	// }

	// 时间转换
	taskInDate, err := time.ParseInLocation(
		"2006-01-02 15:04:05",
		task.InDate,
		utils.ChinaLocation,
	)
	if err != nil {
		return fmt.Errorf("任务开始时间格式错误：%v", err)
	}

	// 1. 检查任务是否已存在
	existTask := &blky_po.SjanPackagingTask{}
	has, err := s.Session.Where("bill_no = ?", task.BillNo).Get(existTask)
	if err != nil {
		return fmt.Errorf("查询任务失败: %v", err)
	}

	// 如果任务已存在，执行更新操作
	if has {
		// 更新任务信息
		existTask.InDate = taskInDate
		existTask.PNo = task.PNo
		existTask.Pname = task.Pname
		existTask.UnitNum = task.UnitNum

		_, err = s.Session.ID(existTask.Id).Update(existTask)
		if err != nil {
			return fmt.Errorf("更新包装任务失败: %v", err)
		}

		// 删除原有的明细记录
		_, err = s.Session.Where("bill_no = ?", task.BillNo).Delete(&blky_po.SjanPackagingDetail{})
		if err != nil {
			return fmt.Errorf("删除原包装明细失败: %v", err)
		}
	} else {
		// 2. 保存新任务信息
		packagingTask := &blky_po.SjanPackagingTask{
			BillNo:     task.BillNo,
			InDate:     taskInDate,
			PNo:        task.PNo,
			Pname:      task.Pname,
			UnitNum:    task.UnitNum,
			CreateTime: time.Now(),
		}

		_, err = s.Session.Insert(packagingTask)
		if err != nil {
			return fmt.Errorf("添加包装任务失败: %v", err)
		}
	}

	// 预处理阶段：构建所需的数据结构和索引
	successCount := 0
	failedCount := 0

	// 1. 构建条码映射
	barcodeMap := make(map[string]string)                          // 条码到箱码的映射
	ubarcodeBarcodeMap := make(map[string][]string)                // 箱码到条码列表的映射
	detailsMap := make(map[string]external_vo.PackagingDetailItem) // 条码信息映射

	// 2. 批量处理相关变量
	packagingDetails := make([]interface{}, 0, batchSize) // 待插入的明细记录集合

	// 3. 将详情保存到映射中便于后续处理
	for _, detail := range task.Data {
		if detail.Barcode == "" || detail.Ubarcode == "" {
			failedCount++
			log.Errorf("明细数据缺少条码信息: %+v", detail)
			continue
		}

		barcodeMap[detail.Barcode] = detail.Ubarcode
		if _, ok := ubarcodeBarcodeMap[detail.Ubarcode]; !ok {
			ubarcodeBarcodeMap[detail.Ubarcode] = make([]string, 0)
		}
		ubarcodeBarcodeMap[detail.Ubarcode] = append(ubarcodeBarcodeMap[detail.Ubarcode], detail.Barcode)
		detailsMap[detail.Barcode] = detail
	}

	// 4. 收集所有条码和箱码，用于批量查询出库记录
	allCodes := make([]string, 0, len(barcodeMap)*2)
	for barcode, ubarcode := range barcodeMap {
		allCodes = append(allCodes, barcode)
		allCodes = append(allCodes, ubarcode)
	}

	// 5. 批量查询相关的出库记录
	outboundDetails := make([]blky_po.SjanOutboundDetail, 0)
	if len(allCodes) > 0 {
		// 分批查询，每批最多1000个条码
		batchSize := 1000
		for i := 0; i < len(allCodes); i += batchSize {
			end := i + batchSize
			if end > len(allCodes) {
				end = len(allCodes)
			}
			batchCodes := allCodes[i:end]

			tempDetails := make([]blky_po.SjanOutboundDetail, 0)
			err = s.Session.In("barcode", batchCodes).Find(&tempDetails)
			if err != nil {
				log.Errorf("批量查询出库记录失败: %v", err)
				continue
			}
			outboundDetails = append(outboundDetails, tempDetails...)
		}
	}

	// 6. 构建出库记录映射
	outboundMap := make(map[string]blky_po.SjanOutboundDetail)
	for _, outbound := range outboundDetails {
		outboundMap[outbound.Barcode] = outbound
	}

	// 7. 批量查询现有的sync_outbound_log记录
	existingSyncLogs := make([]blky_po.SyncOutboundLog, 0)
	billNos := make([]string, 0)
	for _, outbound := range outboundDetails {
		billNos = append(billNos, outbound.BillNo)
	}

	if len(billNos) > 0 {
		// 去重
		uniqueBillNos := make([]string, 0)
		billNoMap := make(map[string]bool)
		for _, billNo := range billNos {
			if _, exists := billNoMap[billNo]; !exists {
				billNoMap[billNo] = true
				uniqueBillNos = append(uniqueBillNos, billNo)
			}
		}

		// 分批查询
		for i := 0; i < len(uniqueBillNos); i += 1000 {
			end := i + 1000
			if end > len(uniqueBillNos) {
				end = len(uniqueBillNos)
			}
			batchBillNos := uniqueBillNos[i:end]

			tempLogs := make([]blky_po.SyncOutboundLog, 0)
			err = s.Session.In("bill_no", batchBillNos).Find(&tempLogs)
			if err != nil {
				log.Errorf("批量查询出库日志记录失败: %v", err)
				continue
			}
			existingSyncLogs = append(existingSyncLogs, tempLogs...)
		}
	}

	// 8. 构建现有日志记录映射
	syncLogMap := make(map[string]map[string]blky_po.SyncOutboundLog) // ubarcode -> barcode -> log
	for _, log := range existingSyncLogs {
		if _, ok := syncLogMap[log.SourceBarcode]; !ok {
			syncLogMap[log.SourceBarcode] = make(map[string]blky_po.SyncOutboundLog)
		}
		syncLogMap[log.SourceBarcode][log.Barcode] = log
	}

	// 9. 准备要新增、更新和删除的记录
	syncLogsToInsert := make([]interface{}, 0, batchSize)
	syncLogsToUpdate := make([]interface{}, 0, batchSize)
	syncLogIdsToDelete := make([]int64, 0)

	// 第一阶段：处理包装明细数据
	now := time.Now()
	for _, detail := range task.Data {
		if detail.Barcode == "" || detail.Ubarcode == "" {
			continue // 已经在预处理阶段处理过错误
		}

		// 转换明细采集时间
		detailInDate, err := time.ParseInLocation(
			"2006-01-02 15:04:05",
			detail.InDate,
			utils.ChinaLocation,
		)
		if err != nil {
			log.Errorf("明细采集时间格式错误: %v, 使用当前时间代替", err)
			detailInDate = now
		}

		// 创建明细记录
		packagingDetail := &blky_po.SjanPackagingDetail{
			BillNo:     task.BillNo,
			Barcode:    detail.Barcode,
			Ubarcode:   detail.Ubarcode,
			InDate:     detailInDate,
			CreateTime: now,
		}

		packagingDetails = append(packagingDetails, packagingDetail)

		// 批量插入处理
		if len(packagingDetails) >= batchSize {
			affected, err := s.Session.Insert(packagingDetails...)
			if err != nil {
				log.Errorf("批量添加包装明细失败，批次大小: %d, 错误: %v", len(packagingDetails), err)
				failedCount += len(packagingDetails)
			} else {
				successCount += int(affected)
				log.Infof("批量添加包装明细成功，本批次处理: %d 条", affected)
			}
			packagingDetails = make([]interface{}, 0, batchSize)
		}
	}

	// 处理剩余的包装明细批次
	if len(packagingDetails) > 0 {
		affected, err := s.Session.Insert(packagingDetails...)
		if err != nil {
			log.Errorf("批量添加包装明细失败，批次大小: %d, 错误: %v", len(packagingDetails), err)
			failedCount += len(packagingDetails)
		} else {
			successCount += int(affected)
			log.Infof("批量添加包装明细成功，本批次处理: %d 条", affected)
		}
	}

	// 第二阶段：处理SyncOutboundLog表的数据
	// 处理每个箱码关联的出库记录
	processedUbarcodes := make(map[string]bool)

	for ubarcode, barcodes := range ubarcodeBarcodeMap {
		// 避免重复处理
		if processedUbarcodes[ubarcode] {
			continue
		}
		processedUbarcodes[ubarcode] = true

		// 查找关联的出库记录
		var outboundDetail blky_po.SjanOutboundDetail
		found := false

		// 先检查箱码
		if od, exists := outboundMap[ubarcode]; exists {
			outboundDetail = od
			found = true
		} else {
			// 再检查关联的条码
			for _, barcode := range barcodes {
				if od, exists := outboundMap[barcode]; exists {
					outboundDetail = od
					found = true
					break
				}
			}
		}

		if !found {
			continue // 没有关联的出库记录
		}

		// 处理出库记录
		for _, barcode := range barcodes {
			detail := detailsMap[barcode]
			detailInDate, _ := time.ParseInLocation(
				"2006-01-02 15:04:05",
				detail.InDate,
				utils.ChinaLocation,
			)

			if outboundDetail.InDate.Before(detailInDate) {
				continue // 只处理比采集时间更晚的出库记录
			}

			// 检查是否已存在对应的日志记录
			var existingLog blky_po.SyncOutboundLog
			exists := false
			if logMap, ok := syncLogMap[ubarcode]; ok {
				if log, ok := logMap[barcode]; ok {
					existingLog = log
					exists = true
				}
			}

			// 准备日志记录
			syncOutboundLog := blky_po.SyncOutboundLog{
				BillNo:        outboundDetail.BillNo,
				SourceBarcode: ubarcode,
				Barcode:       barcode,
				InDate:        outboundDetail.InDate,
				PNo:           outboundDetail.PNo,
				CreateTime:    now,
			}

			if exists {
				// 更新现有记录
				syncOutboundLog.Id = existingLog.Id
				syncLogsToUpdate = append(syncLogsToUpdate, &syncOutboundLog)

				// 从映射中移除，剩下的将被删除
				delete(syncLogMap[ubarcode], barcode)
			} else {
				// 添加新记录
				syncLogsToInsert = append(syncLogsToInsert, &syncOutboundLog)
			}

			// 批量处理插入
			if len(syncLogsToInsert) >= batchSize {
				_, err := s.Session.Insert(syncLogsToInsert...)
				if err != nil {
					log.Errorf("批量添加出库日志记录失败，批次大小: %d, 错误: %v", len(syncLogsToInsert), err)
				}
				syncLogsToInsert = make([]interface{}, 0, batchSize)
			}

			// 批量处理更新
			if len(syncLogsToUpdate) >= batchSize {
				for _, updateLog := range syncLogsToUpdate {
					_, err := s.Session.ID(updateLog.(*blky_po.SyncOutboundLog).Id).Update(updateLog)
					if err != nil {
						log.Errorf("更新出库日志记录失败，ID: %d, 错误: %v", updateLog.(*blky_po.SyncOutboundLog).Id, err)
					}
				}
				syncLogsToUpdate = make([]interface{}, 0, batchSize)
			}
		}

		// 收集需要删除的记录ID
		for _, logMap := range syncLogMap {
			for _, logRecord := range logMap {
				syncLogIdsToDelete = append(syncLogIdsToDelete, int64(logRecord.Id))

				// 批量删除处理
				if len(syncLogIdsToDelete) >= batchSize {
					_, err := s.Session.In("id", syncLogIdsToDelete).Delete(&blky_po.SyncOutboundLog{})
					if err != nil {
						log.Errorf("批量删除出库日志记录失败，批次大小: %d, 错误: %v", len(syncLogIdsToDelete), err)
					}
					syncLogIdsToDelete = make([]int64, 0, batchSize)
				}
			}
		}
	}

	// 处理剩余的批次
	// 1. 插入
	if len(syncLogsToInsert) > 0 {
		_, err := s.Session.Insert(syncLogsToInsert...)
		if err != nil {
			log.Errorf("批量添加出库日志记录失败，批次大小: %d, 错误: %v", len(syncLogsToInsert), err)
		}
	}

	// 2. 更新
	for _, logs := range syncLogsToUpdate {
		syncLog := logs.(*blky_po.SyncOutboundLog)
		_, err := s.Session.ID(syncLog.Id).Update(syncLog)
		if err != nil {
			log.Errorf("更新出库日志记录失败，ID: %d, 错误: %v", syncLog.Id, err)
		}
	}

	// 3. 删除
	if len(syncLogIdsToDelete) > 0 {
		_, err := s.Session.In("id", syncLogIdsToDelete).Delete(&blky_po.SyncOutboundLog{})
		if err != nil {
			log.Errorf("批量删除出库日志记录失败，批次大小: %d, 错误: %v", len(syncLogIdsToDelete), err)
		}
	}

	// 提交事务
	s.Session.Commit()

	// 返回处理结果
	if failedCount > 0 {
		return fmt.Errorf("添加包装任务部分失败：任务主数据添加成功，明细数据成功 %d 条，失败 %d 条", successCount, failedCount)
	}
	log.Infof("添加包装任务成功：任务主数据添加成功，明细数据添加成功 %d 条", successCount)

	return nil
}

// AddOutboundRecord 添加出库记录及明细
// 参数 record: 出库记录请求
func (s *SjanService) AddOutboundRecord(record external_vo.OutboundRecordRequest) error {
	s.Begin()
	defer s.Close()

	// 检查必填字段
	if record.BillNo == "" || record.CuNo == "" {
		return fmt.Errorf("缺少必要参数：出库单号和代理编号为必填")
	}

	// 时间转换
	var recordInDate time.Time
	var err error
	if record.InDate != "" {
		recordInDate, _ = time.ParseInLocation(
			"2006-01-02 15:04:05",
			record.InDate,
			utils.ChinaLocation,
		)
	} else {
		recordInDate = time.Now()
	}

	// 1. 检查出库单号是否已存在
	existRecord := &blky_po.SjanOutboundRecord{}
	has, err := s.Session.Where("bill_no = ?", record.BillNo).Get(existRecord)
	if err != nil {
		return fmt.Errorf("查询出库记录失败: %v", err)
	}

	// 如果出库单号已存在，执行更新操作
	if has {
		// 更新出库记录信息
		existRecord.CuNo = record.CuNo
		existRecord.InDate = recordInDate
		_, err = s.Session.ID(existRecord.Id).Update(existRecord)
		if err != nil {
			return fmt.Errorf("更新出库记录失败: %v", err)
		}

		// 删除原有的明细记录
		_, err = s.Session.Where("bill_no = ?", record.BillNo).Delete(&blky_po.SjanOutboundDetail{})
		if err != nil {
			return fmt.Errorf("删除原出库明细失败: %v", err)
		}

		// 删除原有的物流码记录
		_, err = s.Session.Where("bill_no = ?", record.BillNo).Delete(&blky_po.SyncOutboundLog{})
		if err != nil {
			return fmt.Errorf("删除原物流码记录失败: %v", err)
		}

		log.Infof("更新出库记录成功: %s", record.BillNo)
	} else {
		// 2. 保存新出库记录信息
		outboundRecord := &blky_po.SjanOutboundRecord{
			BillNo:     record.BillNo,
			CuNo:       record.CuNo,
			InDate:     recordInDate,
			CreateTime: time.Now(),
		}

		_, err = s.Session.Insert(outboundRecord)
		if err != nil {
			return fmt.Errorf("添加出库记录失败: %v", err)
		}
	}

	// 3. 批量保存出库明细和物流码信息
	successCount := 0
	failedCount := 0

	outboundDetails := make([]interface{}, 0, batchSize)   // 原始出库明细
	outboundLogistics := make([]interface{}, 0, batchSize) // 解析后的物流码记录

	if len(record.Data) > 0 {
		for i, detail := range record.Data {
			// 检查必要信息
			if detail.Barcode == "" || detail.PNo == "" {
				failedCount++
				log.Errorf("明细数据缺少必要信息: %+v", detail)
				continue
			}

			// 处理扫码时间
			var detailInDate time.Time
			if detail.InDate != "" {
				detailInDate, _ = time.ParseInLocation(
					"2006-01-02 15:04:05",
					detail.InDate,
					utils.ChinaLocation,
				)
			} else {
				detailInDate = time.Now()
			}
			// 1. 保存原始出库明细
			number := detail.Number
			outboundDetail := &blky_po.SjanOutboundDetail{
				BillNo:     record.BillNo,
				Barcode:    detail.Barcode,
				PNo:        detail.PNo,
				InDate:     detailInDate,
				Number:     number,
				CreateTime: time.Now(),
			}
			outboundDetails = append(outboundDetails, outboundDetail)

			// 箱码情况：需要从包装关联数据查找对应的物流码
			var packagingDetails []struct {
				Barcode string `xorm:"barcode"`
			}
			err := s.Session.Table("blky.sjan_packaging_details").
				Where("ubarcode = ? or barcode = ?", detail.Barcode, detail.Barcode).
				Cols("barcode").
				Find(&packagingDetails)

			if err != nil {
				log.Errorf("查询箱码对应的物流码失败: %v", err)
				continue
			}

			// 如果没有找到包装数据，则从物流码表查询
			if len(packagingDetails) == 0 {
				var logisticsCodes []struct {
					SWLM string `xorm:"swlm"`
				}
				err := s.Session.Table("blky.xlogistics_code").
					Where("swlm = ?", detail.Barcode).
					Cols("swlm").
					Find(&logisticsCodes)

				if err != nil {
					log.Errorf("查询物流码表失败: %v", err)
					continue
				}

				// 如果物流码表有数据，则直接使用当前条码添加到SyncOutboundLog
				if len(logisticsCodes) > 0 {
					logisticsDetail := &blky_po.SyncOutboundLog{
						BillNo:        record.BillNo,
						SourceBarcode: detail.Barcode, // 原箱码
						Barcode:       detail.Barcode, // 使用相同的条码
						PNo:           detail.PNo,     // 产品编号
						InDate:        detailInDate,   // 扫码时间
						CreateTime:    time.Now(),
					}
					outboundLogistics = append(outboundLogistics, logisticsDetail)
				}
			} else {
				// 添加所有关联的物流码记录
				for _, pd := range packagingDetails {
					logisticsDetail := &blky_po.SyncOutboundLog{
						BillNo:        record.BillNo,
						SourceBarcode: detail.Barcode, // 原箱码
						Barcode:       pd.Barcode,     // 物流码
						PNo:           detail.PNo,     // 产品编号
						InDate:        detailInDate,   // 扫码时间
						CreateTime:    time.Now(),
					}
					outboundLogistics = append(outboundLogistics, logisticsDetail)
				}
			}

			// 批量插入处理
			if len(outboundDetails) >= batchSize || i == len(record.Data)-1 {
				// 插入原始出库明细
				if len(outboundDetails) > 0 {
					affected, err := s.Session.Insert(outboundDetails...)
					if err != nil {
						log.Errorf("批量添加出库明细失败，批次大小: %d, 错误: %v", len(outboundDetails), err)
						failedCount += len(outboundDetails)
					} else {
						successCount += int(affected)
						log.Infof("批量添加出库明细成功，本批次处理: %d 条", affected)
					}
					outboundDetails = make([]interface{}, 0, batchSize)
				}

				// 插入物流码记录
				if len(outboundLogistics) > 0 {
					affected, err := s.Session.Insert(outboundLogistics...)
					if err != nil {
						log.Errorf("批量添加物流码记录失败，批次大小: %d, 错误: %v", len(outboundLogistics), err)
					} else {
						log.Infof("批量添加物流码记录成功，本批次处理: %d 条", affected)
					}
					outboundLogistics = make([]interface{}, 0, batchSize)
				}

			}
			//如果是箱码，重新出库，则从sync_un_ubarcode表中删除对应的记录
			if number > 1 {
				unUbarcode := &blky_po.SyncUnUbarcode{}
				_, err := unUbarcode.DeleteUnUbarcode(s.Session, detail.Barcode)
				if err != nil {
					log.Errorf("删除不可用箱码记录失败: %v", err)
				}
			}
		}
	}

	// 提交事务
	s.Session.Commit()

	// 返回处理结果
	if failedCount > 0 {
		return fmt.Errorf("添加出库记录部分失败：出库主数据添加成功，明细数据成功 %d 条，失败 %d 条", successCount, failedCount)
	}

	log.Infof("添加出库记录成功：出库主数据添加成功，明细数据添加成功 %d 条", successCount)
	return nil
}

// AddReturnRecords 添加退货记录
// 参数 request: 退货记录请求
func (s *SjanService) AddReturnRecords(request external_vo.ReturnRecordRequest) error {
	s.Begin()
	defer s.Close()

	// 检查记录数据
	if len(request.Records) == 0 {
		return fmt.Errorf("退货记录数据不能为空")
	}

	// 批量处理统计
	successCount := 0
	failedCount := 0

	// 批量处理相关变量
	newRecords := make([]interface{}, 0, batchSize) // 待插入的新退货记录集合

	// 处理每条退货记录
	for i, record := range request.Records {
		// 检查必填字段
		if record.Barcode == "" || record.PNo == "" || record.CuNo == "" {
			failedCount++
			log.Errorf("退货记录缺少必要信息 barcode: %s, pno: %s, cuNo: %s", record.Barcode, record.PNo, record.CuNo)
			continue
		}

		// 转换退货时间
		var inDate time.Time
		if record.InDate != "" {
			inDate, _ = time.ParseInLocation(
				"2006-01-02 15:04:05",
				record.InDate,
				utils.ChinaLocation,
			)
		} else {
			inDate = time.Now()
		}

		// 检查记录是否已存在
		existRecord := &blky_po.SjanReturnRecord{}
		has, err := s.Session.Where("id = ?", record.Id).Get(existRecord)
		if err != nil {
			failedCount++
			log.Errorf("查询退货记录失败: barcode=%s, cuNo=%s, 错误: %v", record.Barcode, record.CuNo, err)
			continue
		}

		if has {
			// 记录已存在，执行更新
			existRecord.Barcode = record.Barcode
			existRecord.CuNo = record.CuNo
			existRecord.PNo = record.PNo
			existRecord.InDate = inDate
			existRecord.Number = record.Number
			_, err := s.Session.ID(existRecord.Id).Update(existRecord)
			if err != nil {
				failedCount++
				log.Errorf("更新退货记录失败: barcode=%s, 错误: %v", record.Barcode, err)
				continue
			}
			successCount++
			log.Infof("更新退货记录成功: barcode=%s", record.Barcode)
		} else {
			// 创建退货记录
			returnRecord := &blky_po.SjanReturnRecord{
				Id:         record.Id,
				Barcode:    record.Barcode,
				CuNo:       record.CuNo,
				PNo:        record.PNo,
				InDate:     inDate,
				Number:     record.Number,
				CreateTime: time.Now(),
			}
			newRecords = append(newRecords, returnRecord)

			// 当累计到批处理大小或处理到最后一条记录时，执行批量插入
			if len(newRecords) >= batchSize || i == len(request.Records)-1 {
				if len(newRecords) > 0 {
					affected, err := s.Session.Insert(newRecords...)
					if err != nil {
						// 批量插入失败，记录错误但继续处理
						log.Errorf("批量添加退货记录失败，批次大小: %d, 错误: %v", len(newRecords), err)
						failedCount += len(newRecords)
					} else {
						successCount += int(affected)
						log.Infof("批量添加退货记录成功，本批次处理: %d 条", affected)
					}
					newRecords = make([]interface{}, 0, batchSize)
				}
			}

			// 记录不存在，更新最新的出库明细状态
			outboundLog := &blky_po.SyncOutboundLog{}
			updated, err := outboundLog.UpdateLatestRecord(s.Session, record.Barcode, inDate)
			if err != nil {
				log.Errorf("更新出库明细状态失败: barcode=%s, 错误: %v", record.Barcode, err)
			} else if updated {
				log.Infof("更新出库明细状态成功: barcode=%s", record.Barcode)
			}

			// 1. 查询大标小标关系
			packagingDetail := &blky_po.SjanPackagingDetail{}
			exists, err := packagingDetail.GetPackagingDetail(s.Session, record.Barcode)
			if err != nil {
				return err
			}
			if exists && packagingDetail.Ubarcode != "" {
				unUbarcode := &blky_po.SyncUnUbarcode{}
				// 检查是否已经存在不可用记录
				exists, err := unUbarcode.IsUnUbarcode(s.Session, packagingDetail.Ubarcode)
				if err != nil {
					return err
				}
				if !exists {
					// 添加不可用记录
					err = unUbarcode.AddUnUbarcode(s.Session, packagingDetail.Ubarcode, 3)
					if err != nil {
						return err
					}
				}
			}
		}
	}

	// 提交事务
	s.Session.Commit()

	// 返回处理结果
	if failedCount > 0 {
		return fmt.Errorf("处理退货记录部分失败：成功 %d 条，失败 %d 条", successCount, failedCount)
	}

	log.Infof("处理退货记录成功：成功处理 %d 条", successCount)
	return nil
}
