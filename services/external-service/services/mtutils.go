package services

import (
	"bytes"
	"eShop/infra/config"
	"eShop/infra/log"
	"eShop/infra/pkg/app"
	"eShop/infra/pkg/code"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/proto"
	dto2 "eShop/view-model/external-vo/dto"
	"eShop/view-model/omnibus-vo"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"io/ioutil"
	"math"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"
	"unicode"
)

// 错误返回非200的code码 并不返回error
func Mt(jsonStr []byte, url string, isSystem bool, httpType string, storeMasterId int32) (*proto.ExternalResponse, error) {
	str := url + "美团请求参数："
	log.Info(str, string(jsonStr))

	res := new(proto.ExternalResponse)
	res.Code = 200
	res.Message = "Success"

	var result []byte
	var err error

	if httpType == "GET" {
		result, err = HttpGetToMeiTuan(string(jsonStr), url, isSystem, storeMasterId)
	} else {
		result, err = HttpPostToMeiTuan(string(jsonStr), url, isSystem, storeMasterId, utils.ContentTypeToForm)
	}
	//jsonStr,_:= json.Marshal(params)
	//上面的方法是没有返回错误的 所以这里走不进去
	if err != nil {
		res.Code = 400
		res.Message = "美团请求出错"
		res.Error = err.Error()
		return res, nil
	}
	var mapResult map[string]interface{}
	err = json.Unmarshal(result, &mapResult)
	if err != nil {
		res.Code = 500
		res.Message = "美团请求返回数据解码出错"
		res.Error = err.Error()
		return res, nil
	}
	resCode := mapResult["data"]
	v, _ := json.Marshal(resCode)
	//美团请求如果出现错误
	if string(v) == "\"ng\"" {
		var mapError map[string]interface{}
		mapError = mapResult["error"].(map[string]interface{})
		res.Code = 400
		res.ExternalCode = strconv.FormatFloat(mapError["code"].(float64), 'f', -1, 64)
		res.Message = mapError["msg"].(string)
		//res.Message=mapResult["message"].(string)
	} else {
		res.Data = string(v)
	}
	str1 := url + "，美团返回参数："
	log.Info(str1, string(result))
	return res, nil
}

func IsChinese(str string) bool {
	var count int
	for _, v := range str {
		if unicode.Is(unicode.Han, v) {
			count++
			break
		}
	}
	return count > 0
}

func MtGetToken(appPoiCode string, storeMasterId int32) string {

	token := ""
	tokenKey := "mt:token:"
	refreshTokenKey := "mt:refreshToken:"
	tokenurl := "oauth/authorize"

	redisConn := cache.GetRedisConn()
	token = redisConn.Get(tokenKey + appPoiCode).Val()
	jsonPar := ""
	if token == "" {

		lockKey := "eshop:mt:lock:token:" + appPoiCode
		getLock := redisConn.SetNX(lockKey, time.Now().Unix(), time.Minute*1).Val()
		defer redisConn.Del(lockKey)
		if !getLock {
			//没有拿到锁的人说明已经有别的接口去获取token了。这边循环3次去拿token就行了
			for i := 0; i < 3; i++ {
				token = redisConn.Get(tokenKey + cast.ToString(appPoiCode)).Val()
				if token != "" {
					break
				}
				time.Sleep(time.Second * 1)
			}

		} else {
			//首先从redis里面拿数据，拿不到的话就去掉接口
			varss, err := redisConn.Get(refreshTokenKey + appPoiCode).Result()
			fmt.Println(varss)
			refreshToken := redisConn.Get(refreshTokenKey + appPoiCode).Val()
			ispostToken := false
			httpType := "GET"
			contentType := ""
			if refreshToken == "" {
				var TokenRequest = omnibus_vo.TokenRequest{}
				TokenRequest.AppPoiCode = appPoiCode
				TokenRequest.ResponseType = "token"
				jsonStr, _ := json.Marshal(TokenRequest)
				jsonPar = string(jsonStr)
			} else { //调用刷新接口
				tokenurl = "oauth/token"
				ispostToken = true
				httpType = "POST"
				contentType = utils.ContentTypeToForm
				var TokenRequest = omnibus_vo.TokenRefreshRequest{}
				TokenRequest.AppPoiCode = appPoiCode
				TokenRequest.GrantType = "refresh_token"
				TokenRequest.RefreshToken = refreshToken
				jsonStr, _ := json.Marshal(TokenRequest)
				jsonPar = string(jsonStr)
			}

			tokenStr, data1, retCode := GetMeiTuanSign(jsonPar, tokenurl, ispostToken, true, storeMasterId)
			if retCode != code.Success {
				log.Error("获取token失败", jsonPar)
			}
			request, err := HttpCommon(data1, tokenStr, httpType, contentType)
			if err != nil {
				log.Error("获取token失败", err.Error())
			}
			ret := omnibus_vo.TokenResponse{}
			err = json.Unmarshal(request, &ret)
			if err != nil {
				log.Error("获取token失败", err.Error())
			}
			if ret.Status == 0 {
				duration := 29 * 24 * time.Hour
				token = ret.AccessToken
				redisConn.Set(tokenKey+appPoiCode, ret.AccessToken, duration)
				if ret.RefreshToken != "" {
					redisConn.Set(refreshTokenKey+appPoiCode, ret.RefreshToken, 179*24*time.Hour)
				}
			} else {
				log.Error("获取美团token错误:", ret.Message)
			}
		}

	}
	return token
}

// GetMeiTuanSign 获取美团签名
// data json 格式
// apiUrl 例如 poi/getids
// isPost get==false,post==true
// isSystem 是否需要系统参数
func GetMeiTuanSign(data string, apiUrl string, isPost bool, isSystem bool, storeMasterId int32) (string, string, int) {

	//var mapInterface map[string]interface{}
	mapInterface := make(map[string]interface{})
	if len(data) > 0 {
		mapInterface = JsonToMap(data)
	}

	// appId := MtAppId
	// appSecret := MtAppSecret
	// if appChannel == 2 {
	// 	appId = TPMtAppId
	// 	appSecret = TPMtAppSecret
	// }
	mtUrl := config.Get("MtUrl")
	appPoiCode, ishave := mapInterface["app_poi_code"]
	token := ""

	//如果是saasp平台的，并且有门店ID，就需要去拿token
	if storeMasterId == 12 && ishave && apiUrl != "oauth/authorize" && apiUrl != "oauth/token" {
		token = MtGetToken(cast.ToString(appPoiCode), storeMasterId)
	}

	appConfig, retCode := GetStoreMasterChannelAppConfig(storeMasterId, app.Channel_MT)
	if retCode != code.Success {
		log.Error("app.GetStoreMasterChannelAppConfig", storeMasterId, app.Channel_MT, appConfig)
		return "", "", code.ErrorCommon
	}
	appId, _ := strconv.ParseInt(appConfig.AppId, 10, 64)
	appSecret := appConfig.AppSecret
	log.Info("美团appId:" + cast.ToString(appId) + "&appSecret:" + appSecret)
	timestamp := time.Now().Unix()
	if isSystem {
		mapInterface["app_id"] = appId
		mapInterface["timestamp"] = timestamp
	}
	if token != "" {
		mapInterface["access_token"] = token
	}

	// 初始化排序数组
	var array []string
	for key := range mapInterface {
		array = append(array, key)
	}
	// 按照字母编码排序
	sort.Strings(array)

	var signStr strings.Builder
	signStr.WriteString(mtUrl)
	signStr.WriteString(apiUrl + "?")

	var arrayData []string

	for i := 0; i < len(array); i++ {
		for key, item := range mapInterface {
			if array[i] == key {

				typeData := ""
				utf8Data := ""
				switch data := item.(type) {
				case string:
					typeData = data
					utf8Data = typeData
					if IsChinese(typeData) {
						utf8Data = url.QueryEscape(typeData)
					}
				case bool:
					typeData = strconv.FormatBool(data)
					utf8Data = typeData
				case int64:
					typeData = strconv.FormatInt(data, 10)
					utf8Data = typeData
				case float64:
					isInt := IsInt(data)
					if isInt {
						typeData = strconv.FormatInt(int64(data), 10)
					} else {
						typeData = strconv.FormatFloat(data, 'E', -1, 64)
					}
					utf8Data = typeData
				case []string:
					mapValue, error := json.Marshal(item)
					if error == nil {
						//typeData = signStr + string(mapValue)
						typeData = string(mapValue)
						utf8Data = typeData
						if IsChinese(typeData) {
							utf8Data = url.QueryEscape(typeData)
						}
					}
				case []interface{}:
					mapValue, error := json.Marshal(item)
					if error == nil {
						typeData = string(mapValue)
						utf8Data = typeData
						if IsChinese(typeData) {
							utf8Data = url.QueryEscape(typeData)
						}
					}
				case map[string]interface{}:
					mapValue, error := json.Marshal(item)
					if error == nil {
						typeData = string(mapValue)
						utf8Data = typeData
						if IsChinese(typeData) {
							utf8Data = url.QueryEscape(typeData)
						}
					}
				case json.Number:
					typeData = string(data)
					utf8Data = typeData

				}
				if len(typeData) > 0 {
					if i != 0 {
						//signStr = signStr + "&"
						signStr.WriteString("&")
					}
					signStr.WriteString(key + "=" + typeData)

					if key == "app_id" || key == "timestamp" {
						break
					}
					arrayData = append(arrayData, key+"="+utf8Data)
				}

			}
		}
	}
	signStr.WriteString(appSecret)
	//生成签名 参数
	sign := utils.GetMd5String(signStr.String())

	//Post 请求需要的参数(key=value)字符串
	var strData strings.Builder

	var url strings.Builder
	url.WriteString(mtUrl + apiUrl + "?app_id=")
	url.WriteString(strconv.FormatInt(appId, 10) + "&timestamp=" + strconv.FormatInt(timestamp, 10))
	url.WriteString("&sig=" + sign)
	for i := 0; i < len(arrayData); i++ {
		if i != 0 {
			strData.WriteString("&")
		}
		strData.WriteString(arrayData[i])
	}
	if !isPost {
		url.WriteString("&" + strData.String())

	}
	return url.String(), strData.String(), code.Success
}

func IsInt(data float64) bool {
	b := math.Floor(data)
	result := false
	if b == data {
		result = true
	}
	return result

}

// HttpGetToMeiTuan 对美团发起GET请求
// @params data 请求参数 json 格式
// @params url 接口地址  例如 url="poi/getids"
// @params isSystem 是否需要带系统参数 Get必须为
func HttpGetToMeiTuan(data string, url string, isSystem bool, storeMasterId int32) ([]byte, error) {
	apiUrl, _, retCode := GetMeiTuanSign(data, url, false, isSystem, storeMasterId)
	if retCode != code.Success {
		return []byte(""), errors.New("GetMeiTuanSign GetStoreMasterChannelAppConfig failed")
	}
	request, _ := HttpCommon("", apiUrl, "GET", "")
	return request, nil
}

// todo 抽象出来公用
func HttpCommon(data string, url string, httpType string, contentType string) ([]byte, error) {
	request := new(http.Request)
	var err error
	if httpType == "POST" {
		byteData := bytes.NewReader([]byte(data))
		request, err = http.NewRequest("POST", url, byteData)
	} else {
		request, err = http.NewRequest("GET", url, nil)
	}
	if err != nil {
		log.Error(err.Error())
		return nil, err
	}
	if len(contentType) > 0 {
		request.Header.Set("Content-Type", contentType) //"application/json;charset=UTF-8"
	}
	//tr := &http.Transport{
	//	TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	//}
	//client := http.Client{Transport: tr}
	resp, err := utils.HttpTransportClient.Do(request)
	if err != nil {
		log.Error(err.Error())
		return nil, err
	}
	if resp.StatusCode != 200 {
		log.Error(resp)
		byteStr := `{"error":{"code":` + strconv.Itoa(resp.StatusCode) + `,"msg":"` + resp.Status + `"},"data":"ng"}`
		return []byte(byteStr), nil
	}
	respBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Error(err.Error())
		return nil, err
	}
	return respBytes, nil
}

// 1、美团开放平台在优化接口的过程中可能调整或新增字段，请开发者一定不要将签名的传输字段写死，应根据URL全字段（sig除外）参与验签，并用&连接，以免造成验签失败。
// 2、计算签名时，需根据各参数首字母按英文字母a~z排序，而验签后的请求参数无需排序。
// 3、调用美团接口计算签名时中文不要编码，注意字母需区分大小写。
// 4、POST请求，非必须参数不传则不用参与签名计算。
// 5、发送请求时要保证中文需进行 utf-8 编码。部分服务器对 url 部分已自动编码，而body参数需要代码进行手动编码。
// 6、当APP方接收美团POST请求方式推送的信息时，签名需要解码2次，步骤如下：
// step ①：使用 utf-8 解码，此时参数里的中文仍为编码；
// step ②：再次对中文进行 utf-8 解码；
// step ③：验签时，没有值的空参数也要参与签名拼接，参考形式：&extras=[]
// 注意：当APP方接收美团GET请求方式推送的信息时，签名只需解码1次。目前平台GET请求方式的回调接口有：APP方URL 推送全额退款信息、APP方URL 推送部分退款信息、APP方URL 推送用户或客服取消订单。
// HttpGetToMeiTuan 对美团发起GET请求
// @params data json 格式 url 接口地址  例如 url="poi/getids" isSystem 是否需要带系统参数
func HttpPostToMeiTuan(data string, url string, isSystem bool, storeMasterId int32, contentType string) ([]byte, error) {
	apiUrl, bodyData, retCode := GetMeiTuanSign(data, url, true, isSystem, storeMasterId)
	if retCode != code.Success {
		return []byte(""), errors.New("GetMeiTuanSign GetStoreMasterChannelAppConfig failed")
	}
	request, err := HttpCommon(bodyData, apiUrl, "POST", utils.ContentTypeToForm)
	return request, err
}

// JsonToMap  Json 转换成 Map
func JsonToMap(jsonStr string) map[string]interface{} {
	var mapResult map[string]interface{}
	dec := json.NewDecoder(strings.NewReader(jsonStr))
	dec.UseNumber()
	dec.Decode(&mapResult)
	return mapResult
}

// Post请求共用处理方法处理方法
// handName参数表示请求的中文标头，用于记录日志头
// requestByte 参数表求上游请求的Byte数组
// err 表示错误转Json的错误信息
// utilsStr 表示API的URL后缀
func PostHandleFunc(handName string, requestByte []byte, err error, utilsStr string, storeMasterId int32) (*proto.RetailSellStatusResult, error) {
	var result *proto.RetailSellStatusResult
	result = new(proto.RetailSellStatusResult)
	var requestLogString strings.Builder // 用于记录日志
	requestStr := string(requestByte)
	fmt.Println(requestStr)
	if handName == "【批量创建/更新商品[支持商品多规格,不含删除逻辑]】" || handName == "【批量创建/更新商品信息至多店】" {
		requestStr = strings.Replace(requestStr, "\"available_times\":{},", "", -1)
	}
	requestLogString.WriteString(handName + "请求参数:")
	requestLogString.WriteString(requestStr)
	requestLogString.WriteString("响应参数:")
	if err != nil { //
		result.Code = 400
		result.Message = "Code=400,前端传递的参数转Json异常，信息：" + err.Error()
		result.Error.Msg = err.Error()
		requestLogString.WriteString(result.Message) //  响应报错
		log.Info(requestLogString.String())
		return result, nil
	}

	var resp []byte
	resp, err = HttpPostToMeiTuan(requestStr, utilsStr, true, storeMasterId, utils.ContentTypeToForm)
	if err != nil { // 响应报错
		result.Code = 9999999
		result.Message = "Code=400,请求接口服务器异常，信息：" + err.Error()
		result.Error.Msg = err.Error()
		requestLogString.WriteString(result.Message) // 记录日志
		log.Info(requestLogString.String())
		return result, nil
	}
	err = json.Unmarshal(resp, &result)
	resultResp := string(resp)
	if handName == "【批量创建/更新商品信息至多店】" {
		var dataResult dto2.MultipoisBatchinitdataResult
		err = json.Unmarshal(resp, &dataResult)
		if len(result.Data) == 0 && dataResult.Data > 0 {
			result.Code = 200
			result.Message = "成功"
			result.Data = strconv.Itoa(dataResult.Data)
			requestLogString.WriteString("接口返回信息Code=200;Message=成功！返回Json=" + resultResp)
		} else {
			result.Error.Msg = string(resp)
			if result.Error.Code == 711 || result.Error.Code == 703 {
				result.Code = result.Error.Code
			} else if result.Error.Code == 504 {
				// 美团报网关错误，认为是请求服务出错
				result.Code = 9999999
			} else {
				result.Code = 400
			}
			result.Message = result.Error.Msg
			requestLogString.WriteString("接口返回信息Code=400;返回的Json数据：" + resultResp + ";message=失败！" + result.Msg)
		}

	} else {
		if err != nil {
			result.Code = 400
			if result != nil && result.Error != nil {
				if result.Error.Code == 711 || result.Error.Code == 703 {
					result.Code = result.Error.Code
				}
			}

			requestLogString.WriteString("400")
			result.Message = "接口返回信息Code=400;返回的Json数据:" + resultResp + ";message=值转Json异常,错误信息：" + err.Error()
			requestLogString.WriteString(result.Message)
			log.Info(requestLogString.String()) // 记录日志
			return result, nil
		}
		if result.Data == "ok" {
			result.Code = 200
			requestLogString.WriteString("接口返回信息Code=200;Message=成功！返回Json=" + resultResp)
			result.Message = "成功"
		} else {
			result.Code = 400
			if result != nil && result.Error != nil {
				if result.Error.Code == 711 || result.Error.Code == 703 {
					result.Code = result.Error.Code
				} else if result.Error.Code == 504 {
					// 美团报网关错误，认为是请求服务出错
					result.Code = 9999999
				}
			}
			requestLogString.WriteString("接口返回信息Code=400;返回的Json数据：" + resultResp + ";message=失败！" + result.Msg)
			result.Message = result.Error.Msg
		}
	}
	requestLogString.WriteString(string(resp)) // 接口返回信息
	log.Info(requestLogString.String())        // 记录日志
	return result, nil
}

// Get处理方式
//
//	handName参数表示请求的中文标头，用于记录日志头
//
// requestByte 参数表求上游请求的Byte数组
// err 表示错误转Json的错误信息
// utilsStr 表示API的URL后缀
func GetHandleFunc(handName string, requestByte []byte, err error, utilsStr string, storeMasterId int32) (*dto2.RetailSellStatusResult, []byte, string) {
	result := &dto2.RetailSellStatusResult{}
	var resultbyte []byte
	var requestLogString strings.Builder // 用于记录日志
	requestStr := string(requestByte)
	requestLogString.WriteString(handName + "请求参数:")
	requestLogString.WriteString(requestStr)
	requestLogString.WriteString("响应参数:")
	if err != nil {
		result.Code = 400
		result.Message = "Code=400,前端传递的参数转Json异常，信息：" + err.Error()
		result.Msg = err.Error()
		requestLogString.WriteString(result.Message) //  响应报错
		log.Info(requestLogString.String())
		return result, resultbyte, ""
	}
	resp, err := HttpGetToMeiTuan(requestStr, utilsStr, true, storeMasterId)

	log.Info(utilsStr, string(resp)+string(requestByte))
	if err != nil { // 响应报错
		result.Code = 9999999
		result.Message = "Code=400,请求接口服务器异常，信息：" + err.Error() + "接口返回信息：" + string(resp)
		result.Msg = err.Error()
		requestLogString.WriteString(result.Message) // 记录日志
		log.Info(requestLogString.String())
		return result, resultbyte, ""
	}
	err = json.Unmarshal(resp, &result)
	if len(result.Error.Msg) > 0 || len(result.Msg) > 0 {
		if result.Error.Code == 711 {
			result.Code = result.Error.Code
		} else {
			result.Code = 400
		}
		result.Message = "Code=400,请求接口异常，信息：" + string(resp)
		result.Msg = string(resp)
		requestLogString.WriteString(result.Message) // 记录日志
		log.Info(requestLogString.String())
		return result, resultbyte, ""
	}
	result.Code = 200
	result.Message = "成功"
	return result, resp, requestLogString.String()
}

func JsonToString(data interface{}) string {
	marshal, _ := json.Marshal(data)
	return string(marshal)
}
