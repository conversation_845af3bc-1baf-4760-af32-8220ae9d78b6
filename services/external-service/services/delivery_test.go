package services

import (
	"eShop/infra/log"
	"eShop/services/common"
	_ "github.com/go-sql-driver/mysql"
	"testing"
)

func TestDelivery_PushDelivery(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		orderSn string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "发配送"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Delivery{
				BaseService: tt.fields.BaseService,
			}
			log.Init()
			if err := s.PushDelivery("123456"); (err != nil) != tt.wantErr {
				t.Errorf("PushDelivery() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
