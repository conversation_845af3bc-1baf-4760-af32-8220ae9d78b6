package services

import (
	"context"
	"eShop/infra/errors"
	"eShop/infra/log"
	"eShop/infra/utils"
	vo "eShop/view-model/external-vo/offline"
	"encoding/json"
	"fmt"
)

type ForsterService struct {
}

func (s *ForsterService) ForsterSettle(ctx context.Context, dto *vo.FosterPayDTO) error {
	params, _ := json.Marshal(dto)
	log.Infof("forsterSettle: %s", params)

	// 检查参数
	if dto.FosterId == 0 {
		return errors.NewBadRequest("FosterId不能为空")
	}

	// 调用下面三个方法
	if dto.Amount > 0 {
		err := depositSettle(dto)
		if err != nil {
			log.Error("depositSettle: ", err.Error())
			return err
		}

		if len(dto.PayType) > 0 {
			err = fosterSettleBack(dto)
			if err != nil {
				log.Error("fosterSettleBack: ", err.Error())
				return err
			}
		}
	}

	err := fosterSettle(dto.FosterId, dto.StoreId)
	if err != nil {
		log.Error("fosterSettle: ", err.Error())
		return err
	}

	return nil
}

// 押金扣减，押金结算
func depositSettle(dto *vo.FosterPayDTO) error {
	params := make(map[string]interface{})
	params["customerId"] = dto.CustomerId
	params["customerName"] = dto.CustomerName
	params["fosterId"] = dto.FosterId
	params["orderId"] = dto.OrderId
	params["orderNo"] = dto.OrderNo
	params["amount"] = dto.Amount
	params["sellerId"] = dto.SellerId

	_, err := utils.HttpApi("POST", "/api/foster/feign/deposit/settle", fmt.Sprintf("tenant_id|%s", dto.StoreId), params)
	if err != nil {
		log.Error("depositSettle: ", err.Error())
		return err
	}

	return nil
}

// 寄养订单支付，多余押金退款
func fosterSettleBack(dto *vo.FosterPayDTO) error {
	params := make(map[string]interface{})
	params["customerId"] = dto.CustomerId
	fosterIds := make([]int64, 0)
	fosterIds = append(fosterIds, dto.FosterId)
	params["fosterIds"] = fosterIds
	params["orderId"] = dto.OrderId
	params["orderNo"] = dto.OrderNo
	params["payType"] = dto.PayType
	params["sellerId"] = dto.SellerId
	params["sellerName"] = dto.SellerName
	params["receiveAmount"] = 0

	_, err := utils.HttpApi("POST", "/api/foster/feign/foster/settle/back", fmt.Sprintf("tenant_id|%s", dto.StoreId), params)
	if err != nil {
		log.Error("fosterSettleBack: ", err.Error())
		return err
	}

	return nil
}

// 寄养单结算，结算后，更新寄养，房间状态
func fosterSettle(fosterId int64, storeId string) error {
	params := fmt.Sprintf("[%d]", fosterId)

	_, err := utils.HttpApi("POST", "/api/foster/feign/foster/settle/foster", fmt.Sprintf("tenant_id|%s", storeId), params)
	if err != nil {
		log.Error("fosterSettle: ", err.Error())
		return err
	}

	return nil
}
