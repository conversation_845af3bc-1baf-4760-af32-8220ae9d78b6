package services

import (
	"context"
	product_po2 "eShop/domain/product-po"
	"eShop/proto"
	"eShop/view-model/external-vo/dto"
	vo "eShop/view-model/product-vo"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/golang/glog"
	"github.com/spf13/cast"

	"eShop/infra/log"
	"eShop/services/common"

	"xorm.io/xorm"
)

type StoreService struct {
	common.BaseService
}

// StoreAuthorization 店铺授权清理数据
func (s *StoreService) StoreAuthorization(req dto.StoreAuthorization) error {
	s.Begin()
	defer s.Close()

	//获取第三方ID
	ChannelStoreIdMt, AppChannelMt, stateMt := common.GetChannelInfoByStoreId(s.Engine, req.ShopId, Channel_MT)
	//获取第三方ID
	ChannelStoreIdElm, AppChannelElm, stateElm := common.GetChannelInfoByStoreId(s.Engine, req.ShopId, Channel_ELM)

	logPrefix := fmt.Sprintf("店铺清理数据,入参:%+v", req)
	log.Info(logPrefix)

	if req.ShopId == "" {
		return errors.New("店铺ID不能为空")
	}

	// 根据授权类型执行不同的授权逻辑
	switch req.AuthorizationType {
	case 1: // 清理商品
		err := s.cleanProducts(s.Engine, ChannelStoreIdMt, cast.ToString(AppChannelMt), stateMt,
			ChannelStoreIdElm, cast.ToString(AppChannelElm), stateElm, req.ShopId)
		if err != nil {
			return err
		}
	case 2: // 清理分类
		err := s.cleanCategories(s.Engine, ChannelStoreIdMt, cast.ToString(AppChannelMt), stateMt,
			ChannelStoreIdElm, cast.ToString(AppChannelElm), stateElm, req.ShopId)
		if err != nil {
			return err
		}
	case 3: //关闭门店
		return s.CloseShop(ChannelStoreIdMt, cast.ToString(AppChannelMt), stateMt,
			ChannelStoreIdElm, cast.ToString(AppChannelElm), stateElm, req.ShopId)
	default:
		return errors.New("无效的授权类型")
	}

	return nil
}

// CloseShop 关闭门店
func (s *StoreService) CloseShop(ChannelStoreIdMt string, AppChannelMt string, stateMt bool,
	ChannelStoreIdElm string, AppChannelElm string, stateElm bool, shopId string) error {

	if stateElm {
		//关闭饿了么门店
		var elmService ElmProductService
		request := new(proto.ElmBusStatusSetRequest)
		request.ShopId = ChannelStoreIdElm
		request.FinanceCode = shopId
		request.BaiduShopId = ChannelStoreIdElm
		dataResponse, err := elmService.ElmClose(request)
		if err != nil {
			glog.Error("CheckELMProductDetail 查询饿了么数据出错：", request, err, dataResponse)
			return err
		}
		if dataResponse.Errno != 0 {
			glog.Error("CheckELMProductDetail 查询饿了么数据出错：", request, err, dataResponse)
			return errors.New("关闭饿了么门店失败:" + dataResponse.Error)
		}
	}

	if stateMt {
		//关闭美团门店
		var mtService MtProductService
		var model product_po2.RetailDeleteBySpuReq
		model.AppPoiCode = ChannelStoreIdMt
		dataResponseMt, err := mtService.CloseShop(model)
		if err != nil {
			return errors.New("关闭美团门店异常" + err.Error())
		}
		if dataResponseMt.Code != 200 {
			return errors.New(dataResponseMt.Msg)
		}
	}
	return nil
}

// cleanProducts 清理商品
func (s *StoreService) cleanProducts(engine *xorm.Engine, ChannelStoreIdMt string, AppChannelMt string, stateMt bool,
	ChannelStoreIdElm string, AppChannelElm string, stateElm bool, shopId string) error {

	// 清理第三方平台数据
	if stateElm {
		if err := s.cleanElmProducts(ChannelStoreIdElm, AppChannelElm); err != nil {
			return err
		}
	}

	if stateMt {
		if err := s.cleanMtProducts(ChannelStoreIdMt, AppChannelMt, shopId); err != nil {
			return err
		}
	}

	// 清理本地商品数据
	if err := s.cleanLocalProductData(engine, shopId); err != nil {
		return err
	}

	return nil
}

// cleanElmProducts 清理饿了么商品
func (s *StoreService) cleanElmProducts(ChannelStoreIdElm string, AppChannelElm string) error {
	var List []*proto.ElmProductList
	//拉取饿了么商品。然后删除
	reqElm := &proto.ElmGetProductListRequest{
		ShopId:     ChannelStoreIdElm,
		Page:       1,
		Pagesize:   100,
		AppChannel: cast.ToInt32(AppChannelElm),
	}

	offsit := int64(-1)
	var elmService ElmProductService
	dataResponse, err := elmService.GetElmProductList(reqElm)
	if err != nil {
		glog.Error("CheckELMProductDetail 查询饿了么数据出错：", reqElm, err, dataResponse)
	}

	// 获取所有商品列表
	for {
		req1 := &proto.ElmGetProductListRequest{
			ShopId:      ChannelStoreIdElm,
			Page:        1,
			Pagesize:    100,
			AppChannel:  cast.ToInt32(AppChannelElm),
			SkuIdOffset: offsit,
		}
		resp, err := elmService.GetElmProductList(req1)
		if err != nil {
			glog.Info("CheckELMProductDetail 查询饿了么商品列表失败", err.Error(), req1)
			return errors.New("查询饿了么商品列表失败")
		}
		if len(resp.Data.List) > 0 {
			List = append(List, resp.Data.List...)
			offsit = int64(resp.Data.SkuIdOffset)
		}
		if resp.Data.Pages == 1 || len(resp.Data.List) == 0 {
			break
		}
	}

	// 删除商品
	for _, x := range List {
		params := proto.UpdateElmShopSkuPriceRequest{
			ShopId:      ChannelStoreIdElm,
			CustomSkuId: cast.ToString(x.CustomSkuId),
			AppChannel:  cast.ToInt32(AppChannelElm),
		}

		skuDelete, err := elmService.DeleteElmShopSku(&params)
		if err != nil {
			glog.Error("CheckELMProductDetail 删除饿了么商品异常： ", skuDelete, err.Error())
			return errors.New("删除饿了么商品异常" + err.Error())
		}
	}

	return nil
}

// cleanMtProducts 清理美团商品
func (s *StoreService) cleanMtProducts(ChannelStoreIdMt string, AppChannelMt string, shopId string) error {
	var ListMt []*proto.StoreRetailInfo

	getRequest := new(proto.RetailListRequest)
	getRequest.StoreMasterId = cast.ToInt32(AppChannelMt)
	getRequest.AppPoiCode = ChannelStoreIdMt
	getRequest.FinanceCode = shopId
	getRequest.PageIndex = 1
	getRequest.PageSize = 200 //todo 写成配置

	var mtService MtProductService
	dataResponse, err := mtService.RetailList(context.Background(), getRequest)
	if err != nil {
		if strings.Contains(err.Error(), "不存在") {
			return nil
		}
		return errors.New("删除美团商品异常" + err.Error())
	}

	totalPage := int(dataResponse.ExtraInfo.TotalCount/getRequest.PageSize + 1)

	// 获取所有商品列表
	for i := 1; i <= totalPage; i++ {
		syncRequest := new(proto.RetailListRequest)
		syncRequest.StoreMasterId = cast.ToInt32(AppChannelMt)
		syncRequest.AppPoiCode = ChannelStoreIdMt
		syncRequest.FinanceCode = shopId
		syncRequest.PageIndex = int32(i)
		syncRequest.PageSize = 200
		responseLeft, errRpc := mtService.RetailList(context.Background(), syncRequest)
		if errRpc != nil {
			return errors.New("获取美团商品出错" + errRpc.Error())
		}
		ListMt = append(ListMt, responseLeft.Data...)
	}

	// 删除商品
	for _, x := range ListMt {
		var skuInfo []*MtSku
		_ = json.Unmarshal([]byte(x.Skus), &skuInfo)

		requestDel := product_po2.RetailDeleteBySpuReq{
			AppPoiCode:        ChannelStoreIdMt,
			AppFoodCode:       cast.ToString(x.AppFoodCode),
			IsDeleteRetailCat: 1,
		}

		skuDelete, err := mtService.RetailDelete(requestDel)
		glog.Info("删除美团商品： ", " 入参： ", requestDel, " 返回：", skuDelete)
		if err != nil {
			glog.Error("删除美团商品异常： ", requestDel, " err: ", err.Error())
			return errors.New("删除美团商品异常" + err.Error())
		}
	}

	return nil
}

// cleanCategories 清理分类
func (s *StoreService) cleanCategories(engine *xorm.Engine, ChannelStoreIdMt string, AppChannelMt string, stateMt bool,
	ChannelStoreIdElm string, AppChannelElm string, stateElm bool, shopId string) error {
	if stateElm {
		// 获取饿了么店铺分类
		elmService := &ElmProductService{}
		elmCategoryReq := &product_po2.GetElmShopCategoryRequest{
			ShopId:     ChannelStoreIdElm,
			AppChannel: cast.ToInt32(AppChannelElm),
		}

		categoryRes, err := elmService.GetElmShopCategory(elmCategoryReq)
		if err != nil {
			glog.Error("获取饿了么店铺分类失败:", err.Error())
			return errors.New("获取饿了么店铺分类失败:" + err.Error())
		}

		// 删除所有分类
		for _, category := range categoryRes.Data {
			delReq := &vo.DelElmShopCategoryRequest{
				ShopId:       ChannelStoreIdElm,
				CategoryId:   category.CategoryId,
				AppChannel:   cast.ToInt32(AppChannelElm),
				CategoryName: category.CategoryName,
			}

			delRes, err := elmService.DelElmShopCategory(delReq)
			if err != nil {
				glog.Error("删除饿了么店铺分类失败:", err.Error())
				return errors.New("删除饿了么店铺分类失败:" + err.Error())
			}

			if delRes.Code != 200 {
				glog.Error("删除饿了么店铺分类失败:", delRes.Error)
				return errors.New("删除饿了么店铺分类失败:" + delRes.Error)
			}
		}
	}
	//清理美团分类  RetailCatList
	if stateMt {

		var mtService MtProductService
		elmCategoryReq := &proto.AppPoiCodeRequest{
			AppPoiCode:    ChannelStoreIdMt,
			StoreMasterId: cast.ToInt32(AppChannelMt),
		}
		dataResponse, err := mtService.RetailCatList(context.Background(), elmCategoryReq)
		if err != nil {
			glog.Error("获取美团店铺分类失败:", err.Error())
			return errors.New("获取美团店铺分类失败:" + err.Error())
		}
		// 删除所有分类
		for _, category := range dataResponse.Data {
			request := &proto.MtRetailCatDeleteRequest{
				AppPoiCode: ChannelStoreIdMt,
				//AppPoiCode:          "4889_21626516",
				CategoryCode:        "",
				CategoryName:        category.Name,
				StoreMasterId:       cast.ToInt32(AppChannelMt),
				MoveProductToUncate: 1,
			}

			delRes, err := mtService.MtRetailCatDelete(request)
			if err != nil {
				glog.Error("删除美团店铺分类失败:", err.Error())
				return errors.New("删除饿了么店铺分类失败:" + err.Error())
			}

			if delRes.Code != 200 {
				glog.Error("删除美团店铺分类失败:", delRes.Error)
				return errors.New("删除饿了么店铺分类失败:" + delRes.Error)
			}
		}

	}

	// 清理本地分类数据
	if err := s.cleanLocalCategoryData(engine, shopId, ChannelStoreIdMt, ChannelStoreIdElm); err != nil {
		return err
	}

	return nil
}

// cleanLocalProductData 清理本地商品数据
func (s *StoreService) cleanLocalProductData(Engine *xorm.Engine, shopId string) error {
	session := Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return fmt.Errorf("开启事务失败: %w", err)
	}

	// 删除门店商品信息
	sql1 := `DELETE FROM pro_product_store_info 
             WHERE store_id = ? AND channel_id IN (2,3)`
	if _, err := session.Exec(sql1, shopId); err != nil {
		session.Rollback()
		return fmt.Errorf("删除门店商品信息失败: %w", err)
	}

	// 删除门店SPU信息
	sql2 := `DELETE FROM pro_product_store_spu 
             WHERE store_id = ? AND channel_id IN (2,3)`
	if _, err := session.Exec(sql2, shopId); err != nil {
		session.Rollback()
		return fmt.Errorf("删除门店SPU信息失败: %w", err)
	}

	if err := session.Commit(); err != nil {
		session.Rollback()
		return fmt.Errorf("提交事务失败: %w", err)
	}
	//提交后清理垃圾数据
	_, err := session.Exec("DELETE FROM pro_product_store  WHERE store_id = ? AND product_id NOT IN ( SELECT product_id  FROM pro_product_store_info   WHERE store_id = ? )", shopId, shopId)
	if err != nil {
		glog.Error("清理下发数据失败", err.Error())
	}
	return nil
}

// cleanLocalCategoryData 清理本地分类数据
func (s *StoreService) cleanLocalCategoryData(Engine *xorm.Engine, shopId string, channelStoreIdMt, channelStoreIdElm string) error {
	session := Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return fmt.Errorf("开启事务失败: %w", err)
	}

	// 删除渠道门店关联信息
	sql1 := `DELETE FROM pro_category_store_thirdid 
             WHERE (channel_store_id = ? and channel_id = 2) 
             or (channel_store_id = ? and channel_id = 3)`
	if _, err := session.Exec(sql1, channelStoreIdMt, channelStoreIdElm); err != nil {
		session.Rollback()
		return fmt.Errorf("删除渠道门店关联信息失败: %w", err)
	}

	// 删除门店前台分类
	sql2 := `DELETE FROM pro_store_front_category 
             WHERE store_id = ?`
	if _, err := session.Exec(sql2, shopId); err != nil {
		session.Rollback()
		return fmt.Errorf("删除门店前台分类失败: %w", err)
	}

	if err := session.Commit(); err != nil {
		session.Rollback()
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

// 此处查询商品的快照信息
type MtSku struct {
	SkuId string `json:"sku_id"`
}

// CheckStoreClean 检查店铺数据清除状态
func (s *StoreService) CheckStoreClean(req dto.StoreAuthorization) (map[string]interface{}, bool, error) {
	s.Begin()
	defer s.Close()

	result := make(map[string]interface{})

	// 检查商品数据
	productCount, err := s.checkProductData(req.ShopId)
	if err != nil {
		return nil, false, err
	}
	result["product_count"] = productCount

	// 检查分类数据
	categoryCount, err := s.checkCategoryData(req.ShopId)
	if err != nil {
		return nil, false, err
	}
	result["category_count"] = categoryCount

	// 检查第三方平台数据
	thirdPartyData, err := s.checkThirdPartyData(req.ShopId)
	if err != nil {
		return nil, false, err
	}
	result["third_party_data"] = thirdPartyData

	// 判断是否清理完成
	isClean := productCount == 0 && categoryCount == 0 && thirdPartyData == 0

	return result, isClean, nil
}

// 检查商品数据
func (s *StoreService) checkProductData(shopId string) (int64, error) {

	count, err := s.Engine.Table("pro_product_store_info").
		Where("store_id = ? AND channel_id IN (2,3)", shopId).
		Count()

	return count, err
}

// 检查分类数据
func (s *StoreService) checkCategoryData(shopId string) (int64, error) {
	count, err := s.Engine.Table("pro_store_front_category").
		Where("store_id = ?", shopId).
		Count()

	return count, err
}

// 检查第三方平台数据
func (s *StoreService) checkThirdPartyData(shopId string) (int64, error) {
	// 获取第三方ID
	ChannelStoreIdMt, AppChannelMt, stateMt := common.GetChannelInfoByStoreId(s.Engine, shopId, Channel_MT)
	ChannelStoreIdElm, AppChannelElm, stateElm := common.GetChannelInfoByStoreId(s.Engine, shopId, Channel_ELM)

	var nowCount int64 = 0
	if stateMt && ChannelStoreIdMt != "" {
		mtCount, err := s.checkMtData(ChannelStoreIdMt, AppChannelMt)
		if err != nil {
			//绑定的无效门店算检测通过
			if !strings.Contains(err.Error(), "未获取有效门店") {
				return 1, err
			}
		}
		nowCount += mtCount
	}

	if stateElm && ChannelStoreIdElm != "" {
		elmCount, err := s.checkElmData(ChannelStoreIdElm, AppChannelElm)
		if err != nil {
			return 1, err
		}
		nowCount += elmCount
	}

	return nowCount, nil
}

// checkMtData 检查美团数据
func (s *StoreService) checkMtData(channelStoreId string, appChannel int) (int64, error) {
	getRequest := new(proto.RetailListRequest)
	getRequest.StoreMasterId = cast.ToInt32(appChannel)
	getRequest.AppPoiCode = channelStoreId
	getRequest.PageIndex = 1
	getRequest.PageSize = 1

	var mtService MtProductService
	dataResponse, err := mtService.RetailList(context.Background(), getRequest)
	if err != nil {
		if strings.Contains(err.Error(), "不存在") {
			return 0, nil
		}
		return 0, err
	}

	return cast.ToInt64(dataResponse.ExtraInfo.TotalCount), nil
}

// checkElmData 检查饿了么数据
func (s *StoreService) checkElmData(channelStoreId string, appChannel int) (int64, error) {
	elmService := &ElmProductService{}
	elmCategoryReq := &product_po2.GetElmShopCategoryRequest{
		ShopId:     channelStoreId,
		AppChannel: cast.ToInt32(appChannel),
	}

	categoryRes, err := elmService.GetElmShopCategory(elmCategoryReq)
	if err != nil {
		return 0, err
	}

	return int64(len(categoryRes.Data)), nil
}
