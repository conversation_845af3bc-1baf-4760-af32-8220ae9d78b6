package services

import (
	"context"
	omnibus_vo "eShop/view-model/omnibus-vo"
	"errors"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"eShop/infra/log"
	"eShop/infra/pkg/app"
	"eShop/infra/pkg/code"
	"eShop/infra/pkg/util"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/proto/dac"
	"eShop/services/common"
)

const (
	StoreAppChannelRedisKey = "datacenter:store:app-channel:"
)

func GetStoreMasterId(appId string, channelId int32) (storeMasterId int32, retCode int) {
	retCode = code.ErrorCommon

	logHead := "GetStoreMasterId:"
	redisConn := cache.GetRedisConn()
	// channelId  2美团,3饿了么,4京东到家
	key := ""
	switch channelId {
	case app.Channel_ELM:
		key = app.StoreMasterIdElmRedisKey + appId
	case app.Channel_MT:
		key = app.StoreMasterIdMtRedisKey + appId
	case app.Channel_JDDJ:
		key = app.StoreMasterIdJddjRedisKey + appId
	default:
		retCode = code.ErrorCommon
		return
	}

	storeMasterIdTemp, _ := strconv.Atoi(redisConn.Get(key).Val())
	if storeMasterIdTemp != 0 {
		retCode = code.Success
		storeMasterId = int32(storeMasterIdTemp)
		return
	}
	requestParam := &dac.GetStoreMasterIDRequest{AppId: appId, ChannelId: channelId}
	resp, err := GetStoreMasterIDByAppId(requestParam)
	if err != nil || resp.Common.Code != dac.RetCode_SUCCESS {
		log.Error(logHead, "dacClient.RPC.GetStoreMasterIDRequest,", err)
		return
	}

	if resp.StoreMasterId != 0 {
		redisConn.SetNX(key, resp.StoreMasterId, 360*time.Hour)
	}

	retCode = code.Success
	storeMasterId = resp.StoreMasterId
	return
}
func GetStoreMasterChannelAppConfig(id int32, channelId int32) (appConfig *app.ChannelAppConfig, retCode int) {
	logHead := "GetStoreMasterChannelAppConfig:"
	redisConn := cache.GetRedisConn()
	retCode = code.Success

	appConfig = new(app.ChannelAppConfig)
	key := ""

	tokenKey := ""
	jddjToken := ""
	switch channelId {
	case app.Channel_ELM:
		key = app.StoreMasterElmRedisKey + strconv.Itoa(int(id))
	case app.Channel_MT:
		key = app.StoreMasterMtRedisKey + strconv.Itoa(int(id))
	case app.Channel_JDDJ:
		key = app.StoreMasterJddjRedisKey + strconv.Itoa(int(id))
		tokenKey = app.StoreMasterJddjTokenRedisKey + strconv.Itoa(int(id))
		jddjToken = redisConn.Get(tokenKey).Val()
		if jddjToken == "" {
			log.Error(logHead, "京东到家token缺失,", id, channelId)
		}
		appConfig.AppToken = jddjToken
	default:
		retCode = code.ErrorCommon
		return
	}

	StoreMasterInfo := redisConn.Get(key).Val()
	if StoreMasterInfo != "" {
		slice := util.StringToSlice(StoreMasterInfo, app.DelimiterForStoreMaster)
		if len(slice) >= 2 {
			appConfig.AppId = slice[0]
			appConfig.AppSecret = slice[1]
			return
		}
		log.Error(logHead, "util.StringToSlice failed,", StoreMasterInfo, id, channelId)
	}
	log.Info(logHead, "get redis failed,", StoreMasterInfo, id, channelId)
	requestParam := &dac.GetStoreMasterInfoRequest{Id: id}
	resp, err := GetStoreMasterInfo(requestParam)
	if err != nil || resp.Common.Code != dac.RetCode_SUCCESS {
		log.Error(logHead, "dacClient.RPC.GetStoreMasterInfo,", err)
		return
	}
	if resp.Data.ElmAppId != "" && resp.Data.ElmAppSecret != "" {
		key := app.StoreMasterElmRedisKey + strconv.Itoa(int(resp.Data.Id))
		redisConn.SetNX(key, resp.Data.ElmAppId+app.DelimiterForStoreMaster+resp.Data.ElmAppSecret, 360*time.Hour) // 1 年
	}
	if resp.Data.MtAppId != "" && resp.Data.MtAppSecret != "" {
		key := app.StoreMasterMtRedisKey + strconv.Itoa(int(resp.Data.Id))
		redisConn.SetNX(key, resp.Data.MtAppId+app.DelimiterForStoreMaster+resp.Data.MtAppSecret, 360*time.Hour) // 1 年
	}
	if resp.Data.JddjAppId != "" && resp.Data.JddjAppSecret != "" {
		key := app.StoreMasterJddjRedisKey + strconv.Itoa(int(resp.Data.Id))
		redisConn.SetNX(key, resp.Data.JddjAppId+app.DelimiterForStoreMaster+resp.Data.JddjAppSecret, 360*time.Hour) // 1 年
	}
	switch channelId {
	case app.Channel_ELM:
		appConfig.AppId = resp.Data.ElmAppId
		appConfig.AppSecret = resp.Data.ElmAppSecret
	case app.Channel_MT:
		appConfig.AppId = resp.Data.MtAppId
		appConfig.AppSecret = resp.Data.MtAppSecret
	case app.Channel_JDDJ:
		appConfig.AppId = resp.Data.JddjAppId
		appConfig.AppSecret = resp.Data.JddjAppSecret
	}
	return
}

// 根据AppId和渠道获取 店铺主体id(appchannel)
func GetStoreMasterIDByAppId(params *dac.GetStoreMasterIDRequest) (*dac.GetStoreMasterIDResponse, error) {
	logHead := "DataCenter.GetStoreMasterIDByAppId"
	out := new(dac.GetStoreMasterIDResponse)
	out.Common = new(dac.CommonResponse)
	out.Common.Code = dac.RetCode_SUCCESS
	s := common.BaseService{}
	session := s.Engine.NewSession()

	session.Table("store_master").Select("app_channel").Where("is_deleted = ?", 0)
	switch params.ChannelId {
	case app.Channel_ELM:
		session.And("elm_app_id=?", params.AppId)
	case app.Channel_MT:
		session.And("mt_app_id=?", params.AppId)
	case app.Channel_JDDJ:
		session.And("jddj_app_id=?", params.AppId)
	default:
		out.Common.Code = dac.RetCode_InvalidParams
		err := errors.New("InvalidParams")
		return out, err
	}

	storeMaster := omnibus_vo.StoreMaster{}
	has, err := session.Get(&storeMaster)
	if err != nil || !has {
		out.Common.Code = dac.RetCode_ERROR
		log.Error(logHead, "find store_master failed,", utils.JsonEncode(params), has, err)
		return out, err
	}

	out.StoreMasterId = int32(storeMaster.AppChannel)

	return out, nil
}

// 根据店铺主体id(appchannel)和渠道id 获取appid和appsecret
func GetStoreMasterInfo(params *dac.GetStoreMasterInfoRequest) (*dac.GetStoreMasterInfoResponse, error) {
	logHead := "DataCenter.GetStoreMasterInfo"
	out := new(dac.GetStoreMasterInfoResponse)
	out.Common = new(dac.CommonResponse)
	out.Common.Code = dac.RetCode_SUCCESS
	out.Data = new(dac.StoreMasterInfo)

	s := common.BaseService{}
	s.Begin()
	defer s.Close()
	conn := s.Engine.NewSession()
	storeMaster := omnibus_vo.StoreMaster{}

	has, err := conn.Table("datacenter.store_master").
		Select("app_channel,name,elm_app_id,elm_app_secret,mt_app_id,mt_app_secret,jddj_app_id,jddj_app_secret").
		Where("is_deleted = ?", 0).In("app_channel", params.Id).Get(&storeMaster)
	if err != nil || !has {
		out.Common.Code = dac.RetCode_ERROR
		log.Error(logHead, "find store_master failed,", utils.JsonEncode(params), has, err)
		return out, err
	}

	out.Data = &dac.StoreMasterInfo{
		Id:            int32(storeMaster.AppChannel),
		Name:          storeMaster.Name,
		ElmAppId:      storeMaster.ElmAppId,
		ElmAppSecret:  storeMaster.ElmAppSecret,
		MtAppId:       storeMaster.MtAppId,
		MtAppSecret:   storeMaster.MtAppSecret,
		JddjAppId:     storeMaster.JddjAppId,
		JddjAppSecret: storeMaster.JddjAppSecret,
	}

	return out, nil
}

// 根据财务编码获取appChannel
func GetAppChannelByFinanceCode(params *dac.GetAppChannelRequest) (*dac.GetAppChannelResponse, error) {
	out := new(dac.GetAppChannelResponse)
	out.Code = 200
	out.Message = "Success"

	if params.FinanceCode == "" {
		out.Code = 400
		out.Message = "参数非法"
		return out, nil
	}
	s := common.BaseService{}
	s.Begin()
	defer s.Close()
	conn := s.Engine.NewSession()
	var appChannel int32

	has, err := conn.Table("datacenter.store").Select("app_channel").Where("finance_code=?", params.FinanceCode).Get(&appChannel)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		log.Error("GetAppChannelByFinanceCode 查询失败，请求参数：", utils.JsonEncode(params), err)
		return out, err
	}
	if !has {
		out.Code = 400
		out.Message = "未查询到门店数据"
		log.Info("GetAppChannelByFinanceCode 未查询到门店数据，请求参数：", utils.JsonEncode(params))
		return out, nil
	}
	// 数据库的老数据appChannel为0,需要置为1,意为瑞鹏自有
	if appChannel == 0 {
		appChannel = 1
	}
	out.AppChannel = appChannel

	redisConn := cache.GetRedisConn()
	key := StoreAppChannelRedisKey + params.FinanceCode
	redisConn.SetNX(key, appChannel, time.Hour*8760+time.Second*time.Duration(rand.Int63n(300))) //1 年

	return out, nil
}

// 根据财务编码获取appChannel
func GetAppChannelByFinanceCodeStr(shopId string) (*dac.GetAppChannelResponse, error) {
	out := new(dac.GetAppChannelResponse)
	out.Code = 200
	out.Message = "Success"

	if shopId == "" {
		out.Code = 400
		out.Message = "参数非法"
		return out, nil
	}
	s := common.BaseService{}
	s.Begin()
	defer s.Close()
	conn := s.Engine.NewSession()
	var appChannel int32

	has, err := conn.Table("datacenter.store").Select("app_channel").Where("finance_code=?", shopId).Get(&appChannel)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		log.Error("GetAppChannelByFinanceCode 查询失败，请求参数：", utils.JsonEncode(shopId), err)
		return out, err
	}
	if !has {
		out.Code = 400
		out.Message = "未查询到门店数据"
		log.Info("GetAppChannelByFinanceCode 未查询到门店数据，请求参数：", utils.JsonEncode(shopId))
		return out, nil
	}
	// 数据库的老数据appChannel为0,需要置为1,意为瑞鹏自有
	if appChannel == 0 {
		appChannel = 1
	}
	out.AppChannel = appChannel

	redisConn := cache.GetRedisConn()
	key := StoreAppChannelRedisKey + shopId
	redisConn.SetNX(key, appChannel, time.Hour*8760+time.Second*time.Duration(rand.Int63n(300))) //1 年

	return out, nil
}

type StoreAppDto struct {
	FinanceCode string `json:"finance_code"`
	AppChannel  int    `json:"app_channel"`
}

func GetFinanAppChannel(fins []string) (map[string]int, error) {
	s := common.BaseService{}
	s.Begin()
	defer s.Close()
	conn := s.Engine.NewSession()
	var data []StoreAppDto
	sql := "select finance_code, app_channel from datacenter.store s where finance_code  in ('" + strings.Join(fins, "','") + "')"
	err := conn.SQL(sql).Find(&data)
	if err != nil {
		return nil, err
	}

	m := make(map[string]int)
	for i := range data {
		dtos := data[i]
		m[dtos.FinanceCode] = dtos.AppChannel
	}
	return m, nil

}

// 查询与渠道关联的门店信息
func QueryStoreInfo(ctx context.Context, in *dac.StoreInfoRequest) (*dac.StoreInfoResponse, error) {
	out := new(dac.StoreInfoResponse)
	out.Code = 400
	s := common.BaseService{}
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	//var session *xorm.Session
	storeCodeLen := len(in.StoreCode)
	financeCodeLen := len(in.FinanceCode)
	if in.ChannelId == 0 {
		session.Table("store").
			Cols("id,name,shortname,storeCode,finance_code,zilong_id,pointX,pointY,bigregion,provinceid," +
				"province,cityid,city,countyid,county,address,desc,custom_code,elm_delivery,app_channel,delivery_method").Where("1=1")
		if financeCodeLen > 0 {
			session.In("store.finance_code", in.FinanceCode)
		}
	} else {
		//todo sql explain分析
		session.Table("store_relation").
			Join("INNER", "store", "store_relation.finance_code=store.finance_code").
			Select("store_relation.id, store_relation.finance_code, store_relation.channel_id,"+
				"store_relation.channel_store_id, store_relation.custom_code, store_relation.is_create,store.name,store.zilong_id,store.app_channel,store.delivery_method,store.sell_drugs,store.drugs_channel_ids").
			Where("store_relation.channel_id=? and store_relation.channel_store_id != ''", in.ChannelId)
		if storeCodeLen > 0 && financeCodeLen > 0 {
			//既有storeCode 又有 finance_code 则等于财务编码 或者 第三方门店id的都返回
			session.And("store.finance_code IN('?') OR store_relation.channel_store_id IN('?')", strings.Join(in.FinanceCode, "','"), strings.Join(in.StoreCode, "','"))
		} else if storeCodeLen > 0 {
			//只有storeCode 根据storeCode查
			session.In("store_relation.channel_store_id", in.StoreCode)
		} else if financeCodeLen > 0 {
			//financeCode 根据finance_code查
			session.In("store.finance_code", in.FinanceCode)
		}
	}

	if in.Name != "" {
		session.And("store.name like ?", "'%"+in.Name+"%'")
	}
	if err := session.Find(&out.Details); err != nil {
		log.Error(err)
		return nil, err
	}

	out.Code = 200
	return out, nil
}
