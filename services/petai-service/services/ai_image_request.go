package services

import (
	"context"
	"eShop/infra/log"
	"eShop/infra/utils"
	petai_vo "eShop/view-model/petai-vo"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/golang/glog"
	"github.com/limitedlee/microservice/common/config"
	"strings"
	"time"
)

// 简化的请求结构体
type AnnotateRequest struct {
	BasicInfo        string `json:"basic_info"`
	ImageURL         string `json:"image_url"`
	Stream           bool   `json:"stream"`
	SymptomsDesc     string `json:"symptoms_desc"`     // 可选，症状描述
	SymptomsDuration string `json:"symptoms_duration"` //可选，症状持续时间
	SymptomsType     string `json:"symptoms_type"`     //可选，症状类型
	Dialog           string `json:"dialog"`            // 截至用户输入此张图片的对话记录（特别注意，否则相当于标签泄露，会高估模型效果）
	Parts            string `json:"parts"`             // 部位
}

type AnnotateAPIResponse struct {
	ID      string `json:"id,omitempty"`
	Object  string `json:"object,omitempty"`
	Model   string `json:"model,omitempty"`
	Created int64  `json:"created,omitempty"`

	Choices []struct {
		FinishReason string `json:"finish_reason"`
		Index        int    `json:"index"`
		Message      struct {
			Content string `json:"content"`
			Role    string `json:"role"`
		} `json:"message"`
	} `json:"choices,omitempty"`

	Usage *struct {
		CompletionTokens int `json:"completion_tokens"`
		PromptTokens     int `json:"prompt_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage,omitempty"`

	// 👇 兼容错误结构
	Error *struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    int    `json:"code"`
	} `json:"error,omitempty"`
}

func ParamsConvert(medicalPetInfo petai_vo.MedicalPetInfo) map[string]string {
	var params = make(map[string]string)

	if medicalPetInfo.PetName != "" {
		params["昵称"] = medicalPetInfo.PetName
	} else {
		params["昵称"] = "未知"
	}
	if medicalPetInfo.PetSexStr != "" {
		params["性别"] = medicalPetInfo.PetSexStr
	} else {
		params["性别"] = "未知"
	}
	if medicalPetInfo.PetKindofStr != "" {
		params["种类"] = medicalPetInfo.PetKindofStr
	} else {
		params["种类"] = "未知"
	}
	if medicalPetInfo.PetVarietyStr != "" {
		params["品种"] = medicalPetInfo.PetVarietyStr
	} else {
		params["品种"] = "未知"
	}
	if medicalPetInfo.PetAge != "" {
		params["年龄"] = medicalPetInfo.PetAge
	} else {
		params["年龄"] = "未知"
	}
	if medicalPetInfo.PetWeight != "" {
		params["体重"] = medicalPetInfo.PetWeight + "kg"
	} else {
		params["体重"] = "未知"
	}
	if medicalPetInfo.PetNeuteringStr != "" {
		params["绝育状态"] = medicalPetInfo.PetNeuteringStr
	} else {
		params["绝育状态"] = "未知"
	}
	if medicalPetInfo.Feeding != "" {
		params["喂养方式"] = medicalPetInfo.Feeding
	} else {
		params["喂养方式"] = "未知"
	}
	if medicalPetInfo.WashFreq != "" {
		params["洗澡频次"] = medicalPetInfo.WashFreq
	} else {
		params["洗澡频次"] = "未知"
	}

	//免疫情况:1已免疫，2未免疫，3免疫不全，4免疫不详 // todo  petai-v2.3.0暂时不传
	/*switch pmConsultInfo.ImmuneStatus {
	case 1:
		params["免疫状态"] = "已免疫"
	case 2:
		params["未免疫"] = "未免疫"
	case 3:
		params["免疫不全"] = "免疫不全"
	case 4:
		params["免疫不详"] = "免疫不详"
	default:
		params["免疫不详"] = "未知"
	}*/

	return params
}

// CallAnnotatePre
// petInfoId 宠物Id
// imageURL 图片地址 用户上传的图片
// symptomsDesc 症状描述 对应的是用户输入的文本 可为空
// sectionUuid 话题ID
// parts 部位
func CallAnnotatePre(ctx context.Context, petInfoId, imageURL, symptomsDesc, sectionUuid, parts string, petInfo petai_vo.MedicalPetInfo) (string, error) {
	logPrefix := fmt.Sprintf("====CallAnnotatePre,petInfoId:%s,imageURL:%s,symptomsDesc:%s====", petInfoId, imageURL, symptomsDesc)

	aiResult, err := CallAnnotateAPI(ctx, petInfo, imageURL, symptomsDesc, sectionUuid, parts)
	if err != nil {
		log.Errorf("%s CallAnnotateAPI失败,err为%s", logPrefix, err.Error())
		return "", err
	}
	return aiResult, err
}

// CallAnnotateAPI 调用annotate API
// medicalPetInfo 宠物信息
// imageURL 图片地址
// symptomsDesc 症状描述
// sectionUuid 话题id
// parts 部位
func CallAnnotateAPI(ctx context.Context, medicalPetInfo petai_vo.MedicalPetInfo, imageURL, symptomsDesc, sectionUuid, parts string) (string, error) {
	// API配置
	//apiURL := "http://10.204.8.6:8065/annotate" // uat
	//apiURL := "http://10.204.8.5:8065/annotate" // prod
	apiURL := config.GetString("pet_ai.addr")
	log.Info("CallAnnotateAPI 开始执行：", time.Now().Format("2006-01-02 15:04:05"))
	params := ParamsConvert(medicalPetInfo)
	basicInfoBytes, err := json.Marshal(params)
	if err != nil {
		return "", fmt.Errorf("序列化 BasicInfo 失败: %v", err)
	}

	symptomRecent := "未知" // 症状出现时间：1-小于7天，2-小于1个月，3-小于3个月，4-3个月以上
	symptomsType := "未知"
	dialog := new(CozeService).GetDialog(sectionUuid)
	requestData := AnnotateRequest{
		BasicInfo:        string(basicInfoBytes),
		ImageURL:         imageURL,
		Stream:           false,
		SymptomsDesc:     symptomsDesc,
		SymptomsDuration: symptomRecent,
		SymptomsType:     symptomsType,
		Dialog:           dialog,
		Parts:            parts,
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		glog.Error("CallAnnotateAPI 序列化请求数据失败:", err)
		return "", fmt.Errorf("序列化请求数据失败: %v", err)
	}
	// 设置请求头
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	// 调用API
	log.Info("开始调用annotate API:", apiURL)
	response, err := utils.Post(apiURL, jsonData, "application/json", headers)
	log.Info("CallAnnotateAPI 请求参数：", string(jsonData), "response=", response, "err=", err)
	if err != nil {
		log.Error("CallAnnotateAPI 调用annotate API失败:", err)
		return "", fmt.Errorf("调用annotate API失败: %v", err)
	}

	// 解析响应
	var apiResponse AnnotateAPIResponse
	err = json.Unmarshal([]byte(response), &apiResponse)
	if err != nil {
		log.Error("JSON 解析失败:", err)
		return "", fmt.Errorf("annotate API 响应解析失败: %v", err)
	}
	// 👇 判断是否是错误返回
	if apiResponse.Error != nil {
		log.Warning("annotate API 返回错误:", apiResponse.Error.Message)
		if strings.Contains(apiResponse.Error.Message, "cannot identify image file") {
			return "", fmt.Errorf("图片无法识别，请更换清晰的图片")
		}
		return "", fmt.Errorf("annotate API 错误: %s", apiResponse.Error.Message)
	}

	// 👇 正常处理流程
	if len(apiResponse.Choices) == 0 {
		return "", errors.New("annotate API 调用失败：无返回内容")
	}

	choice := apiResponse.Choices[0]
	if choice.FinishReason != "stop" {
		return "", fmt.Errorf("API调用未完成，完成原因: %s", choice.FinishReason)
	}
	return choice.Message.Content, nil
}
