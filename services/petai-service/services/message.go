package services

import (
	petai_po "eShop/domain/petai-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	petai_vo "eShop/view-model/petai-vo"
	"fmt"
	"strings"
	"time"

	"github.com/coze-dev/coze-go"
)

func (s *CozeService) SaveMessage(in petai_vo.ChatReq, cozeMsg *coze.Message) (messageId int, err error) {
	s.<PERSON>gin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	logPrefix := fmt.Sprintf("====保存会话消息====入参:%s|cozeMsg:%s", utils.JsonEncode(in), utils.JsonEncode(cozeMsg))
	log.Info(logPrefix)
	botId := s.GetBotId(in.BotType)
	//组装消息

	session.Begin()
	content := in.Message.TextContent
	imageUrl := ""
	if in.Message.ContentType == petai_po.MessageContentTypeObjectString {
		content = utils.JsonEncode(in.Message.ObjectContent)
		for _, v := range in.Message.ObjectContent {
			if v.Type == "image" {
				imageUrl += "," + v.FileUrl
			}
		}
	}
	question := petai_po.PetaiMessage{
		Uuid:                     in.Uuid,
		Intent:                   petai_po.IntentBaiKe,
		DataFrom:                 petai_po.DataFromAI,
		UserInfoId:               in.UserInfoId,
		BotId:                    botId,
		BotType:                  in.BotType,
		CozeConversationId:       in.CozeConversationId,       //coze中的会话id
		CozeConversationCreateAt: in.CozeConversationCreateAt, //coze会话创建时间
		ConversationId:           in.ConversationId,
		SectionId:                cozeMsg.SectionID,
		ChatId:                   cozeMsg.ChatID,
		Role:                     petai_po.MessageRoleUser,
		Type:                     petai_po.MessageTypeQuestion,
		ContentType:              in.Message.ContentType,
		Content:                  content,
		MessageTime:              time.Now(),
		SectionUuid:              in.LastestSectionUuid,
	}
	if _, err = session.Table("eshop.petai_message").Insert(&question); err != nil {
		log.Error(logPrefix, "插入问题消息失败 err=", err.Error())
		session.Rollback()
		return
	}
	answerUuid := utils.GenerateUUID()
	answer := petai_po.PetaiMessage{
		Uuid:                     answerUuid,
		DataSource:               petai_po.DataSourceCoze,
		DataFrom:                 petai_po.DataFromAI,
		UserInfoId:               in.UserInfoId,
		BotId:                    botId,
		BotType:                  in.BotType,
		CozeConversationId:       in.CozeConversationId,       //coze中的会话id
		CozeConversationCreateAt: in.CozeConversationCreateAt, //coze会话创建时间
		ConversationId:           in.ConversationId,
		SectionId:                cozeMsg.SectionID,
		ChatId:                   cozeMsg.ChatID,
		CozeMessageId:            cozeMsg.ID,
		Role:                     string(cozeMsg.Role),
		Type:                     string(cozeMsg.Type),
		ContentType:              string(cozeMsg.ContentType),
		Content:                  cozeMsg.Content,
		ReasoningContent:         in.ReasoningContent,
		MessageTime:              time.Now().Add(time.Second * 1),
		SectionUuid:              in.LastestSectionUuid,
	}

	//插入消息
	if _, err = session.Table("eshop.petai_message").Insert(&answer); err != nil {
		log.Error(logPrefix, "插入消息失败 err=", err.Error())
		session.Rollback()
		return
	}
	messageId = answer.Id

	session.Commit()

	// 疾病自诊 入库
	if len(in.PetInfoId) > 0 && in.BotType == petai_po.BotTypePetDiagnose && len(cozeMsg.Content) > 0 {

		productName := utils.PetDiagnoseDiseaseName(cozeMsg.Content) //疑似症状/疾病
		//保存诊断结果
		if len(productName) > 0 {
			data := petai_po.UserPetVaccinateRecord{
				PetInfoId:      in.PetInfoId,
				Type:           petai_po.UserPetVaccinateRecordTypePetDiagnose,
				ConversationId: in.ConversationId,
				MessageUuid:    answerUuid,
				OperationYear:  time.Now().Year(),
				OperationDate:  time.Now(),
				ProductName:    productName,
				RecordPhoto:    strings.Trim(imageUrl, ","),
			}
			_, err = session.Table("eshop.user_pet_vaccinate_record").Insert(&data)
			if err != nil {
				log.Error(logPrefix, "插入诊断结果失败 err=", err.Error())
			}
		}

	}

	return

}

// 保存医疗模型返回的数据
func (s *CozeService) SaveMessageFormMedical(in petai_vo.SaveMessageFormMedicalReq) (messageId int, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	logPrefix := fmt.Sprintf("====保存会话医疗模型消息====入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	question := petai_po.PetaiMessage{
		Uuid:           in.Uuid,
		Intent:         petai_po.IntentMedical,
		UserInfoId:     in.UserInfoId,
		DataSource:     0,
		DataFrom:       petai_po.DataFromAI,
		BotType:        petai_po.BotTypeXiaoWenModel,
		BotId:          s.GetBotId(petai_po.BotTypeXiaoWenModel),
		ConversationId: in.ConversationId,
		SectionId:      "",
		ChatId:         "",
		Role:           petai_po.MessageRoleUser,
		Type:           petai_po.MessageTypeQuestion,
		ContentType:    in.ContentType,
		Content:        in.Question,
		MessageTime:    time.Now(),
		SectionUuid:    in.LastestSectionUuid,
		ImageText:      in.ImageText,
	}
	//插入消息

	if _, err = session.Table("eshop.petai_message").Insert(&question); err != nil {
		log.Error(logPrefix, "插入问题消息失败 err=", err.Error())

		return
	}

	if in.ContentType == petai_po.MessageContentTypeObjectString && in.ImageText != "" {
		in.ImageText = ""
	}
	answer := petai_po.PetaiMessage{
		Uuid:             utils.GenerateUUID(),
		DataSource:       petai_po.DataSourcePetaiAI,
		DataFrom:         petai_po.DataFromAI,
		UserInfoId:       in.UserInfoId,
		BotType:          petai_po.BotTypeXiaoWenModel,
		BotId:            s.GetBotId(petai_po.BotTypeXiaoWenModel),
		ConversationId:   in.ConversationId,
		SectionId:        "",
		ChatId:           "",
		CozeMessageId:    "",
		Role:             petai_po.MessageRoleAssistant,
		Type:             petai_po.MessageTypeAnswer,
		ContentType:      in.ContentType,
		Content:          in.Answer,
		ReasoningContent: in.ReasoningContent,
		MessageTime:      time.Now().Add(time.Second * 1),
		SectionUuid:      in.LastestSectionUuid,
		ImageText:        in.ImageText,
	}
	//插入消息
	if _, err = session.Table("eshop.petai_message").Insert(&answer); err != nil {
		log.Error(logPrefix, "插入回答消息失败 err=", err.Error())
		return
	}
	messageId = answer.Id

	return

}

// 保存互联网医院问诊单会话消息
func (s *CozeService) SaveConsultConversationMessage(in petai_vo.SaveConsultConversationMessageReq, pmOrderSn int64) (messageId int, messageUuid string, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	logPrefix := fmt.Sprintf("====保存互联网医院问诊单会话消息====互联网医院问诊单号:%d", pmOrderSn)
	log.Info(logPrefix, "入参:", utils.JsonEncode(in))

	question := petai_po.PetaiConsultMessage{
		Uuid:                  in.Uuid,
		ConsultConversationId: in.ConsultConversationId,
		Role:                  petai_po.MessageRoleUser,
		Type:                  petai_po.MessageTypeQuestion,
		ContentType:           petai_po.MessageContentTypeText,
		Content:               in.Question,
		ReferenceContent:      "", // 问题没有引用程度，
		ReferenceSame:         0,  // 问题没有引用程度，
		MetaData:              "",
	}
	//插入消息
	if _, err = session.Table("eshop.petai_consult_message").Insert(&question); err != nil {
		log.Error(logPrefix, "插入问题消息失败 err=", err.Error())

		return
	}
	messageUuid = utils.GenerateUUID()
	message := petai_po.PetaiConsultMessage{
		Uuid:                  messageUuid,
		ConsultConversationId: in.ConsultConversationId,
		Role:                  petai_po.MessageRoleAssistant,
		Type:                  petai_po.MessageTypeAnswer,
		ContentType:           petai_po.MessageContentTypeText,
		Content:               in.Answer,

		ReferenceContent: "", // 问题没有引用程度，
		ReferenceSame:    0,  // 问题没有引用程度，
		MetaData:         "",
	}
	//插入消息
	if _, err = session.Table("eshop.petai_consult_message").Insert(&message); err != nil {
		log.Error(logPrefix, "插入回答消息失败 err=", err.Error())
		return
	}
	messageId = message.Id

	return

}

// 保存医疗模型返回的数据
func (s *CozeService) SaveNewMessage(in petai_vo.SaveNewMessageReq) (resp *petai_vo.SaveNewMessageResp, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	logPrefix := fmt.Sprintf("SaveNewMessage====保存new会话模型消息====入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	if in.Question == "" { //todo 传空不落库，但是需要返回话题id
		return &petai_vo.SaveNewMessageResp{
			MessageId:   0,
			SectionUuid: utils.GenerateUUID(),
		}, nil
	}
	resp = new(petai_vo.SaveNewMessageResp)
	question := petai_po.PetaiMessage{
		Uuid: utils.GenerateUUID(),
		//Intent:         petai_po.IntentMedical,
		UserInfoId:     in.UserInfoId,
		DataSource:     0,
		DataFrom:       petai_po.DataFromAI,
		BotType:        petai_po.BotTypeXiaoWenModel,
		BotId:          s.GetBotId(petai_po.BotTypeXiaoWenModel),
		ConversationId: in.ConversationId,
		SectionId:      "",
		ChatId:         "",
		Role:           "system",
		Type:           "system",
		ContentType:    petai_po.MessageContentTypeText,
		Content:        in.Question,
		MessageTime:    time.Now(),
		SectionUuid:    utils.GenerateUUID(),
	}

	if _, err = session.Table("eshop.petai_message").Insert(&question); err != nil {
		log.Error(logPrefix, "插入提示消息失败 err=", err.Error())
		return nil, fmt.Errorf("插入提示消息失败")
	}

	return &petai_vo.SaveNewMessageResp{
		MessageId:   question.Id,
		SectionUuid: question.SectionUuid,
	}, nil
}

// GetDialog 按 section_uuid 聚合会话上下文，返回格式化后的整段文本。
// (宠主) 问: xxx
// (医生) 答: yyy
func (s *CozeService) GetDialog(sectionUuid string) string {
	if strings.TrimSpace(sectionUuid) == "" {
		log.Warning("GetDialog: sectionUuid 为空")
		return ""
	}

	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	logPrefix := fmt.Sprintf("GetDialog====获取上下文会话====入参sectionUuid:%s", sectionUuid)
	log.Info(logPrefix)

	const gcLen = 10 * 1024 * 1024 // 10MB
	_, _ = session.Exec("SET SESSION group_concat_max_len = ?", gcLen)

	const sqlStr = `
SELECT /*+ SET_VAR(group_concat_max_len=10485760) */
       GROUP_CONCAT(
         CASE COALESCE(role, intent)
           WHEN 'user'      THEN CONCAT('(宠主) 问: ', IFNULL(content,''))
           WHEN 'assistant' THEN CONCAT('(医生) 答: ', IFNULL(content,''))
           ELSE NULL
         END
         ORDER BY id ASC
         SEPARATOR '\n'
       ) AS chat_text
FROM eshop.petai_message
WHERE section_uuid = ?
  AND COALESCE(role, intent) IN ('user','assistant');`

	// 用 QueryString 直接拿字符串 map，更方便
	rows, err := session.QueryString(sqlStr, sectionUuid)
	if err != nil {
		log.Error("GetDialog: 查询失败", "err", err, "sectionUuid", sectionUuid)
		return ""
	}
	if len(rows) == 0 {
		// 理论上聚合一行也应返回；这里双保险
		return ""
	}

	chatText := rows[0]["chat_text"]
	// chat_text 可能为 NULL -> 空字符串
	chatText = strings.TrimSpace(chatText)
	return chatText
}
