package services

// 请求志辉的前置模型
import (
	"bufio"
	"bytes"
	"context"
	petai_po "eShop/domain/petai-po"
	"eShop/infra/config"
	"eShop/infra/log"
	"eShop/infra/utils"
	petai_vo "eShop/view-model/petai-vo"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/spf13/cast"
)

// 解析JSON验证结构
type MedicalChatDetails struct {
	Probabilities map[string]float64 `json:"probabilities"` // 概率
	ResponseType  string             `json:"response_type"` // 响应类型
}
type MedicalChatRes struct {
	Type           string             `json:"type"`            // 类型:question表示问题，类型:content表示纯文本,option:选项，reasoning_content表示推理内容
	Chunk          string             `json:"chunk"`           // 若类型为content,则表示流式回答内容,若类型为reasoning_content,则表示流式推理内容
	FullResponse   string             `json:"full_response"`   // 若类型为content,则表示完整回答内容,若类型为reasoning_content,则表示完整推理内容
	IsEnd          bool               `json:"is_end"`          // 是否结束
	Intent         string             `json:"intent"`          // 意图
	Confidence     float64            `json:"confidence"`      // 置信度
	ProcessingTime float64            `json:"processing_time"` // 处理时间
	Details        MedicalChatDetails `json:"details"`         // 详细信息
}

type MedicalChatRes2 struct {
	Text           string             `json:"text"`
	Intent         string             `json:"intent"`
	Confidence     float64            `json:"confidence"`
	ProcessingTime float64            `json:"processing_time"`
	ThreadId       string             `json:"thread_id"`
	QuestionUuid   string             `json:"question_uuid"`
	PetInfoId      string             `json:"pet_info_id"`
	Details        MedicalChatDetails `json:"details"`
}

func (s *CozeService) MedicalUrl() (url string) {

	url = config.Get("petai_coze_medical_url")
	if len(url) == 0 {
		// url = "http://10.11.35.163:8066/query" // 志辉本地
		//url = "http://43.142.65.25:8066/query"
		url = "http://115.159.103.55:8066/query"
	}
	return
}

func (s *CozeService) StreamPost(w http.ResponseWriter, flusher http.Flusher, url string, data petai_vo.MedicalStreamPostReq, contentType string, headers map[string]string, imageUrl string, cmd petai_vo.ChatReq) (singleRes MedicalChatRes, uuid string) {
	logPrefix := fmt.Sprintf("====请求医疗模型接口,会话id:%d,地址为%s,入参为%s====,imageUrl=%s,parts==%s", data.ConversationId, url, utils.JsonEncode(data), imageUrl, cmd.Parts)

	//log.Info(logPrefix)

	imageResult := ""
	var err error
	if imageUrl != "" { //todo 请求算法青山AI图片识别
		ctx := context.Background()
		imageResult, err = CallAnnotatePre(ctx, data.PetInfoId, imageUrl, data.Text, data.LastestSectionUuid, cmd.Parts, data.PetDetail)
		if err != nil {
			log.Errorf("%s AI图片识别，请求失败,err为%s", logPrefix, err.Error())
		}
	}
	if imageResult != "" {
		// 发送SSE格式数据
		fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeMessageCompleted, utils.JsonEncode(EventData{
			BotId:            s.GetBotId(petai_po.BotTypeXiaoWenModel),
			Role:             petai_po.MessageRoleAssistant,
			Type:             petai_po.MessageTypeAnswer,
			ClickType:        "reasoning_content",
			Content:          imageResult,
			ReasoningContent: "",
			ContentType:      petai_po.MessageContentTypeText,
		}))
		flusher.Flush()
	}
	uuid = utils.GenerateUUID()

	outMessage := petai_vo.ConvertMessageWithImageTextList(data.Messages)
	medicalReq := petai_vo.MedicalChatReq{
		PetInfoId:            data.PetInfoId,
		QuestionUuid:         uuid,
		Text:                 data.Text,
		ImageText:            imageResult,
		ThreadId:             cast.ToString(data.ConversationId),
		PetName:              data.PetDetail.PetName,
		PetKindofStr:         data.PetDetail.PetKindofStr,         // 宠物种类
		PetVarietyStr:        data.PetDetail.PetVarietyStr,        // 宠物品种,
		PetSexStr:            data.PetDetail.PetSexStr,            // 宠物性别
		PetAge:               data.PetDetail.PetAge,               // 宠物年龄
		PetWeight:            data.PetDetail.PetWeight,            // 宠物体重(单位：kg)
		PetNeuteringStr:      data.PetDetail.PetNeuteringStr,      // 宠物绝育
		LastestVaccinateTime: data.PetDetail.LastestVaccinateTime, // 最近一次疫苗时间
		LastestVaccinateName: data.PetDetail.LastestVaccinateName, // 最近一次疫苗名称
		LastestDewormingTime: data.PetDetail.LastestDewormingTime, // 最近一次驱虫时间
		LastestDewormingName: data.PetDetail.LastestDewormingName, // 最近一次驱虫名称
		Environment:          data.PetDetail.Environment,          // //生活环境(多选)
		Feeding:              data.PetDetail.Feeding,              //喂养方式(多选)
		WashFreq:             data.PetDetail.WashFreq,             //洗澡频率
		AllergyStr:           data.PetDetail.AllergyStr,           // 所有过敏源
		Messages:             outMessage,
		LastestSectionUuid:   data.LastestSectionUuid, //话题uuid
	}
	var reqBody io.Reader = bytes.NewBuffer([]byte(utils.JsonEncode(medicalReq)))
	medicalReqStr := utils.JsonEncode(medicalReq)

	//logPrefix = fmt.Sprintf("%s,==请求模型的参数==medicalReqStr=%s", logPrefix, medicalReqStr)
	logPrefix1 := fmt.Sprintf("====111请求医疗模型接口,会话id:%d,SectionUuid=%s,地址为%s,imageUrl=%s,parts==%s", data.ConversationId, medicalReq.LastestSectionUuid, url, imageUrl, cmd.Parts)
	log.Info(logPrefix1, "准备请求模型,====medicalReqStr==", medicalReqStr)

	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		log.Errorf("%s 创建请求失败,err为%s", logPrefix, err.Error())
		fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeChatFailed, "创建请求失败")
		flusher.Flush()
		return
	}
	// 设置请求头
	if contentType != "" {
		req.Header.Set("Content-Type", contentType)
	} else {
		req.Header.Set("Content-Type", "application/json")
	}
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	// 获取HTTP客户端
	client := httpClientPool.Get().(*http.Client)
	defer httpClientPool.Put(client)

	resp, err := client.Do(req)
	if err != nil {
		log.Errorf("%s 创建请求失败,err为%s", logPrefix, err.Error())
		fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeChatFailed, "创建请求失败")
		flusher.Flush()
		return
	}
	defer resp.Body.Close()

	// 创建带缓冲的读取器
	scanner := bufio.NewScanner(resp.Body)
	// 设置扫描缓冲区大小（处理长行）
	const maxBufferSize = 1024 * 1024 // 1MB
	buf := make([]byte, maxBufferSize)
	scanner.Buffer(buf, maxBufferSize)
	content, reasoningContent := "", ""
	for scanner.Scan() {
		line := scanner.Bytes()
		if len(line) == 0 {
			continue
		}

		var msg MedicalChatRes
		// 解析步骤：
		// 1. 去除SSE前缀
		if !strings.HasPrefix(string(line), "data: ") {
			log.Info(logPrefix, "前置模型返回的结果为百科问题,内容为", string(line))
			// 尝试解析为普通JSON格式
			if err = json.Unmarshal([]byte(line), &singleRes); err != nil {
				log.Errorf("%s 尝试解析为普通JSON格式失败,err为%s", logPrefix, err.Error())
				fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeChatFailed, "尝试解析为普通JSON格式失败")
				flusher.Flush()
				return

			}
			return
		}
		jsonStr := strings.TrimPrefix(string(line), "data: ")

		if err = json.Unmarshal([]byte(jsonStr), &msg); err != nil {
			log.Errorf("%s 解析JSON失败,err=%s,内容=%s", logPrefix, err, string(line))
			continue
		}
		if msg.Type == "reasoning_content" {
			// 发送SSE格式数据
			fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeMessageDelta, utils.JsonEncode(EventData{
				BotId:            s.GetBotId(petai_po.BotTypeXiaoWenModel),
				Role:             petai_po.MessageRoleAssistant,
				Type:             petai_po.MessageTypeAnswer,
				ClickType:        msg.Type,
				ReasoningContent: msg.Chunk,
				ContentType:      petai_po.MessageContentTypeText,
			})) // SSE 格式
		} else {
			//  msg.Type == "content" || msg.Type == "option" || msg.Type == "question"
			// 发送SSE格式数据
			fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeMessageDelta, utils.JsonEncode(EventData{
				BotId:       s.GetBotId(petai_po.BotTypeXiaoWenModel),
				Role:        petai_po.MessageRoleAssistant,
				Type:        petai_po.MessageTypeAnswer,
				ClickType:   msg.Type,
				Content:     msg.Chunk,
				ContentType: petai_po.MessageContentTypeText,
			})) // SSE 格式
		}

		flusher.Flush()

		// 检查结束标志
		if msg.IsEnd && msg.Type == "content" {
			log.Info(logPrefix1, "===模型返回的结果为医疗问题,内容为", utils.JsonEncode(msg))
			content = msg.FullResponse
			if content != "" {
				fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeMessageCompleted, utils.JsonEncode(EventData{
					BotId:            s.GetBotId(petai_po.BotTypeXiaoWenModel),
					Role:             petai_po.MessageRoleAssistant,
					Type:             petai_po.MessageTypeAnswer,
					ClickType:        msg.Type,
					Content:          content,
					ReasoningContent: "",
					ContentType:      petai_po.MessageContentTypeText,
				}))
				flusher.Flush()
			}
		} else if msg.IsEnd && msg.Type == "reasoning_content" {
			reasoningContent = msg.FullResponse
		}

	}

	if err = scanner.Err(); err != nil {
		log.Error("读取流失败:", err)
		return
	}
	if reasoningContent != "" || content != "" {
		// 事件：对话完成
		// 将消息存入本地数据表里

		newContent := data.Text
		newContentType := petai_po.MessageContentTypeText
		if len(cmd.Message.ObjectContent) > 0 { //入参的
			newContent = utils.JsonEncode(cmd.Message.ObjectContent)
			newContentType = petai_po.MessageContentTypeObjectString
		}

		messageId, err := s.SaveMessageFormMedical(petai_vo.SaveMessageFormMedicalReq{
			ConversationId: data.ConversationId,
			UserInfoId:     data.UserInfoId,
			//Question:           data.Text,
			Question:           newContent,
			Answer:             content,
			Uuid:               uuid,
			ReasoningContent:   reasoningContent,
			LastestSectionUuid: data.LastestSectionUuid,
			ContentType:        newContentType,
			ImageText:          imageResult,
		})
		if err != nil {
			log.Errorf("%s 消息存入本地数据表里失败,err=%s", logPrefix, err)
			fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeChatFailed, "消息存入本地数据表里失败")
			flusher.Flush()
			return
		}

		fmt.Fprintf(w, "event: messageid\ndata: %d\n\n", messageId)
		flusher.Flush()

	}

	fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeChatCompleted, utils.JsonEncode(EventData{
		BotId: "default",
	}))
	// todo 获取该会话的采集数据列表
	dataAcquisitionShow, _ := s.ConversationDataAcquisition(petai_vo.ConversationDataAcquisitionReq{ConversationId: data.ConversationId, SectionUuid: data.LastestSectionUuid})
	// stream_completed
	fmt.Fprintf(w, "event: %s\ndata: %v\n\n", EventTypeEnd, dataAcquisitionShow)
	flusher.Flush()
	return
}

// 互联网医院问诊单 创建的会话 发起对话 是 请求基础医疗模型接口
func (s *CozeService) ConsultStreamPost_bak(w http.ResponseWriter, flusher http.Flusher, url string, medicalReq petai_vo.MedicalChatReq, contentType string, headers map[string]string) (singleRes MedicalChatRes) {
	logPrefix := fmt.Sprintf("====请求基础医疗模型接口,订单编号为%d====", medicalReq.PmOrderSn)
	consultConversationId := cast.ToInt64(strings.Split(medicalReq.ThreadId, "_")[1])
	medicalReqStr := utils.JsonEncode(medicalReq)
	var err error
	log.Info(logPrefix, "地址为", url, ",入参为", medicalReqStr)
	var reqBody io.Reader = bytes.NewBuffer([]byte(utils.JsonEncode(medicalReq)))
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		log.Errorf("%s 创建请求失败,err为%s", logPrefix, err.Error())
		fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeChatFailed, "创建请求失败")
		flusher.Flush()
		return
	}
	// 设置请求头
	if contentType != "" {
		req.Header.Set("Content-Type", contentType)
	} else {
		req.Header.Set("Content-Type", "application/json")
	}
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	// 获取HTTP客户端
	client := httpClientPool.Get().(*http.Client)
	defer httpClientPool.Put(client)

	resp, err := client.Do(req)
	if err != nil {
		log.Errorf("%s 创建请求失败,err为%s", logPrefix, err.Error())
		fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeChatFailed, "创建请求失败")
		flusher.Flush()
		return
	}
	defer resp.Body.Close()

	// 创建带缓冲的读取器
	scanner := bufio.NewScanner(resp.Body)
	// 设置扫描缓冲区大小（处理长行）
	const maxBufferSize = 1024 * 1024 // 1MB
	buf := make([]byte, maxBufferSize)
	scanner.Buffer(buf, maxBufferSize)
	for scanner.Scan() {
		line := scanner.Bytes()
		if len(line) == 0 {
			continue
		}

		var msg MedicalChatRes
		// 解析步骤：
		// 1. 去除SSE前缀
		if !strings.HasPrefix(string(line), "data: ") {
			log.Info(logPrefix, "前置模型返回的结果为百科问题,内容为", string(line))
			// 尝试解析为普通JSON格式
			if err = json.Unmarshal([]byte(line), &singleRes); err != nil {
				log.Errorf("%s 尝试解析为普通JSON格式失败,err为%s", logPrefix, err.Error())
				fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeChatFailed, "尝试解析为普通JSON格式失败")
				flusher.Flush()
				return

			}
			fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeChatFailed, "基础模型返回结果意图为百科问题")
			flusher.Flush()
			return
		}
		jsonStr := strings.TrimPrefix(string(line), "data: ")
		if err = json.Unmarshal([]byte(jsonStr), &msg); err != nil {
			log.Errorf("%s 解析JSON失败,err=%s,内容=%s", logPrefix, err, string(line))
			continue
		}
		// 发送SSE格式数据
		fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeMessageDelta, utils.JsonEncode(EventData{
			BotId:       s.GetBotId(petai_po.BotTypeXiaoWenModel),
			Role:        petai_po.MessageRoleAssistant,
			Type:        petai_po.MessageTypeAnswer,
			Content:     msg.Chunk,
			ContentType: petai_po.MessageContentTypeText,
		})) // SSE 格式
		flusher.Flush()

		// 检查结束标志
		if msg.IsEnd {
			log.Info(logPrefix, "基础模型返回的结果为医疗问题,内容为", utils.JsonEncode(msg))
			fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeMessageCompleted, utils.JsonEncode(EventData{
				BotId:       s.GetBotId(petai_po.BotTypeXiaoWenModel),
				Role:        petai_po.MessageRoleAssistant,
				Type:        petai_po.MessageTypeAnswer,
				Content:     msg.FullResponse,
				ContentType: petai_po.MessageContentTypeText,
			}))
			flusher.Flush()

			// 事件：对话完成
			// 将消息存入本地数据表里
			messageId, _, err := s.SaveConsultConversationMessage(petai_vo.SaveConsultConversationMessageReq{
				ConsultConversationId: consultConversationId,
				Question:              medicalReq.Text,
				Answer:                msg.FullResponse,
				Uuid:                  medicalReq.QuestionUuid,
			}, medicalReq.PmOrderSn)
			if err != nil {
				log.Errorf("%s 消息存入本地数据表里失败,err=%s", logPrefix, err)
				fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeChatFailed, "消息存入本地数据表里失败")
				flusher.Flush()
				return
			}
			fmt.Fprintf(w, "event: %s\ndata: %v\n\n", EventTypeEnd, 1)
			fmt.Fprintf(w, "event: messageid\ndata: %d\n\n", messageId)
			flusher.Flush()
			break
		}
	}

	if err = scanner.Err(); err != nil {
		log.Error("读取流失败:", err)
		return
	}
	fmt.Fprintf(w, "event: %s\ndata: %s\n\n", EventTypeChatCompleted, utils.JsonEncode(EventData{
		BotId: s.GetBotId(petai_po.BotTypeXiaoWenModel),
	}))
	flusher.Flush()
	return
}

// 互联网医院问诊单 创建的会话 发起对话 是 请求基础医疗模型接口
func (s *CozeService) ConsultStreamPost(url string, medicalReq petai_vo.MedicalChatReq, contentType string, headers map[string]string) (out MedicalChatRes2, answer string, answerUuid string, err error) {
	logPrefix := fmt.Sprintf("====请求基础医疗模型接口,订单编号为%d====", medicalReq.PmOrderSn)
	consultConversationId := cast.ToInt64(strings.Split(medicalReq.ThreadId, "_")[1])
	medicalReqStr := utils.JsonEncode(medicalReq)

	log.Info(logPrefix, "地址为", url, ",入参为", medicalReqStr)
	defer utils.RunningDuration("互联网医院问诊单发起对话" + cast.ToString(medicalReq.PmOrderSn) + "ConsultStreamPost")()

	body, err := utils.Post(url, []byte(utils.JsonEncode(medicalReq)), contentType, headers)
	if err != nil {
		log.Errorf("%s 创建请求失败,err为%s", logPrefix, err.Error())
		err = errors.New("创建请求失败" + err.Error())
		return
	}
	log.Info(logPrefix, "地址为", url, ",入参为", medicalReqStr, "请求结果：", string(body), "err:", err)
	if err = json.Unmarshal([]byte(body), &out); err != nil {
		log.Errorf("%s 尝试解析为普通JSON格式失败,err为%s", logPrefix, err.Error())
		err = errors.New("尝试解析为普通JSON格式失败" + err.Error())
		return

	}
	answer = out.Text
	// 将消息存入本地数据表里
	_, answerUuid, err = s.SaveConsultConversationMessage(petai_vo.SaveConsultConversationMessageReq{
		ConsultConversationId: consultConversationId,
		Question:              medicalReq.Text,
		Answer:                answer,
		Uuid:                  medicalReq.QuestionUuid,
	}, medicalReq.PmOrderSn)
	if err != nil {
		log.Errorf("%s 消息存入本地数据表里失败,err=%s", logPrefix, err.Error())
		err = errors.New("消息存入本地数据表里失败" + err.Error())
		return
	}

	return

}

func (s *CozeService) ConsultChatReferenceContent(cmd petai_vo.ConsultChatReferenceContentReq) (err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====医生引用模型回答====问诊会话id为%d, 引用的消息uuid为%s, 实际发送内容为%s====", cmd.ConsultConversationId, cmd.MessageUuid, cmd.ReferenceContent)
	log.Info(logPrefix)
	// 查询引用的消息
	message := new(petai_po.PetaiConsultMessage)
	exist, err := session.Table("eshop.petai_consult_message").Where("uuid = ?", cmd.MessageUuid).Get(message)
	if err != nil {
		log.Error(logPrefix, "查询引用的消息失败 err=", err.Error())
		err = errors.New("查询引用的消息失败")
		return
	}
	if !exist {
		log.Error(logPrefix, "引用的消息不存在")
		err = errors.New("引用的消息不存在")
		return
	}
	referenceContent := strings.TrimSpace(cmd.ReferenceContent)
	// 完全引用
	if message.Content == referenceContent {
		message.ReferenceSame = 1
	}
	message.ReferenceContent = referenceContent
	_, err = session.Table("eshop.petai_consult_message").Cols("reference_content", "reference_same").Where("uuid = ?", cmd.MessageUuid).Update(message)
	if err != nil {
		log.Error(logPrefix, "更新引用的消息失败 err=", err.Error())
		err = errors.New("更新引用的消息失败")
		return
	}

	return

}
