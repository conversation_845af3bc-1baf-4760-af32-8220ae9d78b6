package services

import (
	po "eShop/domain/points-po"
	"eShop/infra/errors"
	"eShop/infra/transaction"
	repo "eShop/repo/points-repo"
	vo "eShop/view-model/points-vo"

	"xorm.io/xorm"
)

type ISuperService[Id po.Id, E po.Entity, S vo.SaveVO, U vo.UpdateVO, Q vo.QueryVO, R vo.ResultVO] interface {
	Create(session *xorm.Session, saveVO S) (E, error)
	Update(session *xorm.Session, updateVO U) (E, error)
	Detail(session *xorm.Session, id Id) (R, error)
	Query(session *xorm.Session, queryVO Q) (R, error)
	Page(session *xorm.Session, queryVO Q) ([]R, int64, error)
	List(session *xorm.Session, queryVO Q) ([]R, error)
	Delete(session *xorm.Session, queryVO Q) error
	DeleteByIds(session *xorm.Session, ids ...Id) error
}

type SuperService[Id po.Id, E po.Entity, S vo.SaveVO, U vo.UpdateVO, Q vo.QueryVO, R vo.ResultVO] struct {
	repo repo.ISuperRepo[E]
	// 事务管理器
	tm transaction.TransactionManager
	// 钩子，交由子类实现
	hooks IServiceHooks[Id, E, S, U, Q, R]
}

func NewSuperService[Id po.Id, E po.Entity, S vo.SaveVO, U vo.UpdateVO, Q vo.QueryVO, R vo.ResultVO](
	hooks IServiceHooks[Id, E, S, U, Q, R],
) SuperService[Id, E, S, U, Q, R] {
	return SuperService[Id, E, S, U, Q, R]{
		repo:  repo.NewSuperRepo[E](),
		tm:    transaction.NewTransactionManager(),
		hooks: hooks,
	}
}

func (s SuperService[Id, Entity, SaveVO, UpdateVO, QueryVO, ResultVO]) Create(session *xorm.Session, saveVO SaveVO) (Entity, error) {
	var empty Entity
	var result Entity
	err := s.tm.Required(session, func(session *xorm.Session) error {
		// 执行 BeforeCreate 钩子
		entity, err := s.hooks.BeforeCreate(session, saveVO)
		if err != nil {
			return err
		}

		// 创建实体
		created, err := s.repo.Create(session, entity)
		if err != nil {
			return err
		}

		// 执行 AfterCreate 钩子
		result, err = s.hooks.AfterCreate(session, created)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return empty, err
	}

	return result, nil
}

func (s SuperService[Id, Entity, SaveVO, UpdateVO, QueryVO, ResultVO]) Update(session *xorm.Session, updateVO UpdateVO) (Entity, error) {
	var empty Entity
	var result Entity
	err := s.tm.Required(session, func(session *xorm.Session) error {
		// 执行 BeforeCreate 钩子
		entity, err := s.hooks.BeforeUpdate(session, updateVO)
		if err != nil {
			return err
		}

		// 创建实体
		updated, err := s.repo.Update(session, entity)
		if err != nil {
			return err
		}

		// 执行 AfterCreate 钩子
		result, err = s.hooks.AfterUpdate(session, updated)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return empty, err
	}

	return result, nil
}

func (s SuperService[Id, Entity, SaveVO, UpdateVO, QueryVO, ResultVO]) Detail(session *xorm.Session, id Id) (ResultVO, error) {
	var empty ResultVO
	if id <= 0 {
		return empty, errors.NewBadRequest("id无效")
	}
	var resultVO ResultVO
	err := s.tm.NotSupported(session, func(session *xorm.Session) error {
		entity, err := s.repo.GetById(session, id)
		if err != nil {
			return err
		}

		// 实体转vo
		result, err := new(vo.SuperResultVO[ResultVO]).FromEntity(entity)
		if err != nil {
			return err
		}
		resultVO = result.(ResultVO)

		// 后置处理
		resultVO, err = s.hooks.AfterQuery(session, resultVO)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return empty, err
	}
	return resultVO, nil
}

func (s SuperService[Id, Entity, SaveVO, UpdateVO, QueryVO, ResultVO]) Query(session *xorm.Session, queryVO QueryVO) (ResultVO, error) {
	var empty ResultVO
	var resultVO ResultVO
	err := s.tm.NotSupported(session, func(session *xorm.Session) error {
		// 执行 BeforeQuery 钩子
		queryVO, err := s.hooks.BeforeQuery(session, queryVO)
		if err != nil {
			return errors.NewBadRequest(err.Error())
		}

		entity, err := s.repo.QueryOne(session, queryVO)
		if err != nil {
			return err
		}

		// 实体转vo
		result, err := new(vo.SuperResultVO[ResultVO]).FromEntity(entity)
		if err != nil {
			return err
		}
		resultVO = result.(ResultVO)

		// 后置处理
		resultVO, err = s.hooks.AfterQuery(session, resultVO)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return empty, err
	}
	return resultVO, nil
}

func (s SuperService[Id, Entity, SaveVO, UpdateVO, QueryVO, ResultVO]) Page(session *xorm.Session, queryVO QueryVO) ([]ResultVO, int64, error) {
	var empty []ResultVO
	var result []ResultVO
	var total int64
	err := s.tm.NotSupported(session, func(session *xorm.Session) error {
		// 执行 BeforeCreate 钩子
		queryVO, err := s.hooks.BeforePage(session, queryVO)
		if err != nil {
			return errors.NewBadRequest(err.Error())
		}

		// 分页查询，传入 QueryVO 用于构建查询条件
		entities, t, err := s.repo.Page(session, queryVO)
		if err != nil {
			return err
		}
		total = t

		// 执行 AfterPage 钩子
		result, err = s.hooks.AfterPage(session, entities)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return empty, 0, err
	}
	return result, total, nil
}

// List 获取所有数据
func (s SuperService[Id, Entity, SaveVO, UpdateVO, QueryVO, ResultVO]) List(session *xorm.Session, queryVO QueryVO) ([]ResultVO, error) {
	var empty []ResultVO
	var result []ResultVO
	err := s.tm.NotSupported(session, func(session *xorm.Session) error {
		// 执行 BeforeCreate 钩子
		queryVO, err := s.hooks.BeforePage(session, queryVO)
		if err != nil {
			return errors.NewBadRequest(err.Error())
		}

		// 获取所有数据
		entities, err := s.repo.List(session, queryVO)
		if err != nil {
			return err
		}

		// 执行 AfterPage 钩子转换为ResultVO
		result, err = s.hooks.AfterPage(session, entities)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return empty, err
	}
	return result, nil
}

// Delete 删除数据
func (s SuperService[Id, Entity, SaveVO, UpdateVO, QueryVO, ResultVO]) Delete(session *xorm.Session, queryVO QueryVO) error {
	err := s.tm.Required(session, func(session *xorm.Session) error {
		// 执行删除操作
		entities, err := s.repo.List(session, queryVO)
		if err != nil {
			return err
		}

		if len(entities) == 0 {
			return nil
		}

		ids := make([]Id, len(entities))
		for i, entity := range entities {
			ids[i] = entity.GetId().(Id)
		}

		// 执行 AfterDelete 钩子
		err = s.DeleteByIds(session, ids...)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return errors.NewBadRequest("Delete fail:" + err.Error())
	}
	return nil
}

// DeleteByIds 删除数据
func (s SuperService[Id, Entity, SaveVO, UpdateVO, QueryVO, ResultVO]) DeleteByIds(session *xorm.Session, ids ...Id) error {
	err := s.tm.Required(session, func(session *xorm.Session) error {
		// 执行删除操作
		anyIds := make([]any, len(ids))
		for i, id := range ids {
			anyIds[i] = id
		}
		err := s.repo.Delete(session, anyIds...)
		if err != nil {
			return err
		}

		// 执行 AfterDelete 钩子
		err = s.hooks.AfterDelete(session, ids...)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return errors.NewBadRequest("Delete fail:" + err.Error())
	}
	return nil
}

// ServiceHooks 定义所有可以被子类重写的钩子方法
type IServiceHooks[Id po.Id, E po.Entity, S vo.SaveVO, U vo.UpdateVO, Q vo.QueryVO, R vo.ResultVO] interface {
	BeforeCreate(session *xorm.Session, saveVO S) (E, error)
	AfterCreate(session *xorm.Session, entity E) (E, error)
	BeforeUpdate(session *xorm.Session, updateVO U) (E, error)
	AfterUpdate(session *xorm.Session, entity E) (E, error)
	BeforeQuery(session *xorm.Session, queryVO Q) (Q, error)
	AfterQuery(session *xorm.Session, resultVO R) (R, error)
	BeforePage(session *xorm.Session, queryVO Q) (Q, error)
	AfterPage(session *xorm.Session, entities []E) ([]R, error)
	AfterDelete(session *xorm.Session, ids ...Id) error
}

// ServiceHooks 提供钩子方法的默认实现
type ServiceHooks[Id po.Id, E po.Entity, S vo.SaveVO, U vo.UpdateVO, Q vo.QueryVO, R vo.ResultVO] struct {
	tm transaction.TransactionManager
}

func NewServiceHooks[Id po.Id, E po.Entity, S vo.SaveVO, U vo.UpdateVO, Q vo.QueryVO, R vo.ResultVO]() ServiceHooks[Id, E, S, U, Q, R] {
	return ServiceHooks[Id, E, S, U, Q, R]{
		tm: transaction.NewTransactionManager(),
	}
}

func (h ServiceHooks[Id, E, S, U, Q, R]) BeforeCreate(session *xorm.Session, saveVO S) (E, error) {
	// 转换为实体
	var emtpy E
	entity, err := saveVO.ToEntity(saveVO)
	if err != nil {
		return emtpy, err
	}

	return any(entity).(E), nil
}

func (h ServiceHooks[Id, E, S, U, Q, R]) AfterCreate(session *xorm.Session, entity E) (E, error) {
	return entity, nil
}

func (h ServiceHooks[Id, E, S, U, Q, R]) BeforeUpdate(session *xorm.Session, updateVO U) (E, error) {
	// 转换为实体
	var emtpy E
	entity, err := updateVO.ToEntity(updateVO)
	if err != nil {
		return emtpy, err
	}

	return any(entity).(E), nil
}

func (h ServiceHooks[Id, E, S, U, Q, R]) AfterUpdate(session *xorm.Session, entity E) (E, error) {
	return entity, nil
}

func (h ServiceHooks[Id, E, S, U, Q, R]) BeforeQuery(session *xorm.Session, queryVO Q) (Q, error) {
	return queryVO, nil
}

func (h ServiceHooks[Id, E, S, U, Q, R]) AfterQuery(session *xorm.Session, resultVO R) (R, error) {
	return resultVO, nil
}

func (h ServiceHooks[Id, E, S, U, Q, R]) BeforePage(session *xorm.Session, queryVO Q) (Q, error) {
	return queryVO, nil
}

func (h ServiceHooks[Id, E, S, U, Q, R]) AfterPage(session *xorm.Session, entities []E) ([]R, error) {
	resultVOs := make([]R, len(entities))
	for i, entity := range entities {
		resultVO, err := new(vo.SuperResultVO[R]).FromEntity(entity)
		if err != nil {
			return nil, err
		}
		resultVOs[i] = resultVO.(R)
	}
	return resultVOs, nil
}

func (h ServiceHooks[Id, E, S, U, Q, R]) AfterDelete(session *xorm.Session, ids ...Id) error {
	return nil
}
