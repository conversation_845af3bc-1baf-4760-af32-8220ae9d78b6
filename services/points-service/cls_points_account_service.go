package services

import (
	distribution_po "eShop/domain/distribution-po"
	external_po "eShop/domain/external-po"
	po "eShop/domain/points-po"
	upetmart_po "eShop/domain/upetmart-po"
	"eShop/infra/errors"
	"eShop/infra/log"
	"eShop/infra/utils"
	repo "eShop/repo/points-repo"
	disEnum "eShop/services/distribution-service/enum/dis-distributor"
	vo "eShop/view-model/points-vo"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"xorm.io/xorm"
)

// ClsPointsAccountService 提供了积分账户相关的服务
type ClsPointsAccountService struct {
	SuperService[int, po.ClsPointsAccount, vo.ClsPointsAccountSaveVO, vo.ClsPointsAccountUpdateVO, vo.ClsPointsAccountQueryVO, vo.ClsPointsAccountResultVO]
	repo repo.ClsPointsAccountRepo
}

// NewClsPointsAccountService 创建一个新的 ClsPointsAccountService 实例
func NewClsPointsAccountService() ClsPointsAccountService {
	return ClsPointsAccountService{
		NewSuperService(
			&ClsPointsAccountHooks{
				NewServiceHooks[int, po.ClsPointsAccount, vo.ClsPointsAccountSaveVO, vo.ClsPointsAccountUpdateVO, vo.ClsPointsAccountQueryVO, vo.ClsPointsAccountResultVO](),
			},
		),
		repo.NewClsPointsAccountRepo(),
	}
}

// ListAccounts 获取积分账户列表
func (s ClsPointsAccountService) ListAccounts(session *xorm.Session, queryVO vo.ClsPointsAccountQueryVO) ([]vo.DistributorVO, int64, error) {
	var result []vo.DistributorVO
	var total int64
	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		distributior, err := s.GetDistributor(tx, queryVO.DisId)
		if err != nil {
			return err
		}

		if queryVO.EnterpriseId == 0 {
			queryVO.EnterpriseId = distributior.EnterpriseId
		}

		result, total, err = s.repo.ListAccounts(tx, queryVO)
		return err
	})

	return result, total, err
}

// GivePoints 积分赠送
func (s ClsPointsAccountService) GivePoints(session *xorm.Session, giveVO vo.ClsPointsGiveVO) error {
	s.tm.Required(session, func(tx *xorm.Session) error {
		// 1. 业务校验
		fromDis, toDis, err := s.validateGivePoints(tx, giveVO)
		if err != nil {
			return err
		}

		// 2. 获取需要扣减的积分流水
		flow, err := NewClsPointsFlowService().CostFlows(tx, fromDis.EnterpriseId, giveVO.FromDisId, giveVO.Points)
		if err != nil {
			return errors.NewBadRequest("积分兑换，检查到可用积分不够")
		}

		// 3. 创建积分流水记录
		flows := s.createGivePointsFlows(fromDis, toDis, giveVO.Points, flow)
		_, err = repo.NewClsPointsFlowRepo().BatchCreate(tx, flows)
		if err != nil {
			return err
		}

		// 4. 更新账户积分
		updAccounts := map[int]int{
			giveVO.FromDisId: fromDis.EnterpriseId,
			giveVO.ToDisId:   toDis.EnterpriseId,
		}
		s.BatchUpdateByDisIds(tx, updAccounts, "", 4)

		return nil
	})
	return nil
}

// validateGivePoints 校验积分赠送的业务规则
func (s ClsPointsAccountService) validateGivePoints(tx *xorm.Session, giveVO vo.ClsPointsGiveVO) (fromDis, toDis vo.DistributorVO, err error) {
	// 获取赠送方分销账户信息
	fromDis, err = s.GetDistributor(tx, giveVO.FromDisId)
	if err != nil {
		return fromDis, toDis, errors.NewBadRequest("查询赠送积分的分销账户信息失败")
	}
	if fromDis.EnterpriseId == 0 {
		return fromDis, toDis, errors.NewBadRequest("赠送积分的分销账户没有企业信息")
	}

	// 获取接收方分销账户信息
	toDis, err = s.GetDistributor(tx, giveVO.ToDisId)
	if err != nil {
		return fromDis, toDis, errors.NewBadRequest("查询接收积分的分销账户信息失败")
	}

	// 校验是否同一企业
	if fromDis.EnterpriseId != toDis.EnterpriseId {
		return fromDis, toDis, errors.NewBadRequest("赠送和接收积分的分销账户不在在同一企业下")
	}

	// 校验积分是否足够
	fromAccount, err := s.repo.QueryOne(tx, vo.ClsPointsAccountQueryVO{DisId: giveVO.FromDisId})
	if err != nil {
		return fromDis, toDis, errors.NewBadRequest("赠送积分的账户查询失败")
	}
	if fromAccount.AvailablePoints < giveVO.Points {
		return fromDis, toDis, errors.NewBadRequest("赠送积分的账户积分不足")
	}

	return fromDis, toDis, nil
}

// createGivePointsFlows 创建积分赠送相关的流水记录
func (s ClsPointsAccountService) createGivePointsFlows(fromDis, toDis vo.DistributorVO, points int, flow po.ClsPointsFlow) []po.ClsPointsFlow {
	now := time.Now()
	expireTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 7).Add(-time.Second)
	remark := fmt.Sprintf("%s->%s", fromDis.RealName, toDis.RealName)

	// 查询接收分销员的员工编号
	dmTx := s.tm.DbDmMdm()
	var staffNo string
	mobile := utils.MobileDecrypt(toDis.EncryptMobile)
	dmTx.Table("dm_mdm.shr_t_staff_info").Cols("shr_staff_no").Where("mobile=?", mobile).Limit(1).Get(&staffNo)

	return []po.ClsPointsFlow{
		{ // 赠送方流水
			DisId:        fromDis.DisId,
			EnterpriseId: fromDis.EnterpriseId,
			Type:         2, // 赠送积分
			BizType:      7, // 积分赠送支出
			Points:       points,
			PointsBlky:   flow.PointsBlky,
			PointsSzld:   flow.PointsSzld,
			Region:       flow.Region,
			OccurTime:    now,
			Remark:       remark,
			Operator:     fromDis.RealName,
			StaffNo:      flow.StaffNo,
		},
		{ // 接收方流水
			DisId:           toDis.DisId,
			EnterpriseId:    toDis.EnterpriseId,
			Type:            1, // 赠送积分
			BizType:         6, // 积分赠送支出
			Status:          1,
			Points:          points,
			RemainingPoints: points,
			PointsBlky:      flow.PointsBlky,
			PointsSzld:      flow.PointsSzld,
			Region:          flow.Region,
			OccurTime:       now,
			ExpireTime:      expireTime,
			Remark:          remark,
			Operator:        fromDis.RealName,
			StaffNo:         staffNo,
		},
	}
}

// BatchUpdateByDisIds 根据分销员ID批量更新积分账户
// accountUpdates: 一个 map，键是 disId，值是 enterpriseId
// operateType 操作类型 0-不需要发送消息 1:开单刷积分 2:兑换订单扣减 3:积分过期 4:积分赠送
func (s ClsPointsAccountService) BatchUpdateByDisIds(session *xorm.Session, accountUpdates map[int]int, curDate string, operateType int) error {
	s.tm.Required(session, func(tx *xorm.Session) error {
		if len(accountUpdates) == 0 {
			return nil
		}

		// 1. 获取所有分销员ID
		disIds := make([]int, 0, len(accountUpdates))
		for disId := range accountUpdates {
			disIds = append(disIds, disId)
		}

		// 2. 批量查询积分流水并按分销员ID分组
		disFlowsMap, err := s.getPointsFlowsByDisIds(tx, disIds)
		if err != nil {
			return err
		}

		// 3. 批量查询现有积分账户
		existingAccountsMap, err := s.getExistingAccounts(tx, disIds)
		if err != nil {
			return err
		}

		// 4. 循环处理每个账户
		for disId, enterpriseId := range accountUpdates {
			if err := s.processAccount(tx, disId, enterpriseId, disFlowsMap[disId], existingAccountsMap[disId], curDate, operateType); err != nil {
				log.Errorf("更新积分账户失败: disId=%d, err=%v", disId, err)
				continue // 继续处理下一个，而不是中断整个流程
			}
		}
		return nil
	})
	return nil
}

// getPointsFlowsByDisIds 批量查询并按分销员ID分组积分流水
func (s ClsPointsAccountService) getPointsFlowsByDisIds(tx *xorm.Session, disIds []int) (map[int][]po.ClsPointsFlow, error) {
	var allFlows []po.ClsPointsFlow
	err := tx.Table("cls_points_flow").In("dis_id", disIds).Find(&allFlows)
	if err != nil {
		return nil, errors.NewBadRequest("批量查询积分流水失败: " + err.Error())
	}

	disFlowsMap := make(map[int][]po.ClsPointsFlow)
	for _, flow := range allFlows {
		disFlowsMap[flow.DisId] = append(disFlowsMap[flow.DisId], flow)
	}
	return disFlowsMap, nil
}

// getExistingAccounts 批量查询现有积分账户
func (s ClsPointsAccountService) getExistingAccounts(tx *xorm.Session, disIds []int) (map[int]po.ClsPointsAccount, error) {
	var existingAccounts []po.ClsPointsAccount
	err := tx.In("dis_id", disIds).Find(&existingAccounts)
	if err != nil {
		return nil, errors.NewBadRequest("批量查询积分账户失败: " + err.Error())
	}

	existingAccountsMap := make(map[int]po.ClsPointsAccount)
	for _, acc := range existingAccounts {
		existingAccountsMap[acc.DisId] = acc
	}
	return existingAccountsMap, nil
}

// processAccount 处理单个账户的积分更新
func (s ClsPointsAccountService) processAccount(tx *xorm.Session, disId int, enterpriseId int,
	flows []po.ClsPointsFlow, oldAccount po.ClsPointsAccount, curDate string, operateType int) error {

	// 计算积分并创建新账户
	account := s.calculatePoints(flows, disId, enterpriseId, curDate)

	// 更新或插入账户
	var err error
	if oldAccount.Id > 0 {
		_, err = tx.Where("id = ?", oldAccount.Id).
			Cols("enterprise_id", "available_points", "total_points", "consume_points", "expired_points", "expiring_points").
			Update(&account)
	} else {
		_, err = tx.Insert(&account)
	}

	if err != nil {
		return err
	}

	// 发送消息
	go s.sendPointsChangeMessage(account, oldAccount, operateType)
	return nil
}

// calculatePoints 计算积分账户
func (s ClsPointsAccountService) calculatePoints(flows []po.ClsPointsFlow, disId int, enterpriseId int, curDate string) po.ClsPointsAccount {
	// 计算时间范围
	now := time.Now()
	if len(curDate) > 0 {
		var err error
		now, err = time.ParseInLocation("2006-01-02", curDate, time.Local)
		if err != nil {
			log.Errorf("时间转化失败: %v", err)
			now = time.Now()
		}
	}
	thisMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	nextMonth := thisMonth.AddDate(0, 1, 0)

	var total, available, consume, expired, expiring int
	for _, flow := range flows {
		if flow.Type == 1 { // 积分进账
			total += flow.Points
			if flow.Status == 1 || flow.Status == 2 { // 待使用或部分使用
				available += flow.RemainingPoints
				// 积分过期时间在当前月份内，且不是积分赠送收入，则加入积分即将过期
				if thisMonth.Before(flow.ExpireTime) && flow.ExpireTime.Before(nextMonth) && flow.BizType != 6 {
					expiring += flow.RemainingPoints
				}
			}
			consume += flow.Points - flow.RemainingPoints
		}
		switch flow.BizType {
		case 4: // 商品退货积分扣减
			consume -= flow.RemainingPoints
			if flow.Status == 1 {
				available += flow.RemainingPoints
			}
		case 5, 8: // 积分过期、获赠积分过期
			expired += flow.Points
		}
	}

	return po.ClsPointsAccount{
		DisId:           disId,
		EnterpriseId:    enterpriseId,
		AvailablePoints: available,
		TotalPoints:     total,
		ConsumePoints:   consume,
		ExpiredPoints:   expired,
		ExpiringPoints:  expiring,
	}
}

// sendPointsChangeMessage 发送积分变更消息
func (s ClsPointsAccountService) sendPointsChangeMessage(currentAccount po.ClsPointsAccount, oldAccount po.ClsPointsAccount, operateType int) {
	if operateType == 0 {
		return
	}
	reason := ""
	switch operateType {
	case 1:
		reason = "子龙开单最终（含退单）"
	case 2:
		reason = "积分商城兑换商品"
	case 3:
		reason = "积分过期"
	case 4:
		reason = "积分赠送变动"
	}
	wechatMsgService := NewClsWechatMsgService()
	changPoints := currentAccount.AvailablePoints
	if oldAccount.Id > 0 {
		changPoints = int(math.Abs(float64(oldAccount.AvailablePoints - currentAccount.AvailablePoints)))
	}
	if changPoints > 0 {
		wechatMsgService.SendChangeMsg(currentAccount.DisId, []*vo.MessageValue{
			{Type: "string", Value: time.Now().Format("2006年1月2号")},
			{Type: "int", Value: strconv.Itoa(changPoints)},
			{Type: "int", Value: strconv.Itoa(currentAccount.AvailablePoints)},
			{Type: "string", Value: reason},
		})
	}
}

func (s ClsPointsAccountService) Query(session *xorm.Session, queryVO vo.ClsPointsAccountQueryVO) (vo.ClsPointsAccountResultVO, error) {
	var result vo.ClsPointsAccountResultVO
	disId := queryVO.DisId
	var accounts []vo.ClsPointsAccountResultVO
	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		distributor, err := s.GetDistributor(tx, disId)
		if err != nil {
			return err
		}

		// 查出企业下的所有账号
		queryVO.DisId = 0
		queryVO.EnterpriseId = distributor.EnterpriseId
		accounts, err = s.List(tx, queryVO)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return result, err
	}

	if len(accounts) == 0 {
		return vo.ClsPointsAccountResultVO{}, nil
	}

	result.EnterpriseId = queryVO.EnterpriseId
	result.DisId = disId
	for _, account := range accounts {
		if account.DisId == disId {
			result.Id = account.Id
			result.AvailablePoints += account.AvailablePoints
			result.TotalPoints += account.TotalPoints
			result.ConsumePoints += account.ConsumePoints
			result.ExpiredPoints += account.ExpiredPoints
			result.ExpiringPoints += account.ExpiringPoints
		}
		result.EnterprisePoints += account.AvailablePoints
	}

	return result, nil
}

func (s ClsPointsAccountService) GetDistributor(session *xorm.Session, disId int) (vo.DistributorVO, error) {
	var distributor vo.DistributorVO
	_, err := session.Table("dis_distributor").Alias("dd").Cols("dd.id AS dis_id,de.id AS enterprise_id, dd.dis_role, dd.real_name, dd.encrypt_mobile").
		Join("LEFT", "shop s", "s.id = dd.shop_id").
		Join("LEFT", "dis_enterprise de", "de.scrm_enterprise_id = s.enterprise_id").
		Where("dd.id = ?", disId).Get(&distributor)
	if err != nil {
		log.Error("OrderPointOut GetDistributor error: " + err.Error())
		return vo.DistributorVO{}, errors.NewBadRequest("查询企业ID失败: " + err.Error())
	}

	return distributor, nil
}

// 积分即将过期提醒
func (s ClsPointsAccountService) PointsExpireAlert(session *xorm.Session) error {
	log.Info("积分即将过期提醒：" + time.Now().Format("2006-01-02 00:00:00"))
	var accounts []po.ClsPointsAccount
	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		err := tx.Table("cls_points_account").Where("expiring_points > 0").Find(&accounts)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}

	now := time.Now()
	expireTime := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, time.Local).AddDate(0, 0, -1)
	wechatMsgService := NewClsWechatMsgService()
	if len(accounts) > 0 {
		for _, account := range accounts {
			wechatMsgService.SendExpireMsg(account.DisId, []*vo.MessageValue{
				{
					// 过期积分 100
					Type:  "int",
					Value: strconv.Itoa(account.ExpiringPoints),
				}, {
					// 过期时间 2020年8月1日0：00
					Type:  "string",
					Value: expireTime.Format("2006年1月2日"),
				}, {
					// 温馨提醒
					Type:  "string",
					Value: "您有积分本月底过期，请及时使用",
				},
			})
		}
	}
	return nil
}

// 积分即将过期提醒
func (s ClsPointsAccountService) GivenPointsExpireAlert(session *xorm.Session) error {
	now := time.Now()
	log.Info("赠送积分即将过期提醒：" + now.Format("2006-01-02 00:00:00"))

	// 获取所有赠送积分的订单
	expireTimeStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 3)
	expireTimeEnd := expireTimeStart.AddDate(0, 0, 1)
	accounts := make(map[int]int)
	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		flows, err := repo.NewClsPointsFlowRepo().List(tx, vo.ClsPointsFlowQueryVO{
			Status:          []int{1, 2},
			BizType:         []int{6}, // 积分赠送收入
			ExpireTimeStart: expireTimeStart.Format("2006-01-02"),
			ExpireTimeEnd:   expireTimeEnd.Format("2006-01-02"),
		})
		if err != nil {
			return errors.New("查询需要过期的积分流水失败：" + err.Error())
		}

		for _, flow := range flows {
			accounts[flow.DisId] += flow.RemainingPoints
		}

		return nil
	})
	if err != nil {
		return err
	}

	wechatMsgService := NewClsWechatMsgService()
	if len(accounts) > 0 {
		for disId, points := range accounts {
			wechatMsgService.SendExpireMsg(disId, []*vo.MessageValue{
				{
					// 过期积分 100
					Type:  "int",
					Value: strconv.Itoa(points),
				}, {
					// 过期时间 2020年8月1日0：00
					Type:  "string",
					Value: expireTimeStart.Format("2006年1月2日"),
				}, {
					// 温馨提醒
					Type:  "string",
					Value: "赠送积分 3 天后到期，请及时使用",
				},
			})
		}
	}
	return nil
}

// RegisterClsWorker 创建店员分销员
// 该方法结合了CLSBossDistributorInsert和CLSBeWorkerDistributor的逻辑
// 使用老板角色的企业和店铺处理逻辑，但设置分销员角色为店员
func (s ClsPointsAccountService) RegisterClsWorker(session *xorm.Session, staffNo string) error {
	log.Infof("积分系统创建店员分销员:员工编号=%s", staffNo)

	var staffs []po.ZlDoctorOrgMap
	var operatorMap map[string]vo.SalesOperatorVO
	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		var err error
		if len(staffNo) > 0 {
			err = tx.Table("big_data.zl_doctor_org_map").Where("user_id=?", staffNo).Limit(1).Find(&staffs)
		} else {
			err = tx.Table("big_data.zl_doctor_org_map").Find(&staffs)
		}
		if err != nil || len(staffs) == 0 {
			return errors.NewBadRequest("员工编号查询zl_doctor_org_map失败: " + err.Error())
		}

		var staffNos []string
		for _, staff := range staffs {
			if len(staff.CoSocialReditCode) == 0 {
				continue
			}
			staffNos = append(staffNos, staff.UserId)
		}
		operatorMap, err = s.GetOperatorMap(tx, staffNos)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}

	// 准备数据库会话
	disMobileMap := make(map[int]string)
	codeEnterpriseIdMap := make(map[string]int64)
	enterShopIdMap := make(map[int64]int)
	for _, staff := range staffs {
		operator, exists := operatorMap[staff.UserId]
		if !exists {
			continue
		}
		mobile := operator.Mobile
		// 没有手机号的、测试数据都需要过滤掉
		if len(mobile) == 0 || strings.Contains(mobile, "*") {
			continue
		}

		// 店员分销员不存在或者信用编码不对
		if operator.DisId == 0 || operator.EnterpriseId == 0 || operator.SocialCreditCode != staff.CoSocialReditCode {
			err = s.tm.RequiresNew(session, func(tx *xorm.Session) error {
				var memberId int
				memberId, err = s.CreateMember(tx, staff.UserName, mobile)
				if err != nil || memberId == 0 {
					log.Errorf("积分系统电商会员账号失败: userId=%s, mobile=%s", staff.UserId, mobile)
					return errors.NewBadRequest("积分系统电商会员账号失败")
				}

				req := vo.ClsWorkerDistributorAddReq{
					OrgId:            4,
					MemberId:         cast.ToInt64(memberId),
					Name:             staff.UserName,
					RealName:         staff.UserName,
					Mobile:           mobile,
					SocialCreditCode: staff.CoSocialReditCode,
					EnterpriseId:     0,
					EnterpriseName:   staff.CoName, // 企业名称
					Province:         staff.Province,
					City:             staff.City,
					District:         staff.District,
					Address:          staff.Address,
				}

				// 查询企业信息并处理
				scrmEnterpriseId := codeEnterpriseIdMap[staff.CoSocialReditCode]
				if scrmEnterpriseId == 0 {
					scrmEnterpriseId, err = s.handleEnterprise(tx, req)
					if err != nil {
						return err
					}
					codeEnterpriseIdMap[staff.CoSocialReditCode] = scrmEnterpriseId
				}

				// 查询店铺并处理
				shopId := enterShopIdMap[scrmEnterpriseId]
				if shopId == 0 {
					shopId, err = s.handleShop(tx, scrmEnterpriseId, req)
					if err != nil {
						return err
					}
					enterShopIdMap[scrmEnterpriseId] = shopId
				}

				// 组装并创建分销员数据
				disId, err := s.createDistributor(tx, req, shopId, operator.DisId)
				if err != nil {
					return err
				}

				if operator.DisId == 0 {
					// 创建分销员详情数据
					err = s.createDistributorDetail(tx, req, disId)
					if err != nil {
						return err
					}

					// 创建分销员统计数据
					err = s.createDistributorTotal(tx, req, disId, shopId)
					if err != nil {
						return err
					}
				}

				disMobileMap[disId] = mobile
				return nil
			})
			if err != nil {
				log.Error("积分系统创建店员分销员:创建分销员失败: " + err.Error())
				continue
			}
		}
	}

	if len(disMobileMap) > 0 {
		// 更新积分
		go func(disMobileMap map[int]string) {
			pointsFlowSrv := NewClsPointsFlowService()
			pointsFlowSrv.UpdatebyStaffNo(session, disMobileMap)
		}(disMobileMap)
	}

	return nil
}

// CreateMember 创建会员账号
func (s ClsPointsAccountService) CreateMember(session *xorm.Session, userName, mobile string) (int, error) {
	var memberId int
	var has bool
	s.tm.NotSupported(session, func(tx *xorm.Session) error {
		var err error
		has, err = tx.SQL("SELECT member_id FROM upetmart.upet_member WHERE member_mobile=? LIMIT 1", mobile).Get(&memberId)
		if err != nil {
			log.Error("积分系统创建店员分销员:查询会员编号失败: " + err.Error())
			return errors.NewBadRequest("查询会员编号失败: " + err.Error())
		}

		return nil
	})

	// 手机号没有查到帐号时，创建会员账号
	if !has {
		now := time.Now()
		unixStr := strconv.FormatInt(now.Unix(), 10)

		// 创建会员账号，只填写必填字段
		member := upetmart_po.UpetMember{
			ScrmUserId:      "", // 生成一个唯一标识作为 SCRM 用户 ID
			MemberMobile:    mobile,
			MemberName:      userName,
			MemberTruename:  userName,
			MemberTime:      unixStr,
			MemberLoginTime: unixStr,
		}

		s.tm.Required(session, func(tx *xorm.Session) error {
			_, err := tx.Table("upetmart.upet_member").Insert(&member)
			if err != nil {
				log.Errorf("创建电商会员失败: %v, mobile: %s", err, mobile)
				return errors.NewBadRequest("创建会员账号失败: " + err.Error())
			}

			// 查询插入后的会员ID
			has, err := tx.SQL("SELECT member_id FROM upetmart.upet_member WHERE member_mobile=? LIMIT 1", mobile).Get(&memberId)
			if err != nil || !has {
				log.Errorf("查询新创建的电商会员ID失败: %v, mobile: %s", err, mobile)
				return errors.NewBadRequest("查询新创建的电商会员ID失败")
			}

			return nil
		})
	}

	return memberId, nil
}

// 处理企业信息
func (s ClsPointsAccountService) handleEnterprise(session *xorm.Session, req vo.ClsWorkerDistributorAddReq) (int64, error) {
	var enterprise distribution_po.DisEnterprise
	if len(req.SocialCreditCode) == 0 {
		return 0, errors.NewBadRequest("统一社会信用代码不能为空")
	}

	dataSource := 1 // 默认为自建
	var scrmEnterpriseId int64
	s.tm.NotSupported(session, func(tx *xorm.Session) error {
		_, err := tx.SQL("SELECT * FROM dis_enterprise WHERE social_credit_code=? LIMIT 1", req.SocialCreditCode).Get(&enterprise)
		if err != nil {
			return err
		}

		var scrmEnterprise distribution_po.ScrmEnterprise
		_, err = tx.SQL("SELECT * FROM scrm_enterprise WHERE social_credit_code=? LIMIT 1", req.SocialCreditCode).Get(&scrmEnterprise)
		if err != nil {
			return err
		}
		if scrmEnterprise.Id > 0 {
			scrmEnterpriseId = scrmEnterprise.Id
			dataSource = 2 // 2表示云订货
		}

		return nil
	})

	s.tm.Required(session, func(tx *xorm.Session) error {
		// 组装宠利扫企业数据
		id := enterprise.Id
		enterprise.OrgId = cast.ToInt(req.OrgId)
		enterprise.ScrmEnterpriseId = scrmEnterpriseId
		enterprise.EnterpriseName = req.EnterpriseName
		enterprise.SocialCreditCode = req.SocialCreditCode
		enterprise.Province = req.Province
		enterprise.City = req.City
		enterprise.District = req.District
		enterprise.Address = req.Address
		enterprise.Phone = utils.AddStar(req.Mobile)
		enterprise.EncryptPhone = utils.MobileEncrypt(req.Mobile)
		enterprise.DataSource = int(dataSource)

		if id == 0 {
			_, err := tx.Table(enterprise.TableName()).Insert(&enterprise)
			if err != nil {
				log.Error("handleEnterpirse新增企业信息失败，err=", err.Error())
				return err
			}
		}
		if id > 0 || scrmEnterpriseId == 0 {
			// 更新企业信息
			if scrmEnterpriseId == 0 {
				enterprise.ScrmEnterpriseId = enterprise.Id
			}
			_, err := tx.Table(enterprise.TableName()).Where("id = ?", enterprise.Id).Update(&enterprise)
			if err != nil {
				log.Error("handleEnterpirse更新企业信息失败，err=", err.Error())
				return err
			}
		}

		return nil
	})

	return enterprise.ScrmEnterpriseId, nil
}

// handleShop 处理门店信息
func (s ClsPointsAccountService) handleShop(session *xorm.Session, scrmEnterpriseId int64, req vo.ClsWorkerDistributorAddReq) (int, error) {
	var shop distribution_po.Shop
	s.tm.NotSupported(session, func(tx *xorm.Session) error {
		_, err := tx.SQL("SELECT * FROM shop WHERE enterprise_id=? AND org_id=? LIMIT 1", scrmEnterpriseId, req.OrgId).Get(&shop)
		if err != nil {
			log.Error("handleShop查询门店信息失败，err=", err.Error())
			return err
		}

		return nil
	})

	if shop.Id == 0 {
		shop.OrgId = cast.ToInt(req.OrgId)
		shop.EnterpriseId = scrmEnterpriseId
		shop.ShopName = req.EnterpriseName

		s.tm.Required(session, func(tx *xorm.Session) error {
			_, err := tx.Insert(&shop)
			if err != nil {
				log.Error("handleShop查询门店信息失败，err=", err.Error())
				return err
			}

			return nil
		})
	}

	return shop.Id, nil
}

// createDistributor 创建分销员数据
func (s ClsPointsAccountService) createDistributor(tx *xorm.Session, req vo.ClsWorkerDistributorAddReq, shopId, disId int) (int, error) {
	logPrefix := fmt.Sprintf("创建分销员数据:会员id=%d", req.MemberId)

	// 加密手机号
	encryptMobile := utils.MobileEncrypt(req.Mobile)
	starMobile := utils.AddStar(req.Mobile)

	engine := s.tm.DbDmMdm()
	defer engine.Close()

	// 组装分销员数据
	disDistributor := distribution_po.DisDistributor{
		OrgId:            cast.ToInt(req.OrgId),
		ShopId:           shopId,
		MemberId:         cast.ToInt(req.MemberId),
		DisRole:          disEnum.DisRoleWorker, // 设置为店员角色
		Status:           disEnum.StatusValid,
		ApproveState:     disEnum.ApproveStatePass,
		Name:             req.Name,
		RealName:         req.RealName,
		Mobile:           starMobile,
		EncryptMobile:    encryptMobile,
		HeadImage:        req.HeadImage,
		SocialCodeImage:  utils.MobileEncrypt(req.SocialCodeImage),
		InSystem:         1,
		SocialCreditCode: req.SocialCreditCode,
		ApproveTime:      time.Now(),
	}

	// 插入分销员数据
	if disId > 0 {
		if _, err := tx.ID(disId).Update(&disDistributor); err != nil {
			log.Error(logPrefix, "更新分销员数据失败，err=", err.Error())
			err = errors.New(err.Error())
			return 0, err
		}
	} else {
		if _, err := tx.Insert(&disDistributor); err != nil {
			log.Error(logPrefix, "插入分销员数据失败，err=", err.Error())
			err = errors.New(err.Error())
			return 0, err
		}
		disId = disDistributor.Id
	}

	return disId, nil
}

// createDistributorDetail 创建分销员详情数据
func (s ClsPointsAccountService) createDistributorDetail(session *xorm.Session, req vo.ClsWorkerDistributorAddReq, disId int) error {
	var detail distribution_po.DisDistributorDetail
	s.tm.NotSupported(session, func(tx *xorm.Session) error {
		_, err := tx.SQL("SELECT * FROM dis_distributor_detail WHERE dis_id=? LIMIT 1", disId).Get(&detail)
		if err != nil {
			log.Error("createDistributorDetail查询分销员详情数据失败，err=", err.Error())
			return err
		}
		return nil
	})

	if detail.DisId == 0 {
		detail.DisId = disId
		detail.Province = req.Province
		detail.City = req.City

		s.tm.Required(session, func(tx *xorm.Session) error {
			if err := detail.Insert(tx); err != nil {
				log.Error("createDistributorDetail插入分销员详情数据失败，err=", err.Error())
				return errors.New(err.Error())
			}
			return nil
		})
	}

	return nil
}

// createDistributorTotal 创建分销员统计数据
func (s ClsPointsAccountService) createDistributorTotal(session *xorm.Session, req vo.ClsWorkerDistributorAddReq, disId, shopId int) error {
	var disDistributorTotal distribution_po.DisDistributorTotal
	s.tm.NotSupported(session, func(tx *xorm.Session) error {
		_, err := tx.SQL("SELECT * FROM dis_distributor_total WHERE dis_id=? AND shop_id=? AND org_id=? LIMIT 1", disId, shopId, req.OrgId).Get(&disDistributorTotal)
		if err != nil {
			log.Error("createDistributorDetail查询分销员详情数据失败，err=", err.Error())
			return err
		}
		return nil
	})

	disDistributorTotal.OrgId = cast.ToInt(req.OrgId)
	disDistributorTotal.MemberId = cast.ToInt(req.MemberId)
	disDistributorTotal.DisId = disId
	disDistributorTotal.ShopId = shopId

	if err := disDistributorTotal.Insert(session, disId, cast.ToInt(req.OrgId), shopId, cast.ToInt(req.MemberId)); err != nil {
		log.Error("createDistributorTotal插入分销员统计数据失败，err=", err.Error())
		return errors.New(err.Error())
	}

	return nil
}

func (s ClsPointsAccountService) GetOperatorMap(session *xorm.Session, staffNos []string) (map[string]vo.SalesOperatorVO, error) {
	// 获取所有操作员ID
	if len(staffNos) == 0 {
		return nil, nil
	}

	// 查询staff信息
	dmTx := s.tm.DbDmMdm()
	var staffList []external_po.ShrTStaffInfo
	err := dmTx.Table("dm_mdm.shr_t_staff_info").In("shr_staff_no", staffNos).Find(&staffList)
	if err != nil {
		return nil, errors.NewBadRequest("批量查询分销员记录失败：" + err.Error())
	}

	// 查询distributor信息并构建operator映射
	return s.buildOperatorMap(session, staffList)
}

// buildOperatorMap 构建操作员映射
func (s ClsPointsAccountService) buildOperatorMap(session *xorm.Session, staffList []external_po.ShrTStaffInfo) (map[string]vo.SalesOperatorVO, error) {
	operatorMap := make(map[string]vo.SalesOperatorVO)
	mobileList := make([]string, 0, len(staffList))

	// 初始化operatorMap
	for _, staff := range staffList {
		if len(staff.Mobile) > 0 {
			operatorMap[staff.ShrStaffNo] = vo.SalesOperatorVO{
				StaffNo: staff.ShrStaffNo,
				Mobile:  staff.Mobile,
			}
			mobileList = append(mobileList, utils.MobileEncrypt(staff.Mobile))
		}
	}

	// 查询distributor信息
	distributorMap, err := s.queryDistributorInfo(session, mobileList)
	if err != nil {
		return nil, err
	}

	// 更新operatorMap
	for _, staff := range staffList {
		if len(staff.Mobile) > 0 {
			operator := operatorMap[staff.ShrStaffNo]
			if distributor, ok := distributorMap[utils.MobileEncrypt(staff.Mobile)]; ok {
				operator.DisId = distributor.DisId
				operator.EnterpriseId = distributor.EnterpriseId
				operator.SocialCreditCode = distributor.SocialCreditCode
				operator.RealName = distributor.RealName
				if len(distributor.RealName) == 0 {
					log.Error("buildOperatorMap 检测到操作人为空：encrypt_mobile=" + utils.MobileEncrypt(staff.Mobile))
				}
			}
			operatorMap[staff.ShrStaffNo] = operator
		}
	}

	return operatorMap, nil
}

// queryDistributorInfo 查询分销商信息
func (s ClsPointsAccountService) queryDistributorInfo(session *xorm.Session, mobileList []string) (map[string]vo.SalesOperatorVO, error) {
	var distributorList []struct {
		EncryptMobile    string `xorm:"encrypt_mobile"`
		DisId            int    `xorm:"dis_id"`
		EnterpriseId     int    `xorm:"enterpise_id"`
		SocialCreditCode string `xorm:"social_credit_code"`
		RealName         string `xorm:"real_name"`
	}

	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		return tx.Table("eshop.dis_distributor dd").
			Join("LEFT", "shop s", "s.id=dd.shop_id").
			Join("LEFT", "dis_enterprise de", "de.scrm_enterprise_id=s.enterprise_id").
			Select("dd.encrypt_mobile AS encrypt_mobile, dd.id AS dis_id, de.id AS enterpise_id, de.social_credit_code AS social_credit_code, dd.real_name AS real_name").
			In("dd.encrypt_mobile", mobileList).
			And("dd.org_id = ?", 4).
			Find(&distributorList)
	})

	if err != nil {
		return nil, errors.NewBadRequest("批量查询分销商记录失败：" + err.Error())
	}

	distributorMap := make(map[string]vo.SalesOperatorVO)
	for _, distributor := range distributorList {
		distributorMap[distributor.EncryptMobile] = vo.SalesOperatorVO{
			DisId:            distributor.DisId,
			EnterpriseId:     distributor.EnterpriseId,
			SocialCreditCode: distributor.SocialCreditCode,
			RealName:         distributor.RealName,
		}
	}
	return distributorMap, nil
}

// func (s ClsPointsAccountService) EncryptMobile(session *xorm.Session) error {
// 	var customerInfos []struct {
// 		Id            int64  `xorm:"id"`
// 		Phone         string `xorm:"phone"`
// 		EncryptMobile string `xorm:"encrypt_mobile"`
// 	}
// 	s.tm.NotSupported(session, func(tx *xorm.Session) error {
// 		err := tx.SQL("SELECT id, phone, encrypt_mobile FROM eshop_saas.c_customer_info WHERE encrypt_mobile=''").Find(&customerInfos)
// 		if err != nil {
// 			log.Error("EncryptMobile查询客户信息失败，err=", err.Error())
// 			return err
// 		}
// 		return nil
// 	})

// 	if len(customerInfos) > 0 {
// 		s.tm.NotSupported(session, func(tx *xorm.Session) error {
// 			for _, customerInfo := range customerInfos {
// 				if len(customerInfo.Phone) > 0 {
// 					_, err := tx.Exec("UPDATE eshop_saas.c_customer_info SET encrypt_mobile=?,phone=? WHERE id=?", utils.MobileEncrypt(customerInfo.Phone), utils.AddStar(customerInfo.Phone), customerInfo.Id)
// 					if err != nil {
// 						log.Error("EncryptMobile更新客户信息失败，err=", err.Error())
// 						return err
// 					}
// 				}
// 			}

// 			return nil
// 		})
// 	}
// 	return nil
// }

// ClsPointsAccountHooks 实现了针对 ClsPointsAccount 的特定钩子
type ClsPointsAccountHooks struct {
	ServiceHooks[int, po.ClsPointsAccount, vo.ClsPointsAccountSaveVO, vo.ClsPointsAccountUpdateVO, vo.ClsPointsAccountQueryVO, vo.ClsPointsAccountResultVO]
}
