package services

import (
	po "eShop/domain/points-po"
	vo "eShop/view-model/points-vo"
	"errors"

	"fmt"
	"strings"

	"xorm.io/xorm"
)

type ClsPointsRuleLogService struct {
	SuperService[int, po.ClsPointsRuleLog, vo.ClsPointsRuleLogSaveVO, vo.ClsPointsRuleLogUpdateVO, vo.ClsPointsRuleLogQueryVO, vo.ClsPointsRuleLogResultVO]
}

func NewClsPointsRuleLogService() ClsPointsRuleLogService {
	return ClsPointsRuleLogService{
		NewSuperService(
			&ClsPointsRuleLogHooks{
				NewServiceHooks[int, po.ClsPointsRuleLog, vo.ClsPointsRuleLogSaveVO, vo.ClsPointsRuleLogUpdateVO, vo.ClsPointsRuleLogQueryVO, vo.ClsPointsRuleLogResultVO](),
			},
		),
	}
}

func (s ClsPointsRuleLogService) BatchCreate(session *xorm.Session, ruleUpdateVO vo.ClsPointsRuleUpdateVO) error {
	err := s.tm.RequiresNew(session, func(tx *xorm.Session) error {
		// 1. 查询旧数据
		ruleSrv := NewClsPointsRuleService()
		oldDetail, err := ruleSrv.GetByCode(tx, vo.ClsPointsRuleQueryVO{
			GoodsCode: ruleUpdateVO.GoodsCode,
			Type:      ruleUpdateVO.Type,
		})
		if err != nil {
			return err
		}

		operator := ruleUpdateVO.Operator
		logs := make([]po.ClsPointsRuleLog, 0)

		// 先将旧数据按时间段+地区索引，便于对比
		oldRegionMap := make(map[int]vo.RegionItem)
		for _, t := range oldDetail.RuleTimeItem {
			for _, r := range t.RuleItem {
				oldRegionMap[r.Id] = r
			}
		}

		// 1. 遍历新RuleTimeItem，处理开关状态和时间变更
		for i, newTime := range ruleUpdateVO.RuleTimeItem {
			// 查找对应的旧时间段
			oldTime := oldDetail.RuleTimeItem[i]

			// 状态变更
			if oldTime.Status != newTime.Status {
				logs = append(logs, po.ClsPointsRuleLog{
					GoodsCode:   ruleUpdateVO.GoodsCode,
					Type:        ruleUpdateVO.Type,
					BizType:     1, // 规则变更
					Description: fmt.Sprintf("%s的规则从“%s”变更为“%s”", formatRange(newTime.StartTime, newTime.EndTime), statusText(oldTime.Status), statusText(newTime.Status)),
					Operator:    operator,
				})
			}
			// 时间变更（生效/失效）
			if oldTime.StartTime != newTime.StartTime || oldTime.EndTime != newTime.EndTime {
				var descs []string
				if oldTime.StartTime != newTime.StartTime {
					descs = append(descs, fmt.Sprintf("生效时间从%s变更为%s", formatDate(oldTime.StartTime), formatDate(newTime.StartTime)))
				}
				if oldTime.EndTime != newTime.EndTime {
					descs = append(descs, fmt.Sprintf("失效时间从%s变更为%s", formatDate(oldTime.EndTime), formatDate(newTime.EndTime)))
				}
				if len(descs) > 0 {
					logs = append(logs, po.ClsPointsRuleLog{
						GoodsCode:   ruleUpdateVO.GoodsCode,
						Type:        ruleUpdateVO.Type,
						BizType:     2, // 规则时间变更
						Description: strings.Join(descs, " "),
						Operator:    operator,
					})
				}
			}

			// 处理积分变更
			for _, newRegion := range newTime.RuleItem {
				oldRegion, ok := oldRegionMap[newRegion.Id]
				if !ok {
					continue // 新增的不记录
				}
				if oldRegion.Points != newRegion.Points {
					logs = append(logs, po.ClsPointsRuleLog{
						GoodsCode:   ruleUpdateVO.GoodsCode,
						Type:        ruleUpdateVO.Type,
						BizType:     3, // 积分数变更
						Description: fmt.Sprintf("%s的%s积分从%d变更为%d", formatRange(newTime.StartTime, newTime.EndTime), newRegion.Region, oldRegion.Points, newRegion.Points),
						Operator:    operator,
					})
				}
			}
		}
		if len(logs) == 0 {
			return nil
		}
		_, err = s.repo.BatchCreate(tx, logs)
		if err != nil {
			return errors.New("积分规则日志批量插入失败：" + err.Error())
		}
		return nil
	})
	return err
}

// 工具函数：格式化时间段 2025-01-01 00:00:00 => 2025-01-01
func formatDate(dt string) string {
	if len(dt) < 10 {
		return dt
	}
	return dt[:10]
}

func formatRange(start, end string) string {
	return formatDate(start) + "~" + formatDate(end)
}

// 工具函数
func statusText(status int) string {
	switch status {
	case 1:
		return "开启"
	case 2:
		return "关闭"
	default:
		return "未知"
	}
}

// ClsPointsRuleLogHooks 实现ServiceHooks接口
type ClsPointsRuleLogHooks struct {
	ServiceHooks[int, po.ClsPointsRuleLog, vo.ClsPointsRuleLogSaveVO, vo.ClsPointsRuleLogUpdateVO, vo.ClsPointsRuleLogQueryVO, vo.ClsPointsRuleLogResultVO]
}

func (h ClsPointsRuleLogHooks) BeforeCreate(session *xorm.Session, saveVO vo.ClsPointsRuleLogSaveVO) (po.ClsPointsRuleLog, error) {
	return h.ServiceHooks.BeforeCreate(session, saveVO)
}
