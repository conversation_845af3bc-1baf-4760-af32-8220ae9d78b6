package services

import (
	po "eShop/domain/points-po"
	"eShop/infra/utils"
	repo "eShop/repo/points-repo"
	vo "eShop/view-model/points-vo"

	"xorm.io/xorm"
)

type ClsDisAddrService struct {
	SuperService[int, po.ClsDisAddr, vo.ClsDisAddrSaveVO, vo.ClsDisAddrUpdateVO, vo.ClsDisAddrQueryVO, vo.ClsDisAddrResultVO]
}

func NewClsDisAddrService() ClsDisAddrService {
	return ClsDisAddrService{
		NewSuperService(
			&ClsDisAddrHooks{
				NewServiceHooks[int, po.ClsDisAddr, vo.ClsDisAddrSaveVO, vo.ClsDisAddrUpdateVO, vo.ClsDisAddrQueryVO, vo.ClsDisAddrResultVO](),
			},
		),
	}
}

// ClsDisAddrHooks 实现ServiceHooks接口
type ClsDisAddrHooks struct {
	ServiceHooks[int, po.ClsDisAddr, vo.ClsDisAddrSaveVO, vo.ClsDisAddrUpdateVO, vo.ClsDisAddrQueryVO, vo.ClsDisAddrResultVO]
}

func (h ClsDisAddrHooks) BeforeCreate(session *xorm.Session, saveVO vo.ClsDisAddrSaveVO) (po.ClsDisAddr, error) {
	saveVO.EncryptPhone = utils.MobileEncrypt(saveVO.Phone)
	// 对手机号中间四位改为*
	saveVO.Phone = saveVO.Phone[:3] + "****" + saveVO.Phone[7:]
	return h.ServiceHooks.BeforeCreate(session, saveVO)
}

func (h ClsDisAddrHooks) BeforeUpdate(session *xorm.Session, updateVO vo.ClsDisAddrUpdateVO) (po.ClsDisAddr, error) {
	if updateVO.IsDefault == 0 {
		// 更新其他地址的默认地址为0
		err := repo.NewClsDisAddrRepo().SetDefault(session, 0, updateVO.Id)
		if err != nil {
			return po.ClsDisAddr{}, err
		}
	}
	return h.ServiceHooks.BeforeUpdate(session, updateVO)
}

func (h ClsDisAddrHooks) AfterUpdate(session *xorm.Session, entity po.ClsDisAddr) (po.ClsDisAddr, error) {
	if entity.IsDefault == 1 {
		// 更新其他地址的默认地址为0
		err := repo.NewClsDisAddrRepo().ClearDefaultAddr(session, entity.DisId, entity.Id)
		if err != nil {
			return entity, err
		}
	}
	return entity, nil
}
