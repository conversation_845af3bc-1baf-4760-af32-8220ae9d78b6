package services

import (
	po "eShop/domain/points-po"
	"eShop/infra/converter"
	"eShop/infra/errors"
	"eShop/infra/log"
	utils "eShop/infra/utils"
	repo "eShop/repo/points-repo"
	vo "eShop/view-model/points-vo"
	"time"

	"xorm.io/xorm"
)

// ClsPointsOrderService 提供了积分订单相关的服务
type ClsPointsOrderService struct {
	SuperService[int, po.ClsPointsOrder, vo.ClsPointsOrderSaveVO, vo.ClsPointsOrderUpdateVO, vo.ClsPointsOrderQueryVO, vo.ClsPointsOrderResultVO]
	repo repo.ClsPointsOrderRepo
}

// NewClsPointsOrderService 创建一个新的 ClsPointsOrderService 实例
func NewClsPointsOrderService() ClsPointsOrderService {
	return ClsPointsOrderService{
		NewSuperService(
			// 注入 ClsPointsOrderHooks 的实例
			&ClsPointsOrderHooks{
				// 初始化嵌入的 ServiceHooks
				NewServiceHooks[int, po.ClsPointsOrder, vo.ClsPointsOrderSaveVO, vo.ClsPointsOrderUpdateVO, vo.ClsPointsOrderQueryVO, vo.ClsPointsOrderResultVO](),
			},
		),
		repo.NewClsPointsOrderRepo(),
	}
}

// MultiPage 分页查询积分订单
func (s ClsPointsOrderService) Page(session *xorm.Session, queryVO vo.ClsPointsOrderQueryVO) ([]vo.ClsPointsOrderResultVO, int64, error) {
	var result []vo.ClsPointsOrderResultVO
	var total int64
	var err error

	queryVO.Phone = utils.MobileEncrypt(queryVO.Phone)
	s.tm.NotSupported(session, func(tx *xorm.Session) error {
		// 在事务中执行分页查询
		result, total, err = s.repo.Page(tx, queryVO)
		return nil
	})
	if err != nil {
		return nil, 0, err
	}

	return result, total, nil
}

// MultiDetail 获取积分订单详情
func (s ClsPointsOrderService) Detail(session *xorm.Session, id int) (vo.ClsPointsOrderResultVO, error) {
	var result vo.ClsPointsOrderResultVO
	var err error
	s.tm.NotSupported(session, func(tx *xorm.Session) error {
		// 在事务中执行分页查询
		result, err = s.repo.Detail(tx, id)
		return nil
	})
	if err != nil {
		return vo.ClsPointsOrderResultVO{}, err
	}

	return result, nil
}

// Deliver 订单发货
func (s ClsPointsOrderService) Deliver(session *xorm.Session, updateVO vo.ClsPointsOrderUpdateVO) error {
	var err error
	pointsOrder, err := converter.DeepConvert[po.ClsPointsOrder](updateVO)
	if err != nil {
		return errors.NewBadRequest("ClsPointsOrderService Deliver VO转Entity失败：" + err.Error())
	}
	pointsOrder.Status = 2
	pointsOrder.ExpressTime = time.Now()
	s.tm.NotSupported(session, func(tx *xorm.Session) error {
		_, err = s.repo.Update(tx, pointsOrder)
		return nil
	})
	if err != nil {
		return err
	}

	go func() {
		orderLogRepo := repo.NewClsPointsOrderLogRepo()
		s.tm.NotSupported(session, func(tx *xorm.Session) error {
			_, err := orderLogRepo.Create(tx, po.ClsPointsOrderLog{
				OrderId:   updateVO.Id,
				Status:    2,
				Operator:  "",
				CreatedAt: time.Now(),
			})
			return err
		})
		if err != nil {
			log.Error("订单日志记录失败：", err.Error())
		}
	}()

	return nil
}

// QueryExpress 查询物流信息
func (s ClsPointsOrderService) QueryExpress(session *xorm.Session, id int) (*utils.ExpressInfo, error) {
	var err error
	var order po.ClsPointsOrder
	var phoneEnd string

	// 查询订单信息
	err = s.tm.Required(session, func(tx *xorm.Session) error {
		order, err = s.repo.GetById(tx, id)
		if err != nil {
			return err
		}
		if order.Id == 0 {
			return errors.NewBadRequest("订单不存在")
		}
		if order.Status == 1 {
			return errors.NewBadRequest("订单未发货")
		}
		if order.ExpressNo == "" {
			return errors.NewBadRequest("订单未填写快递单号")
		}

		// 根据addressId查询地址信息中的收货人手机号后四位
		address, err := repo.NewClsDisAddrRepo().GetById(tx, order.AddressId)
		if err != nil {
			return err
		}
		phoneEnd = address.Phone[len(address.Phone)-4:]

		return nil
	})
	if err != nil {
		return nil, err
	}

	// 调用阿里云物流查询API
	ali := utils.NewAliExpress()
	express, err := ali.QueryExpress(order.ExpressCompanyCode, order.ExpressNo, phoneEnd)
	if err != nil {
		return nil, errors.NewBadRequest("物流查询失败: " + err.Error())
	}

	// 异步执行：检查track中的IsSign，如果已签收，则需要更新订单为已完成
	go func(orderId int) {
		if express.Result.DeliveryStatus == "3" && order.Status == 2 {
			// 更新订单为已完成
			s.tm.Required(session, func(tx *xorm.Session) error {
				_, err = s.repo.Update(tx, po.ClsPointsOrder{
					SuperEntity: po.SuperEntity[int]{
						Id: orderId,
					},
					Status: 3,
				})
				if err != nil {
					log.Error("更新订单为已完成失败: ", err.Error())
				}

				return nil
			})
		}
	}(id)

	return express.Result, nil
}

// FinishOrder 自动完成积分兑换订单
func (s ClsPointsOrderService) FinishOrder(session *xorm.Session, curDate string) error {
	startDate := time.Now().AddDate(0, 0, -14).Format("2006-01-02")
	endDate := time.Now().AddDate(0, 0, -13).Format("2006-01-02")
	if len(curDate) > 0 {
		startDate = curDate
		curTime, err := time.Parse("2006-01-02", curDate)
		if err != nil {
			return errors.NewBadRequest("日期格式错误：" + err.Error())
		}
		endDate = curTime.AddDate(0, 0, 1).Format("2006-01-02")
	}

	var orderIds []int
	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		orders, err := s.repo.List(tx, vo.ClsPointsOrderQueryVO{
			Status:           2,
			ExpressTimeStart: startDate,
			ExpressTimeEnd:   endDate,
		})
		if err != nil {
			return err
		}
		if len(orders) == 0 {
			log.Info("FinishOrder 没有需要自动完成的订单, startDate=" + startDate + ", endDate=" + endDate)
			return nil
		}
		for _, order := range orders {
			orderIds = append(orderIds, order.Id)
		}

		return s.repo.FinishOrder(tx, startDate, endDate)
	})
	if err != nil {
		return err
	}

	go func() {
		var orderLogs []po.ClsPointsOrderLog
		if len(orderIds) > 0 {
			for _, orderId := range orderIds {
				orderLogs = append(orderLogs, po.ClsPointsOrderLog{
					OrderId:   orderId,
					Status:    3,
					Operator:  "系统自动",
					CreatedAt: time.Now(),
				})
			}

			orderLogRepo := repo.NewClsPointsOrderLogRepo()
			s.tm.NotSupported(session, func(tx *xorm.Session) error {
				_, err := orderLogRepo.BatchCreate(tx, orderLogs)
				return err
			})
			if err != nil {
				log.Error("订单日志记录失败：", err.Error())
			}
		}
	}()

	return nil
}

// PointOut 积分扣减
func (s ClsPointsOrderService) PointOut(session *xorm.Session, id int) error {
	// 根据id查询到订单信息
	err := s.tm.Required(session, func(tx *xorm.Session) error {
		order, err := s.repo.GetById(tx, id)
		if err != nil || order.Id == 0 {
			log.Error("PointOut error : order not found: ", id)
			return errors.NewBadRequest("没有找到对应的订单信息")
		}

		// 需要新增积分消耗相关的记录
		err = NewClsPointsFlowService().OrderPointOut(tx, order)
		if err != nil {
			log.Error("积分消耗变更失败：orderId=", order.Id)
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

// ClsPointsOrderHooks 实现了针对 ClsPointsOrder 的特定钩子
type ClsPointsOrderHooks struct {
	// 嵌入通用的 ServiceHooks，这样就继承了所有方法的默认实现
	ServiceHooks[int, po.ClsPointsOrder, vo.ClsPointsOrderSaveVO, vo.ClsPointsOrderUpdateVO, vo.ClsPointsOrderQueryVO, vo.ClsPointsOrderResultVO]
}

func (h ClsPointsOrderHooks) BeforeCreate(session *xorm.Session, saveVO vo.ClsPointsOrderSaveVO) (po.ClsPointsOrder, error) {
	log.Info("ClsPointsOrderHooks: BeforeCreate hook triggered：saveVO" + utils.JsonEncode(saveVO))

	// 查询出商品信息
	goodsId := saveVO.GoodsId
	goods, err := repo.NewClsPointsGoodsRepo().GetById(session, goodsId)
	if err != nil {
		return po.ClsPointsOrder{}, err
	}
	if goods.Id == 0 || goods.Status == 2 {
		return po.ClsPointsOrder{}, errors.NewBadRequest("商品不存在或者已下架")
	}

	// 判断有效期
	now := time.Now()
	if now.Before(goods.StartTime) || now.After(goods.EndTime) {
		return po.ClsPointsOrder{}, errors.NewBadRequest("商品不在兑换时间内")
	}

	// 判断库存
	if goods.Stock < saveVO.Quantity {
		return po.ClsPointsOrder{}, errors.NewBadRequest("商品库存不够")
	}

	// 判断兑换数量
	orders, err := repo.NewClsPointsOrderRepo().List(session, vo.ClsPointsOrderQueryVO{
		DisId:   saveVO.DisId,
		GoodsId: saveVO.GoodsId,
	})
	if err != nil {
		return po.ClsPointsOrder{}, err
	}
	if len(orders) > 0 {
		var totalQuantity int
		for _, order := range orders {
			if order.Status == 1 {
				totalQuantity += order.Quantity
			}
		}
		if goods.ExchangeLimit > 0 && totalQuantity+saveVO.Quantity > goods.ExchangeLimit {
			return po.ClsPointsOrder{}, errors.NewBadRequest("商品兑换数量超过限制")
		}
	}

	entity, err := h.ServiceHooks.BeforeCreate(session, saveVO)
	if err != nil {
		return po.ClsPointsOrder{}, err
	}

	entity.OrderNo = utils.GenerateNo("")
	entity.GoodsName = goods.Name
	entity.GoodsType = goods.Type
	entity.PointsCost = goods.PointsPrice * entity.Quantity
	entity.Status = 1
	entity.OrderTime = time.Now()

	return entity, nil
}

func (h ClsPointsOrderHooks) AfterCreate(session *xorm.Session, entity po.ClsPointsOrder) (po.ClsPointsOrder, error) {
	log.Info("ClsPointsOrderHooks: AfterCreate hook triggered for entity:" + utils.JsonEncode(entity))

	// 扣除库存
	session.Table("cls_points_goods").Exec("UPDATE cls_points_goods SET stock = stock - ? WHERE id = ?", entity.Quantity, entity.GoodsId)

	// 需要新增积分消耗相关的记录
	err := NewClsPointsFlowService().OrderPointOut(session, entity)
	if err != nil {
		log.Error("积分消耗变更失败：orderId=", entity.Id)
		return entity, err
	}

	go func() {
		orderLogRepo := repo.NewClsPointsOrderLogRepo()
		h.tm.NotSupported(session, func(tx *xorm.Session) error {
			_, err := orderLogRepo.Create(tx, po.ClsPointsOrderLog{
				OrderId:   entity.Id,
				Status:    1,
				Operator:  "",
				CreatedAt: time.Now(),
			})
			return err
		})
		if err != nil {
			log.Error("订单日志记录失败：", err.Error())
		}
	}()

	return entity, nil
}
