package services

import (
	po "eShop/domain/points-po"
	repo "eShop/repo/points-repo"
	vo "eShop/view-model/points-vo"

	"xorm.io/xorm"
)

// ClsPointsGoodsService 提供了积分商品相关的服务
type ClsPointsGoodsService struct {
	SuperService[int, po.ClsPointsGoods, vo.ClsPointsGoodsSaveVO, vo.ClsPointsGoodsUpdateVO, vo.ClsPointsGoodsQueryVO, vo.ClsPointsGoodsResultVO]
	repo repo.ClsPointsGoodsRepo
}

// NewClsPointsGoodsService 创建一个新的 ClsPointsGoodsService 实例
func NewClsPointsGoodsService() ClsPointsGoodsService {
	return ClsPointsGoodsService{
		NewSuperService(
			// 注入 ClsPointsGoodsHooks 的实例
			&ClsPointsGoodsHooks{
				// 初始化嵌入的 ServiceHooks
				NewServiceHooks[int, po.ClsPointsGoods, vo.ClsPointsGoodsSaveVO, vo.ClsPointsGoodsUpdateVO, vo.ClsPointsGoodsQueryVO, vo.ClsPointsGoodsResultVO](),
			},
		),
		repo.NewClsPointsGoodsRepo(),
	}
}

// ClsPointsGoodsHooks 实现了针对 ClsPointsGoods 的特定钩子
type ClsPointsGoodsHooks struct {
	// 嵌入通用的 ServiceHooks，这样就继承了所有方法的默认实现
	ServiceHooks[int, po.ClsPointsGoods, vo.ClsPointsGoodsSaveVO, vo.ClsPointsGoodsUpdateVO, vo.ClsPointsGoodsQueryVO, vo.ClsPointsGoodsResultVO]
}

// 以下可以根据需要重写特定的钩子方法
// 例如：在创建前进行特定校验或处理
func (h ClsPointsGoodsHooks) BeforeCreate(session *xorm.Session, saveVO vo.ClsPointsGoodsSaveVO) (po.ClsPointsGoods, error) {
	if len(saveVO.StartTime) == 0 {
		saveVO.StartTime = "0001-01-01 00:00:00"
	}
	if len(saveVO.EndTime) == 0 {
		saveVO.EndTime = "9999-12-31 23:59:59"
	}

	return h.ServiceHooks.BeforeCreate(session, saveVO)
}

func (h ClsPointsGoodsHooks) AfterPage(session *xorm.Session, goodsList []po.ClsPointsGoods) ([]vo.ClsPointsGoodsResultVO, error) {
	var resultVOs []vo.ClsPointsGoodsResultVO
	resultVOs, err := h.ServiceHooks.AfterPage(session, goodsList)
	if err != nil {
		return nil, err
	}

	orderRepo := repo.NewClsPointsOrderRepo()
	for i, resultVO := range resultVOs {
		// 查询售出数量
		quantity, err := orderRepo.CountById(session, resultVO.Id)
		if err != nil {
			return nil, err
		}
		resultVOs[i].SaleCount = quantity
	}
	return resultVOs, nil
}

func (h ClsPointsGoodsHooks) AfterQuery(session *xorm.Session, resultVO vo.ClsPointsGoodsResultVO) (vo.ClsPointsGoodsResultVO, error) {
	if resultVO.Id == 0 {
		return resultVO, nil
	}

	orderRepo := repo.NewClsPointsOrderRepo()
	quantity, err := orderRepo.CountById(session, resultVO.Id)
	if err != nil {
		return resultVO, err
	}
	resultVO.SaleCount = quantity
	return resultVO, nil
}
