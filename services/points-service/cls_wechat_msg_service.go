package services

import (
	"bytes"
	"crypto/md5"
	"eShop/infra/errors"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"encoding/hex"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"strconv"
	"time"

	vo "eShop/view-model/points-vo"
)

// ClsWechatMsgService 提供了微信消息相关的服务
type ClsWechatMsgService struct {
	common.BaseService
}

// NewClsWechatMsgService 创建一个新的 ClsWechatMsgService 实例
func NewClsWechatMsgService() ClsWechatMsgService {
	return ClsWechatMsgService{}
}

func (s ClsWechatMsgService) SendChangeMsg(disId int, values []*vo.MessageValue) error {
	model := vo.SendSubscribeMessageReq{
		SubscribeType: "integral",
		TemplateKey:   "user-integral-change",
		Values:        values,
	}

	return s.SendMsg(disId, model)
}

func (s ClsWechatMsgService) SendExpireMsg(disId int, values []*vo.MessageValue) error {
	model := vo.SendSubscribeMessageReq{
		SubscribeType: "integral",
		TemplateKey:   "user-integral-expire",
		Values:        values,
	}
	return s.SendMsg(disId, model)
}

func (s ClsWechatMsgService) SendMsg(disId int, model vo.SendSubscribeMessageReq) error {
	s.Begin()
	defer s.Close()

	session := s.Session
	var scrmUserId string
	_, err := session.SQL(`
		SELECT u.scrm_user_id
		FROM dis_distributor dd
		LEFT JOIN eshop.users u ON u.member_id = dd.member_id
		WHERE dd.id = ? AND u.org_id = 4
	`, disId).Get(&scrmUserId)
	if err != nil || scrmUserId == "" {
		log.Error("查询账户的scrmUserId失败：disId=", disId, "error=", err)
		return err
	}

	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	model.AccessKey = "accessKey"
	model.ScrmUserId = scrmUserId
	model.Timestamp = timestamp
	model.Sign = getMd5(scrmUserId + model.SubscribeType + model.TemplateKey + "accessKey" + timestamp)

	str, _ := json.Marshal(model)
	url := "http://127.0.0.1:11008/mall/wechat/subscribe/message/send"
	ret, err := HttpPostTo(url, str)
	log.Info("ESHOP 微信小程序订阅消息", string(ret), err)
	return nil
}

// 替代 kit.GetMd5 因为引入"github.com/ppkg/kit"会启动报错
func getMd5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

func HttpPostTo(url string, bytesData []byte) ([]byte, error) {
	reader := bytes.NewReader(bytesData)
	request, err := http.NewRequest("POST", url, reader)
	if err != nil {
		return nil, err
	}
	request.Header.Set("org_id", "4")
	request.Header.Set("Content-Type", "application/json;charset=utf-8")

	//client := &http.Client{Transport: tr}
	resp, err := utils.HttpTransportClientTimeout.Do(request)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != 200 {
		return nil, errors.New(resp.Status)
	}
	respBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	} else {
	}
	return respBytes, nil
}
