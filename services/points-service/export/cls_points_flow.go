package export

import (
	"eShop/infra/utils"
	"eShop/services/common"
	service "eShop/services/points-service"
	vo "eShop/view-model/points-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type ClsPointsFlowTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.ClsPointsFlowQueryVO
	writer       *excelize.StreamWriter
	common.BaseService
}

func (e ClsPointsFlowTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.ClsPointsFlowQueryVO)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.Current = 1
	e.ExportParams.Size = 10000

	e.writer, err = e.F.NewStreamWriter(e.<PERSON>et<PERSON>ame)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	e.SetSheetName()
	client := service.NewClsPointsFlowService()

	k := 0
	for {
		ret, _, err := client.Page(nil, *e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.Current += 1
		for i := 0; i < len(ret); i++ {
			k++
			axis := fmt.Sprintf("A%d", k+1)

			// int类型转中文
			inSystemStr := "体系外"
			if ret[i].InSystem == 1 {
				inSystemStr = "体系内"
			}

			disRoleStr := "初始值"
			switch ret[i].DisRole {
			case 1:
				disRoleStr = "老板"
			case 2:
				disRoleStr = "店员"
			case 3:
				disRoleStr = "医生"
			}

			bizTypeStr := "未知"
			switch ret[i].BizType {
			case 1:
				bizTypeStr = "开单" + ret[i].BrandName + "商品"
			case 2:
				bizTypeStr = "分销" + ret[i].BrandName + "商品"
			case 3:
				bizTypeStr = "积分兑换"
			case 4:
				bizTypeStr = "商品退货积分扣减"
			case 5:
				bizTypeStr = "积分到期自动扣减"
			case 6:
				bizTypeStr = "积分赠送收入"
			case 7:
				bizTypeStr = "积分赠送支出"
			case 8:
				bizTypeStr = "获赠积分到期扣减"
			}

			mobile := utils.MobileDecrypt(ret[i].EncryptMobile)

			var orderNo, goodsName string

			if ret[i].Type == 1 || ret[i].BizType == 4 {
				orderNo = ret[i].BillOrderNo
				goodsName = ret[i].BillGoodsName
			} else if ret[i].BizType == 3 {
				orderNo = ret[i].OrderNo
				goodsName = ret[i].OrderGoodsName
			}

			switch e.ExportParams.OperateType {
			case 1:
				// 全局发放导出（如有不同可补充）
				_ = e.writer.SetRow(axis, []interface{}{
					ret[i].OccurTime,      // 时间
					ret[i].DisId,          // 分销员id
					ret[i].DisName,        // 分销员姓名
					inSystemStr,           // 分销员类型
					mobile,                // 分销员手机号
					ret[i].Region,         // 大区
					ret[i].EnterpriseName, // 绑定的企业
					ret[i].HospitalName,   // 绑定的医院
					disRoleStr,            // 企业角色
					bizTypeStr,            // 任务类型
					orderNo,               // 订单编号
					goodsName,             // 详细商品
					ret[i].Points,         // 积分值
					ret[i].PointsBlky,     // 分摊积分数(北京百林)
					ret[i].PointsSzld,     // 分摊积分数(深圳利都)
				})

			case 2:
				// 全局消耗导出，表头与图片一致
				_ = e.writer.SetRow(axis, []interface{}{
					ret[i].OccurTime,                 // 时间
					ret[i].DisId,                     // 分销员id
					ret[i].DisName,                   // 分销员姓名
					inSystemStr,                      // 分销员类型
					mobile,                           // 分销员手机号
					ret[i].Region,                    // 大区
					ret[i].EnterpriseName,            // 绑定的企业
					ret[i].HospitalName,              // 绑定的医院
					disRoleStr,                       // 企业角色
					bizTypeStr,                       // 任务类型
					orderNo,                          // 订单编号
					goodsName,                        // 详细商品
					utils.Fen2Yuan(ret[i].CostPrice), // 商品成本
					ret[i].Points,                    // 积分值
					ret[i].PointsBlky,                // 分摊积分数(北京百林)
					ret[i].PointsSzld,                // 分摊积分数(深圳利都)
				})

			case 3:
				// 分销员
				getPoints := ""
				costPoints := ""
				orderNo := ""
				goodsName := ""
				if ret[i].Type == 1 || ret[i].BizType == 4 {
					getPoints = fmt.Sprint(ret[i].Points)
					orderNo = ret[i].BillOrderNo
					goodsName = ret[i].BillGoodsName
				} else if ret[i].Type == 2 {
					costPoints = fmt.Sprint(ret[i].Points)
					orderNo = ret[i].OrderNo
					goodsName = ret[i].OrderGoodsName
				}
				_ = e.writer.SetRow(axis, []interface{}{
					ret[i].OccurTime, // 日期
					getPoints,        // 获取积分值
					costPoints,       // 消耗积分值
					bizTypeStr,       // 业务名称
					orderNo,          // 关联订单
					goodsName,        // 商品名称
					ret[i].Remark,    // 备注
				})

			case 4:
				// 企业导出
				getPoints := ""
				costPoints := ""
				orderNo := ""
				goodsName := ""
				if ret[i].Type == 1 || ret[i].BizType == 4 {
					getPoints = fmt.Sprint(ret[i].Points)
					orderNo = ret[i].BillOrderNo
					goodsName = ret[i].BillGoodsName
				} else if ret[i].Type == 2 {
					costPoints = fmt.Sprint(ret[i].Points)
					orderNo = ret[i].OrderNo
					goodsName = ret[i].OrderGoodsName
				}
				_ = e.writer.SetRow(axis, []interface{}{
					ret[i].OccurTime, // 日期
					getPoints,        // 获取积分值
					costPoints,       // 消耗积分值
					bizTypeStr,       // 业务名称
					ret[i].Operator,  // 操作人
					orderNo,          // 关联订单
					goodsName,        // 商品名称
					ret[i].Remark,    // 备注
				})
			}
		}
		if len(ret) < int(e.ExportParams.Size) {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return
}

func (e ClsPointsFlowTask) SetSheetName(args ...interface{}) {
	if e.ExportParams == nil {
		return
	}

	switch e.ExportParams.OperateType {
	case 1:
		nameList := []interface{}{
			"时间", "分销员id", "分销员姓名", "分销员类型", "分销员手机号", "大区", "绑定的企业", "绑定的医院", "企业角色", "任务类型", "订单编号", "详细商品", "积分值", "分摊积分数(北京百林)", "分摊积分数(深圳利都)",
		}
		_ = e.writer.SetRow("A1", nameList)

	case 2:
		nameList := []interface{}{
			"时间", "分销员id", "分销员姓名", "分销员类型", "分销员手机号", "大区", "绑定的企业", "绑定的医院", "企业角色", "任务类型", "订单编号", "详细商品", "商品成本", "积分值", "分摊积分数(北京百林)", "分摊积分数(深圳利都)",
		}
		_ = e.writer.SetRow("A1", nameList)

	case 3:
		nameList := []interface{}{
			"日期", "获取积分值", "消耗积分值", "业务名称", "关联订单", "商品名称", "备注",
		}
		_ = e.writer.SetRow("A1", nameList)

	case 4:
		nameList := []interface{}{
			"日期", "获取积分值", "消耗积分值", "业务名称", "操作人", "关联订单", "商品名称", "备注",
		}
		_ = e.writer.SetRow("A1", nameList)
	}
}

func (e ClsPointsFlowTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("积分流水导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e ClsPointsFlowTask) OperationFunc(row []string, orgId int) string {
	return ""
}
