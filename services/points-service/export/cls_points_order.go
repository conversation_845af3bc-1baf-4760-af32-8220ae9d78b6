package export

import (
	po "eShop/domain/points-po"
	"eShop/infra/utils"
	repo "eShop/repo/points-repo"
	"eShop/services/common"
	service "eShop/services/points-service"
	vo "eShop/view-model/points-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type ClsPointsOrderTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.ClsPointsOrderQueryVO
	writer       *excelize.StreamWriter
	common.BaseService
}

func (e ClsPointsOrderTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.ClsPointsOrderQueryVO)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.Current = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.Size = 10000

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()
	client := service.NewClsPointsOrderService()

	k := 0
	for {
		ret, _, err := client.Page(nil, *e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.Current += 1
		for i := 0; i < len(ret); i++ {
			k++
			axis := fmt.Sprintf("A%d", k+1)

			status := "待发货"
			if ret[i].Status == 2 {
				status = "已发货"
			} else if ret[i].Status == 3 {
				status = "已完成"
			}

			goodsType := "实物商品"
			if ret[i].GoodsType == 2 {
				goodsType = "虚拟商品"
			}

			source := "宠利扫小程序"

			address := ret[i].Province + " " + ret[i].City + " " + ret[i].District + " " + ret[i].Ress
			addrPhone := utils.MobileDecrypt(ret[i].AddrEncryptPhone)
			memberMobile := utils.MobileDecrypt(ret[i].MemberEncryptMobile)

			_ = e.writer.SetRow(axis, []interface{}{
				ret[i].OrderNo,        // 订单编号
				ret[i].MemberName,     // 会员名称
				ret[i].PointsCost,     // 兑换积分
				ret[i].OrderTime,      // 兑换时间
				status,                // 订单状态：1-待发货，2-已发货，3-已完成
				goodsType,             // 类型: 1-实物商品，2-虚拟商品
				ret[i].ExpressCompany, // 快递公司
				ret[i].ExpressNo,      // 物流单号
				ret[i].ExpressTime,    // 发货时间
				ret[i].CompleteTime,   // 完成时间
				source,                // 来源: 1-宠利扫小程序
				ret[i].Remark,         // 订单留言
				ret[i].AddrName,       // 收货人
				addrPhone,             // 联系电话
				memberMobile,          // 会员手机
				address,               // 收货地址
				ret[i].GoodsName,      // 商品名称

			})
		}
		if len(ret) < int(e.ExportParams.Size) {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return
}

func (e ClsPointsOrderTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"订单编号", "会员名称", "兑换积分", "兑换时间", "状态", "类型", "快递公司", "物流单号", "发货时间", "完成时间", "来源", "订单留言", "收货人", "联系电话", "会员手机", "收货地址", "商品名称",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e ClsPointsOrderTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("宠利扫积分订单导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e ClsPointsOrderTask) OperationFunc(row []string, orgId int) string {
	// 错误信息
	var msg string

	if len(row) < 2 {
		msg = "模板格式错误，请上传正确模板"
		return msg
	}

	if len(row[0]) == 0 {
		msg = "订单编号不能为空"
		return msg
	}

	if len(row[1]) == 0 {
		msg = "快递公司名不能为空"
		return msg
	}

	if len(row[2]) == 0 {
		msg = "快递单号不能为空"
		return msg
	}

	// 业务校验：1、订单存在 2、订单状态：1-待发货
	e.Begin()
	defer e.Close()

	// 将子上下文传入Session
	session := e.Session

	orderRepo := repo.NewClsPointsOrderRepo()
	order, err := orderRepo.QueryOne(session, vo.ClsPointsOrderQueryVO{
		OrderNo: row[0],
	})
	if err != nil || order.Id == 0 {
		msg = "订单编号不存在"
		return msg
	}

	if order.Status != 1 {
		msg = "订单状态不是待发货"
		return msg
	}

	orderRepo.Update(session, po.ClsPointsOrder{
		SuperEntity: po.SuperEntity[int]{
			Id: order.Id,
		},
		Status:         2, // 发货
		ExpressCompany: row[1],
		ExpressNo:      row[2],
		ExpressTime:    time.Now(),
	})

	return ""
}
