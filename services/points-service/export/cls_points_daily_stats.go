package export

import (
	"eShop/infra/utils"
	"eShop/services/common"
	service "eShop/services/points-service"
	vo "eShop/view-model/points-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type ClsPointsDailyStatsTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.ClsPointsDailyStatsQueryVO
	writer       *excelize.StreamWriter
	common.BaseService
}

func (e ClsPointsDailyStatsTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.ClsPointsDailyStatsQueryVO)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.Current = 1
	e.ExportParams.Size = 10000

	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	e.SetSheetName()
	client := service.NewClsPointsDailyStatsService()

	k := 0
	for {
		ret, _, err := client.Page(nil, *e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.Current += 1
		for i := 0; i < len(ret); i++ {
			k++
			axis := fmt.Sprintf("A%d", k+1)

			_ = e.writer.SetRow(axis, []interface{}{
				ret[i].StatDate,     // 时间
				ret[i].IssueTotal,   // 发放总额
				ret[i].IssueBlky,    // 发放总额(北京百林)
				ret[i].IssueSzld,    // 发放总额(深圳利都)
				ret[i].ConsumeTotal, // 消耗总额
				ret[i].ConsumeBlky,  // 消耗总额(北京百林)
				ret[i].ConsumeSzld,  // 消耗总额(深圳利都)
				ret[i].ExpiredTotal, // 失效总额
				ret[i].ExpiredBlky,  // 失效总额(北京百林)
				ret[i].ExpiredSzld,  // 失效总额(深圳利都)
			})
		}
		if len(ret) < int(e.ExportParams.Size) {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return
}

func (e ClsPointsDailyStatsTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"时间", "发放总额", "发放总额(北京百林)", "发放总额(深圳利都)", "消耗总额", "消耗总额(北京百林)", "消耗总额(深圳利都)", "失效总额", "失效总额(北京百林)", "失效总额(深圳利都)",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e ClsPointsDailyStatsTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("积分每日统计导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e ClsPointsDailyStatsTask) OperationFunc(row []string, orgId int) string {
	return ""
}
