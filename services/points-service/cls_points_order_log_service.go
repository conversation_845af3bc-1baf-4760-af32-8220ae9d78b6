package services

import (
	po "eShop/domain/points-po"
	repo "eShop/repo/points-repo"
	vo "eShop/view-model/points-vo"
	// "eShop/infra/errors" // 根据需要取消注释
	// "eShop/infra/log"    // 根据需要取消注释
	// "xorm.io/xorm"       // 根据需要取消注释
)

// ClsPointsOrderLogService 提供了订单日志相关的服务
type ClsPointsOrderLogService struct {
	SuperService[int, po.ClsPointsOrderLog, vo.ClsPointsOrderLogSaveVO, vo.ClsPointsOrderLogUpdateVO, vo.ClsPointsOrderLogQueryVO, vo.ClsPointsOrderLogResultVO]
	repo repo.ClsPointsOrderLogRepo
}

// NewClsPointsOrderLogService 创建一个新的 ClsPointsOrderLogService 实例
func NewClsPointsOrderLogService() ClsPointsOrderLogService {
	return ClsPointsOrderLogService{
		NewSuperService(
			// 注入 ClsPointsOrderLogHooks 的实例
			&ClsPointsOrderLogHooks{
				// 初始化嵌入的 ServiceHooks
				NewServiceHooks[int, po.ClsPointsOrderLog, vo.ClsPointsOrderLogSaveVO, vo.ClsPointsOrderLogUpdateVO, vo.ClsPointsOrderLogQueryVO, vo.ClsPointsOrderLogResultVO](),
			},
		),
		repo.NewClsPointsOrderLogRepo(),
	}
}

// ClsPointsOrderLogHooks 实现了针对 ClsPointsOrderLog 的特定钩子
type ClsPointsOrderLogHooks struct {
	// 嵌入通用的 ServiceHooks，这样就继承了所有方法的默认实现
	ServiceHooks[int, po.ClsPointsOrderLog, vo.ClsPointsOrderLogSaveVO, vo.ClsPointsOrderLogUpdateVO, vo.ClsPointsOrderLogQueryVO, vo.ClsPointsOrderLogResultVO]
}

// func (h ClsPointsOrderLogHooks) BeforeCreate(session xorm.Session, saveVO vo.ClsPointsOrderLogSaveVO) (vo.ClsPointsOrderLogSaveVO, error) {
// 	log.Info("ClsPointsOrderLogHooks: BeforeCreate hook triggered")
// 	return saveVO, nil
// }

// func (h ClsPointsOrderLogHooks) AfterCreate(session xorm.Session, entity po.ClsPointsOrderLog) (po.ClsPointsOrderLog, error) {
// 	log.Info("ClsPointsOrderLogHooks: AfterCreate hook triggered for entity:", entity.Id)
// 	return entity, nil
// }

// func (h ClsPointsOrderLogHooks) BeforeUpdate(session xorm.Session, updateVO vo.ClsPointsOrderLogUpdateVO) (vo.ClsPointsOrderLogUpdateVO, error) {
//  log.Info("ClsPointsOrderLogHooks: BeforeUpdate hook triggered for ID:", updateVO.Id)
// 	return updateVO, nil
// }
