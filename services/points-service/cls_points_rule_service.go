package services

import (
	po "eShop/domain/points-po"
	"eShop/infra/errors"
	"eShop/infra/log"
	"eShop/infra/utils"
	repo "eShop/repo/points-repo"
	vo "eShop/view-model/points-vo"
	"slices"
	"time"

	"xorm.io/xorm"
)

type ClsPointsRuleService struct {
	SuperService[int, po.ClsPointsRule, vo.ClsPointsRuleSaveVO, vo.ClsPointsRuleUpdateVO, vo.ClsPointsRuleQueryVO, vo.ClsPointsRuleResultVO]
	repo repo.ClsPointsRuleRepo
}

func NewClsPointsRuleService() ClsPointsRuleService {
	return ClsPointsRuleService{
		NewSuperService(
			&ClsPointsRuleHooks{
				NewServiceHooks[int, po.ClsPointsRule, vo.ClsPointsRuleSaveVO, vo.ClsPointsRuleUpdateVO, vo.ClsPointsRuleQueryVO, vo.ClsPointsRuleResultVO](),
			},
		),
		repo.NewClsPointsRuleRepo(),
	}
}

// BatchCreate 批量创建积分规则，支持多个商品和区域的规则创建
func (s ClsPointsRuleService) BatchCreate(session *xorm.Session, saveVO vo.ClsPointsRuleBatchSaveVO) ([]po.ClsPointsRule, error) {
	startTime, endTime, err := s.validateTimeRange(saveVO)
	if err != nil {
		return nil, err
	}

	if err := s.checkExistingRules(session, saveVO); err != nil {
		return nil, err
	}

	entities := s.createEntities(saveVO, startTime, endTime)
	var result []po.ClsPointsRule
	err = s.tm.NotSupported(session, func(session *xorm.Session) error {
		var err error
		result, err = s.repo.BatchCreate(session, entities)
		return err
	})
	if err != nil {
		return nil, err
	}

	return result, nil
}

// validateTimeRange 验证并处理时间范围，如果未指定则使用默认的时间范围
// 默认开始时间为 0001-01-01 00:00:00，结束时间为 9999-12-31 23:59:59
func (s ClsPointsRuleService) validateTimeRange(saveVO vo.ClsPointsRuleBatchSaveVO) (time.Time, time.Time, error) {
	// 解析时间字符串
	startTime, err := utils.ParseTime(saveVO.StartTime)
	if err != nil {
		return time.Time{}, time.Time{}, err
	}
	endTime, err := utils.ParseTime(saveVO.EndTime)
	if err != nil {
		return time.Time{}, time.Time{}, err
	}
	return startTime, endTime, nil
}

// checkExistingRules 检查是否存在重复的积分规则
// 通过比对商品编码、区域和时间范围来判断是否存在重复规则
// 如果存在重复规则，返回详细的错误信息
func (s ClsPointsRuleService) checkExistingRules(session *xorm.Session, saveVO vo.ClsPointsRuleBatchSaveVO) error {
	// 查询指定时间范围内的积分规则
	var rules []po.ClsPointsRule
	var err error
	err = s.tm.NotSupported(session, func(session *xorm.Session) error {
		rules, err = s.repo.List(session, vo.ClsPointsRuleQueryVO{
			StartTime: saveVO.StartTime,
			EndTime:   saveVO.EndTime,
			Type:      saveVO.Type,
		})
		return err
	})
	if err != nil {
		return err
	}

	// 检查商品和区域是否重复
	existsRules := make([]po.ClsPointsRule, 0)
	for _, item := range saveVO.GoodsCode {
		for _, rule := range rules {
			if rule.GoodsCode == item {
				for _, ruleItem := range saveVO.RuleItem {
					if ruleItem.Region == rule.Region {
						existsRules = append(existsRules, rule)
					}
				}
			}
		}
	}

	// 如果存在重复规则，返回详细错误信息
	if len(existsRules) > 0 {
		var errMsg = ""
		for _, rule := range existsRules {
			errMsg += "商品【" + rule.GoodsCode + "】在【" + rule.Region + "】已经存在时间：【" + rule.StartTime.Format("2006-01-02 15:04:05") + "】到【" + rule.EndTime.Format("2006-01-02 15:04:05") + "】的积分规则\n"
		}
		return errors.New(errMsg)
	}
	return nil
}

// createEntities 根据保存参数创建积分规则实体列表
// 支持无区域规则（points=0）和有区域规则两种场景
// 返回待保存的积分规则实体列表
func (s ClsPointsRuleService) createEntities(saveVO vo.ClsPointsRuleBatchSaveVO, startTime, endTime time.Time) []po.ClsPointsRule {
	entities := make([]po.ClsPointsRule, 0)
	// 遍历商品编码
	for _, item := range saveVO.GoodsCode {
		// 处理无区域规则的情况
		if len(saveVO.RuleItem) == 0 {
			entities = append(entities, po.ClsPointsRule{
				GoodsCode: item,
				Type:      saveVO.Type,
				Region:    "",
				Points:    0,
			})
			continue
		}

		// 处理有区域规则的情况
		for _, rule := range saveVO.RuleItem {
			entities = append(entities, po.ClsPointsRule{
				GoodsCode: item,
				Type:      saveVO.Type,
				Region:    rule.Region,
				Points:    rule.Points,
				Status:    saveVO.Status,
				StartTime: startTime,
				EndTime:   endTime,
			})
		}
	}
	return entities
}

// MultiPage 分页查询积分规则
func (s ClsPointsRuleService) MultiPage(session *xorm.Session, queryVO vo.ClsPointsRuleQueryVO) ([]vo.MultiRuleResultVO, int64, error) {
	// 打印Page的参数vo
	log.Info("ClsPointsRuleService: Page vo=", utils.InterfaceToJSON(queryVO))

	rules := make([]vo.MultiRuleResultVO, 0)
	var total int64
	err := s.tm.NotSupported(session, func(session *xorm.Session) error {
		var err error
		rules, total, err = s.repo.Page(session, queryVO)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, 0, err
	}

	return rules, total, nil
}

// checkTimeConflict 检查时间范围是否存在冲突
func (s ClsPointsRuleService) checkTimeConflict(session *xorm.Session, timeItem vo.RuleTimeItem, updateVO vo.ClsPointsRuleUpdateVO) error {
	// 检查时间范围冲突
	var existingRules []po.ClsPointsRule
	var err error
	err = s.tm.NotSupported(session, func(session *xorm.Session) error {
		existingRules, err = s.repo.List(session, vo.ClsPointsRuleQueryVO{
			GoodsCode: updateVO.GoodsCode,
			StartTime: timeItem.StartTime,
			EndTime:   timeItem.EndTime,
			Type:      updateVO.Type,
			Status:    []int{1},
		})
		return err
	})
	if err != nil {
		return err
	}

	var regions []string
	updateIds := make(map[int]bool)
	for _, rule := range timeItem.RuleItem {
		regions = append(regions, rule.Region)
		updateIds[rule.Id] = true
	}

	// 检查是否存在冲突的规则
	var conflictRules []po.ClsPointsRule
	for _, rule := range existingRules {
		if _, ok := updateIds[rule.Id]; !ok && slices.Contains(regions, rule.Region) {
			conflictRules = append(conflictRules, rule)
		}
	}

	// 如果存在冲突规则，返回错误
	if len(conflictRules) > 0 {
		var errMsg string
		for _, rule := range conflictRules {
			errMsg += "商品【" + rule.GoodsCode + "】在【" + rule.Region + "】已经存在时间：【" +
				rule.StartTime.Format("2006-01-02 15:04:05") + "】到【" +
				rule.EndTime.Format("2006-01-02 15:04:05") + "】的积分规则\n"
		}
		return errors.New(errMsg)
	}

	return nil
}

// BatchUpdate 批量更新积分规则
func (s ClsPointsRuleService) BatchUpdate(session *xorm.Session, updateVO vo.ClsPointsRuleUpdateVO) ([]po.ClsPointsRule, error) {
	// 转换为实体的数组
	newEntities := make([]po.ClsPointsRule, 0)
	for _, timeItem := range updateVO.RuleTimeItem {
		// 解析时间
		startTime, err := utils.ParseTime(timeItem.StartTime)
		if err != nil {
			return nil, errors.New("开始时间格式错误: " + err.Error())
		}
		endTime, err := utils.ParseTime(timeItem.EndTime)
		if err != nil {
			return nil, errors.New("结束时间格式错误: " + err.Error())
		}

		// 检查时间范围
		if startTime.After(endTime) {
			return nil, errors.New("开始时间不能大于结束时间")
		}

		// 检查时间范围冲突
		if err := s.checkTimeConflict(session, timeItem, updateVO); err != nil {
			return nil, err
		}

		// 为每个规则项创建更新实体
		for _, ruleItem := range timeItem.RuleItem {
			entity := po.ClsPointsRule{
				GoodsCode: updateVO.GoodsCode,
				StartTime: startTime,
				EndTime:   endTime,
				Region:    ruleItem.Region,
				Points:    ruleItem.Points,
				Status:    timeItem.Status,
				Type:      updateVO.Type,
			}
			newEntities = append(newEntities, entity)
		}
	}

	ruleLogSrv := NewClsPointsRuleLogService()
	ruleLogSrv.BatchCreate(session, updateVO)

	// 操作数据库
	var result []po.ClsPointsRule
	err := s.tm.Required(session, func(tx *xorm.Session) error {
		// 删除旧规则
		err := s.Delete(tx, vo.ClsPointsRuleQueryVO{
			GoodsCode: updateVO.GoodsCode,
			Type:      updateVO.Type,
			Status:    []int{1, 2},
		})
		if err != nil {
			return err
		}

		// 创建新规则
		if len(newEntities) > 0 {
			entities, err := s.repo.BatchCreate(tx, newEntities)
			if err != nil {
				return err
			}
			result = append(result, entities...)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetByCode 根据商品编码获取积分规则
func (s ClsPointsRuleService) GetByCode(session *xorm.Session, queryVO vo.ClsPointsRuleQueryVO) (vo.ClsPointsRuleDetailVO, error) {
	rules := make([]po.ClsPointsRule, 0)
	var err error

	err = s.tm.NotSupported(session, func(session *xorm.Session) error {
		queryVO.Status = []int{1, 2}
		rules, err = s.repo.List(session, queryVO)
		return err
	})
	if err != nil {
		return vo.ClsPointsRuleDetailVO{}, err
	}

	if len(rules) == 0 {
		return vo.ClsPointsRuleDetailVO{}, nil
	}

	// 使用 map 按时间分组
	timeRuleMap := make(map[string]*vo.RuleTimeItem)
	var firstRule po.ClsPointsRule

	for i, rule := range rules {
		if i == 0 {
			firstRule = rule
		}

		// 生成时间规则的key
		timeKey := rule.StartTime.Format("2006-01-02 15:04:05") + "_" + rule.EndTime.Format("2006-01-02 15:04:05")

		// 处理时间规则
		if timeRule, exists := timeRuleMap[timeKey]; exists {
			// 如果时间规则已存在，添加区域规则
			timeRule.RuleItem = append(timeRule.RuleItem, vo.RegionItem{
				Id:     rule.Id,
				Region: rule.Region,
				Points: rule.Points,
			})
		} else {
			// 如果时间规则不存在，创建新的
			timeRuleMap[timeKey] = &vo.RuleTimeItem{
				StartTime: rule.StartTime.Format("2006-01-02 15:04:05"),
				EndTime:   rule.EndTime.Format("2006-01-02 15:04:05"),
				Status:    rule.Status,
				RuleItem: []vo.RegionItem{{
					Id:     rule.Id,
					Region: rule.Region,
					Points: rule.Points,
				}},
			}
		}
	}

	// 构建返回结果
	detail := vo.ClsPointsRuleDetailVO{
		GoodsCode:    firstRule.GoodsCode,
		Type:         firstRule.Type,
		RuleTimeItem: make([]vo.RuleTimeItem, 0, len(timeRuleMap)),
	}

	// 将时间规则添加到结果中
	for _, timeRule := range timeRuleMap {
		detail.RuleTimeItem = append(detail.RuleTimeItem, *timeRule)
	}

	return detail, nil
}

type ClsPointsRuleHooks struct {
	ServiceHooks[int, po.ClsPointsRule, vo.ClsPointsRuleSaveVO, vo.ClsPointsRuleUpdateVO, vo.ClsPointsRuleQueryVO, vo.ClsPointsRuleResultVO]
}
