package common

import (
	"context"
	"eShop/infra/cache"
	"eShop/infra/config"
	"eShop/infra/log"
	"eShop/infra/utils"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"errors"
	"fmt"
	"net/http"
	"os"
	"sync"
	"time"

	"github.com/coze-dev/coze-go"
)

// 定义事件类型
const (
	// EventTypeChatCreated      string = "event:conversation.chat.created"      // 对话创建
	// EventTypeChatInprogress   string = "event:conversation.chat.in_progress"  // 对话chat中
	EventTypeMessageDelta     string = "conversation.message.delta"     // 消息增量
	EventTypeMessageCompleted string = "conversation.message.completed" // 消息完成
	EventTypeChatCompleted    string = "conversation.chat.completed"    // 对话完成
	EventTypeChatFailed       string = "conversation.chat.failed"       // 对话完成
	EventTypeEnd              string = "done"

	GuizuPetBloodCozeBotId = "guizu_pet_blood_coze_bot_id" //宠物贵族血统鉴定师 - coze 智能体id
	GuizuPetImageCozeBotId = "guizu_pet_image_coze_bot_id" //宠物图片生成师 - coze 智能体id

	XiaowenAiCozeType = "xiaowen_ai" // coze账号类型：xiaowen_ai 小闻养宠助手AI
	GuizuPetCozeType  = "guizu_pet"  // coze账号类型：guizu_pet 贵族裂变活动

)

type EventData struct {
	Id             string `json:"id"`
	ConversationId string `json:"conversation_id"`
	BotId          string `json:"bot_id"`
	Role           string `json:"role"`
	Type           string `json:"type"`
	Content        string `json:"content"`
	ContentType    string `json:"content_type"`
	ChatId         string `json:"chat_id"`
}

// 定义包级别的客户端池
var httpClientPool = &sync.Pool{
	New: func() interface{} {
		return &http.Client{
			Timeout: time.Minute * 15,
			Transport: &http.Transport{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 100,
				IdleConnTimeout:     90 * time.Second,
				DisableKeepAlives:   false,
			},
		}
	},
}

// coze 相关配置
type CozeConfig struct {
	BotId       string `json:"bot_id"`        //智能体id
	CozeToken   string `json:"coze_token"`    //coze token字段
	CozeApiBase string `json:"coze_api_base"` //coze平台openai的api地址
}
type CozeAuthJwt struct {
	OauthJwtClientID           string `json:"oauth_jwt_client_id"`             // coze oauth应用的client_id
	OauthJwtPublicKeyID        string `json:"oauth_jwt_public_key_id"`         // coze oauth应用的public_key_id
	OauthJwtPrivateKeyFilePath string `json:"oauth_jwt_private_key_file_path"` // coze oauth应用的private_key文件路径
}
type CozeService struct {
}

// NewCozeService 创建coze服务
func NewCozeService() *CozeService {
	return &CozeService{}
}

func (s *CozeService) GetBotId(botType string) (botId string) {
	botId = config.Get(botType)
	if botId == "" {
		switch botType {
		case GuizuPetBloodCozeBotId:
			botId = "7530216755986251791" // 用于贵族裂变活动 - coze宠物贵族血统鉴定师（这个是小闻养宠助手AI里的智能体 用来测试的）
		case GuizuPetImageCozeBotId:
			botId = "7526851960020992046" // 用于贵族裂变活动 - coze宠物图片生成师（这个是小闻养宠助手AI里的智能体 用来测试的）
		default:
			botId = ""
		}

	}

	return
}

// coze 个人访问令牌
func (s *CozeService) GetPersonalToken(cozeType string) (token string) {
	personalTokenKey := fmt.Sprintf("%s_personal_coze_token", cozeType)
	if cozeType == XiaowenAiCozeType {
		personalTokenKey = "petai_coze_token"
	}
	token = config.Get(personalTokenKey) // coze 个人访问令牌
	fmt.Printf("=====coze token为%s=====", token)
	return
}

func (s *CozeService) GetCozeTokenType(cozeType string) (tokenType string) {
	switch cozeType {
	case XiaowenAiCozeType:
		tokenType = config.Get("petai_coze_token_type") // 访问coze 接口使用的token类型：oauth_jwt 或者 personal_token
	case GuizuPetCozeType:
		tokenType = config.Get("guizu_pet_coze_token_type") // 访问coze 接口使用的token类型：oauth_jwt 或者 personal_token
	}
	if len(tokenType) == 0 {
		tokenType = "oauth_jwt"
	}
	return
}

func (s *CozeService) GetCozeAuthJwtConfig(cozeType string) (c CozeAuthJwt) {
	switch cozeType {
	case XiaowenAiCozeType:
		c.OauthJwtClientID = config.Get("petai_coze_jwt_oauth_client_id")
		c.OauthJwtPublicKeyID = config.Get("petai_coze_jwt_oauth_public_key_id")
		c.OauthJwtPrivateKeyFilePath = "./coze_oauth_private_key.pem"
	case GuizuPetCozeType:
		c.OauthJwtClientID = config.Get("guizu_pet_coze_jwt_oauth_client_id")
		c.OauthJwtPublicKeyID = config.Get("guizu_pet_coze_jwt_oauth_public_key_id")
		c.OauthJwtPrivateKeyFilePath = "./guizu_pet_coze_oauth_private_key.pem"
	}
	return
}

func (s *CozeService) InitCozeConfig(userInfoId string, cozeType string, botType string) (c CozeConfig) {
	logPrefix := fmt.Sprintf("获取每个用户的coze-token-%s", userInfoId)
	log.Info(logPrefix, "开始获取用户的coze-token")
	c.CozeApiBase = coze.CnBaseURL
	tokenType := s.GetCozeTokenType(cozeType)
	c.BotId = s.GetBotId(botType) //智能体id
	log.Infof("%s|tokenType为%s|BotId为%s", logPrefix, tokenType, c.BotId)
	//  访问coze 接口使用的token类型：oauth_jwt 或者 personal_token
	if tokenType == "personal_token" {
		// 个人访问令牌
		c.CozeToken = s.GetPersonalToken(cozeType)
	} else {
		// jwt oauth 类型
		// 先从redis里获取 jwt oauth的token
		cacheK := fmt.Sprintf("%s_coze_token_%s", cozeType, userInfoId)
		if cozeType == XiaowenAiCozeType {
			cacheK = fmt.Sprintf("petai_coze_token_%s", userInfoId)
		}
		var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])

		authJwtToken := mCache.Get(string(cache_source.EShop), cacheK)
		log.Infof("%s|cacheKey为%s|cache的值为%s", logPrefix, cacheK, utils.JsonEncode(authJwtToken))
		if len(authJwtToken) > 0 && len(authJwtToken[0].(string)) > 0 {
			c.CozeToken = authJwtToken[0].(string)
		} else {
			var err error
			c.CozeToken, err = s.GetCozeTokenByJwt(userInfoId, cozeType)
			if err != nil {
				log.Errorf("%s|生成新的coze-token失败,错误为%s", logPrefix, err.Error())
			}

		}

	}
	return

}

func (s *CozeService) NewCozeAPI(userInfoId string, cozeType string, botType string) (cozeCli coze.CozeAPI, c CozeConfig) {
	c = s.InitCozeConfig(userInfoId, cozeType, botType)

	authCli := coze.NewTokenAuth(c.CozeToken)
	// 从池中获取客户端
	client := httpClientPool.Get().(*http.Client)
	defer httpClientPool.Put(client)
	cozeCli = coze.NewCozeAPI(authCli, coze.WithBaseURL(c.CozeApiBase), coze.WithHttpClient(client))
	return
}

// coze 通过jwt auth获取token
func (s *CozeService) GetCozeTokenByJwt(userInfoId string, cozeType string) (token string, err error) {
	c := s.GetCozeAuthJwtConfig(cozeType)
	if len(c.OauthJwtClientID) == 0 || len(c.OauthJwtPublicKeyID) == 0 || len(c.OauthJwtPrivateKeyFilePath) == 0 {
		err = errors.New("coze jwt oauth 配置信息不全")
		return
	}
	logPrefix := fmt.Sprintf("====通过jwt auth获取coze token====入参:%s", utils.JsonEncode(userInfoId))
	log.Info(logPrefix)
	// Read private key from file
	privateKeyBytes, err := os.ReadFile(c.OauthJwtPrivateKeyFilePath)
	if err != nil {
		log.Error(logPrefix, "读取coze oauth应用的private_key文件失败,错误信息为", err.Error())
		fmt.Printf("%s Error reading private key file: %v\n", logPrefix, err)
		return
	}
	jwtOauthPrivateKey := string(privateKeyBytes)

	// 从池中获取客户端
	client := httpClientPool.Get().(*http.Client)
	defer httpClientPool.Put(client)
	ttl := 86400
	// The jwt oauth type requires using private to be able to issue a jwt token, and through the jwt token,
	// apply for an access_token from the coze service.The sdk encapsulates this procedure,
	// and only needs to use get_access_token to obtain the access_token under the jwt oauth process.
	// Generate the authorization token The default ttl is 900s, and developers can customize the expiration time,
	// which can be set up to 24 hours at most.
	oauth, err := coze.NewJWTOAuthClient(coze.NewJWTOAuthClientParam{
		ClientID: c.OauthJwtClientID, PublicKey: c.OauthJwtPublicKeyID, PrivateKeyPEM: jwtOauthPrivateKey, TTL: &ttl,
	}, coze.WithAuthBaseURL(coze.CnBaseURL), coze.WithAuthHttpClient(client))
	if err != nil {
		log.Error(logPrefix, "初始化jwt oauth client失败,错误信息为", err.Error())
		fmt.Printf("Error creating JWT OAuth client: %v\n", err)
		return
	}
	ctx := context.Background()

	resp, err := oauth.GetAccessToken(ctx, nil)
	log.Info(logPrefix, "获取access token返回结果为", utils.JsonEncode(resp))
	if err != nil {
		log.Error(logPrefix, "获取access token失败,错误信息为", err.Error())
		fmt.Printf("Error getting access token: %v\n", err)
		return
	}
	// 保存到redis
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	cacheK := fmt.Sprintf("%s_coze_token_%s", cozeType, userInfoId)
	if cozeType == XiaowenAiCozeType {
		cacheK = fmt.Sprintf("petai_coze_token_%s", userInfoId)
	}
	mCache.Save(string(cache_source.EShop), cacheK, resp.AccessToken, time.Hour*23)
	return resp.AccessToken, nil

}
