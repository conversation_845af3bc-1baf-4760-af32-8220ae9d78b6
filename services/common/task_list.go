package common

import (
	po "eShop/domain/distribution-po"
	"eShop/infra/enum"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	mq "eShop/infra/mq"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
)

type TaskListService struct {
	BaseService
}

// 创建任务
func (h TaskListService) CreatTask(r *http.Request, req vo.TaskList) error {
	h.Begin()
	defer h.Close()

	session := h.Session
	task := po.TaskList{}
	copier.Copy(&task, req)

	task.TaskStatus = enum.TaskStatusNotStart
	// 宠物saas 的创建任务
	if _, ok := enum.EshopTaskMap[int64(req.TaskContent)]; ok {
		jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
		if err != nil {
			log.Error("获取登录信息失败: err", err.Error())
			return err
		}
		jwtjson, _ := json.Marshal(jwtInfo)
		task.CreateId = jwtInfo.UserId
		task.CreateName = jwtInfo.UserName
		task.RequestHeader = string(jwtjson)
		if req.TaskContent == enum.TaskContentChainProductImport { //连锁商品导入
			// 如果是代运营账号导入连锁商品， 如果source_chain_id不为空，则使用source_chain_id，否则使用chain_id
			if cast.ToInt64(jwtInfo.SourceChainId) > 0 {
				task.ContextData = jwtInfo.SourceChainId
			} else {
				task.ContextData = jwtInfo.ChainId
			}
		} else if req.TaskContent >= enum.TaskContentProductBatchUp && req.TaskContent <= enum.TaskContentProductBatchPrice { //门店商品操作导入
			task.ContextData = jwtInfo.TenantId
		} else if req.TaskContent == enum.TaskContentInventoryLocationImport {
			task.ContextData = fmt.Sprintf("%s,%s", jwtInfo.ChainId, jwtInfo.TenantId)
		}
	} else {
		jwtInfo, err := jwtauth.GetJwtInfo(r)
		if err != nil {
			log.Error("获取登录信息失败: err", err.Error())
			return err
		}
		task.CreateName = jwtInfo.Name
		task.CreateId = jwtInfo.UserNo
		task.CreateMobile = jwtInfo.Mobile
	}

	task.ExtendedData = enum.TaskContentMap[task.TaskContent]
	ipAddress := r.Header.Get("X-Forwarded-For")
	if ipAddress == "" {
		ipAddress = r.RemoteAddr
	}
	ipParts := strings.Split(ipAddress, ":")
	ip := ipParts[0]

	task.CreateIp = ip

	_, err := session.Insert(&task)
	if err != nil {
		return err
	}
	//请求参数转为字符串
	bt, _ := json.Marshal(task)
	//插入成功之后丢到MQ去消费
	QueueName := enum.TaskQueue
	if req.TaskContent == enum.TaskContentBlkyCodeImport {
		QueueName = enum.TaskBlkyBoxxCodeQueue
	}
	if ok := mq.PublishRabbitMQ(QueueName, string(bt), "eshop"); !ok {
		log.Error("mq推送失败，", string(bt))
		// 失败后更新任务 task_status = 4
		session.Exec("update task_list set task_status=4,err_mes=?", "mq推送失败")
		return errors.New("创建队列任务失败")
	}

	return nil
}

func (h TaskListService) GetTaskList(in vo.GetTaskListRequest) ([]vo.TaskList, int, error) {
	h.Begin()
	defer h.Close()

	//Engine := h.Engine

	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	ssql := "select * from task_list "
	countsql := "select count(1) from task_list "
	whereSql := " where 1 = 1 "
	if in.CreateId != "" && in.Promoter == 0 {
		//whereSql += fmt.Sprintf(" and create_id = \"%s\" ", in.CreateId)
		whereSql += fmt.Sprintf(" and create_id =  '" + in.CreateId + "'")
	}

	if in.Promoter == 2 {
		whereSql += fmt.Sprintf("and create_id != '" + in.CreateId + "'")
	}

	if in.TaskStatus != 0 {
		whereSql += fmt.Sprintf(" and task_status = %d ", in.TaskStatus)
	}
	if in.Id != 0 {
		whereSql += fmt.Sprintf(" and id = %d ", in.Id)
	}
	if in.ContentStr != "" {
		whereSql += fmt.Sprintf(" and task_content  in (%s)", in.ContentStr)
	}

	if in.Sort != "" {
		if in.Sort == "createTimeDesc" {
			whereSql += " order by create_time desc "
		}
		if in.Sort == "createTimeAsc" {
			whereSql += " order by create_time asc "
		}
		if in.Sort == "idDesc" {
			whereSql += " order by id desc "
		}
		if in.Sort == "idAsc" {
			whereSql += " order by id asc"
		}
	}
	countsql += whereSql
	//获取数据列表总条数
	var out []vo.TaskList
	//分页
	if in.PageIndex > 0 && in.PageSize > 0 {
		whereSql += fmt.Sprintf(" LIMIT %d,%d ", (in.PageIndex-1)*in.PageSize, in.PageSize)
	}
	sqls := ssql + whereSql

	Count, err := h.Engine.SQL(countsql).Count(&out)
	err = h.Engine.SQL(sqls).Find(&out)
	if err != nil {
		log.Error(err)
		return nil, 0, err
	}

	return out, int(Count), nil
}
