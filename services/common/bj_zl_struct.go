package common

type ZLBaseResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

// 历史病例列表
type AppointmentMedRecordListResp struct {
	Data []*PetCase `json:"data"`
	//总条数
	PageCount int64 `json:"page_count"`
}

type ZLPetCaseList struct {
	ZLBaseResponse
	Data struct {
		Total    int       `json:"total"`
		Page     int       `json:"page"`
		PageSize int       `json:"page_size"`
		List     []PetCase `json:"list"`
	} `json:"data"`
}
type ZLErrorResponse struct {
	Name     string `json:"name"`
	Message  string `json:"message"`
	Code     int    `json:"code"`
	Status   int    `json:"status"`
	Type     string `json:"type"`
	Previous struct {
		Name    string        `json:"name"`
		Message string        `json:"message"`
		Code    int           `json:"code"`
		Type    string        `json:"type"`
		Msg     string        `json:"msg"`
		Data    []interface{} `json:"data"`
	} `json:"previous"`
	Msg  string        `json:"msg"`
	Data []interface{} `json:"data"`
}

type PetCase struct {
	Orgid          int    `json:"orgid"`
	RegId          int    `json:"reg_id"`
	MedType        int    `json:"med_type"`
	PhysicianId    int    `json:"physician_id"`
	PhysicianName  string `json:"physician_name"`
	CusLogicid     int    `json:"cus_logicid"`
	CusName        string `json:"cus_name"`
	PetLogicid     int    `json:"pet_logicid"`
	PetName        string `json:"pet_name"`
	CreateTime     string `json:"create_time"`
	EndTime        string `json:"end_time"`
	RecordType     int    `json:"record_type"`
	HospitalName   string `json:"hospital_name"`
	RecordTypeText string `json:"record_type_text"`
	MainSymptom    string `json:"main_symptom"`
}

type ZLPetCaseDetail struct {
	ZLBaseResponse
	Data PetCaseDetail `json:"data"`
}
type PetCaseDetail struct {
	Orgid               int         `json:"orgid"`
	MedType             string      `json:"med_type"`
	RegId               int         `json:"reg_id"`
	PhysicianId         int         `json:"physician_id"`
	PhysicianName       string      `json:"physician_name"`
	MainSymptom         string      `json:"main_symptom"`
	CusLogicid          int         `json:"cus_logicid"`
	CusName             string      `json:"cus_name"`
	PetName             string      `json:"pet_name"`
	PetLogicid          int         `json:"pet_logicid"`
	CreateTime          string      `json:"create_time"`
	EndTime             string      `json:"end_time"`
	ChiefComplaint      interface{} `json:"chief_complaint"`
	PastHistory         interface{} `json:"past_history"`
	PhysicalDescription interface{} `json:"physical_description"`
	TreatmentOpinion    interface{} `json:"treatment_opinion"`
	DoctorAdvice        string      `json:"doctor_advice"`
	HospitalName        string      `json:"hospital_name"`
	MedLevelText        string      `json:"med_level_text"`
	DiseaseUrgencyText  string      `json:"disease_urgency_text"`
	PhysicalLevelText   string      `json:"physical_level_text"`
	MedResultText       string      `json:"med_result_text"`
	LeaveSymptom        string      `json:"leave_symptom"`
	MedTypeText         string      `json:"med_type_text"`
	PhysicalExamination []struct {
		Weight        string `json:"weight"`
		Temperature   string `json:"temperature"`
		BreathingRate string `json:"breathing_rate"`
		HeartRate     string `json:"heart_rate"`
	} `json:"physical_examination"`
	MedMedias []struct {
		Id     int         `json:"id"`
		PicUrl string      `json:"pic_url"`
		Remark interface{} `json:"remark"`
	} `json:"med_medias"`
	//目前体况
	IllnessDesc string `json:"illness_desc"`
	//住院病例描述
	PhysicalDesc string `json:"physical_desc"`
	// 宠物信息
	PetInfo struct {
		PetName          string  `json:"pet_name"`           // 宠物名称
		PetLogicid       int     `json:"pet_logicid"`        // 宠物id
		PetGenderText    string  `json:"pet_gender_text"`    // 宠物性别
		PetKindofText    string  `json:"pet_kindof_text"`    // 宠物品种
		PetVarietyText   string  `json:"pet_variety_text"`   // 宠物种类
		ColorTxt         string  `json:"color_txt"`          // 宠物颜色
		PetAge           string  `json:"pet_age"`            // 宠物年龄
		PetWeight        float64 `json:"pet_weight"`         // 宠物体重
		PetNeuteringText string  `json:"pet_neutering_text"` // 绝育状态
		PetStatusText    string  `json:"pet_status_text"`    // 宠物状态
		IsVaccinatedTxt  string  `json:"is_vaccinated_txt"`  // 接种状态
		IsDewormingTxt   string  `json:"is_deworming_txt"`   // 驱虫状态
	} `json:"pet_info"`
	//住院医嘱
	InpatientDoctorAdvice string `json:"inpatient_doctor_advice"`
}

// 报告
type ZLPetCaseReportList struct {
	ZLBaseResponse
	Data []PetCaseReport `json:"data"`
}

type PetCaseReport struct {
	Id                  int    `json:"id"`
	PrescriptionType    int    `json:"prescription_type"`
	PhysicianId         int    `json:"physician_id"`
	PhysicianName       string `json:"physician_name"`
	CreateTime          string `json:"create_time"`
	PrescriptionDetails []struct {
		Id              int     `json:"id"`
		Group           int     `json:"group"`
		DrugsNickName   string  `json:"drugs_nick_name"`
		FeedMethod      int     `json:"feed_method"`
		FeedMethodCn    string  `json:"feed_method_cn"`
		CountUnit       string  `json:"count_unit"`
		CountPerFeed    float32 `json:"count_per_feed"`
		FeedTotal       int     `json:"feed_total"`
		FeedTimesPerDay string  `json:"feed_times_per_day"`
		FeedDays        int     `json:"feed_days"`
		DrugId          int     `json:"drug_id"`
		PrescriptionId  int     `json:"prescription_id"`
		ProductCode     string  `json:"product_code"`
		HasDone         bool    `json:"has_done"`
		HasLisCancel    bool    `json:"has_lis_cancel"`
		LisCancelReason string  `json:"lis_cancel_reason"`
	} `json:"prescription_details"`
}

type ZLReportDetail struct {
	Code int            `json:"code"`
	Msg  string         `json:"msg"`
	Data []ReportDetail `json:"data"`
}

type ReportDetail struct {
	Id                      int           `json:"id"`
	ReportName              string        `json:"report_name"`
	SendingTime             string        `json:"sending_time"`
	CreateTime              string        `json:"create_time"`
	PhysicianId             int           `json:"physician_id"`
	PhysicianName           string        `json:"physician_name"`
	PrescriptionType        int           `json:"prescription_type"`
	ReportingTime           string        `json:"reporting_time"`
	ResultRemark            string        `json:"result_remark"`
	DeviceName              string        `json:"device_name"`
	DeviceType              string        `json:"device_type"`
	KindAge                 string        `json:"kind_age"`
	Conclusion              string        `json:"conclusion"`
	DeviceSource            string        `json:"device_source"`
	ExecPhysicianName       string        `json:"exec_physician_name"`
	AssistantPhysicianName1 string        `json:"assistant_physician_name_1"`
	Images                  []interface{} `json:"images"`
	Result                  struct {
		NumberDetail []struct {
			ItemId         int    `json:"item_id"`
			ItemName       string `json:"item_name"`
			Remark         string `json:"remark"`
			DeviceId       int    `json:"device_id"`
			DeviceSource   string `json:"device_source"`
			ResultValue    string `json:"result_value"`
			ResultRange    string `json:"result_range"`
			Unit           string `json:"unit"`
			Max            string `json:"max"`
			Min            string `json:"min"`
			ResultSymbolId int    `json:"result_symbol_id"`
			ResultSymbol   string `json:"result_symbol"`
		} `json:"number_detail"`
		PaperDetail []interface{} `json:"paper_detail"`
	} `json:"result"`
}

// 字典列表
type DicRes struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		DosingUnit   []DicData `json:"dosing_unit"`
		UseFrequency []DicData `json:"use_frequency"`
		UsingMethod  []DicData `json:"using_method"`
	} `json:"data"`
}
type DicData struct {
	Id          int    `json:"id"`
	Name        string `json:"name"`
	EnglishName string `json:"english_name"`
}

// 病种库
type DiseaseRes struct {
	Code int       `json:"code"`
	Msg  string    `json:"msg"`
	Data []Disease `json:"data"`
}
type Disease struct {
	Id              int    `json:"id"`
	Name            string `json:"name"`
	Code            string `json:"code"`
	NameEn          string `json:"name_en"`
	NameFullPinyin  string `json:"name_full_pinyin"`
	NameFirstPinyin string `json:"name_first_pinyin"`
	Level           int    `json:"level"`
	Pid             int    `json:"pid"`
	DifficultyLevel int    `json:"difficulty_level"`
}
