package services

import (
	"context"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	marketing_vo "eShop/view-model/marketing-vo"
	order_vo "eShop/view-model/order-vo"
	vo "eShop/view-model/order-vo"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"

	"github.com/stretchr/testify/assert"
	"xorm.io/xorm"
)

func TestShopOrderService_Commit(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
		Request     *http.Request
		JwtInfo     *jwtauth.XCShopPayload
	}
	type args struct {
		ctx context.Context
		in  order_vo.CommitRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "支付测试"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// s := ShopOrderService{
			// 	BaseService: tt.fields.BaseService,
			// 	Request:     tt.fields.Request,
			// 	JwtInfo:     tt.fields.JwtInfo,
			// }

			NotifyUrl := "https://awen.uat.rvet.cn/order-api/order/offlinenotify"
			// 构建扫码支付请求参数
			payReq := &utils.Pay{
				OrderNo:     "99641282981884941",    // 订单号
				PayAmount:   1,                      // 实际支付金额
				PayTotal:    1,                      // 订单总金额
				OrderName:   "门店收银-123456",      // 订单名称
				ProductDesc: "门店收银付款",         // 订单描述
				AppId:       5,                      // SAAS应用
				TransType:   20,                     // B扫C（标准）
				BarCode:     "13155995365619015522", // 付款码
				MerchantId:  "1705261836",           // 商户号
				ClientIP:    "127.0.0.1",            // 客户端IP
				ValidTime:   300,                    // 订单有效期(分钟)
				Timestamp:   time.Now().UnixMilli(), // 时间戳
				NotifyURL:   NotifyUrl,              // 回调地址
			}
			jsonBytes, err := json.Marshal(payReq)

			// 生成签名
			sign, err := utils.MakeStandardPaySign(jsonBytes, 5)
			if err != nil {

			}
			payReq.Sign = sign

			// 发起扫码支付请求
			payUrl := "https://awen.uat.rvet.cn/pay/pay-api/pay" // 支付接口地址
			respBody, err := utils.HttpPost(payUrl, []byte(utils.JsonEncode(payReq)), "")
			if err != nil {

			}

			// 解析支付响应
			var payResp order_vo.StandardPayResponse
			if err := json.Unmarshal(respBody, &payResp); err != nil {

			}
			return
		})
	}
}

func TestGetDeliveryMoney(t *testing.T) {
	type args struct {
		in          order_vo.CommitRequest
		TotalWeight float64
	}
	tests := []struct {
		name    string
		args    args
		want    int
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "获取配送费"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			in := vo.CalcRequest{}
			in.Data.TenantID = "CX0011"
			in.Data.DestinationX = 114.024157
			in.Data.DestinationY = 22.5289
			got, err := GetDeliveryMoney(in, 5)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDeliveryMoney() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetDeliveryMoney() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestScannerPay(t *testing.T) {
	type args struct {
		orderSn string
		session *xorm.Session
		in      vo.CommitRequest
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ScannerPay(tt.args.orderSn, tt.args.session, tt.args.in); (err != nil) != tt.wantErr {
				t.Errorf("ScannerPay() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCouponService_CheckCouponAvailability(t *testing.T) {
	// 初始化必要的依赖
	log.Init()

	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		param *marketing_vo.CheckCouponAvailabilityReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *marketing_vo.CheckCouponAvailabilityRes
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "成功校验优惠券可用性",
			args: args{
				param: &marketing_vo.CheckCouponAvailabilityReq{
					//OrderType: 3,
					//ChannelId: 1,
					StoreId:    "530236368643759103",
					CustomerId: "540571614736509970",
					Details: []order_vo.OrderDetail{
						{
							ProductType:       1,
							BuyType:           1,
							BuyTypeList:       []order_vo.BuyTypeInfo{},
							DiscountPrice:     300,
							IsEdit:            false,
							ItemID:            "100072002",
							ProductCategoryID: "20008",
							ProductName:       "阿德所发生的发生发的阿斯顿发斯蒂芬阿斯蒂芬萨芬",
							ProductPic:        "https://rpets-saas-cos-pre.rvet.cn/530219708465609002/product/2024/12/12/27f84131f78d4ae6b2d36525e3c4b66e.png",
							RetailPrice:       300,
							SkuID:             100072002,
							TotalBuyCount:     3,
						},
						{
							ProductType:       1,
							BuyType:           1,
							BuyTypeList:       []order_vo.BuyTypeInfo{},
							DiscountPrice:     1000,
							IsEdit:            false,
							ItemID:            "100072001",
							ProductCategoryID: "20008",
							ProductName:       "阿德所发生的发生发的阿斯顿发斯蒂芬阿斯蒂芬萨芬",
							ProductPic:        "https://rpets-saas-cos-pre.rvet.cn/530219708465609002/product/2024/12/12/27f84131f78d4ae6b2d36525e3c4b66e.png",
							RetailPrice:       1000,
							SkuID:             100072001,
							TotalBuyCount:     4,
						},
					},
				},
			},
			want: &marketing_vo.CheckCouponAvailabilityRes{
				Code:    0,
				Message: "",
				Data: marketing_vo.CouponAvailabilityData{
					AvailableCoupons:   []marketing_vo.CouponAvailabilityInfo{},
					UnavailableCoupons: []marketing_vo.CouponAvailabilityInfo{},
				},
			},
			wantErr: assert.NoError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ShopOrderService{
				BaseService: tt.fields.BaseService,
			}
			got, err := s.CheckCouponAvailability(tt.args.param)
			if !tt.wantErr(t, err, fmt.Sprintf("CheckCouponAvailability(%v)", tt.args.param)) {
				return
			}
			assert.Equalf(t, tt.want, got, "CheckCouponAvailability(%v)", tt.args.param)
		})
	}
}

func TestShopOrderService_AssignCommission(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
		Request     *http.Request
		JwtInfo     *jwtauth.XCShopPayload
	}
	type args struct {
		orderSn   string
		tenantId  string
		staffName string
		staffId   string
		operatorId string
		operatorName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "手动分配业绩",
			args: args{
				orderSn:   "9964128299026996",
				tenantId:  "576534157590154085",
				staffName: "张三",
				staffId:   "606307459296918539",
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := ShopOrderService{
				BaseService: tt.fields.BaseService,
				Request:     tt.fields.Request,
				JwtInfo:     tt.fields.JwtInfo,
			}
			tt.wantErr(t, s.AssignCommission(tt.args.orderSn, tt.args.staffName, tt.args.staffId, tt.args.operatorId, tt.args.operatorName), fmt.Sprintf("AssignCommission(%v, %v, %v, %v)", tt.args.orderSn, tt.args.tenantId, tt.args.staffName, tt.args.staffId))
		})
	}
}
