package services

import (
	"context"
	distribution_po "eShop/domain/distribution-po"
	"eShop/domain/inventory-po/warehouse"
	marketing_po "eShop/domain/marketing-po"
	omnibus_po "eShop/domain/omnibus-po"
	order_po "eShop/domain/order-po"
	po "eShop/domain/product-po"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	mq "eShop/infra/mq"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/services/common"
	external_service "eShop/services/external-service/services"
	"eShop/services/inventory-service/iobound"
	"eShop/services/marketing-service/services"
	omnibus_service "eShop/services/omnibus-service/services"
	product_service "eShop/services/product-service/services"
	offline_vo "eShop/view-model/external-vo/offline"
	iobound_vo "eShop/view-model/inventory-vo/iobound"
	marketing_vo "eShop/view-model/marketing-vo"
	omnibus_vo "eShop/view-model/omnibus-vo"
	order_vo "eShop/view-model/order-vo"
	product_vo "eShop/view-model/product-vo"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/shopspring/decimal"

	sms "eShop/services/omnibus-service/services"

	"github.com/golang/glog"
	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
	"xorm.io/xorm"
)

type ShopOrderService struct {
	common.BaseService
	Request *http.Request
	JwtInfo *jwtauth.XCShopPayload
}

// NewLocationService 创建库位服务
func NewShopOrderService() *ShopOrderService {
	return &ShopOrderService{}
}

// 门店订单详情
func (s ShopOrderService) Detail(in order_vo.DetailRequest) (out order_vo.DetailResponse, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	// 获取子订单信息
	orderSli, _, _, _, err := new(order_po.OrderMain).GetOrderInfo(session, order_po.OrderInfoRequest{
		ParentOrderSn: in.ParentOrderSn,
		OrderSn:       in.OrderSn,
	})
	if err != nil {
		return out, fmt.Errorf("查询订单详情失败: %v", err)
	}
	if len(orderSli) == 0 {
		return out, fmt.Errorf("订单不存在")
	}

	// 这个是子订单
	order := orderSli[0]
	_, detailMap, err := new(order_po.OrderDetail).GetOrderDetail(session, order_po.OrderDetailRequest{
		OrderSn: order.OrderSn,
	})
	if err != nil {
		return out, fmt.Errorf("查询订单详情失败: %v", err)
	}
	out.OrderSn = order.OrderSn
	out.MemberName = order.MemberName
	out.MemberTel = order.MemberTel
	out.EnMemberTel = order.EnMemberTel
	// todo 待以后补充宠物名称
	out.PetName = ""
	out.CashierName = detailMap[order.OrderSn].CashierName
	out.CreateTime = order.CreateTime.Format("2006-01-02 15:04:05")
	out.PayTime = order.PayTime.Format("2006-01-02 15:04:05")
	out.PayAmount = int(order.PayAmount)
	out.PrivilegeAmount = int(order.Privilege)
	out.TotalAmount = int(order.GoodsTotal)
	out.SellerMemo = detailMap[order.OrderSn].SellerMemo
	out.OrderStatus = order.OrderStatus
	out.OrderStatusChild = order.OrderStatusChild

	// 优惠列表
	_, promotionMap, err := new(order_po.OrderPromotion).GetOrderPromotion(session, order_po.OrderPromotionRequest{
		OrderSn: order.OrderSn,
	})
	if err != nil {
		return out, fmt.Errorf("查询订单优惠列表失败: %v", err)
	}
	if len(promotionMap[order.OrderSn]) > 0 {
		for _, promotion := range promotionMap[order.OrderSn] {
			out.PromotionList = append(out.PromotionList, order_vo.PromotionInfo{
				PromotionID:    promotion.PromotionId,
				PromotionType:  promotion.PromotionType,
				PromotionTitle: promotion.PromotionTitle,
				PromotionFee:   promotion.PromotionFee,
			})
		}
	}

	// 获取商品信息
	_, orderProductMap, err := new(order_po.OrderProduct).GetOrderProduct(session, order_po.OrderProductRequest{
		OrderSn: order.OrderSn,
	})
	if err != nil {
		return out, fmt.Errorf("查询订单商品失败: %v", err)
	}
	// 获取单个商品参与了哪些活动
	_, _, orderPromotionRecordMap2, err := new(order_po.OrderPromotionRecord).GetOrderPromotionRecord(session, order_po.OrderPromotionRecordRequest{
		OrderSn: order.OrderSn,
	})
	if err != nil {
		return out, fmt.Errorf("查询订单商品活动失败: %v", err)
	}

	// 获取订单退款信息
	refundOrderSli, _, refundOrderMap2, err := new(order_po.RefundOrder).GetRefundOrder(session, order_po.RefundOrderRequest{
		OrderSn: order.OrderSn,
	})
	if err != nil {
		return out, fmt.Errorf("查询订单退款信息失败: %v", err)
	}
	refundSnSli := make([]string, 0)
	for _, refundOrder := range refundOrderSli {
		refundSnSli = append(refundSnSli, refundOrder.RefundSn)
	}

	// 获取订单退款信息
	refundOrderProductSli := make([]*order_po.RefundOrderProduct, 0)
	if len(refundSnSli) > 0 {
		refundOrderProductSli, _, _, err = new(order_po.RefundOrderProduct).GetRefundOrderProduct(session, order_po.RefundOrderProductRequest{
			RefundSns: refundSnSli,
		})
		if err != nil {
			return out, fmt.Errorf("查询订单退款信息失败: %v", err)
		}
	}

	refundProductCntMap := make(map[int]int)     // 每行订单商品对应的退货数量
	refundProductPaymentMap := make(map[int]int) // 每行订单商品对应的退货金额
	for _, refundProduct := range refundOrderProductSli {
		refundProductCntMap[refundProduct.OrderProductId] += refundProduct.Tkcount
		refundProductPaymentMap[refundProduct.OrderProductId] += utils.Yuan2Fen(cast.ToFloat64(refundProduct.RefundAmount))
	}
	orderProductPaymentMap := make(map[int]int) // 每行订单商品对应的实收金额
	for _, product := range orderProductMap[order.OrderSn] {
		orderProductPaymentMap[product.Id] = product.PaymentTotal
	}

	for _, product := range orderProductMap[order.OrderSn] {
		//  该行订单商品是否可以退货
		refundStatus := "CANNOT_REFUND"
		// todo 补充单个商品参与的活动列表
		productPromotionList := make([]order_vo.PromotionInfo, 0)
		if len(orderPromotionRecordMap2[cast.ToString(product.Id)]) > 0 {
			for _, promotion := range orderPromotionRecordMap2[cast.ToString(product.Id)] {
				productPromotionList = append(productPromotionList, order_vo.PromotionInfo{
					PromotionID:    promotion.PromotionId,
					PromotionType:  promotion.PromotionType,
					PromotionTitle: promotion.PromotionTitle,
					PromotionFee:   promotion.Promotion,
				})
			}
		}
		// 通过退货数量 来判断该条商品是否可以退货
		if refundProductCntMap[product.Id] < product.Number {
			refundStatus = "CAN_REFUND"
		}

		// 查询卡的有效期及使用范围
		validityText := ""
		discountRange := ""

		// 根据BuyType判断是否为次卡或储值卡相关商品
		if product.BuyType >= 100 && product.BuyType <= 103 {
			// 次卡（102: 购买次卡, 103: 续卡）
			if product.BuyType == 102 || product.BuyType == 103 {
				timeCardInfo, err := new(order_po.StoreCardOrder).GetTimeCardValidity(session, order.OrderSn)
				if err == nil && timeCardInfo != nil {
					// 根据不同有效期类型生成友好的文本描述
					validityText = getValidityDescription(timeCardInfo.Validity, timeCardInfo.EnableTime, timeCardInfo.ExpirationTime)
					discountRange = timeCardInfo.DiscountRange
				} else if err != nil {
					log.Error("查询次卡有效期和使用范围失败:", err)
				}
			}
			// 储值卡（100: 购买储值卡, 101: 续卡）
			if product.BuyType == 100 || product.BuyType == 101 {
				storeCardInfo, err := new(order_po.StoreCardOrder).GetStoreCardValidity(session, order.OrderSn)
				if err == nil && storeCardInfo != nil {
					// 根据不同有效期类型生成友好的文本描述
					validityText = getValidityDescription(storeCardInfo.Validity, storeCardInfo.EnableTime, storeCardInfo.ExpirationTime)
					discountRange = storeCardInfo.DiscountRange
				} else if err != nil {
					log.Error("查询储值卡有效期和使用范围失败:", err)
				}
			}
		}

		out.OrderProduct = append(out.OrderProduct, order_vo.OrderProduct{
			Id:             product.Id,
			ProductID:      product.ProductId,
			SkuID:          product.SkuId,
			ProductName:    product.ProductName,
			Image:          product.Image,
			ProductType:    product.ProductType,
			Specs:          product.Specs,
			BarCode:        product.BarCode,
			LocationCode:   product.LocationCode,
			MarkingPrice:   product.MarkingPrice,
			PayPrice:       product.PayPrice,
			SubtotalAmount: product.MarkingPrice * product.Number,
			Number:         product.Number,
			PaymentTotal:   product.PaymentTotal,
			EmployeeName:   product.EmployeeName,
			PromotionList:  productPromotionList,
			RefundStatus:   refundStatus,
			RemainCount:    product.Number - refundProductCntMap[product.Id],
			RemainAmount:   product.PaymentTotal - refundProductPaymentMap[product.Id],
			BuyType:        product.BuyType,
			Validity:       validityText,  // 添加卡有效期
			DiscountRange:  discountRange, // 添加卡使用范围
		})
	}

	// 获取订单支付信息
	_, orderPaymentMap, err := new(order_po.OrderPayment).GetOrderPayment(session, order_po.OrderPaymentRequest{
		OrderSn: order.OrderSn,
	})
	if err != nil {
		return out, fmt.Errorf("查询订单支付信息失败: %v", err)
	}
	for _, payment := range orderPaymentMap[order.OrderSn] {
		out.OrderPayment = append(out.OrderPayment, order_vo.OrderPayment{
			PayType: payment.PayType,
			Amount:  payment.Amount,
			Change:  payment.Change,
			Pay:     payment.Pay,
			TradeNo: payment.TradeNo,
			CardNo:  payment.CardNo,
		})
	}

	// 获取退款方式详情
	_, RefundOrderPayDetailMap, _, err := new(order_po.RefundOrderPayDetail).
		GetRefundOrderPayDetail(session, order_po.RefundOrderPayDetailRequest{RefundSns: refundSnSli})
	if err != nil {
		log.Error("获取退款方式详情失败,err=", err.Error())
		return out, fmt.Errorf("获取退款方式详情失败,err=%s", err.Error())
	}

	for _, v := range RefundOrderPayDetailMap {
		out.RefundMethod = append(out.RefundMethod, v...)
	}

	for _, refundOrderProduct := range refundOrderProductSli {
		tmp := order_vo.OrderRefund{
			SkuID:          refundOrderProduct.SkuId,
			ProductName:    refundOrderProduct.ProductName,
			MarkingPrice:   refundOrderProduct.MarkingPrice,
			PayPrice:       refundOrderProduct.RefundPrice,
			TkCount:        refundOrderProduct.Tkcount,
			SubtotalAmount: refundOrderProduct.MarkingPrice * refundOrderProduct.Tkcount,
			PaymentTotal:   refundOrderProduct.RefundPrice * refundOrderProduct.Tkcount,
			RefundAmount:   utils.Yuan2Fen(cast.ToFloat64(refundOrderProduct.RefundAmount)),
			Operator:       "", // todo 待以后补充
		}

		// 处理组合支付方式的退款文案
		if payDetails, ok := RefundOrderPayDetailMap[refundOrderProduct.RefundSn]; ok {
			var methodTexts []string
			methodMap := make(map[string]struct{}) // 用于去重

			// 遍历所有支付方式并获取文案
			for _, detail := range payDetails {
				text := distribution_po.GetRefundMethodText(detail.PaymentType, refundOrderProduct.BuyType)
				if _, exists := methodMap[text]; !exists {
					methodMap[text] = struct{}{}
					methodTexts = append(methodTexts, text)
				}
			}

			// 将所有支付方式文案用逗号连接
			tmp.RefundMethod = strings.Join(methodTexts, ",")
		} else {
			// 如果没有找到支付明细，则显示未知支付方式
			tmp.RefundMethod = distribution_po.GetRefundMethodText(0, refundOrderProduct.BuyType)
		}

		refundOrder, ok := refundOrderMap2[refundOrderProduct.RefundSn]
		tmp.Operator = refundOrder.Operator
		tmp.RefundTime = refundOrder.RefundedTime
		if ok {
			tmp.RefundType = refundOrder.RefundType
			tmp.RefundReason = refundOrder.RefundReason
		}
		out.OrderRefundList = append(out.OrderRefundList, tmp)
	}

	// 次卡
	if order.OrderType == 23 {
		// 获取储值卡订单
		out.StoreCardOrder, err = new(order_po.StoreCardOrder).GetStoreCardOrderByOrderSn(session, order.OrderSn)
		if err != nil {
			return out, fmt.Errorf("查询储值卡订单失败: %v", err)
		}

	} else if order.OrderType == 22 {
		// 获取次卡订单
		out.TimeCardOrder, err = new(order_po.TimeCardOrder).GetTimeCardOrderByOrderSn(session, order.OrderSn)
		if err != nil {
			return out, fmt.Errorf("查询次卡订单失败: %v", err)
		}
	}

	return out, nil
}

// 提交订单
func (s ShopOrderService) Commit(ctx context.Context, in order_vo.CommitRequest) (out order_vo.OrderSubmitResponse, err error) {
	logPrefix := fmt.Sprintf("提交订单====，入参：%+v", in)
	log.Info(logPrefix)

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	//只有普通商品才有优惠活动
	//运费
	freight := in.CalcInfo.Freight
	if in.OrderType == 1 || in.OrderType == 3 || in.OrderType == 2 {
		//先调用购物车算法，校验活动是否都还在
		calcIn := order_vo.CalcRequest{}
		calcIn.Data = in.CalcInfo
		calcIn.IsCommit = 1
		calcIn.OrderType = in.OrderType
		calcOut, err := s.Calc(calcIn)
		if err != nil {
			return out, fmt.Errorf("活动有变动: %v", err)
		}
		//校验购物车的金额和提交订单的是否一致
		if calcOut.CalcInfo.SettlementAmount != in.CalcInfo.SettlementAmount {
			return out, errors.New("订单金额发生变化，请重新刷新")
		}
		//检测订单库存是否足够  todo
	}
	if len(in.OrderPayment) == 0 && in.CalcInfo.SettlementAmount > 0 {
		return out, errors.New("订单支付类型不能为空")
	}

	//寄养销售ID
	SellerId := ""
	//寄养销售名称
	SellerName := ""
	//寄养ID
	JyID := ""
	//押金付款多少
	JYMoney := 0

	now := time.Now().Local()
	//判断是否预订单
	if in.CalcInfo.ChannelId == 1 && in.OrderType == 1 {

		m, _ := time.ParseDuration("90m")
		date := now.Add(m) //配送时间增加一个半小时，判断业务逻辑
		expectedTime, err := time.ParseInLocation(utils.DATETIME_LAYOUT, in.ExpectedTime, time.Local)
		if err != nil {
			return out, fmt.Errorf("预计送达时间参数错误: %v", err)
		}
		if expectedTime.Before(time.Now()) {
			return out, fmt.Errorf("请重新选择送达时间: %v", err)
		}
		if expectedTime.After(date) {
			in.OrderType = 2
		}
	}

	if in.CalcInfo.ChannelId != 1 {

		//有抹零
		if in.RoundingAmount > 0 {

			item := order_vo.DiscountInfo{
				DiscountType:  "9",
				DiscountName:  "抹零",
				DiscountMoney: in.RoundingAmount,
			}
			in.CalcInfo.DiscountInfo = append(in.CalcInfo.DiscountInfo, item)
			//in.OrderDiscountAmount -= in.RoundingAmount

			records := allocateDiscount(in.CalcInfo.Details, in.RoundingAmount, true, func(detail order_vo.OrderDetail) bool {
				// 检查商品是否在活动商品列表中且不是赠品且未被次卡抵扣完
				if detail.BuyType == 2 || detail.DeductionBuyCount <= 0 {
					return false
				}
				return true
			})

			// 补充优惠记录信息
			for i := range records {
				// 查找对应的商品明细
				for x, detail := range in.CalcInfo.Details {
					if detail.ItemID == records[i].OrderDcId {
						if records[i].SkuPromotion > 0 || records[i].Promotion > 0 {
							// 更新 ItemAmount，减去优惠分摊金额
							in.CalcInfo.Details[x].ItemAmount -= records[i].SkuPromotion
							in.CalcInfo.Details[x].ItemSubtotalAmount -= records[i].Promotion
							// 补充优惠记录信息
							records[i].OrderDcId = cast.ToString(detail.ItemID)
							records[i].PromotionType = 9
							records[i].PromotionTitle = "抹零"
							in.CalcInfo.PromotionRecords = append(in.CalcInfo.PromotionRecords, records[i])
						}

					}
				}
			}

		}

		//说明有整单打折，需要均摊
		if in.OriginalAmount != (in.RoundingAmount + in.OrderDiscountAmount) {
			maxAmount := in.OriginalAmount - in.RoundingAmount - in.OrderDiscountAmount
			item := order_vo.DiscountInfo{
				DiscountType:  "10",
				DiscountName:  "整单打折",
				DiscountMoney: maxAmount,
			}
			in.CalcInfo.DiscountInfo = append(in.CalcInfo.DiscountInfo, item)
			//in.OrderDiscountAmount -= maxAmount
			records := allocateDiscount(in.CalcInfo.Details, maxAmount, true, func(detail order_vo.OrderDetail) bool {
				// 检查商品是否在活动商品列表中且不是赠品且未被次卡抵扣完
				if detail.BuyType == 2 || detail.DeductionBuyCount <= 0 {
					return false
				}
				return true
			})

			// 补充优惠记录信息
			for i := range records {
				// 查找对应的商品明细
				for x, detail := range in.CalcInfo.Details {
					if detail.ItemID == records[i].OrderDcId {
						if records[i].SkuPromotion > 0 || records[i].Promotion > 0 {
							// 更新 ItemAmount，减去优惠分摊金额
							in.CalcInfo.Details[x].ItemAmount -= records[i].SkuPromotion
							in.CalcInfo.Details[x].ItemSubtotalAmount -= records[i].Promotion
							// 补充优惠记录信息
							records[i].OrderDcId = cast.ToString(detail.ItemID)
							records[i].PromotionType = 10
							records[i].PromotionTitle = "整单打折"
							in.CalcInfo.PromotionRecords = append(in.CalcInfo.PromotionRecords, records[i])
						}

					}
				}
			}
		}
	}
	//out.CalcInfo = in.CalcInfo
	//金额计算完成，进行订单商品表数据转换成表数据
	//orderMains := make([]*order_po.OrderMain, 0)
	orderDetails := make([]*order_po.OrderDetail, 0)
	orderProducts := make([]*order_po.OrderProduct, 0)
	orderPayments := make([]*order_po.OrderPayment, 0)
	orderPromotions := make([]*order_po.OrderPromotion, 0)
	orderPromotionRecords := make([]*order_po.OrderPromotionRecord, 0)

	//查询仓库信息
	var warehouseInfo = warehouse.WarehouseRelationShop{}
	warehouse, err := warehouseInfo.GetWarehouseByShopAndChannel(session, in.CalcInfo.TenantID, in.CalcInfo.ChannelId)
	if err != nil {
		return out, err
	}

	SkuId := make([]int, 0)
	for _, x := range in.CalcInfo.Details {
		SkuId = append(SkuId, cast.ToInt(x.SkuID))
		if x.ProductType == 6 {
			JyID = x.JyID
			SellerId = x.EmployeeID
			SellerName = x.EmployeeName
		}
	}

	//查询商品信息 FindStoreSkuList
	var storeProductService product_service.StoreProductService
	storeSkuListReq := product_vo.FindStoreSkuListReq{
		StoreId:   in.CalcInfo.TenantID,
		ChannelId: in.CalcInfo.ChannelId,
		SkuIds:    SkuId,
	}
	productMap := make(map[int]po.ProSku)
	productInfoMap := make(map[int]product_vo.FindStoreProductList)
	productList, _, err := storeProductService.FindStoreSkuList(storeSkuListReq)
	if err != nil {
		return out, fmt.Errorf("查询商品信息失败: %v", err)
	}
	// 创建map存储商品信息,以skuId为key
	for _, product := range productList {
		for _, sku := range product.Sku {
			productMap[sku.BaseSku.Id] = sku.BaseSku
			productInfoMap[sku.BaseSku.Id] = product
		}
	}

	req := omnibus_vo.StoreGetReq{
		OrgId:    in.OrgId,
		TenantId: in.CalcInfo.TenantID,
	}

	omservrice := omnibus_service.StoreService{}
	store, err := omservrice.StoreMode(req)
	if err != nil {
		return out, fmt.Errorf("查询店铺信息失败: %v", err)
	}

	//商品总重量
	SumWeight := in.CalcInfo.TotalWeight
	//优惠前总金额
	SumTotal := 0
	for _, x := range in.CalcInfo.Details {
		SumTotal += x.TotalBuyCount * x.RetailPrice
	}

	//总优惠金额
	SumPrivilege := 0
	//计算商品总重量

	//计算总优惠
	for _, x := range in.CalcInfo.DiscountInfo {

		SumPrivilege += x.DiscountMoney

	}
	OrderStatus := 30
	OrderStatusChild := 20106
	if (len(in.OrderPayment) > 0 && in.OrderPayment[0].PayType == 11) || in.CalcInfo.ChannelId == 1 {
		OrderStatus = 10
		OrderStatusChild = 20101
	}

	//now := time.Now()
	//主订单
	var order = new(order_po.OrderMain)
	//主订单号
	orderSn := utils.GetSn("order", s.Engine)[0]
	order.WarehouseId = warehouse.WarehouseId
	order.WarehouseName = warehouse.WarehouseName
	order.WarehouseCode = in.CalcInfo.TenantID
	order.OrderSn = orderSn
	order.ParentOrderSn = ""
	order.OldOrderSn = orderSn
	order.OrderStatus = OrderStatus
	order.OrderStatusChild = OrderStatusChild
	order.ShopId = in.CalcInfo.TenantID
	order.ShopName = in.ShopName
	order.MemberId = in.CalcInfo.CustomerInfo.ID
	order.MemberName = in.CalcInfo.CustomerInfo.Name
	order.MemberTel = utils.AddStar(in.CalcInfo.CustomerInfo.Phone)
	order.EnMemberTel = utils.MobileEncrypt(in.CalcInfo.CustomerInfo.Phone)
	order.ReceiverName = in.CalcInfo.CustomerInfo.Name
	order.ReceiverMobile = utils.AddStar(in.CalcInfo.CustomerInfo.Phone)
	order.EnReceiverMobile = utils.MobileEncrypt(in.CalcInfo.CustomerInfo.Phone)
	order.ReceiverPhone = utils.AddStar(in.CalcInfo.CustomerInfo.Phone)
	order.EnReceiverPhone = utils.MobileEncrypt(in.CalcInfo.CustomerInfo.Phone)
	order.Total = in.OrderDiscountAmount
	order.GoodsTotal = SumTotal
	order.PayTotal = in.OrderDiscountAmount
	order.GoodsPayTotal = in.OrderDiscountAmount - freight
	order.Privilege = SumPrivilege
	order.ActualReceiveTotal = in.OrderDiscountAmount - freight
	order.TotalWeight = utils.Yuan2Fen(SumWeight)
	order.IsPay = 1
	order.PayAmount = in.OrderDiscountAmount
	order.OrderType = in.OrderType
	order.DeliverTime = now
	order.AppChannel = store.AppChannel
	if order.OrderType == 0 {
		order.OrderType = 1
	}
	order.DeliveryType = 6
	order.Source = 3
	order.ChannelId = in.CalcInfo.ChannelId
	order.UserAgent = 6
	order.IsVirtual = 0
	//order.IsPushTencent = 1
	order.OrderPayType = "02"
	order.OrgId = in.OrgId
	order.Freight = freight
	order.Lng = cast.ToString(in.CalcInfo.DestinationX)
	order.Lat = cast.ToString(in.CalcInfo.DestinationY)
	order.CreateTime = now
	order.ConfirmTime = now
	order.PayTime = now

	if in.CalcInfo.ChannelId == 1 {
		order.DeliveryType = 2
		order.UserAgent = 3
		order.ReceiverName = in.CalcInfo.ReceiverName
		order.ReceiverMobile = utils.AddStar(in.CalcInfo.ReceiverPhone)
		order.EnReceiverMobile = utils.MobileEncrypt(in.CalcInfo.ReceiverPhone)
		order.ReceiverPhone = utils.AddStar(in.CalcInfo.ReceiverPhone)
		order.EnReceiverPhone = utils.MobileEncrypt(in.CalcInfo.ReceiverPhone)
		order.ReceiverState = in.CalcInfo.ReceiverState
		order.ReceiverCity = in.CalcInfo.ReceiverCity
		order.ReceiverDistrict = in.CalcInfo.ReceiverDistrict
		order.ReceiverAddress = in.CalcInfo.ReceiverAddress
		t := time.Time{}
		order.PayTime = t
		order.ConfirmTime = t
		order.IsPay = 0

		if in.OrderType != 3 {
			//查询对应配送方式
			ConfigReq := omnibus_vo.GetDeliveryConfigReq{}
			ConfigReq.FinanceCode = order.ShopId
			ConfigReq.OrgId = order.OrgId
			ConfigReq.ChannelID = order.ChannelId
			omn := omnibus_service.StoreService{}
			data, err := omn.GetDeliveryConfig(ConfigReq)
			if err != nil {
				return out, fmt.Errorf("查询店铺配送方式失败: %v", err)
			}
			LogisticsName := ""
			if data.ID > 0 {
				switch data.DeliveryMethod {
				case 1:
					LogisticsName = "外卖平台配送"
				case 2:
					LogisticsName = "三方配送"
				case 3:
					LogisticsName = "聚合配送"
				case 4:
					LogisticsName = "自行配送"
				}

			}
			order.LogisticsName = LogisticsName
		}

	}
	if len(in.OrderPayment) > 0 && in.OrderPayment[0].PayType == 11 {
		t := time.Time{}
		order.PayTime = t
		order.ConfirmTime = t
		order.IsPay = 0
	}
	if in.OrderType == 3 {
		order.DeliveryType = 3
	}
	for _, pay := range in.OrderPayment {
		if pay.PayType == 3 {
			JYMoney += pay.Amount
		}
	}
	//发短信参数
	smsreq := omnibus_vo.SmsSendMessageRequest{}
	smsreq.StoreId = order.ShopId
	smsreq.Mobile = in.CalcInfo.CustomerInfo.Phone
	//orderMains = append(orderMains, order)
	//插入订单主表
	session.Begin()

	err = new(order_po.OrderMain).Create(session, order)
	if err != nil {
		session.Rollback()
		return out, fmt.Errorf("插入订单主表失败: %v", err)
	}

	// 后续可以直接使用orderMains[0].Id
	var count int32
	_, err = session.SQL("select count(1)  from dc_order.order_main where (parent_order_sn = '' or parent_order_sn = order_sn) and channel_id = ? and shop_id=? and create_time between ? and ? ",
		order.ChannelId, order.ShopId, order.CreateTime.Format("2006-01-02")+" 00:00:00", utils.GetTimeNow(order.CreateTime)).Get(&count)

	//订单详情赋值
	var orderDetail = new(order_po.OrderDetail)
	orderDetail.OrderSn = orderSn
	orderDetail.SellerMemo = in.Remark
	orderDetail.PushThirdOrder = 0
	orderDetail.SplitOrderResult = 1
	orderDetail.CashierName = in.CashierName
	orderDetail.CashierId = in.CashierId
	orderDetail.ExpectedTime = time.Now()
	orderDetail.PickupCode = cast.ToString(count)

	orderDetail.Latitude = in.CalcInfo.DestinationY
	orderDetail.Longitude = in.CalcInfo.DestinationX
	orderDetail.PetName = in.PetName
	orderDetail.PetId = in.PetId

	if in.CalcInfo.ChannelId == 1 {
		// 定义布局，必须使用 Go 的时间格式
		layout := "2006-01-02 15:04:05"
		sh, _ := time.LoadLocation("Asia/Shanghai")
		// 解析字符串为 time.Time
		t, err := time.ParseInLocation(layout, in.ExpectedTime, sh)
		if err != nil {
			return out, fmt.Errorf("插入订单详情失败: %v", err)
		}
		orderDetail.ExpectedTime = t
		orderDetail.SellerMemo = ""
		orderDetail.BuyerMemo = in.Remark
	}

	orderDetails = append(orderDetails, orderDetail)

	for _, orderPro := range in.CalcInfo.Details {

		byyType := 0
		if orderPro.BuyType == 2 {
			byyType = 2
		}
		//如果是储值卡或者持卡购买或续卡，就要改购买TYPE
		cardStr := productMap[cast.ToInt(orderPro.SkuID)].ProductSpecs
		if in.OrderType == 22 {
			if in.BuyTimeCard.Type == "1" {
				byyType = 102
			} else {
				byyType = 103
			}
			cardStr = "充值" + in.BuyTimeCard.Num + "次送" + in.BuyTimeCard.NumGift + "次"
		}
		if in.OrderType == 23 {
			if in.BuyStoreCard.Type == "1" {
				byyType = 100
			} else {
				byyType = 101
			}
			cardStr = "充值" + cast.ToString(utils.Fen2Yuan(in.BuyStoreCard.Amount)) + "元送" + cast.ToString(utils.Fen2Yuan(in.BuyStoreCard.AmountGift)) + "元"
		}
		ChannelAategoryName := productInfoMap[cast.ToInt(orderPro.SkuID)].CategoryNavOnline
		if in.CalcInfo.ChannelId == 100 {
			ChannelAategoryName = productInfoMap[cast.ToInt(orderPro.SkuID)].CategoryNav
		}
		if orderPro.DeductionBuyCount > 0 {

			item := order_po.OrderProduct{
				OrderSn:             orderSn, // 订单号会在后续生成时填充
				ProductId:           cast.ToString(productInfoMap[cast.ToInt(orderPro.SkuID)].ProductId),
				SkuId:               cast.ToString(orderPro.SkuID),
				ProductName:         orderPro.ProductName,
				ProductType:         orderPro.ProductType,
				BuyType:             byyType,
				Number:              orderPro.DeductionBuyCount,
				PayPrice:            orderPro.ItemAmount,
				DiscountPrice:       orderPro.ItemAmount,
				MarkingPrice:        orderPro.RetailPrice,
				PaymentTotal:        orderPro.ItemSubtotalAmount,
				SkuPayTotal:         orderPro.SubtotalAmount,
				SubBizOrderId:       orderPro.ItemID,
				Image:               orderPro.ProductPic,
				BarCode:             productMap[cast.ToInt(orderPro.SkuID)].BarCode,
				EmployeeId:          orderPro.EmployeeID,
				EmployeeName:        orderPro.EmployeeName,
				LocationCode:        productMap[cast.ToInt(orderPro.SkuID)].LocationCode,
				Specs:               cardStr,
				ChannelCategoryName: ChannelAategoryName,
			}

			orderProducts = append(orderProducts, &item)
		}

		newItemId := utils.GetGuid32()
		// 如果是次卡抵扣的商品，需要拆分出来
		if orderPro.TotalBuyCount != orderPro.DeductionBuyCount {
			itemCard := order_po.OrderProduct{
				OrderSn:             orderSn, // 订单号会在后续生成时填充
				ProductId:           cast.ToString(productInfoMap[cast.ToInt(orderPro.SkuID)].ProductId),
				SkuId:               cast.ToString(orderPro.SkuID),
				ProductName:         orderPro.ProductName,
				ProductType:         orderPro.ProductType,
				BuyType:             1,
				Number:              orderPro.TotalBuyCount - orderPro.DeductionBuyCount,
				PayPrice:            0,
				DiscountPrice:       0,
				MarkingPrice:        0,
				PaymentTotal:        0,
				SkuPayTotal:         0,
				SubBizOrderId:       newItemId,
				Specs:               cardStr,
				EmployeeId:          orderPro.EmployeeID,
				EmployeeName:        orderPro.EmployeeName,
				LocationCode:        productMap[cast.ToInt(orderPro.SkuID)].LocationCode,
				ChannelCategoryName: ChannelAategoryName,
			}
			orderProducts = append(orderProducts, &itemCard)

			//然后把次卡抵扣的itemID换了
			for i, record := range in.CalcInfo.PromotionRecords {
				if record.OrderDcId == orderPro.ItemID && record.PromotionType == 6 {
					in.CalcInfo.PromotionRecords[i].OrderDcId = newItemId
				}
			}

		}
	}

	//填充支付信息
	for _, x := range in.OrderPayment {
		item := order_po.OrderPayment{
			TenantId:      cast.ToInt64(in.CalcInfo.TenantID),
			OrderId:       order.Id, //
			OrderSn:       orderSn,  // 订单号会在后续生成时填充
			PayType:       x.PayType,
			Amount:        x.Amount,
			RemainAmount:  x.Amount,
			RetreatAmount: 0,
			Change:        x.Change,
			Pay:           x.Amount,
			PaymentStatus: "",
			TradeNo:       "",
			CardNo:        x.CardNo,
			Remark:        "",
			CreatedBy:     cast.ToInt64(in.CashierId),
			CreatedTime:   now,
			UpdatedBy:     cast.ToInt64(in.CashierId),
			UpdatedTime:   now,
		}
		orderPayments = append(orderPayments, &item)
	}
	// 处理订单优惠信息
	for _, discount := range in.CalcInfo.DiscountInfo {
		promotion := &order_po.OrderPromotion{
			OrderSn:        orderSn,
			PromotionId:    0, // 默认为0
			PromotionType:  cast.ToInt(discount.DiscountType),
			PromotionTitle: discount.DiscountName,
			PromotionFee:   discount.DiscountMoney,
			PoiCharge:      discount.DiscountMoney,
		}
		orderPromotions = append(orderPromotions, promotion)
	}

	// 处理订单商品优惠记录
	for _, record := range in.CalcInfo.PromotionRecords {
		promotionRecord := &order_po.OrderPromotionRecord{
			OrderSn:        orderSn,
			OrderDcId:      record.OrderDcId,
			Sku:            record.Sku,
			Promotion:      record.Promotion,
			SkuPromotion:   record.SkuPromotion,
			Num:            record.Num,
			PromotionId:    record.PromotionId,
			PromotionType:  record.PromotionType,
			PromotionTitle: record.PromotionTitle,
			CardNo:         record.CardNo,
			ID:             utils.GetGuid32(),
		}
		orderPromotionRecords = append(orderPromotionRecords, promotionRecord)
	}

	//插入订单详情
	if len(orderDetails) > 0 {
		err = orderDetail.BatchCreate(session, orderDetails)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("插入订单详情失败: %v", err)
		}
	}

	//插入订单商品表
	if len(orderProducts) > 0 {
		var orderProduct order_po.OrderProduct
		err = orderProduct.BatchCreate(session, orderProducts)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("插入订单商品表失败: %v", err)
		}

	}

	//插入订单支付信息
	if len(orderPayments) > 0 {
		var orderPayment order_po.OrderPayment
		err = orderPayment.BatchCreate(session, orderPayments)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("插入订单支付信息失败: %v", err)
		}
	}

	// 插入订单优惠信息
	if len(orderPromotions) > 0 {
		err = new(order_po.OrderPromotion).BatchCreate(session, orderPromotions)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("插入订单优惠信息失败: %v", err)
		}
	}

	// 插入订单商品优惠记录
	if len(orderPromotionRecords) > 0 {
		err = new(order_po.OrderPromotionRecord).BatchCreate(session, orderPromotionRecords)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("插入订单商品优惠记录失败: %v", err)
		}
	}

	//判断是否要锁库
	if in.OrderType == 1 || in.OrderType == 3 || in.OrderType == 2 {
		err = s.FreezeInventory(session, orderSn)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("冻结库存失败: %v", err)
		}
	}

	// 生成子订单号
	childOrderSn := utils.GetSn("order", s.Engine)[0]

	// 复制主订单数据作为子订单
	//childOrderMain := order_po.OrderMain{}
	childOrderDetails := make([]*order_po.OrderDetail, 0)
	childOrderProducts := make([]*order_po.OrderProduct, 0)
	childOrderPayments := make([]*order_po.OrderPayment, 0)
	childOrderPromotions := make([]*order_po.OrderPromotion, 0)
	childOrderPromotionRecords := make([]*order_po.OrderPromotionRecord, 0)

	// 复制主订单信息并修改相关字段

	childMain := *order // 复制主订单数据1
	childMain.OrderSn = childOrderSn
	childMain.ParentOrderSn = orderSn
	childMain.OldOrderSn = childOrderSn
	childMain.Id = 0

	// 插入子订单数据

	err = order.Create(session, &childMain)
	if err != nil {
		session.Rollback()
		return out, fmt.Errorf("插入子订单主表失败: %v", err)
	}

	// 复制订单详情
	for _, detail := range orderDetails {
		childDetail := *detail
		childDetail.OrderSn = childOrderSn
		childOrderDetails = append(childOrderDetails, &childDetail)
	}

	// 复制订单商品并重新生成SubBizOrderId
	subBizOrderIdMap := make(map[string]string) // 用于存储旧SubBizOrderId到新SubBizOrderId的映射
	for _, product := range orderProducts {
		childProduct := *product
		childProduct.Id = 0
		childProduct.OrderSn = childOrderSn
		// 生成新的SubBizOrderId
		newSubBizOrderId := utils.GetGuid32()
		// 保存新旧SubBizOrderId的映射关系
		subBizOrderIdMap[childProduct.SubBizOrderId] = newSubBizOrderId
		childProduct.SubBizOrderId = newSubBizOrderId
		childOrderProducts = append(childOrderProducts, &childProduct)
	}

	// 复制订单支付信息
	for _, payment := range orderPayments {
		childPayment := *payment
		childPayment.Id = 0
		childPayment.OrderSn = childOrderSn
		childPayment.OrderId = childMain.Id
		childOrderPayments = append(childOrderPayments, &childPayment)
	}

	// 复制订单优惠信息
	for _, promotion := range orderPromotions {
		childPromotion := *promotion
		childPromotion.Id = 0
		childPromotion.OrderSn = childOrderSn
		childOrderPromotions = append(childOrderPromotions, &childPromotion)
	}

	// 复制订单商品优惠记录
	for _, record := range orderPromotionRecords {
		childRecord := *record
		childRecord.OrderSn = childOrderSn
		childRecord.ID = utils.GetGuid32()
		//childRecord.OrderDcId=subBizOrderIdMap[childRecord.OrderDcId]
		// 再通过原始SubBizOrderId找到新的SubBizOrderId
		if newSubBizOrderId, ok := subBizOrderIdMap[childRecord.OrderDcId]; ok {
			childRecord.OrderDcId = newSubBizOrderId
		}

		childOrderPromotionRecords = append(childOrderPromotionRecords, &childRecord)
	}

	if len(childOrderDetails) > 0 {
		err = orderDetail.BatchCreate(session, childOrderDetails)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("插入子订单详情失败: %v", err)
		}
	}

	if len(childOrderProducts) > 0 {
		var orderProduct order_po.OrderProduct
		err = orderProduct.BatchCreate(session, childOrderProducts)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("插入子订单商品表失败: %v", err)
		}

		// 更新子订单orderPromotionRecords的OrderDcId
		for i := range childOrderPromotionRecords {

			if id, ok := subBizOrderIdMap[childOrderPromotionRecords[i].OrderDcId]; ok {
				childOrderPromotionRecords[i].OrderDcId = cast.ToString(id)
			}
		}
		//插入子订单完成后，校验库存
		// 处理库存扣减
		if in.OrderType == 1 || in.OrderType == 3 { // 普通订单才需要扣减库存
			//// 构造出库请求
			//outBoundDetails := make([]iobound_vo.OutBoundDetailCommand, 0)
			//for _, product := range childOrderProducts {
			//	if product.ProductType == 1 {
			//		outBoundDetails = append(outBoundDetails, iobound_vo.OutBoundDetailCommand{
			//			SkuId:           cast.ToInt(product.SkuId),
			//			ProductName:     product.ProductName,
			//			IoCount:         product.Number,
			//			IoPrice:         utils.Yuan2Fen(product.PayPrice), // 使用零售价作为出库价
			//			ItemDetailRefId: product.SubBizOrderId,
			//		})
			//	}
			//}
			//outBoundCmd := iobound_vo.OutBoundCommand{
			//	WarehouseId: 0, // 不指定仓库ID,让系统自动选择
			//	RefId:       childMain.Id,
			//	RefNo:       childOrderSn,
			//	RefType:     1,
			//	Details:     outBoundDetails,
			//	ChannelId:   100,
			//	Remark:      "订单出库",
			//}
			//
			//// 调用库存服务扣减库存
			//ioBoundService := iobound.NewIoBoundService()
			//_, err = ioBoundService.OrderOut(ctx, session, outBoundCmd)
			//if err != nil {
			//	session.Rollback()
			//	return fmt.Errorf("扣减库存失败: %v", err)
			//}
		}
	}

	if len(childOrderPayments) > 0 {
		var orderPayment order_po.OrderPayment
		err = orderPayment.BatchCreate(session, childOrderPayments)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("插入子订单支付信息失败: %v", err)
		}
	}

	if len(childOrderPromotions) > 0 {
		err = new(order_po.OrderPromotion).BatchCreate(session, childOrderPromotions)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("插入子订单优惠信息失败: %v", err)
		}
	}

	if len(childOrderPromotionRecords) > 0 {
		err = new(order_po.OrderPromotionRecord).BatchCreate(session, childOrderPromotionRecords)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("插入子订单商品优惠记录失败: %v", err)
		}
	}

	// 处理储值卡支付
	for _, payment := range childOrderPayments {
		// 判断是否是储值卡支付
		if payment.PayType == 10 {
			// 查询储值卡信息
			storeCardQuery := marketing_po.StoreCardQuery{
				StoreId:    in.CalcInfo.TenantID,
				ChainId:    cast.ToInt64(in.CalcInfo.ChainID),
				CustomerId: cast.ToInt64(in.CalcInfo.CustomerInfo.ID),
				CardNo:     payment.CardNo,
			}

			storeCard, err := new(marketing_po.MStoreCard).GetStoreCardInfo(session, storeCardQuery)
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("查询储值卡信息失败: %v", err)
			}

			// 计算需要扣减的金额
			deductAmount := utils.Fen2Yuan(payment.Amount) // 转换为元

			// 优先扣减本金
			principalDeduct := deductAmount
			giftDeduct := float64(0)

			if storeCard.Balance < deductAmount {
				principalDeduct = storeCard.Balance
				giftDeduct = deductAmount - storeCard.Balance
				if storeCard.GiftBalance < giftDeduct {
					session.Rollback()
					return out, fmt.Errorf("储值卡余额不足")
				}
			}

			// 更新储值卡记录
			_, err = session.Exec("UPDATE eshop_saas.m_store_card_record SET principal = principal - ?, gift_amount = gift_amount - ?,total_amount=total_amount-? WHERE card_no = ? AND customer_id = ? AND status = 'IN_USE' ",
				principalDeduct, giftDeduct, principalDeduct+giftDeduct, payment.CardNo, cast.ToInt64(in.CalcInfo.CustomerInfo.ID))
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("更新储值卡余额失败: %v", err)
			}

			// 使用 decimal 库重构计算逻辑
			principalDeductDec := decimal.NewFromFloat(principalDeduct)
			Balance := decimal.NewFromFloat(storeCard.Balance)
			GiftBalance := decimal.NewFromFloat(storeCard.GiftBalance)
			// 执行减法操作
			result := Balance.Sub(principalDeductDec).Add(GiftBalance)
			// 插入储值卡使用记录
			useRecord := &marketing_po.MStoreCardUseRecord{
				ChainId:             cast.ToInt64(storeCard.StoreCard.ChainId),
				TenantId:            cast.ToInt64(in.CalcInfo.TenantID),
				RecordId:            storeCard.RecordID, // 这里需要补充实际的记录ID
				CardId:              storeCard.StoreCard.CardId,
				CardNo:              payment.CardNo,
				CardName:            storeCard.CardName,
				OperateType:         "CONSUMPTION",
				Balance:             storeCard.Balance - principalDeduct,
				GiftBalance:         storeCard.GiftBalance - giftDeduct,
				BalanceChange:       -deductAmount,
				OrderId:             cast.ToInt64(order.Id), // 订单ID待补充
				OrderNo:             childOrderSn,
				CustomerId:          cast.ToInt64(in.CalcInfo.CustomerInfo.ID),
				EmployeeId:          cast.ToInt64(in.CashierId),
				Remark:              "订单支付",
				Status:              "SUCCESS",
				CreatedBy:           cast.ToInt64(in.CashierId),
				CreatedTime:         time.Now(),
				UpdatedBy:           cast.ToInt64(in.CashierId),
				UpdatedTime:         time.Now(),
				PrincipalChange:     -(principalDeduct),
				GiftChange:          -(giftDeduct),
				ReturnablePrincipal: storeCard.Balance - principalDeduct,
				ReturnableGift:      storeCard.GiftBalance - giftDeduct,
			}

			_, err = session.Insert(useRecord)
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("插入储值卡使用记录失败: %v", err)
			}

			//消费模板和参数
			templateParam := sms.SmsConsumeParams{}
			templateParam.TenantName = order.ShopName
			templateParam.PayAmount = cast.ToString(deductAmount)
			templateParam.BalanceAmount = result.StringFixed(2)
			byt, err := json.Marshal(templateParam)
			if err != nil {
				glog.Error(templateParam, "转换短信参数失败", err.Error())
			}
			smsreq.TemplateParam = string(byt)
			smsreq.TemplateCode = omnibus_po.SmsTemplateStoreCardConsume

		}
	}

	//次卡逻辑处理

	// 处理次卡使用
	for _, detail := range in.CalcInfo.Details {
		for _, buyType := range detail.BuyTypeList {
			// 判断是否是次卡使用
			if buyType.Num > 0 { // 次卡支付
				// 查询次卡记录
				timeCardRecord, err := new(marketing_po.MTimeCardRecord).GetTimeCardByCardNo(session, buyType.CardNo, cast.ToInt64(in.CalcInfo.CustomerInfo.ID))
				if err != nil {
					session.Rollback()
					return out, fmt.Errorf("查询次卡记录失败: %v", err)
				}
				if timeCardRecord == nil {
					session.Rollback()
					return out, fmt.Errorf("次卡不存在或已失效: %s", buyType.CardNo)
				}

				// 计算扣减次数和金额
				deductNum := buyType.Num
				buyNumDeduct := deductNum
				giftNumDeduct := 0

				// 优先扣减购买次数
				if timeCardRecord.BuyNum < deductNum {
					buyNumDeduct = timeCardRecord.BuyNum
					giftNumDeduct = deductNum - timeCardRecord.BuyNum
					if timeCardRecord.GiftNum < giftNumDeduct {
						session.Rollback()
						return out, fmt.Errorf("次卡可用次数不足")
					}
				}

				// 计算扣减金额 (每次扣减金额 = 购买时的价格/购买时的次数)
				deductAmountPerTime := timeCardRecord.Price / float64(timeCardRecord.BuyingNum)
				totalDeductAmount := deductAmountPerTime * float64(deductNum)

				// 更新次卡记录
				err = timeCardRecord.UpdateTimeCardUse(session,
					buyType.CardNo,
					cast.ToInt64(in.CalcInfo.CustomerInfo.ID),
					buyNumDeduct,
					giftNumDeduct,
					deductNum,
					totalDeductAmount,
					timeCardRecord.Version)

				if err != nil {
					session.Rollback()
					return out, fmt.Errorf("更新次卡记录失败: %v", err)
				}

				// 插入次卡使用记录
				useRecord := &marketing_po.MTimeCardUseRecord{
					ChainId:        timeCardRecord.ChainId,
					TenantId:       cast.ToInt(in.CalcInfo.TenantID),
					RecordId:       timeCardRecord.Id,
					CardId:         timeCardRecord.CardId,
					CardNo:         timeCardRecord.CardNo,
					CardName:       timeCardRecord.CardName,
					OperateType:    "CONSUMPTION",
					Balance:        timeCardRecord.Balance - totalDeductAmount,
					BalanceChange:  -totalDeductAmount,
					TotalChange:    -deductNum,
					CurrentChange:  -buyNumDeduct,
					GiftChange:     -giftNumDeduct,
					Num:            timeCardRecord.BuyNum - buyNumDeduct,
					GiftNum:        timeCardRecord.GiftNum - giftNumDeduct,
					ReturnableBuy:  buyNumDeduct,
					ReturnableGift: giftNumDeduct,
					OrderId:        childMain.Id,
					OrderNo:        childOrderSn,
					CustomerId:     timeCardRecord.CustomerId,
					EmployeeId:     cast.ToInt(in.CashierId),
					Remark:         "订单消费",
					Status:         "SUCCESS",
					CreatedBy:      cast.ToInt64(in.CashierId),
					CreatedTime:    time.Now(),
					UpdatedBy:      cast.ToInt64(in.CashierId),
					UpdatedTime:    time.Now(),
				}
				err = new(marketing_po.MTimeCardUseRecord).CreateTimeCardUseRecord(session, useRecord)
				if err != nil {
					session.Rollback()
					return out, fmt.Errorf("插入次卡使用记录失败: %v", err)
				}
			}
		}
	}

	// 在session.Commit()之前添加优惠券处理逻辑
	IsCoupon := false
	//判断是否使用了优惠卷，用户虽然选了。但是可能不满足条件
	for _, detail := range in.CalcInfo.DiscountInfo {
		if detail.DiscountType == "5" {
			IsCoupon = true
			break
		}
	}

	// 处理优惠券使用
	if len(in.CalcInfo.Coupons) > 0 && IsCoupon {
		for _, coupon := range in.CalcInfo.Coupons {
			err = new(marketing_po.MarketingCouponReceiver).UseMarketingCoupon(
				session,
				coupon.Code,
				cast.ToInt64(in.CalcInfo.CustomerInfo.ID),
				childMain.Id, // 使用子订单ID
				childOrderSn, // 使用子订单号
			)
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("更新优惠券使用状态失败: %v", err)
			}
		}
	}
	//如果是购买储值卡
	if in.OrderType == 23 {
		storeCard := new(marketing_po.MStoreCard)

		// 查询储值卡基础信息
		cardInfo, err := storeCard.GetCardInfo(session,
			cast.ToInt64(in.CalcInfo.ChainID),
			cast.ToInt64(in.CalcInfo.TenantID),
			cast.ToInt64(in.BuyStoreCard.CardID))
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("查询储值卡基础信息失败: %v", err)
		}

		recordId := int64(0)
		recordBalance := 0
		if in.BuyStoreCard.Type == "1" { // 开卡
			// 创建储值卡记录
			cardRecord := &marketing_po.MStoreCardRecord{
				ChainId:        cast.ToInt64(in.CalcInfo.ChainID),
				TenantId:       cast.ToInt64(in.CalcInfo.TenantID),
				CardId:         cast.ToInt64(in.BuyStoreCard.CardID),
				CardNo:         utils.GenerateCardNo(),
				CustomerId:     cast.ToInt64(in.CalcInfo.CustomerInfo.ID),
				Principal:      utils.Fen2Yuan(in.BuyStoreCard.Amount),
				GiftAmount:     utils.Fen2Yuan(in.BuyStoreCard.AmountGift),
				Status:         "IN_USE",
				ValidType:      cardInfo.ValidType, // 永久有效
				CreatedBy:      cast.ToInt64(in.CashierId),
				UpdatedBy:      cast.ToInt64(in.CashierId),
				TotalAmount:    utils.Fen2Yuan(in.BuyStoreCard.Amount + in.BuyStoreCard.AmountGift),
				Price:          utils.Fen2Yuan(in.BuyStoreCard.Amount),
				CustomerName:   in.CalcInfo.CustomerInfo.Name,
				CustomerMobile: in.CalcInfo.CustomerInfo.Phone,
				EmployeeId:     cast.ToInt64(in.CashierId),
				CardName:       cardInfo.Name,
			}
			recordBalance = in.BuyStoreCard.Amount + in.BuyStoreCard.AmountGift
			if cardRecord.ValidType == "VALID_DAYS" {
				cardRecord.EnableTime = time.Now()
				cardRecord.ExpirationTime = time.Now().AddDate(0, 0, cardInfo.WithinDay)
			}
			if cardRecord.ValidType == "FIXED_TIME" || cardRecord.ValidType == "DEADLINE" {
				cardRecord.EnableTime = cardInfo.StartTime
				cardRecord.ExpirationTime = cardInfo.EndTime
			}
			if cardRecord.EnableTime.After(time.Now()) {
				cardRecord.Status = "NOT_START"
			}

			if cardRecord.ValidType == "PERMANENT" {
				t := time.Date(9999, 12, 1, 31, 23, 59, 59, time.Local)
				cardRecord.ExpirationTime = t
			}

			err = storeCard.CreateOrUpdateStoreCardRecord(session, cardRecord, false)
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("创建储值卡记录失败: %v", err)
			}
			recordId = cardRecord.Id

			// 创建储值卡使用记录
			useRecord := &marketing_po.MStoreCardUseRecord{
				ChainId:             cast.ToInt64(in.CalcInfo.ChainID),
				TenantId:            cast.ToInt64(in.CalcInfo.TenantID),
				CardId:              cast.ToInt64(in.BuyStoreCard.CardID),
				CardNo:              cardRecord.CardNo,
				CardName:            in.BuyStoreCard.CardName,
				OperateType:         "OPEN_CARD",
				Balance:             utils.Fen2Yuan(in.BuyStoreCard.Amount),
				PrincipalChange:     utils.Fen2Yuan(in.BuyStoreCard.Amount),
				BalanceChange:       utils.Fen2Yuan(in.BuyStoreCard.Amount + in.BuyStoreCard.AmountGift),
				GiftBalance:         utils.Fen2Yuan(in.BuyStoreCard.AmountGift),
				GiftChange:          utils.Fen2Yuan(in.BuyStoreCard.AmountGift),
				OrderId:             cast.ToInt64(childMain.Id), // 使用子订单ID
				OrderNo:             childOrderSn,               // 使用子订单号
				CustomerId:          cast.ToInt64(in.CalcInfo.CustomerInfo.ID),
				EmployeeId:          cast.ToInt64(in.CashierId),
				Status:              "SUCCESS",
				Remark:              "购买储值卡",
				RecordId:            cardRecord.Id,
				CreatedBy:           cast.ToInt64(in.CashierId),
				UpdatedBy:           cast.ToInt64(in.CashierId),
				ReturnableGift:      utils.Fen2Yuan(in.BuyStoreCard.AmountGift),
				ReturnablePrincipal: utils.Fen2Yuan(in.BuyStoreCard.Amount),
			}

			err = storeCard.CreateStoreCardUseRecord(session, useRecord)
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("创建储值卡使用记录失败: %v", err)
			}

		} else { // 续卡
			// 先查询现有储值卡记录
			cardRecord, err := storeCard.GetStoreCardRecord(session,
				in.BuyStoreCard.CardNo,
				cast.ToInt64(in.CalcInfo.CustomerInfo.ID))
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("查询储值卡记录失败: %v", err)
			}

			// 计算新的余额
			newPrincipal := utils.Fen2Yuan(in.BuyStoreCard.Amount)
			newGiftAmount := utils.Fen2Yuan(in.BuyStoreCard.AmountGift)
			newTotalAmount := utils.Fen2Yuan(in.BuyStoreCard.Amount) + utils.Fen2Yuan(in.BuyStoreCard.AmountGift)
			recordBalance = in.BuyStoreCard.Amount + in.BuyStoreCard.AmountGift + utils.Yuan2Fen(cardRecord.TotalAmount)
			// 更新储值卡记录
			updateRecord := &marketing_po.MStoreCardRecord{
				CardNo:      in.BuyStoreCard.CardNo,
				CustomerId:  cast.ToInt64(in.CalcInfo.CustomerInfo.ID),
				Principal:   newPrincipal,
				GiftAmount:  newGiftAmount,
				TotalAmount: newTotalAmount,
				Version:     cardRecord.Version,
			}

			err = storeCard.CreateOrUpdateStoreCardRecord(session, updateRecord, true)
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("更新储值卡记录失败: %v", err)
			}
			recordId = cardRecord.Id

			// 创建储值卡使用记录
			useRecord := &marketing_po.MStoreCardUseRecord{
				ChainId:             cast.ToInt64(in.CalcInfo.ChainID),
				TenantId:            cast.ToInt64(in.CalcInfo.TenantID),
				RecordId:            cardRecord.Id,
				CardId:              cast.ToInt64(in.BuyStoreCard.CardID),
				CardNo:              in.BuyStoreCard.CardNo,
				CardName:            in.BuyStoreCard.CardName,
				OperateType:         "TOP_UP",
				Balance:             utils.Fen2Yuan(in.BuyStoreCard.Amount + utils.Yuan2Fen(cardRecord.Principal)), // 使用计算好的新总额
				BalanceChange:       utils.Fen2Yuan(in.BuyStoreCard.Amount + in.BuyStoreCard.AmountGift),
				OrderId:             cast.ToInt64(childMain.Id),
				OrderNo:             childOrderSn,
				CustomerId:          cast.ToInt64(in.CalcInfo.CustomerInfo.ID),
				EmployeeId:          cast.ToInt64(in.CashierId),
				Status:              "SUCCESS",
				Remark:              "储值卡续费",
				CreatedBy:           cast.ToInt64(in.CashierId),
				UpdatedBy:           cast.ToInt64(in.CashierId),
				ReturnableGift:      utils.Fen2Yuan(utils.Yuan2Fen(newGiftAmount) + utils.Yuan2Fen(cardRecord.GiftAmount)), // 使用计算好的新赠送金额
				ReturnablePrincipal: utils.Fen2Yuan(in.BuyStoreCard.Amount + utils.Yuan2Fen(cardRecord.Principal)),         // 使用计算好的新本金
				PrincipalChange:     utils.Fen2Yuan(in.BuyStoreCard.Amount),
				GiftBalance:         utils.Fen2Yuan(utils.Yuan2Fen(newGiftAmount) + utils.Yuan2Fen(cardRecord.GiftAmount)), // 使用计算好的新赠送金额
				GiftChange:          utils.Fen2Yuan(in.BuyStoreCard.AmountGift),
			}

			err = storeCard.CreateStoreCardUseRecord(session, useRecord)
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("创建储值卡使用记录失败: %v", err)
			}
		}
		_, err = session.Exec("update dc_order.order_product  set sku_id = ? where order_sn = ? or order_sn=?", cast.ToString(recordId), order.OrderSn, childMain.OrderSn)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("创建次卡订单失败: %v", err)
		}
		// 创建储值卡订单
		cardOrder := &marketing_po.MStoreCardOrder{
			TenantId:       cast.ToInt64(in.CalcInfo.TenantID),
			CardInfoId:     cardInfo.Id,
			CardId:         cast.ToInt64(in.BuyStoreCard.CardID),
			CardName:       cardInfo.Name,
			CardType:       "STORE_CARD",
			RecordId:       recordId,
			OrderAmount:    utils.Fen2Yuan(in.BuyStoreCard.Amount),
			OrderId:        cast.ToInt64(childMain.Id),
			OrderNo:        childOrderSn,
			Status:         "SUCCESS",
			BizType:        "STORE_OPEN_CARD",
			Amount:         utils.Fen2Yuan(in.BuyStoreCard.Amount),
			AmountGift:     utils.Fen2Yuan(in.BuyStoreCard.AmountGift),
			CustomerId:     cast.ToInt64(in.CalcInfo.CustomerInfo.ID),
			CustomerName:   in.CalcInfo.CustomerInfo.Name,
			CustomerMobile: in.CalcInfo.CustomerInfo.Phone,
			SellerId:       cast.ToInt64(in.CashierId),
			SellerName:     in.CashierName,
			CreatedBy:      cast.ToInt64(in.CashierId),
			UpdatedBy:      cast.ToInt64(in.CashierId),
		}

		if in.BuyStoreCard.Type == "2" {
			cardOrder.BizType = "STORE_TOP_UP"
		}

		err = storeCard.CreateStoreCardOrder(session, cardOrder)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("创建储值卡订单失败: %v", err)
		}
		//储值卡充值模板和参数
		templateParam := sms.SmsRechargeParams{}
		templateParam.TenantName = order.ShopName
		templateParam.ChangeAmount = cast.ToString(cardOrder.OrderAmount)
		templateParam.BalanceAmount = fmt.Sprintf("%.2f", utils.Fen2Yuan(recordBalance))
		byt, err := json.Marshal(templateParam)
		if err != nil {
			glog.Error(templateParam, "转换短信参数失败", err.Error())
		}
		smsreq.TemplateParam = string(byt)
		smsreq.TemplateCode = omnibus_po.SmsTemplateStoreCardRecharge
	}

	// 处理次卡购买
	if in.OrderType == 22 { // 次卡订单
		timeCard := new(marketing_po.MTimeCard)

		// 查询次卡基础信息
		cardInfo, err := timeCard.GetCardInfo(session,
			cast.ToInt64(in.CalcInfo.ChainID),
			cast.ToInt64(in.CalcInfo.TenantID),
			cast.ToInt64(in.BuyTimeCard.CardID))
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("查询次卡基础信息失败: %v", err)
		}

		recordId := int64(0)
		//卡剩余次数
		smsNum := 0
		if in.BuyTimeCard.Type == "1" { // 开卡
			// 创建次卡记录
			cardRecord := &marketing_po.MTimeCardRecord{
				ChainId:        cast.ToInt(in.CalcInfo.ChainID),
				TenantId:       cast.ToInt(in.CalcInfo.TenantID),
				CardId:         cast.ToInt(in.BuyTimeCard.CardID),
				CardNo:         utils.GenerateCardNo(),
				CardName:       cardInfo.Name,
				ValidType:      cardInfo.ValidType,
				EnableTime:     time.Now(),
				ExpirationTime: cardInfo.EndTime,
				TotalNum:       cast.ToInt(in.BuyTimeCard.Num) + cast.ToInt(in.BuyTimeCard.NumGift),
				BuyNum:         cast.ToInt(in.BuyTimeCard.Num),
				GiftNum:        cast.ToInt(in.BuyTimeCard.NumGift),
				BuyingNum:      cast.ToInt(in.BuyTimeCard.Num),
				BuyingGiftNum:  cast.ToInt(in.BuyTimeCard.NumGift),
				Balance:        utils.Fen2Yuan(in.BuyTimeCard.Amount),
				Price:          utils.Fen2Yuan(in.BuyTimeCard.Amount),
				CustomerId:     cast.ToInt(in.CalcInfo.CustomerInfo.ID),
				CustomerName:   in.CalcInfo.CustomerInfo.Name,
				CustomerMobile: in.CalcInfo.CustomerInfo.Phone,
				Status:         "IN_USE",
				CreatedBy:      cast.ToInt64(in.CashierId),
				UpdatedBy:      cast.ToInt64(in.CashierId),
			}

			if cardRecord.ValidType == "VALID_DAYS" {
				cardRecord.EnableTime = time.Now()
				cardRecord.ExpirationTime = time.Now().AddDate(0, 0, cardInfo.WithinDay)
			}
			if cardRecord.ValidType == "FIXED_TIME" || cardRecord.ValidType == "DEADLINE" {
				cardRecord.EnableTime = cardInfo.StartTime
				cardRecord.ExpirationTime = cardInfo.EndTime
			}
			if cardRecord.EnableTime.After(time.Now()) {
				cardRecord.Status = "NOT_START"
			}
			if cardRecord.ValidType == "PERMANENT" {
				t := time.Date(9999, 12, 1, 31, 23, 59, 59, time.Local)
				cardRecord.ExpirationTime = t
			}
			_, err = session.Insert(cardRecord)
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("创建次卡记录失败: %v", err)
			}
			recordId = int64(cardRecord.Id)

			// 创建次卡使用记录
			useRecord := &marketing_po.MTimeCardUseRecord{
				ChainId:        cast.ToInt(in.CalcInfo.ChainID),
				TenantId:       cast.ToInt(in.CalcInfo.TenantID),
				RecordId:       cardRecord.Id,
				CardId:         cast.ToInt(in.BuyTimeCard.CardID),
				CardNo:         cardRecord.CardNo,
				CardName:       cardInfo.Name,
				OperateType:    "OPEN_CARD",
				Balance:        utils.Fen2Yuan(in.BuyTimeCard.Amount),
				BalanceChange:  utils.Fen2Yuan(in.BuyTimeCard.Amount),
				TotalChange:    cast.ToInt(in.BuyTimeCard.Num) + cast.ToInt(in.BuyTimeCard.NumGift),
				CurrentChange:  cast.ToInt(in.BuyTimeCard.Num),
				GiftChange:     cast.ToInt(in.BuyTimeCard.NumGift),
				Num:            cast.ToInt(in.BuyTimeCard.Num),
				GiftNum:        cast.ToInt(in.BuyTimeCard.NumGift),
				ReturnableBuy:  cast.ToInt(in.BuyTimeCard.Num),
				ReturnableGift: cast.ToInt(in.BuyTimeCard.NumGift),
				OrderId:        childMain.Id,
				OrderNo:        childOrderSn,
				CustomerId:     cast.ToInt(in.CalcInfo.CustomerInfo.ID),
				EmployeeId:     cast.ToInt(in.CashierId),
				Status:         "SUCCESS",
				Remark:         "购买次卡",
				CreatedBy:      cast.ToInt64(in.CashierId),
				UpdatedBy:      cast.ToInt64(in.CashierId),
			}

			err = useRecord.CreateTimeCardUseRecord(session, useRecord)
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("创建次卡使用记录失败: %v", err)
			}
			smsNum = cardRecord.TotalNum
		} else { // 续卡
			// 先查询现有次卡记录
			cardRecord, err := new(marketing_po.MTimeCardRecord).GetTimeCardByCardNo(session,
				in.BuyTimeCard.CardNo,
				cast.ToInt64(in.CalcInfo.CustomerInfo.ID))
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("查询次卡记录失败: %v", err)
			}
			recordId = int64(cardRecord.Id)

			// 更新次卡记录
			err = cardRecord.UpdateTimeCardUse(session,
				in.BuyTimeCard.CardNo,
				cast.ToInt64(in.CalcInfo.CustomerInfo.ID),
				-cast.ToInt(in.BuyTimeCard.Num), // 负数表示增加次数
				-cast.ToInt(in.BuyTimeCard.NumGift),
				-(cast.ToInt(in.BuyTimeCard.Num) + cast.ToInt(in.BuyTimeCard.NumGift)),
				-utils.Fen2Yuan(in.BuyTimeCard.Amount),
				cardRecord.Version)
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("更新次卡记录失败: %v", err)
			}

			// 创建次卡使用记录
			useRecord := &marketing_po.MTimeCardUseRecord{
				ChainId:        cast.ToInt(in.CalcInfo.ChainID),
				TenantId:       cast.ToInt(in.CalcInfo.TenantID),
				RecordId:       cardRecord.Id,
				CardId:         cast.ToInt(in.BuyTimeCard.CardID),
				CardNo:         in.BuyTimeCard.CardNo,
				CardName:       cardInfo.Name,
				OperateType:    "TOP_UP",
				Balance:        cardRecord.Balance + utils.Fen2Yuan(in.BuyTimeCard.Amount),
				BalanceChange:  utils.Fen2Yuan(in.BuyTimeCard.Amount),
				TotalChange:    cast.ToInt(in.BuyTimeCard.Num) + cast.ToInt(in.BuyTimeCard.NumGift),
				CurrentChange:  cast.ToInt(in.BuyTimeCard.Num),
				GiftChange:     cast.ToInt(in.BuyTimeCard.NumGift),
				Num:            cardRecord.BuyNum + cast.ToInt(in.BuyTimeCard.Num),
				GiftNum:        cardRecord.GiftNum + cast.ToInt(in.BuyTimeCard.NumGift),
				ReturnableBuy:  cast.ToInt(in.BuyTimeCard.Num),
				ReturnableGift: cast.ToInt(in.BuyTimeCard.NumGift),
				OrderId:        childMain.Id,
				OrderNo:        childOrderSn,
				CustomerId:     cast.ToInt(in.CalcInfo.CustomerInfo.ID),
				EmployeeId:     cast.ToInt(in.CashierId),
				Status:         "SUCCESS",
				Remark:         "次卡续费",
				CreatedBy:      cast.ToInt64(in.CashierId),
				UpdatedBy:      cast.ToInt64(in.CashierId),
			}
			smsNum = cast.ToInt(in.BuyTimeCard.Num) + cast.ToInt(in.BuyTimeCard.NumGift) + cardRecord.TotalNum
			err = useRecord.CreateTimeCardUseRecord(session, useRecord)
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("创建次卡使用记录失败: %v", err)
			}
		}

		_, err = session.Exec("update dc_order.order_product  set sku_id = ? where order_sn = ? or order_sn=?", cast.ToString(recordId), order.OrderSn, childMain.OrderSn)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("创建次卡订单失败: %v", err)
		}
		// 创建次卡订单
		cardOrder := &marketing_po.MTimeCardOrder{
			TenantId:       cast.ToInt(in.CalcInfo.TenantID),
			CardInfoId:     cast.ToInt(recordId),
			CardId:         cast.ToInt(in.BuyTimeCard.CardID),
			CardName:       cardInfo.Name,
			CardType:       "TIME_CARD",
			RecordId:       cast.ToInt(recordId),
			OrderAmount:    utils.Fen2Yuan(in.BuyTimeCard.Amount),
			OrderId:        childMain.Id,
			OrderNo:        childOrderSn,
			Status:         "SUCCESS",
			BizType:        "TIME_OPEN_CARD",
			Num:            cast.ToInt(in.BuyTimeCard.Num),
			NumGift:        cast.ToInt(in.BuyTimeCard.NumGift),
			CustomerId:     cast.ToInt(in.CalcInfo.CustomerInfo.ID),
			CustomerName:   in.CalcInfo.CustomerInfo.Name,
			CustomerMobile: in.CalcInfo.CustomerInfo.Phone,
			SellerId:       cast.ToInt64(in.CashierId),
			SellerName:     in.CashierName,
			CreatedBy:      cast.ToInt64(in.CashierId),
			UpdatedBy:      cast.ToInt64(in.CashierId),
		}

		if in.BuyTimeCard.Type == "2" {
			cardOrder.BizType = "TIME_TOP_UP"
		}

		_, err = session.Insert(cardOrder)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("创建次卡订单失败: %v", err)
		}
		//储值卡充值模板和参数
		templateParam := sms.SmsRechargeCountParams{}
		templateParam.TenantName = order.ShopName
		templateParam.ChangeAmount = cast.ToString(cardOrder.OrderAmount)
		templateParam.TotalNum = cast.ToString(smsNum)
		byt, err := json.Marshal(templateParam)
		if err != nil {
			glog.Error(templateParam, "转换短信参数失败", err.Error())
		}
		smsreq.TemplateParam = string(byt)
		smsreq.TemplateCode = omnibus_po.SmsTemplateTimeCardRecharge
	}
	isPay := false

	glog.Info("周翔打印商品数据 Commit", order, orderProducts[0])

	//进行扫码支付
	if len(in.OrderPayment) > 0 && in.OrderPayment[0].PayType == 11 {
		err = ScannerPay(orderSn, session, in)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("扫码支付失败: %v", err)
		}
		isPay = true
	}
	if in.CalcInfo.ChannelId == 1 {
		isPay = true
	}
	if isPay {
		if err := mq.PublishRabbitMQV2("order.nopay", "ordercenter", order.OrderSn, int64(15*time.Minute/time.Millisecond)); err != nil {
			glog.Error(order.OrderSn, "，阿闻到家小程序和竖屏订单加入未支付取消队列失败，", err)
		}
	} else {
		if in.OrderType == 1 || in.OrderType == 3 {
			err = s.OrderOutInventory(session, orderSn)
			if err != nil {
				session.Rollback()
				return out, fmt.Errorf("扣减库存失败: %v", err)
			}
		}
	}
	if len(in.OrderPayment) > 0 && in.OrderPayment[0].PayType == 10 && in.CalcInfo.ChannelId == 1 {
		//小程序是储值卡支付的，进行支付回调
		err = Offlinenotify(*order)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("支付回调失败: %v", err)
		}
	}
	//如果是寄养订单要走寄养流程
	if JyID != "" {
		par := offline_vo.FosterPayDTO{}
		par.FosterId = cast.ToInt64(JyID)
		par.PayType = in.YJPayType
		par.OrderId = cast.ToInt64(childMain.Id)
		par.OrderNo = childMain.OrderSn
		par.CustomerId = cast.ToInt64(in.CalcInfo.CustomerInfo.ID)
		par.CustomerName = in.CalcInfo.CustomerInfo.Name
		par.SellerId = cast.ToInt64(SellerId)
		par.SellerName = SellerName
		par.StoreId = in.CalcInfo.TenantID
		if JYMoney > 0 {
			par.Amount = utils.Fen2Yuan(JYMoney)
		}
		externalService := external_service.ForsterService{}
		err = externalService.ForsterSettle(context.Background(), &par)
		if err != nil {
			session.Rollback()
			return out, fmt.Errorf("寄养逻辑调用失败: %v", err)
		}
	}
	//发送短信
	if smsreq.TemplateCode != "" {
		go func() {
			smsService := sms.SmsService{}
			err := smsService.SendMessage(smsreq)
			if err != nil {
				log.Error("发送短信失败", smsreq, err.Error())
			}
		}()
	}

	//session.Rollback()
	session.Commit()

	// 如果是门店订单(channel_id=100)，提交成功后计算员工业绩
	if childMain.ChannelId == 100 && len(childOrderProducts) > 0 {
		// 调用封装好的异步方法
		go s.AsyncCalculateCommission(*order, childOrderProducts, in.CashierName, in.CashierId, in.CashierId, in.CashierName)
	}

	out.OrderSn = orderSn
	return
}

// 计算运费方法
func GetDeliveryMoney(in order_vo.CalcRequest, TotalWeight float64) (int, error) {

	// 构建获取配送费参数
	payReq := &order_vo.GetDeliveryMoneyRequest{
		ShopId:       in.Data.TenantID,     // 订单号
		DestinationX: in.Data.DestinationX, // 实际支付金额
		DestinationY: in.Data.DestinationY, // 订单总金额
		TotalWeight:  TotalWeight,
	}

	// 发起扫码支付请求
	payUrl := "http://127.0.0.1:7040/order-api/upetDj/GetDeliveryMoney" // 支付接口地址
	//payUrl := "https://awen.uat.rvet.cn/order-api/upetDj/GetDeliveryMoney" // 支付接口地址

	respBody, err := utils.HttpPost(payUrl, []byte(utils.JsonEncode(payReq)), "")
	if err != nil {
		//session.Rollback()
		return 0, fmt.Errorf("获取配送费失败: %v", err)
	}

	// 解析支付响应
	var payResp order_vo.PromotionCalcResponse
	if err := json.Unmarshal(respBody, &payResp); err != nil {
		//session.Rollback()
		return 0, fmt.Errorf("解析支付响应失败: %v", err)
	}

	if payResp.Code != 200 {
		return 0, fmt.Errorf("获取配送费失败" + payResp.Message)
	}
	return int(payResp.UpetDjMoneyByMinUnit), nil
}

// 扫码支付方法
func ScannerPay(orderSn string, session *xorm.Session, in order_vo.CommitRequest) error {
	epayConfig, err := new(order_po.EpayConfig).GetEpayConfig(session, cast.ToInt64(in.CalcInfo.TenantID), "WX_MINI")
	if err != nil {
		//session.Rollback()
		return fmt.Errorf("查询支付配置失败: %v", err)
	}
	NotifyUrl := config.GetString("pay-center-notifyurl")
	//测试用
	//NotifyUrl = "https://awen.uat.rvet.cn/order-api/order/offlinenotify"
	// 构建扫码支付请求参数
	payReq := &utils.Pay{
		OrderNo:     orderSn,                              // 订单号
		PayAmount:   cast.ToInt64(in.OrderDiscountAmount), // 实际支付金额
		PayTotal:    cast.ToInt64(in.OrderDiscountAmount), // 订单总金额
		OrderName:   "门店收银-" + orderSn,                    // 订单名称
		ProductDesc: "门店收银付款",                             // 订单描述
		AppId:       7,                                    // SAAS应用
		TransType:   20,                                   // B扫C（标准）
		BarCode:     in.OrderPayment[0].BarCode,           // 付款码
		MerchantId:  epayConfig.AppMerchantID,             // 商户号
		ClientIP:    "127.0.0.1",                          // 客户端IP
		ValidTime:   300,                                  // 订单有效期(分钟)
		Timestamp:   time.Now().UnixMilli(),               // 时间戳
		NotifyURL:   NotifyUrl,                            // 回调地址
	}
	jsonBytes, err := json.Marshal(payReq)
	if err != nil {
		return fmt.Errorf("序列化支付请求失败: %v", err)
	}
	// 生成签名
	sign, err := utils.MakeStandardPaySign(jsonBytes, payReq.AppId) // 添加第二个参数 1
	if err != nil {
		//session.Rollback()
		return fmt.Errorf("生成支付签名失败: %v", err)
	}
	payReq.Sign = sign

	// 发起扫码支付请求
	payUrl := "http://127.0.0.1:7035/pay/pay-api/pay" // 支付接口地址
	respBody, err := utils.HttpPost(payUrl, []byte(utils.JsonEncode(payReq)), "")
	if err != nil {
		//session.Rollback()
		return fmt.Errorf("发起扫码支付请求失败: %v", err)
	}

	// 解析支付响应
	var payResp order_vo.StandardPayResponse
	if err := json.Unmarshal(respBody, &payResp); err != nil {
		//session.Rollback()
		return fmt.Errorf("解析支付响应失败: %v", err)
	}
	if payResp.Code != 200 {
		return errors.New(payResp.Message)
	}

	//// 更新订单支付信息
	//updateSQL := `UPDATE dc_order.order_main SET
	//		pay_sn = ?,
	//		pay_time = ?,
	//			trade_no = ?
	//			WHERE order_sn = ?`
	//
	//_, err = session.Exec(updateSQL,
	//	payResp.ThirdPayNo, // 第三方支付流水号
	//	payResp.PayTime,    // 支付时间
	//	payResp.TradeNo,    // 支付中心订单号
	//	orderSn)

	// if err != nil {
	// 	//session.Rollback()
	// 	return fmt.Errorf("更新订单支付信息失败: %v", err)
	// }
	return nil
}

// 购物车计算
func (s ShopOrderService) Calc(in order_vo.CalcRequest) (out order_vo.CalcResponse, err error) {

	logPrefix := fmt.Sprintf("购物车计算数据====，入参：%+v", in)
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	//所有商品SKUID
	var SkuId []int
	//所有商品分类ID
	var CategoryID []int
	//特价商品SKUID
	var SpecialSkuId []int
	//赠品SKUID
	var SendSkuId []int
	//计算改价金额
	var UpdateAmount int
	//特价商品优惠金额
	var SpeciaAmount int
	//赠送优惠金额
	var SendAmount int
	//计算优惠卷金额
	var discountAmount int
	//满减优惠金额
	var discountThresholdAmount int
	// 在其他变量声明后添加
	var maxDiscountAmount int
	var maxDiscountActivity *marketing_po.MarketingActivityInfo
	in.Data.PromotionRecords = make([]order_po.OrderPromotionRecord, 0)
	in.Data.DiscountInfo = make([]order_vo.DiscountInfo, 0)
	//先把入参的结构复制给出参
	out.CalcInfo = in.Data
	out.CalcInfo.Amount = 0

	//商品总重量
	SumWeight := 0
	//优惠前总金额
	SumTotal := 0

	//查询商品信息 FindStoreSkuList
	var storeProductService product_service.StoreProductService
	storeSkuListReq := product_vo.FindStoreSkuListReq{
		StoreId:   in.Data.TenantID,
		ChannelId: in.Data.ChannelId,
		SkuIds:    SkuId,
	}

	productList, _, err := storeProductService.FindStoreSkuList(storeSkuListReq)
	if err != nil {
		return out, fmt.Errorf("查询商品信息失败: %v", err)
	}
	// 创建map存储商品信息,以skuId为key
	productMap := make(map[int]po.ProSku)
	for _, product := range productList {
		for _, sku := range product.Sku {
			productMap[sku.BaseSku.Id] = sku.BaseSku
		}
	}

	//计算商品总重量
	for _, x := range in.Data.Details {
		if sku, ok := productMap[cast.ToInt(x.SkuID)]; ok {
			SumWeight += utils.Yuan2Fen(sku.WeightForUnit) * x.TotalBuyCount
		}
		SumTotal += x.TotalBuyCount * x.RetailPrice
	}
	out.CalcInfo.TotalWeight = utils.Fen2Yuan(SumWeight)

	freight := 0
	//计算配送费
	if in.Data.ChannelId == 1 && in.OrderType != 3 {
		freightItem, err := GetDeliveryMoney(in, out.CalcInfo.TotalWeight)
		if err != nil {
			return out, fmt.Errorf("获取配送费失败: %v", err)
		}
		freight = freightItem
		out.CalcInfo.Freight = freightItem
	}

	// 在 Calc 方法开始处定义优惠记录切片
	var promotions []order_po.OrderPromotion
	var promotionRecords []order_po.OrderPromotionRecord

	// 1. 先计算次卡优惠
	var timeCardAmount int
	timeCardQuery := marketing_po.TimeCardQuery{
		StoreId:    in.Data.TenantID,
		ChainId:    cast.ToInt64(in.Data.ChainID),
		CustomerId: cast.ToInt(in.Data.CustomerInfo.ID),
	}

	// 收集需要查询的分类ID
	var SevSkuid []int
	for i, detail := range in.Data.Details {
		out.CalcInfo.Details[i].DeductionBuyCount = detail.TotalBuyCount
		out.CalcInfo.Details[i].ItemAmount = detail.DiscountPrice
		out.CalcInfo.Details[i].ItemSubtotalAmount = detail.DiscountPrice * detail.TotalBuyCount
		if detail.ProductType == 4 {
			SevSkuid = append(SevSkuid, cast.ToInt(detail.SkuID))
		}
		if detail.BuyType == 1 {
			out.CalcInfo.Details[i].SubtotalAmount = detail.DiscountPrice * detail.TotalBuyCount
			out.CalcInfo.Details[i].ItemSubtotalAmount = detail.DiscountPrice * detail.TotalBuyCount
		}
	}
	timeCardQuery.SkuIds = SevSkuid

	if len(SevSkuid) > 0 {
		// 获取次卡信息
		timeCards, err := new(marketing_po.MTimeCard).GetTimeCardInfo(s.Engine.NewSession(), timeCardQuery)
		if err != nil {
			return out, fmt.Errorf("查询次卡信息失败: %v", err)
		}
		// 检查次卡抵扣次数是否超过购买数量
		for _, detail := range in.Data.Details {
			var totalDeductNum int
			for _, buyType := range detail.BuyTypeList {
				totalDeductNum += buyType.Num
			}
			if totalDeductNum > detail.TotalBuyCount {
				return out, fmt.Errorf("次卡抵扣数量不能超过总明细购买数量，商品:%s", detail.ProductName)
			}
		}

		// 构建次卡映射
		cardMap := make(map[string]marketing_po.TimeCardInfo)
		for _, card := range timeCards {
			cardMap[cast.ToString(card.CardNo)] = card
		}

		// 遍历订单明细，计算次卡优惠
		for i, detail := range out.CalcInfo.Details {
			// 先找出所有可用的次卡
			var availableCards []marketing_po.TimeCardInfo
			for _, card := range timeCards {
				canUse := false
				for _, product := range card.ApplyProducts {
					if product.ProductRefId == cast.ToInt(detail.SkuID) {
						canUse = true
						break
					}
				}

				if canUse && card.CanUse && (card.TotalNum) > 0 {
					availableCards = append(availableCards, card)
				}
			}

			// 如果BuyTypeList为空,则需要初始化
			if len(detail.BuyTypeList) == 0 {
				for _, card := range availableCards {
					buyType := struct {
						CardName  string `json:"card_name"`
						CardID    string `json:"card_id"`
						CardNo    string `json:"card_no"`
						Num       int    `json:"num"`
						UnusedNum int    `json:"unused_num"`
					}{
						CardName:  card.TimeCard.CardName,
						CardID:    cast.ToString(card.TimeCard.Id),
						CardNo:    card.CardNo,
						Num:       0,
						UnusedNum: card.TotalNum,
					}
					out.CalcInfo.Details[i].BuyTypeList = append(out.CalcInfo.Details[i].BuyTypeList, buyType)
				}
			} else {
				// BuyTypeList已存在,检查是否需要补充
				existingCards := make(map[string]bool)
				for _, buyType := range detail.BuyTypeList {
					existingCards[buyType.CardNo] = true
				}

				// 补充缺失的卡
				for _, card := range availableCards {
					if !existingCards[card.CardNo] {
						buyType := struct {
							CardName  string `json:"card_name"`
							CardID    string `json:"card_id"`
							CardNo    string `json:"card_no"`
							Num       int    `json:"num"`
							UnusedNum int    `json:"unused_num"`
						}{
							CardName:  card.TimeCard.CardName,
							CardID:    cast.ToString(card.TimeCard.Id),
							CardNo:    card.CardNo,
							Num:       0,
							UnusedNum: card.TotalNum,
						}
						out.CalcInfo.Details[i].BuyTypeList = append(out.CalcInfo.Details[i].BuyTypeList, buyType)
					}
				}
			}
		}
		// 遍历订单明细，计算次卡优惠
		for i, detail := range out.CalcInfo.Details {
			// 处理已有的BuyTypeList
			for j, buyType := range detail.BuyTypeList {
				if card, ok := cardMap[buyType.CardNo]; ok {
					canUse := false
					for _, product := range card.ApplyProducts {
						if product.ProductRefId == cast.ToInt(detail.SkuID) {
							canUse = true
							break
						}
					}

					if !canUse || !card.CanUse {
						out.CalcInfo.Details[i].BuyTypeList[j].Num = 0
						continue
					}

					availableNum := card.TotalNum
					if buyType.Num > availableNum {
						out.CalcInfo.Details[i].BuyTypeList[j].Num = availableNum
						buyType.Num = availableNum
					}

					if buyType.Num > 0 {
						TimeDiscountAmount := detail.RetailPrice * buyType.Num
						timeCardAmount += TimeDiscountAmount

						out.CalcInfo.Details[i].DeductionBuyCount = out.CalcInfo.Details[i].DeductionBuyCount - buyType.Num
						if detail.DeductionBuyCount > 0 {
							out.CalcInfo.Details[i].SubtotalAmount = detail.RetailPrice * (detail.TotalBuyCount - buyType.Num)
							out.CalcInfo.Details[i].ItemSubtotalAmount = detail.RetailPrice * (detail.TotalBuyCount - buyType.Num)
						} else {
							out.CalcInfo.Details[i].SubtotalAmount = 0
							out.CalcInfo.Details[i].ItemSubtotalAmount = 0
						}

						//添加SKU级别的优惠信息记录
						record := order_po.OrderPromotionRecord{
							ID:             "",
							OrderSn:        "",
							OrderDcId:      cast.ToString(detail.ItemID),
							Sku:            cast.ToString(detail.SkuID),
							Promotion:      detail.RetailPrice * buyType.Num,
							SkuPromotion:   detail.RetailPrice,
							Num:            buyType.Num,
							PromotionId:    0,
							PromotionType:  6,
							PromotionTitle: "次卡抵扣",
							CardNo:         card.CardNo,
						}
						promotionRecords = append(promotionRecords, record)

					}
				}
			}
		}
	}

	// 添加次卡优惠信息
	if timeCardAmount > 0 {
		item := order_vo.DiscountInfo{
			DiscountType:  "6",
			DiscountName:  "次卡抵扣",
			DiscountMoney: timeCardAmount,
		}
		out.CalcInfo.DiscountInfo = append(out.CalcInfo.DiscountInfo, item)
	}
	mapCheck := make(map[string]int)
	//找出各种特殊商品
	for _, x := range in.Data.Details {
		SkuId = append(SkuId, cast.ToInt(x.SkuID))
		CategoryID = append(CategoryID, cast.ToInt(x.SkuID))
		//特价商品
		if x.BuyType == 3 {
			SpecialSkuId = append(SpecialSkuId, cast.ToInt(x.SkuID))
		}
		//赠品
		if x.BuyType == 2 {
			if mapCheck[cast.ToString(x.SkuID)] == 0 {
				SendSkuId = append(SendSkuId, cast.ToInt(x.SkuID))
				mapCheck[cast.ToString(x.SkuID)] = 1
			}
		}

		//改价
		if x.IsEdit {
			NowAmount := (x.RetailPrice - x.DiscountPrice) * x.TotalBuyCount
			UpdateAmount += NowAmount
			//添加SKU级别的优惠信息记录
			record := order_po.OrderPromotionRecord{
				ID:             "",
				OrderSn:        "",
				OrderDcId:      cast.ToString(x.ItemID),
				Sku:            cast.ToString(x.SkuID),
				Promotion:      NowAmount,
				SkuPromotion:   x.RetailPrice - x.DiscountPrice,
				Num:            x.TotalBuyCount,
				PromotionId:    0,
				PromotionType:  1,
				PromotionTitle: "商品改价",
				CardNo:         "",
			}
			promotionRecords = append(promotionRecords, record)
		}
		out.CalcInfo.Amount += x.RetailPrice * x.TotalBuyCount
	}
	//说明有改价的，记录改价金额
	if UpdateAmount > 0 {
		item := order_vo.DiscountInfo{}
		item.DiscountType = "1"
		item.DiscountName = "商品改价"
		item.DiscountMoney = UpdateAmount
		out.CalcInfo.DiscountInfo = append(out.CalcInfo.DiscountInfo, item)
	}

	//赠品是否存在
	if len(SendSkuId) > 0 {
		var GiftService services.GiftService
		param := marketing_vo.GiftListReq{}
		param.StoreId = in.Data.TenantID
		param.SkuIds = SendSkuId
		GifList, err := GiftService.GiftList(&param)
		if err != nil {
			return out, errors.New("查询赠送商品失败:" + err.Error())
		}
		// 构建赠品SKU映射
		mapsku := make(map[int]bool)
		for _, gift := range GifList {
			mapsku[gift.SkuId] = true
		}
		if len(GifList) > 0 {
			for i, detail := range out.CalcInfo.Details {
				if detail.BuyType == 2 {
					if mapsku[detail.SkuID] {

						SendAmount += detail.TotalBuyCount * detail.RetailPrice
						//添加SKU级别的优惠信息记录
						record := order_po.OrderPromotionRecord{
							ID:             "",
							OrderSn:        "",
							OrderDcId:      cast.ToString(detail.ItemID),
							Sku:            cast.ToString(detail.SkuID),
							Promotion:      detail.RetailPrice * detail.TotalBuyCount,
							SkuPromotion:   detail.RetailPrice,
							Num:            detail.TotalBuyCount,
							PromotionId:    0,
							PromotionType:  7,
							PromotionTitle: "赠品",
							CardNo:         "",
						}
						promotionRecords = append(promotionRecords, record)
						//break
					} else {
						//赠品不存在，改成普通商品,普通商品需要把各种值赋值
						out.CalcInfo.Details[i].BuyType = 1
						out.CalcInfo.Details[i].DiscountPrice = detail.RetailPrice
						out.CalcInfo.Details[i].ItemAmount = detail.RetailPrice
						out.CalcInfo.Details[i].ItemSubtotalAmount = detail.RetailPrice * detail.TotalBuyCount
						//如果是提交订单校验数据，不主动修复数据
						if in.IsCommit == 1 {
							return out, fmt.Errorf("该商品不是赠品，商品:%s", detail.ProductName)
						}
					}
				}

			}
			//说明有改价的，记录改价金额
			if SendAmount > 0 {
				item := order_vo.DiscountInfo{}
				item.DiscountType = "7"
				item.DiscountName = "赠品"
				item.DiscountMoney = SendAmount
				out.CalcInfo.DiscountInfo = append(out.CalcInfo.DiscountInfo, item)
			}

		}
	}

	//特价商品计算
	if len(SpecialSkuId) > 0 {
		var ActivityService services.ActivityService
		//特价商品是否存在，价格多少
		req := marketing_vo.RunningActivityProductsReq{}
		req.StoreId = in.Data.TenantID
		req.ChainId = cast.ToInt64(in.Data.ChainID)

		// 将int数组转为逗号分隔的字符串
		skuIdStr := ""
		for i, id := range SpecialSkuId {
			if i > 0 {
				skuIdStr += ","
			}
			skuIdStr += cast.ToString(id)
		}
		req.SkuIds = skuIdStr

		SpecialList, _, err := ActivityService.GetRunningActivityProducts(&req)
		if err != nil {
			return out, errors.New("查询特价商品失败:" + err.Error())
		}
		//说明有特价商品
		if len(SpecialList) > 0 {
			//遍历订单中的特价商品
			for i, detail := range out.CalcInfo.Details {
				if detail.BuyType == 3 {
					//查找对应的特价活动商品
					for _, special := range SpecialList {
						if cast.ToInt(detail.SkuID) == special.SkuId {
							//固定价格
							itemSpeciaAmount := 0
							if special.ActivityDetail.TypeClass == 1 {
								// 查找对应SKUID的营销商品数据
								activityPrice := special.ActivityPrice
								retailPrice := detail.RetailPrice
								out.CalcInfo.Details[i].DiscountPrice = activityPrice
								out.CalcInfo.Details[i].ItemAmount = activityPrice
								out.CalcInfo.Details[i].SubtotalAmount = activityPrice * detail.TotalBuyCount
								//out.Data.Details[i].DiscountRate = activityPrice / retailPrice
								SpeciaAmount += (retailPrice - activityPrice) * detail.TotalBuyCount
								itemSpeciaAmount = (retailPrice - activityPrice) * detail.TotalBuyCount
							}
							//打折
							if special.ActivityDetail.TypeClass == 2 {
								retailPrice := detail.RetailPrice
								discountRate := cast.ToInt(special.DiscountRate * 10)
								discountPrice := cast.ToInt(retailPrice*discountRate) / 100
								out.CalcInfo.Details[i].DiscountPrice = discountPrice
								out.CalcInfo.Details[i].ItemAmount = discountPrice
								out.CalcInfo.Details[i].SubtotalAmount = discountPrice * detail.TotalBuyCount
								out.CalcInfo.Details[i].DiscountRate = special.DiscountRate
								SpeciaAmount += (retailPrice - discountPrice) * detail.TotalBuyCount
								itemSpeciaAmount = (retailPrice - discountPrice) * detail.TotalBuyCount
							}
							//添加SKU级别的优惠信息记录
							record := order_po.OrderPromotionRecord{
								ID:             "",
								OrderSn:        "",
								OrderDcId:      cast.ToString(detail.ItemID),
								Sku:            cast.ToString(detail.SkuID),
								Promotion:      itemSpeciaAmount,
								SkuPromotion:   itemSpeciaAmount / detail.TotalBuyCount,
								Num:            detail.TotalBuyCount,
								PromotionId:    special.ActivityDetail.Id,
								PromotionType:  2,
								PromotionTitle: special.ActivityDetail.Name,
								CardNo:         "",
							}
							promotionRecords = append(promotionRecords, record)
							break
						}
					}
				}
			}

		}

		//有特价商品记录特价商品优惠金额
		if SpeciaAmount > 0 {
			item := order_vo.DiscountInfo{}
			item.DiscountType = "2"
			item.DiscountName = "特价商品"
			item.DiscountMoney = SpeciaAmount
			out.CalcInfo.DiscountInfo = append(out.CalcInfo.DiscountInfo, item)
		}
	}

	// 在计算满减之前，添加储值卡计算逻辑
	var storeCardAmount int
	var storeCard *marketing_po.StoreCardInfo
	if len(in.Data.CalculateStoreCard.CardNo) > 0 && in.Data.ChannelId != 1 {
		storeCardQuery := marketing_po.StoreCardQuery{
			StoreId:     in.Data.TenantID,
			ChainId:     cast.ToInt64(in.Data.ChainID),
			CustomerId:  cast.ToInt64(in.Data.CustomerInfo.ID),
			CardNo:      in.Data.CalculateStoreCard.CardNo,
			SkuIds:      SkuId,
			CategoryIds: CategoryID,
		}

		// 获取储值卡信息
		var err error
		storeCard, err = new(marketing_po.MStoreCard).GetStoreCardInfo(session, storeCardQuery)
		if err != nil {
			return out, fmt.Errorf("查询储值卡信息失败: %v", err)
		}

		//儲值卡活動計算
		if storeCard != nil {
			// 遍历订单明细，计算储值卡优惠
			for i, detail := range out.CalcInfo.Details {
				// 跳过特价商品
				if detail.BuyType == 3 {
					continue
				}
				// 跳过赠品
				if detail.BuyType == 2 {
					continue
				}

				// 获取实际参与优惠的数量（总数量减去次卡抵扣的数量）
				availableCount := detail.TotalBuyCount
				for _, buyType := range detail.BuyTypeList {
					availableCount -= buyType.Num
				}
				if availableCount <= 0 {
					continue
				}

				// 查找商品对应的折扣规则
				var discount float64 = 100 // 默认不打折
				for _, cardDiscount := range storeCard.Discounts {

					if detail.ProductType == 1 { //普通商品
						discount = cardDiscount.ProductDiscount
					} else if detail.ProductType == 4 { //服务
						discount = cardDiscount.ServiceDiscount
					} else if detail.ProductType == 6 && detail.IsCanDis == 1 { //寄养
						discount = storeCard.StoreCard.FosterDiscount
					}

					//不限制
					if storeCard.StoreCard.DiscountRange == "UN_LIMIT" {
						break
					}

					// 按商品ID匹配
					if cardDiscount.RefId == cast.ToInt64(detail.SkuID) && storeCard.StoreCard.DiscountRange == "PRODUCT" {
						break
					}
					// 按分类ID匹配
					if cardDiscount.RefId == cast.ToInt64(detail.ProductCategoryID) && storeCard.StoreCard.DiscountRange == "PRODUCT_CATEGORY" {
						break
					}
					discount = 100
				}

				// 计算优惠金额
				if discount < 100 {
					originalAmount := detail.ItemAmount
					discountAmountItem := int(float64(originalAmount) * (1 - discount/100))
					out.CalcInfo.Details[i].ItemAmount = detail.ItemAmount - discountAmountItem
					out.CalcInfo.Details[i].ItemSubtotalAmount = (detail.ItemAmount - discountAmountItem) * detail.DeductionBuyCount
					if discountAmountItem > 0 {
						storeCardAmount += discountAmountItem * detail.DeductionBuyCount
						// 更新商品的优惠后金额
						//out.CalcInfo.Details[i].SubtotalAmount = detail.DiscountPrice*availableCount - discountAmountItem
						//添加SKU级别的优惠信息记录
						record := order_po.OrderPromotionRecord{
							ID:             "",
							OrderSn:        "",
							OrderDcId:      cast.ToString(detail.ItemID),
							Sku:            cast.ToString(detail.SkuID),
							Promotion:      detail.DeductionBuyCount * discountAmountItem,
							SkuPromotion:   discountAmountItem,
							Num:            detail.DeductionBuyCount,
							PromotionId:    0,
							PromotionType:  3,
							PromotionTitle: storeCard.CardName,
							CardNo:         storeCard.CardNo,
						}
						promotionRecords = append(promotionRecords, record)
					}
				}
			}

			// 添加储值卡优惠信息
			if storeCardAmount > 0 {
				item := order_vo.DiscountInfo{
					DiscountType:  "3", // 储值卡类型
					DiscountName:  storeCard.CardName,
					DiscountMoney: storeCardAmount,
				}
				out.CalcInfo.DiscountInfo = append(out.CalcInfo.DiscountInfo, item)
			}
		}

	}

	// 满减查询，计算满减
	activityQuery := marketing_po.RunningActivityProductsQuery{
		ChainId: cast.ToInt64(in.Data.ChainID),
		StoreId: in.Data.TenantID,
		Type:    4,
	}
	activityList, _, err := new(marketing_po.MarketingActivity).GetRunningActivityThreshold(session, activityQuery)

	if len(activityList) > 0 {
		//// 遍历所有满减活动,找出最大优惠金额
		//var maxDiscountAmount int
		//var maxDiscountActivity *marketing_po.MarketingActivityInfo
		activitySkuMap := make(map[int][]int)
		for _, activity := range activityList {
			// 计算当前订单总金额(元)
			var orderAmount float64
			for _, detail := range out.CalcInfo.Details {
				// 赠品和寄养不参与满减计算
				if detail.BuyType == 2 || detail.ProductType == 6 {
					continue
				}
				//如果是在排除商品中的就跳过
				for _, product := range activity.MarketingProducts {
					if cast.ToInt(detail.SkuID) == product.SkuId && product.ApplyType == 3 {

						continue
					}
				}
				// 如果是指定商品参与满减,检查商品是否在活动商品中
				for _, product := range activity.MarketingProducts {

					//满减默认按购买数量算
					num := detail.TotalBuyCount
					DeductionNum := 0
					for _, x := range detail.BuyTypeList {
						DeductionNum += x.Num
					}

					if DeductionNum > 0 {
						num = detail.DeductionBuyCount
					}
					if product.ApplyType == 1 {
						//按折后价算满减价格
						orderAmount += cast.ToFloat64(detail.ItemAmount*num) / 100
						//记录参与满减的商品
						activitySkuMap[activity.Id] = append(activitySkuMap[activity.Id], detail.SkuID)
						break
					}
					//按分类算
					if cast.ToInt(detail.ProductCategoryID) == product.ProductRefId && product.ApplyType == 2 {
						//按折后价算满减价格
						orderAmount += cast.ToFloat64(detail.ItemAmount*num) / 100
						//记录参与满减的商品
						activitySkuMap[activity.Id] = append(activitySkuMap[activity.Id], detail.SkuID)
						break
					}
					//按商品算
					if cast.ToInt(detail.SkuID) == product.ProductRefId && product.ApplyType == 1 {
						//按折后价算满减价格
						orderAmount += cast.ToFloat64(detail.ItemAmount*num) / 100
						//记录参与满减的商品
						activitySkuMap[activity.Id] = append(activitySkuMap[activity.Id], detail.SkuID)
						break
					}
				}
			}

			// 遍历满减规则,找出最大优惠金额
			for _, rule := range activity.FullReductionRules {
				thresholdAmount := cast.ToFloat64(rule.ThresholdAmount)
				if orderAmount >= thresholdAmount {
					discountAmount1 := cast.ToInt(cast.ToFloat64(rule.DiscountAmount) * 100)
					if discountAmount1 > maxDiscountAmount {
						maxDiscountAmount = discountAmount1
						maxDiscountActivity = &activity
					}
				}
			}
		}

		// 满减优惠分摊
		if maxDiscountAmount > 0 && maxDiscountActivity != nil {
			// 获取当前活动的商品列表
			activitySkus := activitySkuMap[maxDiscountActivity.Id]
			discountThresholdAmount = maxDiscountAmount
			// 使用 allocateDiscount 计算每个商品的优惠分摊
			records := allocateDiscount(out.CalcInfo.Details, maxDiscountAmount, true, func(detail order_vo.OrderDetail) bool {
				// 检查商品是否在活动商品列表中且不是赠品且未被次卡抵扣完
				if detail.BuyType == 2 || detail.DeductionBuyCount <= 0 {
					return false
				}
				// 检查商品是否在活动商品列表中
				for _, skuID := range activitySkus {
					if skuID == detail.SkuID {
						return true
					}
				}
				return false
			})

			// 补充优惠记录信息
			for i := range records {

				// 查找对应的商品明细
				for x, detail := range out.CalcInfo.Details {
					if detail.ItemID == records[i].OrderDcId {
						// 更新 ItemAmount，减去优惠分摊金额
						out.CalcInfo.Details[x].ItemAmount -= records[i].SkuPromotion
						out.CalcInfo.Details[x].ItemSubtotalAmount -= records[i].Promotion
						// 补充优惠记录信息
						records[i].OrderDcId = cast.ToString(detail.ItemID)
						records[i].PromotionId = maxDiscountActivity.Id
						records[i].PromotionType = 4
						records[i].PromotionTitle = maxDiscountActivity.Name
						break
					}
				}
			}
			promotionRecords = append(promotionRecords, records...)

		}

		// 如果有满足条件的满减活动
		if maxDiscountAmount > 0 {
			item := order_vo.DiscountInfo{
				DiscountType:  "4", // 满减类型
				DiscountName:  maxDiscountActivity.Name,
				DiscountMoney: maxDiscountAmount,
			}
			out.CalcInfo.DiscountInfo = append(out.CalcInfo.DiscountInfo, item)

		}
	}

	//查询优惠卷是否存在，并且获取详细信息
	if len(in.Data.Coupons) > 0 {
		param := marketing_vo.UserCouponDetailReq{}
		param.StoreId = in.Data.TenantID
		param.CustomerId = in.Data.CustomerInfo.ID
		for i := 0; i < len(in.Data.Coupons); i++ {
			param.Code = in.Data.Coupons[i].Code
			param.CouponID = in.Data.Coupons[i].CouponID
			var CouponService services.CouponService
			data, err := CouponService.GetUserCouponDetail(&param)
			if err != nil && err.Error() != "优惠券不存在" {
				return out, errors.New("查询优惠卷失败:" + err.Error())
			}
			//查询到了优惠卷数据
			if data != nil {
				//计算可用商品总金额
				var totalAmount float64
				for _, detail := range out.CalcInfo.Details {
					//全部商品可用
					if data.ApplyProduct == 1 {
						totalAmount += cast.ToFloat64(detail.ItemAmount * detail.DeductionBuyCount)
					}
					//指定商品可用
					if data.ApplyProduct == 2 {
						for _, product := range data.Products {
							if product.SkuId == detail.SkuID {
								totalAmount += cast.ToFloat64(detail.ItemAmount * detail.DeductionBuyCount)
							}
						}
					}
					//指定商品不可用
					if data.ApplyProduct == 3 {
						canUse := true
						for _, product := range data.Products {
							if product.SkuId == detail.SkuID {
								canUse = false
								break
							}
						}
						if canUse {
							totalAmount += cast.ToFloat64(detail.ItemAmount * detail.DeductionBuyCount)

						}
					}
				}
				//计算优惠金额
				//var discountAmount float64
				if (data.Threshold > 0 && totalAmount >= cast.ToFloat64(data.Threshold*100)) || (data.Threshold == 0 && totalAmount > 0) {
					//满减券
					if data.Type == 1 {

						discountAmount = utils.Yuan2Fen(data.Discount)
					}
					//折扣券
					if data.Type == 2 {
						//计算折扣金额
						discountAmount = cast.ToInt(totalAmount * ((10 - cast.ToFloat64(data.Discount)) / 10))
						//如果有最高优惠限制
						if data.BestOffer > 0 && discountAmount > utils.Yuan2Fen(data.BestOffer) {
							discountAmount = utils.Yuan2Fen(data.BestOffer)
						}
					}
				}
				//临时优惠
				itemYH := discountThresholdAmount + UpdateAmount + SpeciaAmount + SendAmount + timeCardAmount + storeCardAmount
				if out.CalcInfo.Amount-itemYH <= discountAmount {
					discountAmount = out.CalcInfo.Amount - itemYH - 1
				}

				// 在优惠券计算完成后添加记录
				if discountAmount > 0 && data != nil {
					// 使用 allocateDiscount 计算每个商品的优惠分摊
					records := allocateDiscount(out.CalcInfo.Details, discountAmount, true, func(detail order_vo.OrderDetail) bool {
						return canUseCoupon(detail, data)
					})

					// 补充其他字段并更新商品金额
					for i := range records {
						records[i].OrderSn = ""
						//records[i].Promotion = discountAmount
						records[i].PromotionId = data.Id
						records[i].PromotionType = 5
						records[i].PromotionTitle = data.Name
						records[i].CardNo = in.Data.Coupons[0].Code

						// 更新对应商品的 ItemAmount
						ItemID := records[i].OrderDcId
						for j := range out.CalcInfo.Details {
							if out.CalcInfo.Details[j].ItemID == ItemID {
								// 减去优惠分摊金额
								out.CalcInfo.Details[j].ItemAmount -= records[i].SkuPromotion
								out.CalcInfo.Details[j].ItemSubtotalAmount -= records[i].Promotion
								// 更新小计金额
								break
							}
						}
					}
					promotionRecords = append(promotionRecords, records...)

					// 添加储值卡优惠信息
					if discountAmount > 0 {
						item := order_vo.DiscountInfo{
							DiscountType:  "5", // 优惠卷
							DiscountName:  data.Name,
							DiscountMoney: discountAmount,
						}
						out.CalcInfo.DiscountInfo = append(out.CalcInfo.DiscountInfo, item)
					}
				}
			}
		}
	}
	//优惠卷没使用的话清除掉
	if discountAmount == 0 {
		out.CalcInfo.Coupons = make([]order_vo.CouponInfo, 0)
	}

	// 3. 最终计算总优惠和结算金额
	SumPromotionAmount := discountThresholdAmount + UpdateAmount + SpeciaAmount + SendAmount + discountAmount + timeCardAmount + storeCardAmount
	out.CalcInfo.SettlementAmount = out.CalcInfo.Amount - SumPromotionAmount + freight
	out.CalcInfo.PromotionAmount = SumPromotionAmount

	// 将优惠记录添加到返回结构体
	out.CalcInfo.Promotions = promotions
	out.CalcInfo.PromotionRecords = promotionRecords

	return out, err
}

// 辅助函数：判断商品是否可用优惠券
func canUseCoupon(detail order_vo.OrderDetail, coupon *marketing_vo.CouponDetailResp) bool {
	if detail.BuyType == 2 { // 跳过赠品
		return false
	}

	switch coupon.ApplyProduct {
	case 1: // 全部商品可用
		return true
	case 2: // 指定商品可用
		for _, product := range coupon.Products {
			if product.SkuId == detail.SkuID {
				return true
			}
		}
		return false
	case 3: // 指定商品不可用
		for _, product := range coupon.Products {
			if product.SkuId == detail.SkuID {
				return false
			}
		}
		return true
	}
	return false
}

// 按商品分摊优惠金额的辅助函数   isJy 是否要计算寄养项目的
func allocateDiscount(details []order_vo.OrderDetail, totalDiscount int, isJy bool, filterFunc func(detail order_vo.OrderDetail) bool) []order_po.OrderPromotionRecord {
	var records []order_po.OrderPromotionRecord

	// 计算可参与优惠的商品总金额
	totalAmount := 0
	var validDetails []order_vo.OrderDetail
	for _, detail := range details {
		// 排除次卡抵扣和赠品
		if detail.BuyType == 2 { // 跳过赠品
			continue
		}
		//如果是寄养，并且不计算优惠的跳过
		if detail.ProductType == 6 && !isJy { // 跳过赠品
			continue
		}

		// 排除已经被次卡抵扣的部分
		if detail.DeductionBuyCount <= 0 {
			continue
		}

		if filterFunc(detail) {
			// 使用 DeductionBuyCount 而不是 TotalBuyCount
			itemAmount := detail.ItemAmount * detail.DeductionBuyCount
			totalAmount += detail.ItemSubtotalAmount

			// 创建新的 detail 副本，避免修改原始数据
			detailCopy := detail
			detailCopy.ItemAmount = itemAmount
			detailCopy.TotalBuyCount = detail.DeductionBuyCount
			validDetails = append(validDetails, detailCopy)
		}
	}

	if len(validDetails) == 0 || totalAmount == 0 {
		return records
	}

	// 记录已分配的优惠金额
	allocatedDiscount := 0

	// 为除最后一个商品外的所有商品分配优惠
	for i := 0; i < len(validDetails)-1; i++ {
		detail := validDetails[i]
		// 按比例计算优惠金额
		Promotion := int(float64(totalDiscount) * float64(detail.ItemAmount) / float64(totalAmount))
		allocatedDiscount += Promotion
		// 计算单个商品的优惠金额,向下取整
		SkuPromotion := Promotion / detail.TotalBuyCount
		record := order_po.OrderPromotionRecord{
			OrderDcId:    cast.ToString(detail.ItemID),
			Sku:          cast.ToString(detail.SkuID),
			SkuPromotion: SkuPromotion,
			Promotion:    Promotion,
			Num:          detail.TotalBuyCount,
		}
		records = append(records, record)
	}

	// 最后一个商品获得剩余的所有优惠金额
	lastDetail := validDetails[len(validDetails)-1]
	lastPromotion := totalDiscount - allocatedDiscount

	// 计算单个商品的优惠金额,向下取整
	LastSkuPromotion := lastPromotion / lastDetail.TotalBuyCount
	record := order_po.OrderPromotionRecord{
		OrderDcId:    cast.ToString(lastDetail.ItemID),
		Sku:          cast.ToString(lastDetail.SkuID),
		Promotion:    lastPromotion,
		SkuPromotion: LastSkuPromotion,
		Num:          lastDetail.TotalBuyCount,
	}
	records = append(records, record)

	return records
}

// CheckCouponAvailability 检查优惠券可用性
func (s *ShopOrderService) CheckCouponAvailability(param *marketing_vo.CheckCouponAvailabilityReq) (*marketing_vo.CheckCouponAvailabilityRes, error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 初始化返回结果
	result := &marketing_vo.CheckCouponAvailabilityRes{
		Data: marketing_vo.CouponAvailabilityData{
			AvailableCoupons:   make([]marketing_vo.CouponAvailabilityInfo, 0),
			UnavailableCoupons: make([]marketing_vo.CouponAvailabilityInfo, 0),
		},
	}

	// 1. 获取用户所有优惠券
	userCoupons, _, err := new(services.CouponService).GetCouponReceiverList(&marketing_vo.CouponReceiverListReq{
		StoreId:    param.StoreId,
		CustomerId: param.CustomerId,
		Status:     1,
	})
	if err != nil {
		return nil, fmt.Errorf("获取用户优惠券失败: %v", err)
	}
	// 如果没有优惠券，直接返回空结果
	if len(userCoupons) == 0 {
		return result, nil
	}

	// 3. 遍历每张优惠券进行验证
	for _, coupon := range userCoupons {
		couponInfo := marketing_vo.CouponAvailabilityInfo{
			Id:        coupon.Id,
			Name:      coupon.CouponName,
			Type:      coupon.CouponType,
			Threshold: coupon.Threshold,
			Discount:  coupon.Discount,
			BestOffer: coupon.BestOffer,
			StartTime: coupon.EnableTime,
			EndTime:   coupon.ExpireTime,
			Code:      coupon.Code,
			CouponId:  coupon.CouponId,
			Remark:    coupon.Remark,
		}
		// 构造包含当前优惠券的计算请求
		calcReq := order_vo.CalcRequest{
			Data: order_vo.CalcInfo{
				TenantID: coupon.StoreId,
				ChainID:  cast.ToString(coupon.ChainId),
				CustomerInfo: order_vo.CustomerInfo{
					ID: param.CustomerId,
				},
				Details: param.Details,
				Coupons: []order_vo.CouponInfo{{
					Code:     coupon.Code,
					CouponID: cast.ToString(coupon.CouponId),
				}},
			},
		}

		// 调用订单计算服务
		calcRes, err := s.Calc(calcReq)
		// 检查优惠券是否可用
		if err != nil || len(calcRes.CalcInfo.Coupons) == 0 {
			couponInfo.UnavailableReason = "商品不满足使用条件"
			result.Data.UnavailableCoupons = append(result.Data.UnavailableCoupons, couponInfo)
			continue
		}

		// 查找优惠金额
		var found bool
		for _, discount := range calcRes.CalcInfo.DiscountInfo {
			if discount.DiscountType == "5" { // 优惠券类型
				couponInfo.DiscountAmount = discount.DiscountMoney
				found = true
				break
			}
		}
		// 根据是否找到优惠金额决定优惠券是否可用
		if found {
			result.Data.AvailableCoupons = append(result.Data.AvailableCoupons, couponInfo)
		} else {
			couponInfo.UnavailableReason = "商品不满足使用条件"
			result.Data.UnavailableCoupons = append(result.Data.UnavailableCoupons, couponInfo)
		}
	}

	return result, nil
}

// FreezeInventory 锁定库存
func (s ShopOrderService) FreezeInventory(session *xorm.Session, orderSn string) error {
	// 查询订单信息
	order, err := new(order_po.OrderMain).GetOrderDetail(session, order_po.OrderInfoRequest{
		OrderSn: orderSn,
	})
	if err != nil {
		return fmt.Errorf("查询订单信息失败: %v", err)
	}

	// 查询订单商品
	orderProduct, err := new(order_po.OrderProduct).GetOrderProductPM(session, order_po.OrderProductRequest{
		OrderSn: orderSn,
	})
	if err != nil {
		return fmt.Errorf("查询订单商品失败: %v", err)
	}
	if len(orderProduct) == 0 {
		return nil
	}

	// 构建锁库请求
	ioBoundSvc := iobound.NewIoBoundService()
	freezeCmd := &iobound_vo.OutBoundCommand{
		ChainId:     order.ChainId,
		StoreId:     order.ShopId,
		WarehouseId: order.WarehouseId,
		ChannelId:   order.ChannelId,
		RefId:       order.Id,
		RefNo:       order.OrderSn,
		RefType:     3,
		Remark:      "销售订单冻结库存",
		Details:     make([]iobound_vo.OutBoundDetailCommand, 0),
		Operator:    "system",
	}

	// 构建锁库明细
	for _, product := range orderProduct {
		freezeCmd.Details = append(freezeCmd.Details, iobound_vo.OutBoundDetailCommand{
			SkuId:           cast.ToInt(product.SkuId),
			ProductName:     product.ProductName,
			IoPrice:         product.PayPrice,
			IoCount:         product.Number,
			ItemDetailRefId: product.SubBizOrderId,
		})
	}

	// 执行锁库
	_, err = ioBoundSvc.Freeze(context.Background(), session, freezeCmd)
	if err != nil {
		return fmt.Errorf("锁定库存失败: %v", err)
	}

	return nil
}

// OrderOutInventory 出库
func (s ShopOrderService) OrderOutInventory(session *xorm.Session, orderSn string) error {
	// 查询订单信息
	order, err := new(order_po.OrderMain).GetOrderDetail(session, order_po.OrderInfoRequest{
		OrderSn: orderSn,
	})
	if err != nil {
		return fmt.Errorf("查询订单信息失败: %v", err)
	}

	// 查询订单商品
	orderProduct, err := new(order_po.OrderProduct).GetOrderProductPM(session, order_po.OrderProductRequest{
		OrderSn: orderSn,
	})
	if err != nil {
		return fmt.Errorf("查询订单商品失败: %v", err)
	}

	// 构建锁库请求
	ioBoundSvc := iobound.NewIoBoundService()
	freezeCmd := iobound_vo.OutBoundCommand{
		ChainId:     order.ChainId,
		StoreId:     order.ShopId,
		WarehouseId: order.WarehouseId,
		ChannelId:   order.ChannelId,
		RefId:       order.Id,
		RefNo:       order.OrderSn,
		RefType:     3,
		Remark:      "销售订单出库",
		Details:     make([]iobound_vo.OutBoundDetailCommand, 0),
		Operator:    "system",
	}
	if len(orderProduct) == 0 {
		return nil
	}

	// 构建锁库明细
	for _, product := range orderProduct {
		freezeCmd.Details = append(freezeCmd.Details, iobound_vo.OutBoundDetailCommand{
			SkuId:           cast.ToInt(product.SkuId),
			ProductName:     product.ProductName,
			IoPrice:         product.PayPrice,
			IoCount:         product.Number,
			ItemDetailRefId: product.SubBizOrderId,
		})
	}

	// 执行出库
	_, err = ioBoundSvc.OrderOut(context.Background(), session, freezeCmd)
	if err != nil {
		return fmt.Errorf("订单出库失败: %v", err)
	}

	return nil
}

// 模拟支付回调用
type NotifyRequest struct {
	//支付平台流水号
	TradeNo string `json:"tradeNo,omitempty"`
	//商户流水号,仅能用大小写字母与数,字，且在商户系统具有唯一性
	OutTradeNo string `json:"outTradeNo"`
	//订单日期格式：YYYYMMDD
	OrderTime string `json:"orderTime"`
	//实付金额 单位分
	PayPrice string `json:"payPrice"`
	//订单金额 单位分
	TotalPrice string `json:"totalPrice"`
	//优惠金额 单位分
	Discount string `json:"discount"`
	//添加时间
	AddTime string `json:"addTime"`
	//支付时间
	PayTime string `json:"payTime"`
	//商品编号
	ProductId string `json:"productId"`
	//商品名称 最长 32 字节
	ProductName string `json:"productName"`
	//商品描述
	ProductDesc string `json:"productDesc"`
	//后台回调地址
	OfflineNotifyUrl string `json:"offlineNotifyUrl"`
	//客户端 IP
	ClientIP string `json:"clientIP"`
	//商户号
	MerchantId string `json:"merchantId"`
	//扩展信息 预留字段，JSON 格式
	ExtendInfo string `json:"extendInfo"`
	//扣款通道返回的流水号
	ChannelNo string `json:"channelNo"`
	//签名
	Sign string `json:"sign"`
	//支付方式  1：微信 JSAPI，2：C扫B，3：B扫C,8:储蓄卡支付
	PayType string `json:"payType"`
	//订单号
	OrderId string `json:"orderId"`
}

type NotifyResponse struct {
	Result string `json:"result,omitempty"`
}

// 计算运费方法
func Offlinenotify(in order_po.OrderMain) error {

	// 构建获取配送费参数
	payReq := &NotifyRequest{
		OrderId:  in.OrderSn,
		TradeNo:  utils.GetGuid32(),
		PayTime:  time.Now().Format(utils.DATETIME_LAYOUT),
		PayPrice: cast.ToString(utils.Fen2Yuan(in.PayAmount)),
		PayType:  "8",
	}

	// 发起扫码支付请求
	payUrl := "http://127.0.0.1:7040/order-api/order/offlinenotify" // 支付接口地址
	//payUrl := "https://awen.uat.rvet.cn/order-api/order/offlinenotify" // 支付接口地址
	respBody, err := utils.HttpPost(payUrl, []byte(utils.JsonEncode(payReq)), "")
	if err != nil {
		//session.Rollback()
		return fmt.Errorf("发起扫码支付请求失败: %v", err)
	}

	// 解析支付响应
	var payResp NotifyResponse
	if err := json.Unmarshal(respBody, &payResp); err != nil {
		//session.Rollback()
		return fmt.Errorf("解析支付响应失败: %v", err)
	}

	if payResp.Result != "success" {
		return fmt.Errorf("支付回调失败" + in.OrderSn)
	}
	return nil
}

// 异步计算员工业绩
func (s ShopOrderService) AsyncCalculateCommission(
	orderMain order_po.OrderMain,
	orderProducts []*order_po.OrderProduct,
	cashierName string,
	cashierId string,
	operatorId string,
	operatorName string,
) error {
	log.Infof("开始为订单 %s 计算员工业绩", orderMain.OrderSn)
	commissionService := omnibus_service.CommissionService{}
	commissionReq := &omnibus_vo.AssignOrderCommissionReq{
		OrderItems: make([]omnibus_vo.OrderItemCommission, 0, len(orderProducts)),
		Operator:   operatorName,
		OperatorId: cast.ToInt64(operatorId),
	}

	for _, product := range orderProducts {
		// 跳过赠品和次卡抵扣的部分，这些不参与业绩计算
		if product.BuyType == 2 || product.BuyType == 1 {
			continue
		}
		assignedEmpId := cast.ToInt64(product.EmployeeId)
		if orderMain.ChannelId != 100 {
			assignedEmpId = cast.ToInt64(cashierId)
		}
		commissionReq.OrderItems = append(commissionReq.OrderItems, omnibus_vo.OrderItemCommission{
			OrderId:       int(orderMain.Id), // 注意类型转换
			OrderNo:       orderMain.OrderSn,
			SkuId:         cast.ToInt64(product.SkuId),
			ProductName:   product.ProductName,
			ProductType:   product.ProductType,
			SalesAmount:   int64(product.MarkingPrice * product.Number), // 原始销售额，单位分
			ActualAmount:  int64(product.SkuPayTotal),                   // 实收金额，单位分
			OrderTime:     orderMain.CreateTime,                         // 使用订单创建时间
			StoreId:       cast.ToInt64(orderMain.ShopId),               // 修正类型转换
			Channel:       orderMain.ChannelId,
			AssignedEmpId: assignedEmpId,
			UnitPrice:     int64(product.PayPrice),
			Quantity:      product.Number,
			CustomerName:  orderMain.MemberName,
		})
	}

	// 如果没有需要计算业绩的商品项，则不调用
	if len(commissionReq.OrderItems) == 0 {
		log.Infof("订单 %s 没有需要计算员工业绩的商品项", orderMain.OrderSn)
		return nil
	}

	count, err := commissionService.AssignOrderCommission(commissionReq)
	if err != nil {
		log.Errorf("订单 %s 计算员工业绩失败: %v", orderMain.OrderSn, err)
		return fmt.Errorf("订单 %s 计算员工业绩失败: %v", orderMain.OrderSn, err)
	}
	log.Infof("订单 %s 成功计算 %d 条员工业绩", orderMain.OrderSn, count)
	return nil
}

// AssignCommission 手动触发订单业绩分配
func (s ShopOrderService) AssignCommission(orderSn, staffName, staffId, operatorId, operatorName string) error {
	logPrefix := fmt.Sprintf("手动分配业绩====，订单号：%s, 员工ID: %s, 员工姓名: %s", orderSn, staffId, staffName)
	log.Info(logPrefix)
	// 加redis锁
	lockKey := fmt.Sprintf("assign_commission_lock_%s", orderSn)
	if !cache.RedisLock(lockKey, 10) {
		return fmt.Errorf("系统繁忙，请稍后再试")
	}

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 重新查询一次以获取 OrderMain 结构
	orderMain := &order_po.OrderMain{}
	has, err := session.Where("order_sn = ?", orderSn).Get(orderMain)
	if err != nil {
		return fmt.Errorf("查询 OrderMain 结构失败: %v", err)
	}
	if !has {
		return fmt.Errorf("无法获取订单 %s 的完整信息", orderSn)
	}

	// 查询订单商品信息
	orderProducts, _, err := new(order_po.OrderProduct).GetOrderProduct(session, order_po.OrderProductRequest{
		OrderSn: orderSn,
	})
	if err != nil {
		return fmt.Errorf("查询订单商品信息失败: %v", err)
	}

	// 检查是否有商品项
	if len(orderProducts) == 0 {
		log.Infof("订单 %s 没有商品项，无需分配业绩", orderSn)
		return nil // 或者返回一个特定的错误或消息
	}

	// 调用异步方法计算业绩
	err = s.AsyncCalculateCommission(*orderMain, orderProducts, staffName, staffId, operatorId, operatorName)
	if err != nil {
		return err
	}

	log.Infof("订单 %s 的业绩分配任务已成功触发，分配给员工 %s (ID: %s)", orderSn, staffName, staffId)
	return nil
}

// getValidityDescription 根据有效期类型和时间生成友好的文本描述
func getValidityDescription(validType string, enableTime time.Time, expirationTime time.Time) string {
	switch validType {
	case "PERMANENT":
		return "永久有效"
	case "VALID_DAYS":
		// 计算从启用时间开始的有效天数
		days := int(expirationTime.Sub(enableTime).Hours() / 24)
		return fmt.Sprintf("办卡时起%d天内有效", days)
	case "DEADLINE":
		return fmt.Sprintf("截止至%s有效", expirationTime.Format("2006-01-02"))
	case "FIXED_TIME":
		return fmt.Sprintf("%s至%s", enableTime.Format("2006-01-02"), expirationTime.Format("2006-01-02"))
	default:
		return validType // 如果是未知类型，直接返回原始值
	}
}
