package services

import (
	order_po "eShop/domain/order-po"
	"eShop/infra/utils"
	"eShop/services/common"
	vo "eShop/view-model/order-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
)

type PayService struct {
	common.BaseService
}

func NewPayService() *PayService {
	return &PayService{}
}

// GetPayInfo 获取支付参数
func (s *PayService) GetPayInfo(req vo.PayRequest) (*vo.WXPayResponse, error) {
	s.<PERSON>gin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 查询订单信息
	orderSli, _, _, _, err := new(order_po.OrderMain).GetOrderInfo(session, order_po.OrderInfoRequest{
		OrderSn: req.OrderSn,
	})
	if err != nil {
		return nil, fmt.Errorf("查询订单信息失败: %v", err)
	}
	if len(orderSli) == 0 {
		return nil, fmt.Errorf("订单不存在")
	}
	order := orderSli[0]

	// 查询支付配置
	epayConfig, err := new(order_po.EpayConfig).GetEpayConfig(session, cast.ToInt64(order.ShopId), "WX_MINI")
	if err != nil {
		return nil, fmt.Errorf("查询支付配置失败: %v", err)
	}
	NotifyUrl := config.GetString("pay-center-notifyurl")
	// 构建支付请求参数
	payReq := &vo.Pay{
		OrderNo:      order.OrderSn,
		PayAmount:    int64(order.PayAmount),
		PayTotal:     int64(order.PayTotal),
		OrderName:    "SAAS商品-" + order.OrderSn,
		ProductDesc:  "SAAS商品",
		AppId:        7,
		TransType:    19,
		MerchantId:   epayConfig.AppMerchantID,
		ClientIP:     "127.0.0.1",
		ValidTime:    300,
		Timestamp:    time.Now().UnixMilli(),
		OrderPayType: order.OrderPayType,
		NotifyURL:    NotifyUrl, // 配置回调地址
		OpenId:       req.OpenId,
		SubAppId:     req.SubAppId,
	}

	// 生成签名
	jsonBytes, err := json.Marshal(payReq)
	if err != nil {
		return nil, fmt.Errorf("序列化支付参数失败: %v", err)
	}
	sign, err := utils.MakeStandardPaySign(jsonBytes, req.AppId)
	if err != nil {
		return nil, fmt.Errorf("生成支付签名失败: %v", err)
	}
	payReq.Sign = sign

	// 发起扫码支付请求
	payUrl := "http://127.0.0.1:7035/pay/pay-api/pay" // 支付接口地址

	//payUrl := "https://awen.uat.rvet.cn/pay/pay-api/pay" // 支付接口地址
	respBody, err := utils.HttpPost(payUrl, []byte(utils.JsonEncode(payReq)), "")
	if err != nil {
		//session.Rollback()
		return nil, fmt.Errorf("获取支付参数失败: %v", err)
	}

	// 解析支付响应
	var payResp vo.WXPayResponse
	if err := json.Unmarshal(respBody, &payResp); err != nil {
		//session.Rollback()
		return nil, fmt.Errorf("解析支付响应失败: %v", err)
	}
	if payResp.Code != 200 {
		return nil, errors.New(payResp.Message)
	}

	//// 更新订单支付信息
	//updateSQL := `UPDATE dc_order.order_main SET
	//		pay_sn = ?,
	//		pay_time = ?,
	//			trade_no = ?
	//			WHERE order_sn = ?`
	//
	//_, err = session.Exec(updateSQL,
	//	payResp.ThirdPayNo, // 第三方支付流水号
	//	payResp.PayTime,    // 支付时间
	//	payResp.TradeNo,    // 支付中心订单号
	//	orderSn)

	// if err != nil {
	// 	//session.Rollback()
	// 	return nil, fmt.Errorf("更新订单支付信息失败: %v", err)
	// }

	return &payResp, nil
}
