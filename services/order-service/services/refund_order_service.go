package services

import (
	"context"
	marketing_po "eShop/domain/marketing-po"
	omnibus_po "eShop/domain/omnibus-po"
	order_po "eShop/domain/order-po"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	payment2 "eShop/infra/payment"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/services/common"
	iobound2 "eShop/services/inventory-service/iobound"
	"eShop/services/omnibus-service/services"
	"eShop/view-model/inventory-vo/iobound"
	omnibus_vo "eShop/view-model/omnibus-vo"
	vo "eShop/view-model/order-vo"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"time"

	"github.com/shopspring/decimal"
	"github.com/spf13/cast"

	"xorm.io/xorm"
)

type RefundOrderService struct {
	common.BaseService
}

func NewRefundOrderService() *RefundOrderService {
	return &RefundOrderService{}
}

// RefundHandler 退款处理器接口
type RefundHandler interface {
	Handle(session *xorm.Session, req vo.RefundRequest, order order_po.OrderMainView, jwtInfo *jwtauth.XCShopPayload) error
}

// getRefundHandler 获取退款处理器
func (s *RefundOrderService) getRefundHandler(orderType int) (RefundHandler, error) {
	switch orderType {
	case 23: // 储值卡订单
		return &StoreCardRefundHandler{}, nil
	case 22: // 次卡订单
		return &TimeCardRefundHandler{}, nil
	default: // 普通商品订单
		return &NormalRefundHandler{}, nil
	}
}

// ApplyRefund 申请退款
func (s *RefundOrderService) ApplyRefund(req vo.RefundRequest, jwtInfo *jwtauth.XCShopPayload) error {
	log.Infof("开始处理退款申请, orderSn: %s, userId: %s", req.OrderSn, jwtInfo.UserId)
	s.Begin()
	defer s.Close()

	// 加redis锁
	redisConn := cache.GetRedisConn()
	lockKey := fmt.Sprintf("lock:apply_refund_%s", req.OrderSn)
	if lock, err := redisConn.SetNX(lockKey, 1, 10*time.Second).Result(); err != nil {
		return err
	} else if !lock {
		return errors.New("系统繁忙，请稍后再试")
	}

	session := s.Engine.NewSession()
	defer session.Close()

	// 如果储值卡退卡，则先查出订单号 eshop_saas.m_store_card_use_record
	if req.IsRefundCard == 100 {
		orderSn, err := new(marketing_po.MStoreCardUseRecord).GetOrderSn(session, req.CardNo)
		if err != nil {
			return fmt.Errorf("查询储值卡使用记录失败: %v", err)
		}
		req.OrderSn = orderSn
	} else if req.IsRefundCard == 102 {
		// 次卡退卡，则先查出订单号 eshop_saas.m_time_card_use_record
		orderSn, err := new(marketing_po.MTimeCardUseRecord).GetTimeCardOrderSn(session, req.CardNo)
		if err != nil {
			return fmt.Errorf("查询次卡使用记录失败: %v", err)
		}
		req.OrderSn = orderSn
	}
	// 2. 查询订单信息
	order, err := new(order_po.OrderMain).GetOrderDetail(session, order_po.OrderInfoRequest{
		OrderSn: req.OrderSn,
	})
	if err != nil {
		return fmt.Errorf("查询订单信息失败: %v", err)
	}
	// 判断是否有订单记录
	if order.Id == 0 {
		return fmt.Errorf("订单不存在: %s", req.OrderSn)
	}

	// 3. 检查订单是否可退款
	if err = s.checkCanRefund(order); err != nil {
		log.Errorf("订单状态不可退款: orderSn=%s, orderStatus=%d, error=%v", order.OrderSn, order.OrderStatus, err)
		return err
	}
	session.Begin()

	if req.Type == 1 { // 全部退款
		refundHandler, err := s.getRefundHandler(order.OrderType)
		if err != nil {
			session.Rollback()
			return err
		}
		if err = refundHandler.Handle(session, req, order, jwtInfo); err != nil {
			session.Rollback()
			return err
		}
	} else { // 部分退款
		if err = s.handlePartialRefund(session, req, order, jwtInfo); err != nil {
			session.Rollback()
			return err
		}
	}
	session.Commit()

	return nil
}

// handlePartialRefund 处理部分退款
func (s *RefundOrderService) handlePartialRefund(session *xorm.Session, req vo.RefundRequest, order order_po.OrderMainView, jwtInfo *jwtauth.XCShopPayload) error {
	if req.ReType == 0 {
		return errors.New("请选择退货类型")
	}
	if req.RefundType == 0 {
		return errors.New("请选择退回方式")
	}
	if req.RefundMethod == 0 {
		return errors.New("请选择退款方式")
	}

	// 2. 查询订单商品信息
	orderProducts, err := new(order_po.OrderProduct).GetOrderProducts(session, order_po.OrderProductRequest{
		OrderSn: req.OrderSn,
	})
	if err != nil {
		return fmt.Errorf("查询订单商品失败: %v", err)
	}

	// 3. 计算退款金额并检查是否最后一次退款
	refundAmount, refundCardNum, isLastRefund, err := s.calculatePartialRefund(req.Products, orderProducts)
	if err != nil {
		return err
	}
	// 生成退款单号
	refundSn := utils.GetSn("refund", session.Engine())[0]

	// 退次卡
	if refundCardNum > 0 {
		if err = s.RefundTimeCard(session, order, cast.ToInt64(jwtInfo.UserId), refundCardNum); err != nil {
			log.Errorf("处理次卡退款失败: orderSn=%s, refundSn=%s, error=%v", req.OrderSn, refundSn, err)
			return err
		}
	}

	// 4. 创建退款单
	params := order_po.RefundOrderParams{
		RefundType:   req.RefundType,
		RefundReason: req.RefundReason,
		Remark:       req.Remark,
		RefundAmount: refundAmount,
		Type:         req.Type,
		ReType:       req.ReType,
		Operator:     jwtInfo.UserName,
	}
	refundOrder, err := new(order_po.RefundOrder).CreateRefundOrder(session, order, params, refundSn)
	if err != nil {
		return err
	}

	// 5. 创建退款商品记录
	if err = s.CreateRefundProducts(session, refundOrder, orderProducts, req); err != nil {
		return err
	}

	// 更新订单状态（根据是否最后一个商品决定状态）
	updateFields := map[string]interface{}{
		"refund_amount": order.RefundAmount + refundAmount,
		"update_time":   time.Now(),
	}
	if isLastRefund {
		updateFields["order_status"] = 0
		updateFields["order_status_child"] = 20107
	}

	if err = new(order_po.OrderMain).Update(session, order, updateFields); err != nil {
		return fmt.Errorf("更新订单信息失败: %v", err)
	}

	//更新order_product表的退款数
	for _, product := range req.Products {
		if _, err = session.Table("dc_order.order_product").
			Where("id = ?", product.OrderProductId).
			Incr("refund_num", product.RefundNum).
			Update(map[string]interface{}{
				"update_time": time.Now(),
			}); err != nil {
			session.Rollback()
			return fmt.Errorf("更新商品退款数量失败: %v", err)
		}

	}

	// 9. 记录退款日志
	refundLog := &order_po.RefundOrderLog{
		RefundSn:      refundSn,
		Reason:        req.RefundReason,
		Money:         refundAmount,
		OperationType: "商家同意退款",
		Operationer:   jwtInfo.UserName,
		ResType:       "商家同意退款",
		NotifyType:    "agree",
		Ctime:         time.Now(),
	}
	if err = new(order_po.RefundOrderLog).Create(session, refundLog); err != nil {
		return fmt.Errorf("创建退款日志失败: %v", err)
	}

	// 根据退款方式创建退款支付明细, 部分退款不考虑
	if err = s.createRefundPaymentDetails(session, order, refundOrder, jwtInfo.UserName, req); err != nil {
		log.Errorf("创建退款支付明细失败: orderSn=%s, refundSn=%s, error=%v", req.OrderSn, refundSn, err)
		return err
	}

	// 只有退货退款(ReType=2)才调用退库存接口
	if req.ReType == 2 {
		if err = s.handleRefundInventory(session, refundOrder, order, req.Products); err != nil {
			return fmt.Errorf("处理退款退库存失败: %v", err)
		}
	}

	log.Infof("退款申请处理完成: orderSn=%s, refundSn=%s", req.OrderSn, refundSn)
	return session.Commit()
}

// calculatePartialRefund 计算部分退款金额并检查是否是最后一次退款
func (s *RefundOrderService) calculatePartialRefund(refundProducts []*vo.RefundProduct, orderProducts []*order_po.OrderProductExt) (refundAmount int, refundCardNum int, isLastRefund bool, err error) {
	// 构建商品ID到退款数量的映射
	toRefundMap := make(map[int]*vo.RefundProduct)
	for _, p := range refundProducts {
		toRefundMap[p.OrderProductId] = p
	}

	// 遍历订单商品，同时计算退款金额和检查是否最后一次退款
	isLastRefund = true

	var (
		buyTotal    = 0 // 购买数量
		refundTotal = 0 // 退款数量
	)
	for _, product := range orderProducts {
		buyTotal += product.Number
		refundTotal += product.RefundNum
		// 本次要退的数量
		toRefund := toRefundMap[product.Id]
		if toRefund == nil {
			continue
		}
		// 如果是次卡退款，则不进行退款,只退次数
		if product.BuyType == 1 {
			refundTotal += toRefund.RefundNum
			// 检查退款数量是否超过购买数量
			refundTotalDec := decimal.NewFromInt(int64(toRefund.RefundNum + product.RefundNum))
			productTotalDec := decimal.NewFromInt(int64(product.Number))
			if refundTotalDec.GreaterThan(productTotalDec) {
				return 0, 0, false, fmt.Errorf("商品[%d]退款数量[%s]超过购买数量[%s]", product.Id, refundTotalDec.String(), productTotalDec.String())
			}
			refundCardNum += toRefund.RefundNum
		} else {
			// 检查退款金额是否超过支付金额
			// 使用decimal处理金额计算
			existingRefund := decimal.NewFromFloat(product.RefundAmount)
			newRefund := decimal.NewFromFloat(utils.Fen2Yuan(toRefund.RefundAmount))
			paymentTotal := decimal.NewFromFloat(utils.Fen2Yuan(product.PaymentTotal))

			// 计算总退款金额
			totalRefund := existingRefund.Add(newRefund)

			// 检查总退款金额是否超过支付金额
			if totalRefund.GreaterThan(paymentTotal) {
				return 0, 0, false, fmt.Errorf("商品[%d]累计退款金额[%s]超过支付金额[%s]",
					product.Id,
					totalRefund.String(),
					paymentTotal.String(),
				)
			}
			refundTotal += toRefund.RefundNum
			// 检查退款数量是否超过购买数量
			refundTotalDec := decimal.NewFromInt(int64(toRefund.RefundNum + product.RefundNum))
			productTotalDec := decimal.NewFromInt(int64(product.Number))
			if refundTotalDec.GreaterThan(productTotalDec) {
				return 0, 0, false, fmt.Errorf("商品[%d]退款数量[%s]超过购买数量[%s]", product.Id, refundTotalDec.String(), productTotalDec.String())
			}
			// 本次退款金额
			refundAmount += toRefund.RefundAmount
		}
	}
	// 检查是否还有未退完的商品
	if refundTotal < buyTotal {
		isLastRefund = false
	}
	return refundAmount, refundCardNum, isLastRefund, nil
}

// NormalRefundHandler 普通订单退款处理器
type NormalRefundHandler struct{}

func (h *NormalRefundHandler) Handle(session *xorm.Session, req vo.RefundRequest, order order_po.OrderMainView, jwtInfo *jwtauth.XCShopPayload) error {
	//  查询订单商品信息
	orderProducts, err := new(order_po.OrderProduct).GetOrderProducts(session, order_po.OrderProductRequest{
		OrderSn: req.OrderSn,
	})
	if err != nil {
		return fmt.Errorf("查询订单商品失败: %v", err)
	}

	// 创建退款单
	refundSn := utils.GetSn("refund", session.Engine())[0]

	log.Infof("开始创建退款单: orderSn=%s, refundSn=%s", req.OrderSn, refundSn)
	// 整单退默认退货退款
	req.ReType = 2
	refundOrder, err := new(order_po.RefundOrder).CreateRefundOrder(session, order, order_po.RefundOrderParams{
		RefundType:   req.RefundType,
		RefundReason: req.RefundReason,
		Remark:       req.Remark,
		Type:         req.Type,
		ReType:       req.ReType,
		Operator:     jwtInfo.UserName,
	}, refundSn)
	if err != nil {
		return err
	}
	s := NewRefundOrderService()
	// 创建退款商品记录
	if err = s.CreateRefundProducts(session, refundOrder, orderProducts, req); err != nil {
		log.Errorf("创建退款商品记录失败: orderSn=%s, refundSn=%s, error=%v", req.OrderSn, refundSn, err)
		return err
	}
	// 如果是指定方式退款，则不处理储值卡
	if req.RefundType == 1 {
		// 检查并处理储值卡退款
		if err = s.handleStoreCardRefund(session, order, jwtInfo); err != nil {
			log.Errorf("处理储值卡退款失败: orderSn=%s, refundSn=%s, error=%v", req.OrderSn, refundSn, err)
			return err
		}
	}
	// 检查并处理次卡退款
	if err = s.handleTimeCardRefund(session, order, cast.ToInt64(jwtInfo.UserId)); err != nil {
		log.Errorf("处理次卡退款失败: orderSn=%s, refundSn=%s, error=%v", req.OrderSn, refundSn, err)
		return err
	}
	// }
	// 更新订单状态
	if err = new(order_po.OrderMain).UpdateOrderStatus(session, order); err != nil {
		log.Errorf("更新订单状态失败: orderSn=%s, refundSn=%s, error=%v", req.OrderSn, refundSn, err)
		return err
	}

	// 记录退款日志
	refundLog := &order_po.RefundOrderLog{
		RefundSn:      refundSn,
		Reason:        req.RefundReason,
		Money:         order.PayAmount,
		OperationType: "商家同意退款",
		Operationer:   jwtInfo.UserName,
		ResType:       "商家同意退款",
		Ctime:         time.Now(),
	}
	if err = new(order_po.RefundOrderLog).Create(session, refundLog); err != nil {
		log.Errorf("创建退款日志失败: orderSn=%s, refundSn=%s, error=%v", req.OrderSn, refundSn, err)
		return fmt.Errorf("创建退款日志失败: %v", err)
	}

	// 全部退款时构造退款商品列表
	var refundProducts []*vo.RefundProduct
	for _, product := range orderProducts {
		refundProducts = append(refundProducts, &vo.RefundProduct{
			OrderProductId: product.Id,
			RefundNum:      product.Number,
			RefundAmount:   product.PaymentTotal,
		})
	}

	// 调用退库存接口
	if err = s.handleRefundInventory(session, refundOrder, order, refundProducts); err != nil {
		log.Errorf("处理退款退库存失败: orderSn=%s, refundSn=%s, error=%v", req.OrderSn, refundSn, err)
		return fmt.Errorf("处理退款退库存失败: %v", err)
	}

	// 根据退款方式创建退款支付明细
	if err = s.createRefundPaymentDetails(session, order, refundOrder, jwtInfo.UserName, req); err != nil {
		log.Errorf("创建退款支付明细失败: orderSn=%s, refundSn=%s, error=%v", req.OrderSn, refundSn, err)
		return err
	}

	// 异步发送短信通知
	go s.SendRefundSmsNotification(session, order, refundSn)

	log.Infof("退款申请处理完成: orderSn=%s, refundSn=%s", req.OrderSn, refundSn)
	return nil
}

// sendRefundSmsNotification 发送退款短信通知
func (s *RefundOrderService) SendRefundSmsNotification(session *xorm.Session, orderInfo order_po.OrderMainView, refundSn string) {
	// 1. 查询储值卡使用记录
	storeCardUseRecord := new(marketing_po.MStoreCardUseRecord)
	exists, err := session.Where("order_no = ? AND operate_type = ?", orderInfo.OrderSn, marketing_po.OperateTypeConsumption).Get(storeCardUseRecord)
	if err != nil {
		log.Errorf("查询储值卡使用记录失败: orderSn=%s, refundSn=%s, error=%v", orderInfo.OrderSn, refundSn, err)
		return
	}

	// 如果没有储值卡使用记录，直接返回
	if !exists {
		return
	}
	storeCard := new(marketing_po.MStoreCard)
	storeCardRecord, err := storeCard.GetStoreCardRecord(session, storeCardUseRecord.CardNo, storeCardUseRecord.CustomerId)
	if err != nil {
		log.Errorf("查询储值卡记录失败: orderSn=%s, refundSn=%s, error=%v", orderInfo.OrderSn, refundSn, err)
		return
	}
	smsParams := map[string]string{
		"tenantName":    orderInfo.ShopName,
		"refundAmount":  cast.ToString(utils.Fen2Yuan(orderInfo.PayAmount)),
		"balanceAmount": cast.ToString(storeCardRecord.TotalAmount),
	}
	templateParam, _ := json.Marshal(smsParams)
	// 创建短信服务实例
	smsService := services.SmsService{}
	// 构造发送消息请求
	sendRequest := omnibus_vo.SmsSendMessageRequest{
		StoreId:       orderInfo.ShopId,
		Mobile:        utils.MobileDecrypt(orderInfo.EnMemberTel),
		TemplateCode:  omnibus_po.SmsTemplateStoreCardRefund, // 储值卡退款通知
		TemplateParam: string(templateParam),
	}
	// 调用SendMessage方法发送短信
	if err = smsService.SendMessage(sendRequest); err != nil {
		log.Errorf("发送储值卡退款短信通知失败: orderSn=%s, refundSn=%s, error=%v", orderInfo.OrderSn, refundSn, err)
		return
	}

	log.Infof("成功发送储值卡退款短信通知: orderSn=%s, refundSn=%s", orderInfo.OrderSn, refundSn)
}

// StoreCardRefundHandler 储值卡退款处理器
type StoreCardRefundHandler struct {
	service *RefundOrderService
}

func (h *StoreCardRefundHandler) Handle(session *xorm.Session, req vo.RefundRequest, order order_po.OrderMainView, jwtInfo *jwtauth.XCShopPayload) error {
	// 创建服务实例以便在协程中使用
	if h.service == nil {
		h.service = NewRefundOrderService()
	}

	// 查询储值卡记录
	storeCard := new(marketing_po.MStoreCardRecordExt)
	if exists, err := session.Table("eshop_saas.m_store_card_record").Alias("a").
		Join("left", "eshop_saas.m_store_card_use_record b", "a.card_no = b.card_no").
		Select("a.id,a.status,b.customer_id,b.card_id,b.card_no,b.card_name,a.tenant_id,a.chain_id,b.employee_id,"+
			"b.balance_change,b.principal_change,b.gift_change,b.balance,a.total_amount,a.principal,a.gift_amount,"+
			"a.refund_amount,b.operate_type,a.price").
		Where("b.order_no = ? AND b.customer_id = ? and b.operate_type in ('OPEN_CARD','TOP_UP')", order.OrderSn, order.MemberId).
		Get(storeCard); err != nil {
		return fmt.Errorf("查询储值卡信息失败: %v", err)
	} else if !exists {
		return fmt.Errorf("储值卡不存在")
	}

	// 检查储值卡状态
	if storeCard.Status != "IN_USE" && storeCard.Status != "NOT_START" {
		return fmt.Errorf("储值卡状态异常[%s]，不可退款", storeCard.Status)
	}

	// 判断是开卡，还是继充
	// 开卡退卡要更新status的状态为RETIRED_CARD
	status := storeCard.Status
	recordStatus := marketing_po.OperateTypeRefund
	if req.IsRefundCard == 100 || storeCard.OperateType == marketing_po.OperateTypeOpenCard {
		status = "RETIRED_CARD"
		recordStatus = marketing_po.OperateTypeRECYCLE
	}

	if req.IsRefundCard == 0 && storeCard.OperateType == marketing_po.OperateTypeOpenCard {
		var payCount int64
		if _, err := session.SQL("SELECT COUNT(1) FROM eshop_saas.m_store_card_use_record WHERE card_no = ? AND operate_type IN ('CONSUMPTION','TOP_UP')", storeCard.CardNo).Get(&payCount); err != nil {
			return fmt.Errorf("查询储值卡消费记录失败: %v", err)
		}
		// 如果有消费记录或二次充值记录，则不允许退单
		if payCount > 0 {
			return fmt.Errorf("该储值卡已产生消费或发生二次充值，不支持退单。如需退款，请前往\"会员中心-客户管理-客户卡包\"操作退卡。")
		}
	}

	var refundOrder *order_po.RefundOrder
	var refundSn string
	refundAmount := req.RefundCardAmount
	// 只有当req.IsRefundCard != 100时，才创建退款单等记录
	if req.IsRefundCard != 100 {
		refundSn = utils.GetSn("refund", session.Engine())[0]
		// 创建退款单
		var err error
		refundOrder, err = new(order_po.RefundOrder).CreateRefundOrder(session, order, order_po.RefundOrderParams{
			RefundType:   req.RefundType,
			RefundReason: req.RefundReason,
			Remark:       req.Remark,
			Type:         req.Type,
			Operator:     req.Operator,
			ReType:       1,
			RefundAmount: refundAmount,
		}, refundSn)
		if err != nil {
			return err
		}

		// 创建退款商品记录
		orderProducts, err := new(order_po.OrderProduct).GetOrderProducts(session, order_po.OrderProductRequest{
			OrderSn: req.OrderSn,
		})

		if err != nil {
			return fmt.Errorf("查询订单商品失败: %v", err)
		}

		if err = h.service.CreateRefundProducts(session, refundOrder, orderProducts, req); err != nil {
			log.Errorf("创建退款商品记录失败: orderSn=%s, refundSn=%s, error=%v", req.OrderSn, refundSn, err)
			return err
		}
	} else {
		// 使用订单号作为退款记录备注
		refundSn = order.OrderSn
	}

	// 创建退款支付明细
	// 对于req.IsRefundCard == 100的情况，需要特殊处理，因为没有refundOrder
	if req.IsRefundCard == 100 {
		refundAmount = utils.Yuan2Fen(storeCard.Principal)
		// 为开卡退款创建一个临时的退款支付明细
		var PayType int
		switch req.RefundMethod {
		case 1:
			PayType = order_po.PayTypeWechat
		case 2:
			PayType = order_po.PayTypeAlipay
		case 3:
			PayType = order_po.PayTypeCash
		case 4:
			PayType = order_po.PayTypeOther
		case 5:
			PayType = order_po.PayTypeTimeCard
		}
		refundPayDetail := &order_po.RefundOrderPayDetail{
			TenantId:      cast.ToInt64(order.ShopId),
			OrderId:       int64(order.Id),
			OrderSn:       order.OrderSn,
			RefundSn:      refundSn,
			PaymentType:   PayType, // 使用请求中指定的退款方式
			PaymentAmount: utils.Yuan2Fen(storeCard.Principal),
			PaymentStatus: order_po.RefundStatusSuccess,
			CreatedBy:     jwtInfo.UserName,
			UpdatedBy:     jwtInfo.UserName,
		}
		if err := new(order_po.RefundOrderPayDetail).Create(session, refundPayDetail); err != nil {
			log.Errorf("创建退款支付明细失败: orderSn=%s, refundSn=%s, error=%v", req.OrderSn, refundSn, err)
			return fmt.Errorf("创建退款支付明细失败: %v", err)
		}
	} else if refundOrder != nil {
		// 非开卡退款，使用正常的退款支付明细创建流程
		if err := h.service.createRefundPaymentDetails(session, order, refundOrder, jwtInfo.UserName, req); err != nil {
			log.Errorf("创建退款支付明细失败: orderSn=%s, refundSn=%s, error=%v", req.OrderSn, refundSn, err)
			return err
		}
	}
	// 更新储值卡金额扣减
	var totalAmount, principal, giftAmount, afterRefundAmount, BalanceChange, PrincipalChange, GiftChange float64
	if req.IsRefundCard == 0 {
		afterRefundAmount = decimal.NewFromFloat(storeCard.RefundAmount).Add(decimal.NewFromFloat(storeCard.PrincipalChange)).Add(decimal.NewFromFloat(storeCard.GiftChange)).InexactFloat64()
		totalAmount = decimal.NewFromFloat(storeCard.TotalAmount).Sub(decimal.NewFromFloat(storeCard.BalanceChange)).InexactFloat64()
		principal = decimal.NewFromFloat(storeCard.Principal).Sub(decimal.NewFromFloat(storeCard.PrincipalChange)).InexactFloat64()
		giftAmount = decimal.NewFromFloat(storeCard.GiftAmount).Sub(decimal.NewFromFloat(storeCard.GiftChange)).InexactFloat64()
		BalanceChange = storeCard.BalanceChange
		PrincipalChange = storeCard.PrincipalChange
		GiftChange = storeCard.GiftChange
	} else {
		BalanceChange = storeCard.TotalAmount
		PrincipalChange = storeCard.Principal
		GiftChange = storeCard.GiftAmount
		afterRefundAmount = decimal.NewFromFloat(storeCard.RefundAmount).Add(decimal.NewFromFloat(storeCard.Principal)).Add(decimal.NewFromFloat(storeCard.GiftAmount)).InexactFloat64()
	}
	_, err := session.Where("id = ?", storeCard.Id).Cols("total_amount", "principal", "gift_amount", "refund_amount", "status").
		Update(&marketing_po.MStoreCardRecord{
			TotalAmount:  totalAmount,
			Principal:    principal,
			GiftAmount:   giftAmount,
			RefundAmount: afterRefundAmount,
			Status:       status,
		})
	if err != nil {
		return err
	}
	// 更新储值卡订单表退款信息
	if err := new(marketing_po.MStoreCardOrder).UpdateStoreCardOrderRefund(session, order.OrderSn, utils.Fen2Yuan(refundAmount)); err != nil {
		return err
	}

	// 创建储值卡退款记录
	storeCardRecord := &marketing_po.MStoreCardUseRecord{
		TenantId:        storeCard.TenantId,
		ChainId:         storeCard.ChainId,
		RecordId:        storeCard.Id,
		CardId:          storeCard.CardId,
		CardNo:          cast.ToString(storeCard.CardNo),
		CardName:        storeCard.CardName,
		CustomerId:      storeCard.CustomerId,
		EmployeeId:      storeCard.EmployeeId,
		OrderId:         int64(order.Id),
		OrderNo:         order.OrderSn,
		OperateType:     recordStatus,
		CreatedBy:       cast.ToInt64(jwtInfo.UserId),
		UpdatedBy:       cast.ToInt64(jwtInfo.UserId),
		Remark:          fmt.Sprintf("退款单号: %s", refundSn),
		Status:          "SUCCESS",
		BalanceChange:   -BalanceChange,
		PrincipalChange: -PrincipalChange,
		GiftChange:      -GiftChange,
		Balance:         principal,
		GiftBalance:     giftAmount,
	}
	// 创建操作信息
	operateInfo := marketing_po.NewOperateInfo(order.Id, order.OrderSn, cast.ToInt64(jwtInfo.UserId), jwtInfo.UserName)
	if err := new(marketing_po.MStoreCardUseRecord).RefundStoreCard(session, storeCardRecord, operateInfo); err != nil {
		return fmt.Errorf("创建储值卡退款记录失败: %v", err)
	}

	// 只有当req.IsRefundCard != 100时，才更新订单状态和创建退款日志
	if req.IsRefundCard != 100 {
		// 更新订单状态
		if err := new(order_po.OrderMain).UpdateOrderStatus(session, order); err != nil {
			return err
		}
		// 记录退款日志
		refundLog := &order_po.RefundOrderLog{
			RefundSn:      refundSn,
			Reason:        req.RefundReason,
			Money:         order.PayAmount,
			OperationType: "储值卡订单退款",
			Operationer:   jwtInfo.UserName,
			ResType:       "退款成功",
			Ctime:         time.Now(),
		}
		if err := new(order_po.RefundOrderLog).Create(session, refundLog); err != nil {
			return fmt.Errorf("创建退款日志失败: %v", err)
		}
	}

	return nil
}

// TimeCardRefundHandler 次卡退款处理器
type TimeCardRefundHandler struct{}

func (h *TimeCardRefundHandler) Handle(session *xorm.Session, req vo.RefundRequest, order order_po.OrderMainView, jwtInfo *jwtauth.XCShopPayload) error {
	// 查询次卡记录
	timeCard := new(marketing_po.MTimeCardUseExt)
	exists, err := session.Table("eshop_saas.m_time_card_record").Alias("a").
		Join("left", "eshop_saas.m_time_card_use_record b", "a.card_no = b.card_no").
		Select("a.id,a.status,b.total_change,b.customer_id,b.card_id,b.card_no,b.card_name,a.tenant_id,a.chain_id,a.buy_num,"+
			"a.balance as all_balance,a.total_num,a.gift_num as all_gift_num,b.record_id,b.employee_id,b.current_change,"+
			"b.gift_change,b.balance,b.gift_num,b.balance_change,b.operate_type,a.price").
		Where("b.order_no = ? AND a.customer_id = ? and b.operate_type in ('OPEN_CARD','TOP_UP')", order.OrderSn, order.MemberId).
		Get(timeCard)
	if err != nil {
		return fmt.Errorf("查询次卡信息失败: %v", err)
	}
	if !exists {
		return fmt.Errorf("次卡不存在")
	}

	// 确定退款状态
	status := timeCard.Status
	recordStatus := marketing_po.OperateTypeRefund
	// 退款退款卡状态
	if req.IsRefundCard == 102 || timeCard.OperateType == marketing_po.OperateTypeOpenCard {
		status = "RETIRED_CARD"
		recordStatus = marketing_po.OperateTypeRECYCLE
	}

	// 如果为次卡开卡有消费记录，则不允许退单
	if req.IsRefundCard == 0 && timeCard.OperateType == marketing_po.OperateTypeOpenCard {
		timeCardUseRecord := new(marketing_po.MTimeCardUseRecord)
		exists, err := session.Where("card_no = ? AND operate_type in ('CONSUMPTION','TOP_UP')", timeCard.CardNo).Get(timeCardUseRecord)
		if err != nil {
			return fmt.Errorf("查询次卡消费记录失败: %v", err)
		}
		if exists {
			return fmt.Errorf("次卡有消费记录或发生二次充值，不支持退单。如需退款，请前往\"会员中心-客户管理 客户卡包\"操作退卡。")
		}
	}

	// 3. 检查次卡状态
	if timeCard.Status != "IN_USE" && timeCard.Status != "NOT_START" {
		return fmt.Errorf("次卡状态异常[%s]，不可退款", timeCard.Status)
	}

	s := NewRefundOrderService()
	refundAmount := order.PayAmount

	var refundOrder *order_po.RefundOrder
	var refundSn string

	// 使用decimal计算更新值，确保精度
	totalChangeDecimal := decimal.NewFromInt(int64(timeCard.TotalChange))
	currentChangeDecimal := decimal.NewFromInt(int64(timeCard.CurrentChange))
	giftChangeDecimal := decimal.NewFromInt(int64(timeCard.GiftChange))
	balanceChangeDecimal := decimal.NewFromFloat(timeCard.BalanceChange)
	var totalNumDecimal, buyNumDecimal, giftNumDecimal int
	var balanceDecimal, refundAmountDecimal float64

	// 4.如果是次卡退卡，则使用请求的退款金额
	if req.IsRefundCard == 102 {
		refundSn = order.OrderSn
		refundAmount = utils.Yuan2Fen(timeCard.Balance)
		// 计算更新后的值
		totalNumDecimal = 0
		balanceDecimal = timeCard.AllBalance
		buyNumDecimal = 0
		giftNumDecimal = 0
		refundAmountDecimal = timeCard.AllBalance

	} else {
		refundSn = utils.GetSn("refund", session.Engine())[0]
		refundOrder, err = new(order_po.RefundOrder).CreateRefundOrder(session, order, order_po.RefundOrderParams{
			RefundType:   req.RefundType,
			RefundReason: req.RefundReason,
			Remark:       req.Remark,
			Type:         req.Type,
			Operator:     req.Operator,
			RefundAmount: refundAmount,
			ReType:       1,
		}, refundSn)
		if err != nil {
			return err
		}

		// 创建退款商品记录
		orderProducts, err := new(order_po.OrderProduct).GetOrderProducts(session, order_po.OrderProductRequest{
			OrderSn: req.OrderSn,
		})
		if err != nil {
			return fmt.Errorf("查询订单商品失败: %v", err)
		}

		if err = s.CreateRefundProducts(session, refundOrder, orderProducts, req); err != nil {
			log.Errorf("创建退款商品记录失败: orderSn=%s, refundSn=%s, error=%v", req.OrderSn, refundSn, err)
			return err
		}

		// 计算更新后的值
		totalNumDecimal = int(decimal.NewFromInt(int64(timeCard.TotalNum)).Sub(totalChangeDecimal).IntPart())
		buyNumDecimal = int(decimal.NewFromInt(int64(timeCard.BuyNum)).Sub(currentChangeDecimal).IntPart())
		giftNumDecimal = int(decimal.NewFromInt(int64(timeCard.AllGiftNum)).Sub(giftChangeDecimal).IntPart())
		balanceDecimal = decimal.NewFromFloat(timeCard.AllBalance).Sub(balanceChangeDecimal).InexactFloat64()
		refundAmountDecimal = decimal.NewFromFloat(timeCard.AllBalance).Sub(balanceChangeDecimal).InexactFloat64()

	}
	// 6. 创建次卡退款记录
	timeCardUseRecord := &marketing_po.MTimeCardUseRecord{
		TenantId:      timeCard.TenantId,
		ChainId:       timeCard.ChainId,
		RecordId:      timeCard.RecordId,
		CardId:        timeCard.CardId,
		CardNo:        timeCard.CardNo,
		CardName:      timeCard.CardName,
		CustomerId:    timeCard.CustomerId,
		EmployeeId:    timeCard.EmployeeId,
		OrderId:       order.Id,
		OrderNo:       order.OrderSn,
		OperateType:   recordStatus,
		Balance:       -balanceDecimal,
		TotalChange:   -totalNumDecimal,
		BalanceChange: balanceChangeDecimal.Neg().InexactFloat64(),
		CurrentChange: int(currentChangeDecimal.Neg().InexactFloat64()),
		GiftChange:    int(giftChangeDecimal.Neg().InexactFloat64()),
		Num:           buyNumDecimal,
		GiftNum:       giftNumDecimal,
		CreatedBy:     cast.ToInt64(jwtInfo.UserId),
		UpdatedBy:     cast.ToInt64(jwtInfo.UserId),
		Remark:        fmt.Sprintf("退款单号: %s", refundSn),
		Status:        "SUCCESS",
	}

	if err = timeCardUseRecord.CreateTimeCardUseRecord(session, timeCardUseRecord); err != nil {
		return fmt.Errorf("创建次卡退款记录失败: %v", err)
	}

	if req.IsRefundCard == 102 {
		// 为开卡退款创建一个临时的退款支付明细
		var PayType int
		switch req.RefundMethod {
		case 1:
			PayType = order_po.PayTypeWechat
		case 2:
			PayType = order_po.PayTypeAlipay
		case 3:
			PayType = order_po.PayTypeCash
		case 4:
			PayType = order_po.PayTypeOther
		case 5:
			PayType = order_po.PayTypeTimeCard
		}
		refundPayDetail := &order_po.RefundOrderPayDetail{
			TenantId:      cast.ToInt64(order.ShopId),
			OrderId:       int64(order.Id),
			OrderSn:       order.OrderSn,
			RefundSn:      refundSn,
			PaymentType:   PayType, // 使用请求中指定的退款方式
			PaymentAmount: utils.Yuan2Fen(timeCard.Balance),
			PaymentStatus: order_po.RefundStatusSuccess,
			CreatedBy:     jwtInfo.UserName,
			UpdatedBy:     jwtInfo.UserName,
		}
		if err := new(order_po.RefundOrderPayDetail).Create(session, refundPayDetail); err != nil {
			log.Errorf("创建退款支付明细失败: orderSn=%s, refundSn=%s, error=%v", req.OrderSn, refundSn, err)
			return fmt.Errorf("创建退款支付明细失败: %v", err)
		}
	} else {
		if err = s.createRefundPaymentDetails(session, order, refundOrder, jwtInfo.UserName, req); err != nil {
			return err
		}
	}

	// 7. 更新次卡记录
	_, err = session.Where("id = ?", timeCard.Id).Cols("total_num", "buy_num", "gift_num", "refund_amount", "status").
		Update(&marketing_po.MTimeCardRecord{
			TotalNum:     totalNumDecimal,
			BuyNum:       buyNumDecimal,
			GiftNum:      giftNumDecimal,
			RefundAmount: refundAmountDecimal,
			Balance:      balanceDecimal,
			Status:       status,
		})
	if err != nil {
		return err
	}

	if req.IsRefundCard != 102 {
		// 8. 更新订单状态
		if err = new(order_po.OrderMain).UpdateOrderStatus(session, order); err != nil {
			return err
		}
		// 9. 记录退款日志
		refundLog := &order_po.RefundOrderLog{
			RefundSn:      refundSn,
			Reason:        req.RefundReason,
			Money:         refundAmount,
			OperationType: "次卡订单退款",
			Operationer:   jwtInfo.UserName,
			ResType:       "退款成功",
			Ctime:         time.Now(),
		}
		if err = new(order_po.RefundOrderLog).Create(session, refundLog); err != nil {
			return fmt.Errorf("创建退款日志失败: %v", err)
		}
	}
	return nil
}

// 检查订单是否可以退款
func (s *RefundOrderService) checkCanRefund(order order_po.OrderMainView) error {
	if order.OrderStatus != 20 && order.OrderStatus != 30 {
		return fmt.Errorf("订单状态不支持退款")
	}
	return nil
}

// 检查是否使用了次卡
func (s *RefundOrderService) checkTimeCardUsage(session *xorm.Session, orderSn string) (bool, *marketing_po.MTimeCardUseRecord, error) {
	promotionRecord := new(order_po.OrderPromotionRecord)
	exists, err := session.Where("order_sn = ? AND promotion_type = 6", orderSn).Get(promotionRecord)
	if err != nil {
		return false, nil, fmt.Errorf("查询订单促销记录失败: %v", err)
	}
	if !exists {
		return false, nil, nil
	}

	// 查询次卡使用记录
	timeCardRecord := new(marketing_po.MTimeCardUseRecord)
	exists, err = session.Where("order_no = ?", orderSn).Get(timeCardRecord)
	if err != nil {
		return false, nil, fmt.Errorf("查询次卡使用记录失败: %v", err)
	}
	if !exists {
		return false, nil, nil
	}

	return true, timeCardRecord, nil
}

// createRefundPaymentDetails 创建退款支付明细
func (s *RefundOrderService) createRefundPaymentDetails(session *xorm.Session, order order_po.OrderMainView, refundOrder *order_po.RefundOrder, UserName string, req vo.RefundRequest) error {
	if req.Type == 2 {
		var PayType int
		switch req.RefundMethod {
		case 1:
			PayType = order_po.PayTypeWechat
		case 2:
			PayType = order_po.PayTypeAlipay
		case 3:
			PayType = order_po.PayTypeCash
		case 4:
			PayType = order_po.PayTypeOther
		case 5:
			PayType = order_po.PayTypeTimeCard
		}
		refundPayDetail := &order_po.RefundOrderPayDetail{
			TenantId:      cast.ToInt64(order.ShopId),
			OrderRefundId: int64(refundOrder.Id),
			RefundSn:      refundOrder.RefundSn,
			OrderId:       int64(order.Id),
			OrderSn:       order.OrderSn,
			PaymentType:   PayType, // 使用原支付方式
			PaymentAmount: utils.Yuan2Fen(refundOrder.RefundAmount),
			PaymentStatus: order_po.RefundStatusSuccess,
			CreatedBy:     UserName,
			UpdatedBy:     UserName,
		}
		if err := new(order_po.RefundOrderPayDetail).Create(session, refundPayDetail); err != nil {
			return fmt.Errorf("创建退款支付明细失败: %v", err)
		}
	} else {
		// 1. 查询原订单支付信息
		orderPayment := new(order_po.OrderPayment)
		payments, _, err := orderPayment.GetOrderPayment(session, order_po.OrderPaymentRequest{OrderSn: order.OrderSn})
		if err != nil {
			return fmt.Errorf("查询订单支付信息失败: %v", err)
		}
		// 2. 按原支付方式创建退款明细
		for _, payment := range payments {
			PayType := payment.PayType
			switch req.RefundMethod {
			case 1:
				PayType = order_po.PayTypeWechat
			case 2:
				PayType = order_po.PayTypeAlipay
			case 3:
				PayType = order_po.PayTypeCash
			case 4:
				PayType = order_po.PayTypeOther
			case 5:
				PayType = order_po.PayTypeTimeCard
			}
			refundPayDetail := &order_po.RefundOrderPayDetail{
				TenantId:          cast.ToInt64(order.ShopId),
				OrderRefundId:     int64(refundOrder.Id),
				RefundSn:          refundOrder.RefundSn,
				OrderId:           int64(order.Id),
				OrderSn:           order.OrderSn,
				SourcePaymentId:   int64(payment.Id),
				SourcePaymentType: orderPayment.GetPaymentTypeText(payment.PayType),
				PaymentType:       PayType, // 使用原支付方式
				PaymentAmount:     payment.Amount,
				PaymentStatus:     order_po.RefundStatusSuccess,
				CreatedBy:         UserName,
				UpdatedBy:         UserName,
			}
			if err = new(order_po.RefundOrderPayDetail).Create(session, refundPayDetail); err != nil {
				return fmt.Errorf("创建退款支付明细失败: %v", err)
			}

			// 更新原支付记录的退款金额
			if err = new(order_po.OrderPayment).UpdateRefundAmount(session, payment.Id, payment.RemainAmount); err != nil {
				return err
			}

			//如果是扫码支付,则调用扫码退款 支付方式:1:现金,2:余额,3:押金,4:标记收款,5:微信,6:支付宝,7:自有POS：8:挂账,10储值卡，11扫码支付
			if (req.RefundMethod == 0 && payment.PayType == 11) || (order.ChannelId == 1 && payment.PayType == 5) {
				refundResult, err := payment2.HandleScanPayRefund(session, order.ParentOrderSn, refundOrder.RefundSn, payment.RemainAmount)
				if err != nil {
					return fmt.Errorf("扫码支付退款失败: %v", err)
				}
				if refundResult.Code != 200 {
					return fmt.Errorf("扫码支付退款失败: %s", refundResult.Message)
				}
			}
		}
	}
	return nil
}

// handleTimeCardRefund 处理次卡退款
func (s *RefundOrderService) handleTimeCardRefund(session *xorm.Session, order order_po.OrderMainView, UserId int64) error {
	// 1. 检查是否使用了次卡
	timeCardUsed, timeCardRecord, err := s.checkTimeCardUsage(session, order.OrderSn)
	if err != nil {
		return fmt.Errorf("检查次卡使用失败: %v", err)
	}

	if !timeCardUsed || timeCardRecord == nil {
		return nil
	}
	if err = s.RefundTimeCard(session, order, UserId, int(math.Abs(float64(timeCardRecord.TotalChange)))); err != nil {
		return fmt.Errorf("处理次卡退款失败: %v", err)
	}

	return nil
}

// RefundTimeCard 处理次卡退款逻辑
func (s *RefundOrderService) RefundTimeCard(session *xorm.Session, order order_po.OrderMainView, UserId int64, refundCardNum int) error {
	// 1. 查询次卡使用记录
	timeCardUseRecord := new(marketing_po.MTimeCardUseRecord)
	exists, err := session.Where("order_id = ?", order.Id).Get(timeCardUseRecord)
	if err != nil {
		return fmt.Errorf("查询次卡使用记录失败: %v", err)
	}
	if !exists {
		return errors.New("次卡使用记录不存在")
	}

	// 3. 查询次卡记录
	timeCardRecord := new(marketing_po.MTimeCardRecord)
	exists, err = session.Where("card_no = ?", timeCardUseRecord.CardNo).Get(timeCardRecord)
	if err != nil {
		return fmt.Errorf("查询次卡记录失败: %v", err)
	}
	if !exists {
		return errors.New("次卡记录不存在")
	}

	// 4. 计算可退次数
	giftNumToAdd := min(refundCardNum, timeCardRecord.BuyingGiftNum-timeCardRecord.GiftNum)
	buyNumToAdd := refundCardNum - giftNumToAdd

	// 确保退还的次数不超过 MTimeCard 的数量
	if giftNumToAdd < 0 {
		giftNumToAdd = 0
	}
	if buyNumToAdd < 0 {
		buyNumToAdd = 0
	}
	// 确保 buyNumToAdd 不超过 MTimeCard 的 buyingNum
	if buyNumToAdd > (timeCardRecord.BuyingNum - timeCardRecord.BuyNum) {
		buyNumToAdd = timeCardRecord.BuyingNum - timeCardRecord.BuyNum
	}
	// 更新次数
	timeCardRecord.GiftNum += giftNumToAdd
	timeCardRecord.BuyNum += buyNumToAdd
	timeCardRecord.TotalNum = timeCardRecord.GiftNum + timeCardRecord.BuyNum

	_, err = session.Where("id = ?", timeCardRecord.Id).Cols("gift_num", "buy_num", "total_num").Update(timeCardRecord)
	if err != nil {
		return fmt.Errorf("更新次卡记录失败: %v", err)
	}

	// 5. 创建次卡退款记录
	refundRecord := &marketing_po.MTimeCardUseRecord{
		ChainId:       timeCardRecord.ChainId,
		TenantId:      timeCardRecord.TenantId,
		RecordId:      timeCardRecord.Id,
		CardId:        timeCardRecord.CardId,
		CardNo:        timeCardUseRecord.CardNo,
		CardName:      timeCardRecord.CardName,
		OperateType:   marketing_po.OperateTypeRefund,
		Balance:       timeCardRecord.Balance + math.Abs(timeCardUseRecord.BalanceChange),
		BalanceChange: math.Abs(timeCardUseRecord.BalanceChange),
		TotalChange:   refundCardNum,
		CurrentChange: refundCardNum,
		GiftChange:    giftNumToAdd,
		Num:           timeCardRecord.BuyNum,
		GiftNum:       timeCardRecord.GiftNum,
		CustomerId:    timeCardUseRecord.CustomerId,
		OrderId:       order.Id,
		OrderNo:       order.OrderSn,
		EmployeeId:    cast.ToInt(UserId),
		Remark:        fmt.Sprintf("退款单号: %s", order.OrderSn),
	}
	if err = refundRecord.CreateTimeCardUseRecord(session, refundRecord); err != nil {
		return fmt.Errorf("创建次卡退款记录失败: %v", err)
	}

	return nil
}

// createRefundProducts 批量创建退款商品记录
func (s *RefundOrderService) CreateRefundProducts(session *xorm.Session, refundOrder *order_po.RefundOrder, orderProducts []*order_po.OrderProductExt, req vo.RefundRequest) error {
	refundProducts := make([]*order_po.RefundOrderProduct, 0)
	// 全部退款
	if req.Type == 1 {
		for _, product := range orderProducts {
			refundProduct := &order_po.RefundOrderProduct{
				RefundSn:       refundOrder.RefundSn,
				SkuId:          product.SkuId,
				ParentSkuId:    product.ParentSkuId,
				OrderProductId: product.Id,
				ProductName:    product.ProductName,
				ProductType:    product.ProductType,
				ProductPrice:   product.DiscountPrice,
				MarkingPrice:   product.MarkingPrice,
				Quantity:       product.Number,
				Tkcount:        product.Number,
				RefundAmount:   fmt.Sprintf("%.2f", utils.Fen2Yuan(product.PaymentTotal)),
				RefundPrice:    product.PayPrice,
				OcId:           cast.ToString(product.Id),
				Barcode:        product.BarCode,
				Spec:           product.Specs,
				SubBizOrderId:  product.SubBizOrderId,
				BuyType:        product.BuyType,
			}
			refundProducts = append(refundProducts, refundProduct)
		}
	} else {
		// 部分退款
		// 构建商品ID到退款信息的映射
		productRefundMap := make(map[int]*vo.RefundProduct)
		for _, p := range req.Products {
			productRefundMap[p.OrderProductId] = p
		}

		for _, product := range orderProducts {
			if refundInfo, exists := productRefundMap[product.Id]; exists {
				refundAmount := fmt.Sprintf("%.2f", utils.Fen2Yuan(refundInfo.RefundAmount))
				// 如果是次卡退款，则不进行退款,只退次数
				if product.BuyType == 1 {
					refundAmount = "0"
				}
				refundProduct := &order_po.RefundOrderProduct{
					RefundSn:       refundOrder.RefundSn,
					SkuId:          product.SkuId,
					ParentSkuId:    product.ParentSkuId,
					OrderProductId: product.Id,
					ProductName:    product.ProductName,
					ProductType:    product.ProductType,
					ProductPrice:   product.DiscountPrice,
					MarkingPrice:   product.MarkingPrice,
					Quantity:       product.Number,
					Tkcount:        refundInfo.RefundNum,
					RefundAmount:   refundAmount,
					RefundPrice:    product.PayPrice,
					OcId:           cast.ToString(product.Id),
					Barcode:        product.BarCode,
					Spec:           product.Specs,
					SubBizOrderId:  product.SubBizOrderId,
					BuyType:        product.BuyType,
				}
				refundProducts = append(refundProducts, refundProduct)
			}
		}
	}

	return new(order_po.RefundOrderProduct).BatchCreate(session, refundProducts)
}

// handleStoreCardRefund 处理储值卡退款
func (s *RefundOrderService) handleStoreCardRefund(session *xorm.Session, order order_po.OrderMainView, jwtInfo *jwtauth.XCShopPayload) error {
	// 1. 查询储值卡使用记录
	storeCardRecord := new(marketing_po.MStoreCardUseRecord)
	exists, err := session.Where("order_no = ? AND operate_type = ?", order.OrderSn, marketing_po.OperateTypeConsumption).Get(storeCardRecord)
	if err != nil {
		return fmt.Errorf("查询储值卡使用记录失败: %v", err)
	}

	// 如果没有储值卡使用记录，直接返回
	if !exists {
		return nil
	}

	operateInfo := marketing_po.NewOperateInfo(
		order.Id,
		order.OrderSn,
		cast.ToInt64(jwtInfo.UserId),
		jwtInfo.UserName,
	)
	// 1. 增加储值卡余额
	if err = new(marketing_po.MStoreCard).RestoreStoreCardAmount(session, storeCardRecord, operateInfo.OperatorId); err != nil {
		return fmt.Errorf("增加储值卡余额失败: %v", err)
	}
	// 2. 添加储值卡退款记录
	if err = new(marketing_po.MStoreCardUseRecord).RefundStoreCard(session, storeCardRecord, operateInfo); err != nil {
		return fmt.Errorf("添加储值卡退款记录失败: %v", err)
	}

	return nil
}

// ProcessReturn 处理退货
func (s *RefundOrderService) ProcessReturn(req vo.ProcessReturnRequest, jwtInfo *jwtauth.XCShopPayload) error {
	s.Begin()
	defer s.Close()

	logfix := fmt.Sprintf("ProcessReturn req: %v,", utils.JsonEncode(req))
	session := s.Engine.NewSession()
	if err := session.Begin(); err != nil {
		return err
	}
	defer session.Close()

	// 增加redis锁
	lockKey := fmt.Sprintf("refund_return_lock_%s", req.RefundSn)
	if !cache.RedisLock(lockKey, 10) {
		return fmt.Errorf("请勿重复操作")
	}

	// 1. 查询退款单
	refundOrder := new(order_po.RefundOrderExtend)
	exists, err := session.Table("dc_order.refund_order").Alias("ro").Select("ro.*,d.warehouse_id,s.chain_id").
		Join("left", "dc_order.order_main d", "ro.order_sn = d.order_sn").
		Join("left", "datacenter.store s", "d.shop_id = s.finance_code").
		Where("ro.refund_sn = ?", req.RefundSn).Get(refundOrder)
	if err != nil {
		return fmt.Errorf("查询退款单失败: %v", err)
	}
	if !exists {
		return fmt.Errorf("退款单不存在")
	}

	// 2. 检查是否已处理
	if refundOrder.IsPicking == 1 {
		return fmt.Errorf("退货已处理")
	}

	// 3. 转换参数并更新退款商品实际退回数量
	updates := make([]*order_po.ReturnProductUpdate, 0)
	ids := make([]int, 0)
	for _, p := range req.Products {
		updates = append(updates, &order_po.ReturnProductUpdate{
			Id:              p.Id,
			ActualReturnNum: p.ActualReturnNum,
			Remark:          p.Remark,
		})
		ids = append(ids, p.Id)
	}
	// 3. 查询退款商品信息,并校验实际退回的库存ActualReturnNum，如果实际退回的库存小于退款商品的库存，要调用库存服务进行退货出库
	refundProducts, err := new(order_po.RefundOrderProduct).GetRefundOrderProductDetail(session, order_po.RefundOrderProductRequest{
		Ids: ids,
	})
	if err != nil {
		log.Errorf(logfix+"查询退款商品信息失败: %v", err)
		return fmt.Errorf("查询退款商品信息失败: %v", err)
	}

	ioBoundSvc := iobound2.NewIoBoundService()
	RefundOutCmd := iobound.IoBoundCreateCommand{
		ChainId:     int64(refundOrder.ChainId),
		StoreId:     refundOrder.ShopId,
		WarehouseId: refundOrder.WarehouseId,
		ItemType:    8,
		ItemRefId:   refundOrder.Id,
		ItemRefNo:   refundOrder.RefundSn,
		ItemRefType: 4,
		Remark:      "订单退款退货盘点出库",
		Details:     make([]iobound.IoBoundDetailCreateCommand, 0),
		Operator:    "system",
	}
	// 校验实际退回数量并构建出库明细
	for _, reqProduct := range req.Products {
		for _, refundProduct := range refundProducts {
			if reqProduct.Id == refundProduct.Id {
				// 如果实际退回数量小于请求退款数量，需要进行出库处理
				if refundProduct.Tkcount > reqProduct.ActualReturnNum {
					RefundOutCmd.Details = append(RefundOutCmd.Details, iobound.IoBoundDetailCreateCommand{
						BoundType:       2,
						ItemType:        8,
						SkuId:           cast.ToInt(refundProduct.SkuId),
						ProductName:     refundProduct.ProductName,
						IoPrice:         refundProduct.ProductPrice,
						IoCount:         refundProduct.Tkcount - reqProduct.ActualReturnNum, // 出库数量为差值
						ItemDetailRefId: refundProduct.SubBizOrderId,
					})
				}
				continue
			}
		}
	}

	// 如果有需要出库的商品，执行出库操作
	if len(RefundOutCmd.Details) > 0 {
		_, err = ioBoundSvc.Create(context.Background(), session, RefundOutCmd)
		if err != nil {
			session.Rollback()
			log.Errorf(logfix+"订单出库失败: %v", err)
			return fmt.Errorf("订单出库失败: %v", err)
		}
	}
	// 5. 更新退款商品实际退回数量
	if err = new(order_po.RefundOrderProduct).BatchUpdateReturnNum(session, updates); err != nil {
		session.Rollback()
		log.Errorf(logfix+"更新商品退回数量失败: %v", err)
		return fmt.Errorf("更新商品退回数量失败: %v", err)
	}

	// 4. 更新退款单处理状态
	if err = refundOrder.UpdatePickingStatus(session, req.RefundSn, jwtInfo.UserName); err != nil {
		log.Errorf(logfix+"更新退款单状态失败: %v", err)
		session.Rollback()
		return fmt.Errorf("更新退款单状态失败: %v", err)
	}

	return session.Commit()
}

// GetRefundReturnList 获取退货记录列表
func (s *RefundOrderService) GetRefundReturnList(req vo.RefundReturnListRequest) (out []vo.RefundReturnListItem, total int64, err error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	// 1. 查询退款单列表
	refundOrders := make([]*order_po.RefundOrderExtend, 0)
	query := session.Table("dc_order.refund_order").Alias("ro").Select("ro.*,d.pickup_code,om.warehouse_id,om.warehouse_name").
		Join("left", "dc_order.order_detail d", "ro.order_sn = d.order_sn").
		Join("left", "dc_order.order_main om", "ro.order_sn = om.order_sn").
		Where("ro.shop_id = ? and ro.refund_state =?", req.ShopId, order_po.RefundStateSuccess).
		In("ro.channel_id", []int{1, 2, 3}) // 1小程序 2美团 3饿了么

	if req.Status == 1 { // 待处理
		query = query.Where("ro.is_picking = 0")
	} else if req.Status == 2 { // 已处理
		query = query.Where("ro.is_picking = 1")
	}

	if req.OrderSn != "" {
		query = query.Where("ro.order_sn LIKE ?", "%"+req.OrderSn+"%")
	}

	if req.RefundSn != "" {
		query = query.Where("ro.refund_sn LIKE ?", "%"+req.RefundSn+"%")
	}

	// 手机号查询是在order_main表中en_receiver_mobile加密的，需要解密
	if req.Mobile != "" {
		query = query.Where("om.en_receiver_mobile = ?", utils.MobileEncrypt(req.Mobile))
	}

	total, err = query.OrderBy("ro.create_time DESC").Limit(req.PageSize, (req.Page-1)*req.PageSize).
		FindAndCount(&refundOrders)
	if err != nil {
		return nil, 0, fmt.Errorf("查询退款单列表失败: %v", err)
	}

	// 2. 查询退款商品信息
	out = make([]vo.RefundReturnListItem, 0)

	// 先获取所有退款单的 RefundSn
	refundSnList := make([]string, 0)
	for _, order := range refundOrders {
		refundSnList = append(refundSnList, order.RefundSn)
	}

	// 一次性查询所有退款商品信息
	productsMap := make(map[string][]*order_po.RefundOrderProductDetail)
	if len(refundSnList) > 0 {
		products, err := new(order_po.RefundOrderProduct).GetRefundOrderProductDetail(session, order_po.RefundOrderProductRequest{
			RefundSns: refundSnList,
			StoreId:   req.ShopId,
		})
		if err != nil {
			return nil, 0, fmt.Errorf("查询退款商品信息失败: %v", err)
		}

		// 将查询结果按 RefundSn 分类
		for _, product := range products {
			productsMap[product.RefundSn] = append(productsMap[product.RefundSn], product)
		}
	}

	for _, order := range refundOrders {
		products, exists := productsMap[order.RefundSn]
		if !exists {
			continue
		}
		totalRefundCount := 0
		totalQuantity := 0
		productItems := make([]*vo.RefundReturnProductItem, 0)
		for _, p := range products {
			productItems = append(productItems, &vo.RefundReturnProductItem{
				Id:              p.Id,
				ProductName:     p.ProductName,
				Quantity:        p.Quantity,
				ActualReturnNum: p.Tkcount,
				Remark:          p.Remark,
				Specs:           p.Specs,
				LocationCode:    p.LocationCode,
				ProductPrice:    p.ProductPrice,
				MarkingPrice:    p.MarkingPrice,
				Barcode:         p.BarCode,
				Image:           p.Image,
			})
			totalRefundCount += p.ActualReturnNum // 累加退款数量
			totalQuantity += p.Quantity
		}

		out = append(out, vo.RefundReturnListItem{
			RefundSn:         order.RefundSn,
			OrderSn:          order.OrderSn,
			IsPicking:        order.IsPicking,
			Operator:         order.Operator,
			RefundAmount:     order.RefundAmount,
			CreateTime:       order.CreateTime,
			Products:         productItems,
			TotalRefundCount: totalRefundCount,
			TotalQuantity:    totalQuantity,
			PickupCode:       order.PickupCode,
			ProcessTime:      order.ProcessTime.Format("2006-01-02 15:04:05"),
			ChannelId:        order.ChannelId,
			WarehouseId:      order.WarehouseId,
			WarehouseName:    order.WarehouseName,
		})
	}
	return
}

// handleRefundInventory 处理退款退库存
func (s *RefundOrderService) handleRefundInventory(session *xorm.Session, refundOrder *order_po.RefundOrder, order order_po.OrderMainView, refundProduct []*vo.RefundProduct) error {
	// 构造退库存请求参数
	details := make([]iobound.InBoundDetailCommand, 0)
	for _, p := range refundProduct {
		// 查询订单商品信息
		orderProduct := new(order_po.OrderProduct)
		exists, err := session.Where("id = ?", p.OrderProductId).Get(orderProduct)
		if err != nil {
			log.Errorf("查询订单商品失败: %v", err)
			return fmt.Errorf("查询订单商品失败: %v", err)
		}
		if !exists {
			log.Errorf("订单商品不存在,OrderProductId: %d", p.OrderProductId)
			return fmt.Errorf("订单商品不存在: %d", p.OrderProductId)
		}
		// 查询父订单商品信息
		parentOrderProduct := new(order_po.OrderProduct)
		parentExists, parentErr := session.Where("order_sn = ? and sku_id=?", order.ParentOrderSn, orderProduct.SkuId).Get(parentOrderProduct)
		if parentErr != nil {
			log.Errorf("查询父订单商品失败: %v", parentErr)
			return fmt.Errorf("查询父订单商品失败: %v", parentErr)
		}
		if !parentExists {
			log.Errorf("父订单商品不存在,SkuId: %s", orderProduct.SkuId)
			return fmt.Errorf("父订单商品不存在: %s", orderProduct.SkuId)
		}
		// 如果是服务商品，则不退库存
		if orderProduct.ProductType >= 4 {
			continue
		}
		details = append(details, iobound.InBoundDetailCommand{
			ItemDetailRefId: cast.ToString(parentOrderProduct.SubBizOrderId),
			SkuId:           cast.ToInt(orderProduct.SkuId),
			ProductName:     orderProduct.ProductName,
			IoPrice:         orderProduct.PayPrice, // 使用支付价格作为入库价格
			IoCount:         p.RefundNum,
		})
	}
	log.Infof("退货入库明细: %v", utils.JsonEncode(details))
	if len(details) == 0 {
		return nil
	}
	// 调用库存服务的退货入库接口
	ioBoundService := iobound2.NewIoBoundService()
	_, err := ioBoundService.RefundIn(context.Background(), session, iobound.InBoundCommand{
		ChainId:     order.ChainId,
		StoreId:     order.ShopId,
		WarehouseId: order.WarehouseId,
		ChannelId:   order.ChannelId,
		RefId:       refundOrder.Id,
		RefNo:       refundOrder.RefundSn,
		RefType:     3, // 订单退货
		Remark:      "订单退款退货入库",
		Details:     details,
		Operator:    refundOrder.Operator,
	})

	if err != nil {
		return fmt.Errorf("创建退货入库单失败: %v", err)
	}

	return nil
}
