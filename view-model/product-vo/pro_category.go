package product_vo

import (
	product_po "eShop/domain/product-po"
	viewmodel "eShop/view-model"
)

type CategoryListRes struct {
	viewmodel.BasePageHttpResponse
	Data []product_po.ProCategory `json:"data"`
}

type CategoryListForApiRes struct {
	viewmodel.BasePageHttpResponse
	Data []*CategoryInfo `json:"data"`
}
type CategoryListForApiReq struct {
	ChainId string `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"` //连锁ID(前端无需传)
	StoreId string `json:"store_id"`
	Type    int    `json:"type"` //分类类型：1-门店线上，2-门店线下，3-服务分类，4-活体分类

}
type HasUpRunningFullReductionProductReq struct {
	ChainId     string `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"` //连锁ID
	StoreId     string `json:"store_id"`
	CategoryIds []int  `json:"category_ids"`
}

type HasUpRunningCouponProductReq struct {
	ChainId     string `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"` //连锁ID
	StoreId     string `json:"store_id"`
	CategoryIds []int  `json:"category_ids"`
}
type CategoryInfo struct {
	Id          int    `json:"id"`          //分类id
	Name        string `json:"name"`        //分类名称
	ParentId    int    `json:"parent_id"`   //父类id
	Sort        int    `json:"sort"`        //排序
	CreateDate  string `json:"create_date"` //创建时间
	CreatedName string `json:"create_name"` //创建人
}

// 查询分类列表
type CategoryListReq struct {
	//分类ID列表
	Ids []int `json:"ids"`
	//门店ID(备注： 前端无需传此字段)
	StoreId string `json:"store_id"`
	//分类类型：1-门店线上，2-门店线下，3-服务分类，4-活体分类
	Type int `json:"type"`
	//数据类型：1-店内商品，2-新零售商品
	DataType int `json:"data_type"`
	// 仓库id
	WarehouseId int `json:"warehouse_id"`
}

// 添加、编辑分类
type AddCategoryReq struct {
	ChainId  string `json:"chain_id"`  //连锁ID
	Id       int    `json:"id"`        //分类id
	ParentId int    `json:"parent_id"` //父类id
	Name     string `json:"name"`      //分类名称
	Sort     int    `json:"sort"`      //排序
	Type     int    `json:"type"`      //分类类型：1-门店线上，2-门店线下，3-服务分类，4-活体分类
}

// 删除分类
type DelCategoryReq struct {
	Id int `json:"id"` //分类id
}

// 同步第三方门分类参数
type CategorySync struct {
	// 同步类型-必须 0 未知 1 新增 2 修改 3 删除
	SyncType int
	// 连锁ID-必须
	ChainId int64
	//门店ID
	StoreId string
	// 分类Id-必须
	CategoryId string
	// 分类名称-必须
	CategoryName string
	// 上级分类id-必须
	ParentId int
	// 排序-必须
	Sort int
	//员工id
	UserId string
	//是否代运营
	RoleType int
}

type StoreChannel struct {
	AppChannel     int    `json:"app_channel" xorm:"default 0 comment('1.阿闻自有,2.TP代运营') INT 'app_channel'"`
	ChainId        int64  `json:"chain_id" xorm:"default 0 comment('连锁ID') BIGINT 'chain_id'"`
	ChannelId      int    `json:"channel_id" xorm:"not null default 0 comment('渠道id(1-阿闻，2-美团，3-饿了么，4-京东到家)') INT 'channel_id'"`
	ChannelStoreId string `json:"channel_store_id" xorm:"default 'null' comment('渠道门店id') VARCHAR(50) 'channel_store_id'"`
	StoreName      string `json:"store_name" xorm:"default '' comment('门店名称') VARCHAR(50) 'store_name'"`
	FinanceCode    string `json:"finance_code" xorm:"not null comment('财务编码') VARCHAR(50) 'finance_code'"`
}

// 同步所有分类
type SyncAllCategoryReq struct {
	StoreId string `json:"store_id"` //门店ID
}

// 同步执行结果
type SyncCategoryToThirdParamsSesult struct {
	// 分类Id
	CategoryId int
	// 分类名称
	CategoryName string
	// 财务代码
	FinanceCode string
	// shopName 门店名称
	ShopName string
	// 是否同步成功
	IsSuccess bool
	// 执行结果
	Message string
	// channel_id
	ChannelId int
	// 操作类型 1新增 2 修改 3删除
	SyncType int
	// 第三方门店的id
	ChannelStoreId string
	// appChannel
	AppChannel int32
}

type NewElmShopCategoryRequest struct {
	//店铺内分类ID（新增分类时返回的分类ID)
	CategoryId string `protobuf:"bytes,1,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	//商家自定义分类id（不可与 category_id 同时使用）
	ShopCustomId string `protobuf:"bytes,1,opt,name=shop_custom_id,json=shopCustomId,proto3" json:"shop_custom_id"`
	//商家自定义父级分类ID
	ShopCustomParentId string `protobuf:"bytes,1,opt,name=shop_custom_parent_id,json=shopCustomParentId,proto3" json:"shop_custom_parent_id"`
	//门店ID
	ShopId string `protobuf:"bytes,2,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	//上级分类ID
	ParentCategoryId string `protobuf:"bytes,3,opt,name=parent_category_id,json=parentCategoryId,proto3" json:"parent_category_id"`
	//分类名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	//排序
	Rank string `protobuf:"bytes,5,opt,name=rank,proto3" json:"rank"`
	// app渠道 1.阿闻自有,2.TP代运营
	AppChannel int32 `protobuf:"varint,6,opt,name=appChannel,proto3" json:"appChannel"`
}

type DelElmShopCategoryRequest struct {
	//门店ID
	ShopId string `protobuf:"bytes,1,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	//分类ID
	CategoryId string `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	//分类名称
	CategoryName string `protobuf:"bytes,3,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	// app渠道 1.阿闻自有,2.TP代运营
	AppChannel int32 `protobuf:"varint,4,opt,name=appChannel,proto3" json:"appChannel"`
	//商家自定义分类id（不可与 category_id 同时使用）
	ShopCustomId string `protobuf:"bytes,1,opt,name=shop_custom_id,json=shopCustomId,proto3" json:"shop_custom_id"`
}

type UpdateElmShopCategoryRequest struct {
	//商品ID(新增商品时返回的商品ID)
	SkuId int64 `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//商品商家自定义ID, 与sku_id参数互斥
	CustomSkuId int `protobuf:"bytes,1,opt,name=custom_sku_id,json=customSkuId,proto3" json:"custom_sku_id"`
	//店铺内分类ID（新增分类时返回的分类ID)
	CategoryId string `protobuf:"bytes,1,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	//商家自定义分类id（不可与 category_id 同时使用）
	ShopCustomId string `protobuf:"bytes,1,opt,name=shop_custom_id,json=shopCustomId,proto3" json:"shop_custom_id"`
	//商家自定义父级分类ID
	//ShopCustomParentId string `protobuf:"bytes,1,opt,name=shop_custom_parent_id,json=shopCustomParentId,proto3" json:"shop_custom_parent_id"`
	//门店ID
	ShopId string `protobuf:"bytes,2,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	//上级分类ID
	ParentCategoryId string `protobuf:"bytes,3,opt,name=parent_category_id,json=parentCategoryId,proto3" json:"parent_category_id"`
	//分类名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	//排序
	Rank string `protobuf:"bytes,5,opt,name=rank,proto3" json:"rank"`
	// app渠道 1.阿闻自有,2.TP代运营
	AppChannel int32 `protobuf:"varint,6,opt,name=appChannel,proto3" json:"appChannel"`
}

type CateName struct {
	Id         int    `json:"id"`
	ParentName string `json:"parent_name"`
	ChildName  string `json:"child_name"`
}

type BatchImportProductAndCategoryReq struct {
	FileUrl   string `json:"file_url"` //文件地址
	ChainId   string `json:"chain_id"`
	BatchType int    `json:"batch_type"` //
}
