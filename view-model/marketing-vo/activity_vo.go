package marketing_vo

import (
	marketing_po "eShop/domain/marketing-po"
	viewmodel "eShop/view-model"
)

type HasUpRunningSpecialPriceProductReq struct {
	ChannelId int    `json:"channel_id"` // 渠道id
	ChainId   string `json:"chain_id"`   // 连锁id
	StoreId   string `json:"store_id"`   // 店铺id

}
type SaveActivityReq struct {
	// 活动信息
	marketing_po.MarketingActivity
	// 满减活动规则
	FullReductionRules []marketing_po.FullReductionRule `json:"full_reduction_rules"`
	// 满减活动设置
	FullReductionSetting marketing_po.FullReductionSetting `json:"full_reduction_setting"`
	// 特价活动规则
	SpecialPriceRule marketing_po.SpecialPriceRule `json:"special_price_rule"`
	// 特价活动设置
	SpecialPriceProducts []marketing_po.SpecialPriceProduct `json:"special_price_products"`
	//排除的商品列表
	ExcludePriceProducts []marketing_po.SpecialPriceProduct `json:"exclude_price_products"`
}
type SaveActivityRes struct {
	viewmodel.BaseHttpResponse
}

type EndActivityReq struct {
	// 活动id
	Id int `json:"id"`
	// 连锁id(前端不用传)
	ChainId int64 `json:"chain_id"`
	// 门店id(前端不用传)
	StoreId string `json:"store_id"`
}
type EndActivityRes struct {
	viewmodel.BaseHttpResponse
}
type ActivityListReq struct {
	// 连锁id(前端不用传)
	ChainId int64 `json:"chain_id"`
	// 门店id(前端不用传)
	StoreId string `json:"store_id"`
	// 活动状态（0：未开始，1：进行中，2：已结束）
	Status int `json:"status"`
	// 活动名称
	Name string `json:"name"`
	// 活动类型（1：满减活动，2：特价活动）
	Type int `json:"type"`
	// 页码
	PageIndex int `json:"page_index"`
	// 每页条数
	PageSize int `json:"page_size"`
	// 排序字段(默认是活动id倒序)
	OrderBy string `json:"order_by"`
}
type RunningActivityProductsReq struct {
	// 渠道id(前端若不传，则默认是线下门店端)
	ChannelId int `json:"channel_id"`
	// 连锁id(前端不用传)
	ChainId int64 `json:"chain_id"`
	// 门店id(前端不用传)
	StoreId string `json:"store_id"`
	SkuIds  string `json:"sku_ids"` // 商品SKUID
	// 页码
	PageIndex int `json:"page_index"`
	// 每页条数
	PageSize int `json:"page_size"`
}
type RunningActivityProductsRes struct {
	viewmodel.BaseHttpResponse
	Data  []RunningActivityProducts `json:"data"`
	Total int64                     `json:"total"`
}
type RunningActivityProducts struct {
	// 连锁id
	ChainId int64 `json:"chain_id"`
	// 门店id
	StoreId string `json:"store_id"`
	// 商品id
	ProductId int `json:"product_id"`
	// skuId
	SkuId int `json:"sku_id"`
	// 关联类型：1-优惠券 2-次卡 3-储值卡 4-满减 5-特价
	Type int `json:"type"`
	// 商品名称
	Name string `json:"name"`
	// 商品图片
	Pic string `json:"pic"`
	// 库存
	Stock int `json:"stock"`
	// 适用类型：1-商品 2-商品品类
	ApplyType int `json:"apply_type"`
	// 零售价
	RetailPrice int `json:"retail_price"`
	// 活动价
	ActivityPrice int `json:"activity_price"`
	// 折扣率
	DiscountRate float64 `json:"discount_rate"`
	// 活动规则描述
	RuleDescribe string `json:"rule_describe"`
	// 服务时长
	ServiceDuration int `json:"service_duration"`
	// 商品类型
	ProductType int `json:"product_type"`
	// 商品规格
	ProductSpecs string `json:"product_specs"`

	// 分类id
	CategoryId int `json:"category_id"`

	// 活动详情
	ActivityDetail marketing_po.MarketingActivityInfo `json:"activity_detail"`
}
type ActivityListRes struct {
	viewmodel.BaseHttpResponse
	Data struct {
		List  []ActivityListData `json:"list"`  // 列表数据
		Stats ActivityStats      `json:"stats"` // 统计字段
	} `json:"data"`
	Total int64 `json:"total"`
}
type ApiActivityListRes struct {
	viewmodel.BaseHttpResponse
	Data []ActivityListData `json:"data"`
	// 总条数
	Total int64
}
type ActivityStats struct {
	//进行中
	Running int64 `json:"running"`
	// 待生效
	Pending int64 `json:"pending"`
	// 已结束
	Ended int64 `json:"ended"`
}
type ActivityListData struct {
	Id int `json:"id"`
	// 活动名称
	Name string `json:"name"`
	// 活动类型（1：满减活动，2：特价活动）
	Type int `json:"type"`
	// 活动状态（0：未开始，1：进行中，2：已结束）
	Status int `json:"status"`
	// 活动开始时间
	StartTime string `json:"start_time"`
	// 活动结束时间
	EndTime string `json:"end_time"`
	// 活动创建时间
	CreatedTime string `json:"created_time"`
	// 剩余时间(活动结束时间-当前时间)
	ResidueTime string `json:"residue_time"`
	// 活动规则描述
	RuleDescribe string `json:"rule_describe"`
}

type GetActivityDetailReq struct {
	// 活动id
	Id int `json:"id"`
	// 连锁id(前端不用传)
	ChainId int64 `json:"chain_id"`
	// 门店id(前端不用传)
	StoreId string `json:"store_id"`
}
type GetActivityDetailRes struct {
	viewmodel.BaseHttpResponse
	Data ActivityInfo `json:"data"`
}

type ActivityInfo struct {
	// 活动信息
	marketing_po.MarketingActivity
	// 满减活动规则
	FullReductionRules []marketing_po.FullReductionRule `json:"full_reduction_rules"`
	// 满减活动设置
	FullReductionSetting marketing_po.FullReductionSetting `json:"full_reduction_setting"`
	// 特价活动规则
	SpecialPriceRule marketing_po.SpecialPriceRule `json:"special_price_rule"`
	// 特价活动设置
	SpecialPriceProducts []marketing_po.SpecialPriceProduct `json:"special_price_products"`
	//排除的商品列表
	ExcludePriceProducts []marketing_po.SpecialPriceProduct `json:"exclude_price_products"`
}
