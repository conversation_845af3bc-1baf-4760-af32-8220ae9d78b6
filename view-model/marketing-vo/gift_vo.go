package marketing_vo

import (
	viewmodel "eShop/view-model"
	"time"
)

// GiftListReq 赠品列表请求
type GiftListReq struct {
	viewmodel.BasePageHttpRequest
	//赠品名称
	Name string `form:"name"`
	//状态
	Status string `form:"status"`
	//店铺ID
	StoreId string `form:"store_id"`
	// 商品Sku列表
	SkuIds []int `protobuf:"bytes,2,rep,name=skuIds,proto3" json:"sku_ids"`
}

// GiftListRes 赠品列表响应
type GiftListRes struct {
	viewmodel.BasePageHttpResponse
	//列表数据
	Data []GiftListItem `json:"data"`
}

// SaveGiftReq 保存赠品请求
type SaveGiftReq struct {
	//ID,新增时为0
	Id int `json:"id"`
	//连锁ID
	ChainId int64 `json:"chain_id"`
	//店铺ID
	StoreId string `json:"store_id"`
	//赠品名称
	Name string `json:"name" validate:"required,max=100"`
	//商品ID
	ProductId int `json:"product_id" validate:"required"`
	//商品SKU_ID
	SkuId int `json:"sku_id" validate:"required"`
	//是否长期有效 0-否 1-是
	IsPermanent int `json:"is_permanent"`
	//开始时间
	EnableTime string `json:"enable_time"`
	//结束时间
	ExpireTime string `json:"expire_time"`
	//每人限领次数 0不限制, 大于0则限制多少次
	PersonLimit int `json:"person_limit"`
}

// SaveGiftRes 保存赠品响应
type SaveGiftRes struct {
	viewmodel.BaseHttpResponse
}

// 请求结构
type GiftDetailReq struct {
	//赠品ID
	Id int `json:"id" validate:"required"`
	//店铺ID
	StoreId string `json:"store_id" validate:"required"`
}

// 响应结构
type GiftDetailResp struct {
	//ID
	Id int `json:"id"`
	//连锁ID
	ChainId int64 `json:"chain_id"`
	//店铺ID
	StoreId string `json:"store_id"`
	//赠品名称
	Name string `json:"name"`
	//商品ID
	ProductId int `json:"product_id"`
	//商品SKU_ID
	SkuId int `json:"sku_id"`
	//是否长期有效
	IsPermanent int `json:"is_permanent"`
	//开始时间
	EnableTime *string `json:"enable_time"`
	//结束时间
	ExpireTime *string `json:"expire_time"`
	//每人限领次数
	PersonLimit int `json:"person_limit"`
	//总数量
	TotalCount int `json:"total_count"`
	//已使用数量
	UsedCount int `json:"used_count"`
	//状态：1-未开始 2-进行中 3-已结束 4-已禁用
	Status int `json:"status"`
	//商品信息
	ProductInfo struct {
		//商品名称
		ProductName string `json:"product_name"`
		//条码
		Barcode string `json:"barcode"`
		//商品规格
		ProductSpecs string `json:"product_specs"`
		//零售价
		RetailPrice float64 `json:"retail_price"`
		//库存
		Stock int `json:"stock"`
	} `json:"product_info"`
}

// GiftListItem 赠品列表项
type GiftListItem struct {
	//ID
	Id int `json:"id" xorm:"id"`
	//连锁ID
	ChainId int64 `json:"chain_id" xorm:"chain_id"`
	//店铺ID
	StoreId string `json:"store_id" xorm:"store_id"`
	//赠品名称
	Name string `json:"name" xorm:"name"`
	//商品ID
	ProductId int `json:"product_id" xorm:"product_id"`
	//商品SKU_ID
	SkuId int `json:"sku_id" xorm:"sku_id"`
	//是否长期有效
	IsPermanent int `json:"is_permanent" xorm:"is_permanent"`
	//有效期开始时间
	EnableTime *string `xorm:"'enable_time'" json:"enable_time"`
	//有效期结束时间
	ExpireTime *string `xorm:"'expire_time'" json:"expire_time"`
	//每人限领次数
	PersonLimit int `json:"person_limit" xorm:"person_limit"`
	//总数量
	TotalCount int `json:"total_count" xorm:"total_count"`
	//已使用数量
	UsedCount int `json:"used_count" xorm:"used_count"`
	//状态
	Status int `json:"status" xorm:"status"`
	//是否删除
	IsDeleted int `json:"is_deleted" xorm:"is_deleted"`
	//创建时间
	CreatedTime time.Time `json:"created_time" xorm:"created_time"`
	//修改时间
	ModifiedTime time.Time `json:"modified_time" xorm:"modified_time"`

	// 商品规格
	ProductSpecs string `json:"product_specs" xorm:"product_specs"`
	// 商品名称
	ProductName string `json:"product_name" xorm:"product_name"`
	// 零售价
	RetailPrice float64 `json:"retail_price" xorm:"retail_price"`
	// 图片
	Pic string `json:"pic" xorm:"pic"`
}
