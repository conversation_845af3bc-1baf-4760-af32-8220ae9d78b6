package marketing_vo

// PetInvitePageReq 邀请分页查询请求
type PetInvitePageReq struct {
	InviterMobile         string `json:"inviter_mobile" query:"inviter_en_mobile:eq"`                           // 邀请人手机号
	InviterNickname       string `json:"inviter_nickname" query:"inviter_nickname:like"`                        // 邀请人昵称
	InviteeMobile         string `json:"invitee_mobile" query:"invitee_en_mobile:eq"`                           // 被邀请用户手机号
	InviteeNickname       string `json:"invitee_nickname" query:"invitee_nickname:like"`                        // 被邀请用户昵称
	InviteeRegisterStatus int    `json:"invitee_register_status" query:"invitee_register_status:eq,allow_zero"` // 被邀请用户注册状态 0未注册 1已注册
	WorkCode              string `json:"work_code" query:"work_code:eq"`                                        // 关联的作品编号
	VoteTimeStart         string `json:"vote_time_start" query:"vote_time:gte"`                                 // 投票时间开始
	VoteTimeEnd           string `json:"vote_time_end" query:"vote_time:lte"`                                   // 投票时间结束
	CreateTimeStart       string `json:"create_time_start" query:"create_time:gte"`                             // 邀请时间开始
	CreateTimeEnd         string `json:"create_time_end" query:"create_time:lte"`                               // 邀请时间结束
	PageIndex             int    `json:"page_index"`                                                            // 页码
	PageSize              int    `json:"page_size"`                                                             // 每页大小
}

// PetInviteResp 邀请响应结构
type PetInviteResp struct {
	Id                    int    `json:"id"`                      // 主键 (原：Id)
	CreateTime            string `json:"create_time"`             // 邀请时间 (原：创建时间)
	InviterId             string `json:"inviter_id"`              // 邀请人用户id
	InviterNickname       string `json:"inviter_nickname"`        // 邀请人昵称
	InviterMobile         string `json:"inviter_mobile"`          // 邀请人手机号带星
	InviterEnMobile       string `json:"inviter_en_mobile"`       // 邀请人加密手机号
	InviterType           int    `json:"inviter_type"`            // 邀请人客户类型 0新客 1老客
	InviteeId             string `json:"invitee_id"`              // 被邀请用户id
	InviteeOpenId         string `json:"invitee_open_id"`         // 被邀请人OpenId
	InviteeNickname       string `json:"invitee_nickname"`        // 被邀请人昵称
	InviteeMobile         string `json:"invitee_mobile"`          // 被邀请人手机号带星
	InviteeEnMobile       string `json:"invitee_en_mobile"`       // 被邀请人加密手机号
	InviteeRegisterStatus int    `json:"invitee_register_status"` // 被邀请用户注册状态 0未注册 1已注册
	InviteeRegisterTime   string `json:"invitee_register_time"`   // 被邀请用户注册时间
	InviteeType           int    `json:"invitee_type"`            // 被邀请客户类型 0新客 1老客
	VoteStatus            int    `json:"vote_status"`             // 被邀请客户投票状态 0未投票 1已投票 2投票失败
	WorkCode              string `json:"work_code"`               // 关联的作品编号
	VoteTime              string `json:"vote_time"`               // 投票时间
	VoteCount             int    `json:"vote_count"`              // 投票数
	UpdateTime            string `json:"update_time"`             // 更新时间
}
