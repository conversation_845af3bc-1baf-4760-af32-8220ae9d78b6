package omnibus_vo

import (
	po "eShop/domain/product-po"
	viewmodel "eShop/view-model"
	"time"
)

type TaskListAsync struct {
	//任务id
	Id int `json:"id" xorm:"pk autoincr not null comment('任务id') INT 'id'"`
	//任务内容:
	TaskContent int `json:"task_content" xorm:"not null default 0 comment('任务内容:') INT 'task_content'"`
	//任务状态:1:未开始;2:进行中;3:已完成 4:失败
	TaskStatus int8 `json:"task_status" xorm:"not null default 1 comment('任务状态:1:未开始;2:进行中;3:已完成 4:失败') TINYINT(1) 'task_status'"`
	//任务详情
	TaskDetail string `json:"task_detail" xorm:"not null default '' comment('任务详情') VARCHAR(10000) 'task_detail'"`
	//任务KEY
	KeyStr string `json:"key_str" xorm:"not null default '' comment('任务KEY，相同KEY不允许同时进行') VARCHAR(500) 'key_str'"`
	//操作文件路径或者参数
	OperationFileUrl string `json:"operation_file_url" xorm:"not null comment('操作文件路径或者参数') TEXT 'operation_file_url'"`
	RequestHeader    string `json:"request_header" xorm:"default 'null' comment('操作请求的token值，类似userinfo') VARCHAR(255) 'request_header'"`
	//操作结果文件路径
	ResulteFileUrl string `json:"resulte_file_url" xorm:"not null default '' comment('操作结果文件路径') VARCHAR(255) 'resulte_file_url'"`
	CreateId       string `json:"create_id" xorm:"not null default 0 comment('创建人id') VARCHAR(100) 'create_id'"`
	CreateName     string `json:"create_name" xorm:"default '' comment('创建人姓名') VARCHAR(50) 'create_name'"`
	CreateMobile   string `json:"create_mobile" xorm:"default '' comment('创建人手机号') VARCHAR(50) 'create_mobile'"`
	//创建人id
	CreateIp string `json:"create_ip" xorm:"default '' comment('创建人ip') VARCHAR(100) 'create_ip'"`
	//ip所属位置
	IpLocation string `json:"ip_location" xorm:"default '' comment('ip所属位置') VARCHAR(50) 'ip_location'"`
	//成功数量
	SuccessNum int `json:"success_num" xorm:"default 0 comment('成功数量') INT 'success_num'"`
	//失败数量
	FailNum int `json:"fail_num" xorm:"default 0 comment('失败数量') INT 'fail_num'"`
	//任务名称扩展字段
	ExtendedData string `json:"extended_data" xorm:"comment('任务名称扩展字段') TEXT 'extended_data'"`
	//上下文数据
	ContextData string `json:"context_data" xorm:"comment('上下文数据') LONGTEXT 'context_data'"`
	//执行失败次数
	DoCount int `json:"do_count" xorm:"default 0 comment('执行失败次数') INT 'do_count'"`
	//上次执行失败原因
	ErrMes string `json:"err_mes" xorm:"default '' comment('上次执行失败原因') VARCHAR(500) 'err_mes'"`
	//创建时间
	CreateTime time.Time `json:"create_time" xorm:"default 'null' comment('创建时间') DATETIME 'create_time' created"`
	//修改时间
	UpdateTime time.Time `json:"update_time" xorm:"default 'null' comment('修改时间') DATETIME 'update_time' updated"`
	//主体
	OrgId int `json:"org_id" xorm:"default 1 INT 'org_id'"`
}

// 任务列表返回参数
type TaskAsyncListRes struct {
	viewmodel.BasePageHttpResponse
	//业务员列表数据
	Data []TaskListAsync `json:"data"`
}

// 获取请求列表参数
type GetTaskListRequest struct {
	viewmodel.BasePageHttpRequest
	//任务id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//任务内容:1:批量新建;2:批量删除;3:批量更新
	TaskContent int32 `protobuf:"varint,2,opt,name=task_content,json=taskContent,proto3" json:"task_content"`
	//任务状态:1:调度中;2:进行中;3:已完成；4：失败
	TaskStatus int32 `protobuf:"varint,3,opt,name=task_status,json=taskStatus,proto3" json:"task_status"`
	//创建人id
	CreateId string `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	//排序类型：createTimeDesc:根据创建时间倒序
	Sort string `protobuf:"bytes,6,opt,name=sort,proto3" json:"sort"`
	//创建时间
	CreateTime string `protobuf:"bytes,7,opt,name=createtime,proto3" json:"create_time"`
	//主体
	OrgId int `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	//发起人 0:自己;1:全部;2:其他;
	Promoter int32 `protobuf:"varint,11,opt,name=promoter,proto3" json:"promoter"`
	//任务内容:
	ContentStr string `protobuf:"varint,11,opt,name=content_str,proto3" json:"content_str"`
	//主体
	ChainId int `json:"chain_id" xorm:"default 0 comment('连锁ID') INT 'chain_id'"`
}

type AsyncProductParam struct {
	Data []po.ProProductStoreInfoExt2 `json:"data"`
}

type SpuSyncInfo struct {
	Data []struct {
		StoreId   string `json:"store_id"`
		ProductId int    `json:"product_id"`
		ChannelId int    `json:"channel_id"`
	} `json:"data"`
}


