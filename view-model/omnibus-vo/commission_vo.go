package omnibus_vo

import (
	"time"
)

// 提成设置相关VO

// CommissionDetailItem 提成明细项
type CommissionDetailItem struct {
	// 提成明细ID
	Id int `json:"id,omitempty"`
	// 提成类型：1-商品提成，2-服务提成，3-寄养提成，4-活动提成
	CommissionType int `json:"commission_type"`
	// 计算类型：1-按售价，2-按实收金额
	CalcType int `json:"calc_type"`
	// 提成比例(%)
	CommissionRate float64 `json:"commission_rate"`
	// 范围类型：1-全部，2-指定
	ScopeType int `json:"scope_type"`
	// 目标商品列表
	Products []*CommissionProductItem `json:"products,omitempty"`
}

// CommissionProductItem 提成商品项
type CommissionProductItem struct {
	Id int `json:"id"`
	// 提成明细ID
	DetailId int `json:"detail_id"`
	// 商品ID
	SkuId int64 `json:"sku_id"`
	// 商品名称
	ProductName string `json:"product_name"`
	// 商品类型：与CommissionType对应，1-商品 2-服务 3-寄养 4-活体
	ProductType int `json:"product_type"`
}

// CreateCommissionSetupReq 创建提成设置请求
type CreateCommissionSetupReq struct {
	// 连锁ID
	ChainId int64 `json:"chain_id"`
	// 店铺ID
	StoreId string `json:"store_id"`
	// 设置名称
	SetupName string `json:"setup_name" validate:"required" label:"设置名称"`
	// 状态：1-启用，2-禁用
	Status int `json:"status" validate:"required,oneof=1 2" label:"状态"`
	// 操作人
	Operator string `json:"operator"`
	// 操作人ID
	OperatorId int64 `json:"operator_id"`
	// 提成明细
	Details []*CommissionDetailItem `json:"details"`
}

// UpdateCommissionSetupReq 更新提成设置请求
type UpdateCommissionSetupReq struct {
	// 提成设置ID
	Id int `json:"id" validate:"required" label:"提成设置ID"`
	// 连锁ID
	ChainId int64 `json:"chain_id"`
	// 店铺ID
	StoreId string `json:"store_id"`
	// 设置名称
	SetupName string `json:"setup_name"`
	// 状态：1-启用，2-禁用
	Status int `json:"status"`
	// 操作人
	Operator string `json:"operator"`
	// 操作人ID
	OperatorId int64 `json:"operator_id"`
	// 提成明细
	Details []*CommissionDetailItem `json:"details"`
}

// CommissionSetupResp 提成设置响应
type CommissionSetupResp struct {
	// 提成设置ID
	Id int `json:"id"`
	// 设置名称
	SetupName string `json:"setup_name"`
}

// GetCommissionSetupListReq 获取提成设置列表请求
type GetCommissionSetupListReq struct {
	// 店铺ID
	StoreId string `json:"store_id"`
	// 连锁ID
	ChainId int64 `json:"chain_id"`
	// 设置名称
	SetupName string `json:"setup_name"`
	// 状态：1-启用，0-禁用
	Status int `json:"status"`
	// 页码
	PageIndex int `json:"page_index"`
	// 每页条数
	PageSize int `json:"page_size"`
}

// CommissionSetupItem 提成设置列表项
type CommissionSetupItem struct {
	// 提成设置ID
	Id int `json:"id"`
	// 店铺ID
	StoreId string `json:"store_id"`
	// 店铺名称
	StoreName string `json:"store_name"`
	// 设置名称
	SetupName string `json:"setup_name"`
	// 商品提成
	ProductCommission string `json:"product_commission"`
	// 服务提成
	ServiceCommission string `json:"service_commission"`
	// 寄养提成
	FosterCommission string `json:"foster_commission"`
	// 活体提成
	LiveCommission string `json:"live_commission"`
	// 员工数量
	EmployeeCount int `json:"employee_count"`
	// 状态：1-启用，0-禁用
	Status int `json:"status"`
	// 操作人
	Operator string `json:"operator"`
	// 创建时间
	CreatedTime time.Time `json:"created_time"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time"`
}

// GetCommissionSetupListResp 获取提成设置列表响应
type GetCommissionSetupListResp struct {
	// 总数
	Total int64 `json:"total"`
	// 列表
	List []*CommissionSetupItem `json:"list"`
}

// CommissionSetupDetailResp 提成设置详情响应
type CommissionSetupDetailResp struct {
	// 提成设置ID
	Id int `json:"id"`
	// 店铺ID
	StoreId string `json:"store_id"`
	// 店铺名称
	StoreName string `json:"store_name"`
	// 设置名称
	SetupName string `json:"setup_name"`
	// 状态：1-启用，0-禁用
	Status int `json:"status"`
	// 操作人
	Operator string `json:"operator"`
	// 操作人ID
	OperatorId int64 `json:"operator_id"`
	// 创建时间
	CreatedTime time.Time `json:"created_time"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time"`
	// 提成明细
	Details []*CommissionDetailItem `json:"details"`
}

// CommissionDetailCreateItem 创建提成明细项
type CommissionDetailCreateItem struct {
	CommissionType int                      `json:"commission_type"`
	CalcType       int                      `json:"calc_type"`
	CommissionRate float64                  `json:"commission_rate"`
	ScopeType      int                      `json:"scope_type"`
	Products       []*CommissionProductItem `json:"products,omitempty"`
}

// CommissionDetailUpdateItem 更新提成明细项
type CommissionDetailUpdateItem struct {
	CommissionType int                      `json:"commission_type"`
	CalcType       int                      `json:"calc_type"`
	CommissionRate float64                  `json:"commission_rate"`
	ScopeType      int                      `json:"scope_type"`
	Products       []*CommissionProductItem `json:"products,omitempty"`
}

// UpdateCommissionSetupStatusReq 更新提成设置状态请求
type UpdateCommissionSetupStatusReq struct {
	// 提成设置ID
	Id int `json:"id" validate:"required" label:"提成设置ID"`
	// 状态：1-启用，2-禁用
	Status int `json:"status" validate:"required,oneof=1 2" label:"状态"`
	// 操作人
	Operator string `json:"operator"`
	// 操作人ID
	OperatorId int64 `json:"operator_id"`
}

// EmployeeItem 员工项（数据库查询映射）
type EmployeeItem struct {
	// 员工ID
	Id string `json:"id" xorm:"id"`
	// 所属店铺
	TenantId int64 `json:"tenant_id" xorm:"tenant_id"`
	// 员工姓名
	RealName string `json:"real_name" xorm:"real_name"`
	// 手机号码
	Mobile string `json:"mobile" xorm:"mobile"`
	// 角色名称
	RoleName string `json:"role_name" xorm:"role_name"`
	// 是否已选择
	IsSelected bool `json:"is_selected" xorm:"is_selected"`
	// 所属提成设置名称
	SetupName string `json:"setup_name" xorm:"setup_name"`
	// 操作人
	Operator string `json:"operator" xorm:"operator"`
	//员工创建时间
	CreatedTime string `xorm:"created_time" json:"created_time"`
}

// GetEmployeeListReq 获取员工列表请求
type GetEmployeeListReq struct {
	// 提成设置ID
	SetupId int `json:"setup_id" validate:"required" label:"提成活动ID"`
	// 店铺ID
	TenantId int64 `json:"tenant_id" label:"店铺ID"`
	// 员工姓名
	RealName string `json:"real_name" label:"员工姓名"`
	// 手机号码
	Mobile string `json:"mobile" label:"手机号码"`
	// 角色ID
	RoleId int64 `json:"role_id" label:"角色ID"`
	// 来源连锁ID
	SourceChainId int64 `json:"source_chain_id" label:"来源连锁ID"`
	// 页码
	PageIndex int `json:"page_index" label:"页码"`
	// 每页条数
	PageSize int `json:"page_size" label:"每页条数"`
}

// GetEmployeeListResp 获取员工列表响应
type GetEmployeeListResp struct {
	// 总数
	Total int64 `json:"total"`
	// 列表
	List []*EmployeeItem `json:"list"`
}

// UpdateCommissionEmployeesReq 更新提成员工请求
type UpdateCommissionEmployeesReq struct {
	// 店铺ID
	StoreId string `json:"store_id"`
	// 提成设置ID
	SetupId int `json:"setup_id" validate:"required"`
	// 员工ID列表
	Data []string `json:"data" validate:"required"`
	// 操作人
	Operator string `json:"operator"`
	// 操作人ID
	OperatorId int64 `json:"operator_id"`
}

// GetPerformanceListReq 获取员工业绩列表请求
type GetPerformanceListReq struct {
	// 员工姓名
	RealName string `json:"real_name" query:"real_name"`
	// 手机号码
	Mobile string `json:"mobile" query:"mobile"`
	// 角色ID
	RoleId int64 `json:"role_id" query:"role_id"`
	// 开始日期
	StartDate string `json:"start_date" query:"start_date"`
	// 结束日期
	EndDate string `json:"end_date" query:"end_date"`
	// 页码
	PageIndex int `json:"page_index" query:"page_index"`
	// 每页条数
	PageSize int `json:"page_size" query:"page_size"`
	// 店铺ID
	StoreId int64 `json:"store_id" query:"store_id"`
	// 来源连锁ID
	SourceChainId int64 `json:"source_chain_id" query:"source_chain_id"`
	// 是否导出: 0-不导出, 1-导出
	IsExport int `json:"is_export" form:"is_export"`
}

// GetPerformanceListResp 获取员工业绩列表响应
type GetPerformanceListResp struct {
	// 总数
	Total int64 `json:"total"`
	// 列表
	List []*EmployeePerformance `json:"list"`
}

// EmployeePerformance 员工业绩统计，用于存储查询结果
type EmployeePerformance struct {
	// 员工ID
	EmployeeId string `json:"employee_id"`
	// 员工姓名
	RealName string `json:"real_name"`
	// 员工手机号
	Mobile string `json:"mobile"`
	// 角色名称
	RoleName string `json:"role_name"`
	// 商品销售额(分)
	ProductSales int64 `json:"product_sales"`
	// 商品提成(分)
	ProductCommission int64 `json:"product_commission"`
	// 服务销售额(分)
	ServiceSales int64 `json:"service_sales"`
	// 服务提成(分)
	ServiceCommission int64 `json:"service_commission"`
	// 寄养销售额(分)
	FosterSales int64 `json:"foster_sales"`
	// 寄养提成(分)
	FosterCommission int64 `json:"foster_commission"`
	// 活体销售额(分)
	LiveSales int64 `json:"live_sales"`
	// 活体提成(分)
	LiveCommission int64 `json:"live_commission"`
	// 总销售额(分)
	TotalSales int64 `json:"total_sales"`
	// 总提成(分)
	TotalCommission int64 `json:"total_commission"`
}

// GetPerformanceDetailReq 获取员工业绩明细请求
type GetPerformanceDetailReq struct {
	// 员工ID
	EmployeeId string `json:"employee_id" description:"员工ID"`
	// 店铺ID
	StoreId int64 `json:"store_id" description:"店铺ID"`
	//开始日期
	StartDate string `json:"start_date" description:"开始日期"`
	//结束日期
	EndDate string `json:"end_date" description:"结束日期"`
	// 来源连锁ID
	SourceChainId int64 `json:"source_chain_id" description:"来源连锁ID"`
	// 页码
	PageIndex int `json:"page_index" description:"页码"`
	// 每页数量
	PageSize int `json:"page_size" description:"每页数量"`
	// 是否导出: 0-不导出, 1-导出
	IsExport int `json:"is_export" form:"is_export"`
}

// PerformanceDetailItem 业绩明细项
type GetPerformanceDetailResp struct {
	// 记录ID
	Id int64 `json:"id" xorm:"id"`
	// 订单ID
	OrderId string `json:"order_id" xorm:"order_id"`
	// 订单编号
	OrderNo string `json:"order_no" xorm:"order_no"`
	// 商品SKU ID
	SkuId string `json:"sku_id" xorm:"sku_id"`
	// 商品名称
	ProductName string `json:"product_name" xorm:"product_name"`
	// 商品类型: 1-商品, 2-服务, 3-寄养, 4-活体
	ProductType int `json:"product_type" xorm:"product_type"`
	// 销售金额(分)
	SalesAmount int64 `json:"sales_amount" xorm:"sales_amount"`
	// 提成比例(万分比)
	CommissionRate float64 `json:"commission_rate" xorm:"commission_rate"`
	// 提成金额(分)
	CommissionAmount int64 `json:"commission_amount" xorm:"commission_amount"`
	// 订单时间
	OrderTime time.Time `json:"order_time" xorm:"order_time"`
	// 员工姓名
	RealName string `json:"real_name" xorm:"real_name"`
	// 员工手机号
	Mobile string `json:"mobile" xorm:"mobile"`
	// 员工角色
	RoleName string `json:"role_name" xorm:"role_name"`
	// 客户姓名
	CustomerName string `json:"customer_name" xorm:"customer_name"`
	// 单价(分)
	UnitPrice int64 `json:"unit_price" xorm:"unit_price"`
	// 数量
	Quantity int `json:"quantity" xorm:"quantity"`
}

// GetPerformanceDetailListResp 获取员工业绩明细列表响应
type GetPerformanceDetailListResp struct {
	List  []*GetPerformanceDetailResp `json:"list"`
	Total int64                       `json:"total"`
}

// AssignOrderCommissionReq 分配订单业绩请求
type AssignOrderCommissionReq struct {
	// 订单项目列表
	OrderItems []OrderItemCommission `json:"order_items"`
	// 操作人
	Operator string `json:"operator"`
	// 操作人ID
	OperatorId int64 `json:"operator_id"`
}

// OrderItemCommission 订单项目提成信息
type OrderItemCommission struct {
	// 订单ID
	OrderId int `json:"order_id"`
	// 订单编号
	OrderNo string `json:"order_no"`
	// 商品SKU ID
	SkuId int64 `json:"sku_id"`
	// 商品名称
	ProductName string `json:"product_name"`
	// 商品类型
	ProductType int `json:"product_type"`
	// 销售金额(分)
	SalesAmount int64 `json:"sales_amount"`
	// 实收金额(分)
	ActualAmount int64 `json:"actual_amount"`
	// 订单时间
	OrderTime time.Time `json:"order_time"`
	// 店铺ID
	StoreId int64 `json:"store_id"`
	// 订单来源: 1-小程序 2-美团 3-饿了么 100-线下门店
	Channel int `json:"channel"`
	// 商品指定的员工ID，仅用于线下门店
	AssignedEmpId int64 `json:"assigned_emp_id"`
	// 单价
	UnitPrice int64 `json:"unit_price"`
	//数量
	Quantity int `json:"quantity"`
	// 用户名
	CustomerName string `json:"customer_name"`
}
