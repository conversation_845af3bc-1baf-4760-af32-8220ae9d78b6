package omnibus_vo

import (
	viewmode "eShop/view-model"
	"time"
)

// 删除打印机
type DelPrinterReq struct {
	Id int `json:"id"` //分类id
	//门店ID
	StoreId string
}

// 打印机详情
type PrinterInfoReq struct {
	//门店ID
	StoreId string
}

// 打印机详情
type PrinterInfoRes struct {
	viewmode.BaseHttpResponse
	Data PrinterInfo `json:"data"`
}

type PrinterInfo struct {
	Id          int       `json:"id" xorm:"pk autoincr not null INT 'id'"`
	PrinterSn   string    `json:"printer_sn" xorm:"not null comment('打印机编码') VARCHAR(64) 'printer_sn'"`
	StoreId     string    `json:"store_id" xorm:"default '' comment('门店财务编码') VARCHAR(64) 'store_id'"`
	CreateDate  time.Time `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('添加时间') DATETIME 'create_date'"`
	UpdateDate  time.Time `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'update_date'"`
	PrinterName string    `json:"printer_name" xorm:"not null comment('打印机名称') VARCHAR(64) 'printer_name'"`
	PrinterKey  string    `json:"printer_key" xorm:"not null comment('打印机密钥') VARCHAR(100) 'printer_key'"`
}

// 添加、编辑打印机
type AddPrinterReq struct {
	//门店ID
	StoreId     string
	Id          int    `json:"id"`           //id
	PrinterName string `json:"printer_name"` //打印机名称
	PrinterKey  string `json:"printer_key"`  //打印机密钥
	PrinterSn   string `json:"printer_sn"`   //打印机编码
}

type PrintTicketReq struct {
	OrderSn string `json:"order_sn"`
}

// 押金充值返回参数
type CashierTicketInfoRes struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// 储值卡开卡充值
type RechargeTicketInfoRes struct {
	Code    int                 `json:"code"`
	Message string              `json:"message"`
	Data    *RechargeTicketInfo `json:"data"`
}

type DepositTicketInfoRes struct {
	Code    int                `json:"code"`
	Message string             `json:"message"`
	Data    *DepositTicketInfo `json:"data"`
}

type DepositTicketInfo struct {
	StoreName      string `json:"store_name"`      // 门店名称
	CustomerName   string `json:"customer_name"`   // 客户名称
	CustomerMobile string `json:"customer_mobile"` // 手机号
	RechargeAmount string `json:"recharge_amount"` // 充值金额
	DepositAmount  string `json:"deposit_amount"`  // 押金金额
	CashierName    string `json:"cashier_name"`    // 收银员
	PayTime        string `json:"pay_time"`        // 收款时间
	PayType        string `json:"pay_type"`        // 支付方式
	ActualPay      string `json:"actual_pay"`      // 实付
	QRCode         string `json:"qr_code"`         // 店铺二维码（如有）
	StoreId        string
}

type RechargeTicketInfo struct {
	StoreName      string             `json:"store_name"`      // 门店名称
	OrderSn        string             `json:"order_sn"`        // 订单号
	OrderTime      string             `json:"order_time"`      // 下单时间
	CashierName    string             `json:"cashier_name"`    // 收银员
	CustomerName   string             `json:"customer_name"`   // 客户名称
	CustomerMobile string             `json:"customer_mobile"` // 手机号
	Cards          []RechargeCardInfo `json:"cards"`           // 卡信息
	PayType        string             `json:"pay_type"`        // 支付方式
	ActualPay      string             `json:"actual_pay"`      // 实付
	QRCode         string             `json:"qr_code"`         // 店铺二维码
	StoreId        string
}

type RechargeCardInfo struct {
	CardName string `json:"card_name"` // 卡名称
	Recharge string `json:"recharge"`  // 充值金额
	Gift     string `json:"gift"`      // 赠送金额
}

type CashierOrderInfoRes struct {
	Code    int               `json:"code"`
	Message string            `json:"message"`
	Data    *CashierOrderInfo `json:"data"`
}

type CashierOrderInfo struct {
	StoreName      string               `json:"store_name"`      // 门店名称
	OrderSn        string               `json:"order_sn"`        // 订单号
	OrderTime      string               `json:"order_time"`      // 下单时间
	CashierName    string               `json:"cashier_name"`    // 收银员
	CustomerName   string               `json:"customer_name"`   // 客户名称
	CustomerMobile string               `json:"customer_mobile"` // 手机号
	PetName        string               `json:"pet_name"`        // 宠物名称
	Products       []CashierProductInfo `json:"products"`        // 商品明细
	TotalAmount    string               `json:"total_amount"`    // 账单总价
	DiscountAmount string               `json:"discount_amount"` // 优惠金额
	PayType        string               `json:"pay_type"`        // 支付方式
	ShouldPay      string               `json:"should_pay"`      // 应付金额
	ActualPay      string               `json:"actual_pay"`      // 实付
	RefundAmount   string               `json:"refund_amount"`   // 退款金额
	OrderRemark    string               `json:"order_remark"`    // 订单备注
	StoreId        string
}

type CashierProductInfo struct {
	ProductName string `json:"product_name"` // 商品名
	UnitPrice   string `json:"unit_price"`   // 单价
	Quantity    int    `json:"quantity"`     // 数量
	Subtotal    string `json:"subtotal"`     // 小计
}
