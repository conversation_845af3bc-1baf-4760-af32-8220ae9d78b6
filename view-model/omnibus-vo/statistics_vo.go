package omnibus_vo

// 首页六宫格统计请求
// 只需要门店ID
// @Description 获取首页六宫格统计数据
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param store_id body string true "门店ID"
type StatisticsOverviewReq struct {
	StoreId string `json:"store_id"`
}

// 首页趋势图统计请求
// @Description 获取首页趋势图统计数据
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param store_id body string true "门店ID"
// @Param start_date body string false "开始日期"
// @Param end_date body string false "结束日期"
// @Param type body int true "类型 1-储值卡销售 2-商品销售 3-服务销售 4-活体销售 5-次卡销售"
type StatisticsTrendReq struct {
	StoreId   string `json:"store_id"`
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
	Type      int    `json:"type"`
}

type StatisticsOverviewRes struct {
	ActualIncome       float64 `json:"actual_income"` // 实际到账
	ActualIncomeYest   float64 `json:"actual_income_yest"`
	PayOrderCount      int     `json:"pay_order_count"` // 支付订单
	PayOrderCountYest  int     `json:"pay_order_count_yest"`
	ReserveOrderCount  int     `json:"reserve_order_count"` // 预约订单
	ReserveOrderYest   int     `json:"reserve_order_yest"`
	BusinessAmount     float64 `json:"business_amount"` // 营业流水
	BusinessAmountYest float64 `json:"business_amount_yest"`
	ProductCount       int     `json:"product_count"` // 累计商品
	ProductCountYest   int     `json:"product_count_yest"`
	CustomerCount      int     `json:"customer_count"` // 累计客户
	CustomerCountYest  int     `json:"customer_count_yest"`
	SurplusCount       int     `json:"surplus_count"` //短信剩余数量
}

type StatisticsTrendItem struct {
	Date  string  `json:"date"`
	Value float64 `json:"value"`
}

type StatisticsTrendRes struct {
	List []StatisticsTrendItem `json:"list"`
}
