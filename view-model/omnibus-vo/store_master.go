package omnibus_vo

import "time"

// 店铺主体（appChannel）配置表
type StoreMaster struct {
	AppChannel        int       `xorm:"not null pk autoincr comment('店铺主体（类型）id 0.未选择 1.阿闻自有 2.TP代运营') INT(11)"`
	Name              string    `xorm:"not null comment('店铺类型名称') VARCHAR(56)"`
	ElmAppId          string    `xorm:"not null comment('饿了么appid') VARCHAR(56)"`
	ElmAppSecret      string    `xorm:"not null comment('饿了么appsecret') VARCHAR(56)"`
	MtAppId           string    `xorm:"not null comment('美团appid') VARCHAR(56)"`
	MtAppSecret       string    `xorm:"not null comment('美团appsecret') VARCHAR(56)"`
	JddjAppId         string    `xorm:"not null comment('京东到家 应用的 token') VARCHAR(56)"`
	JddjAppSecret     string    `xorm:"not null comment('京东到家 应用的app_key') VARCHAR(56)"`
	JddjAppMerchantId string    `xorm:"not null comment('京东到家 应用的商家ID，用于京东到家回调token的时候区分应用') VARCHAR(56)"`
	UpdateUserNo      string    `xorm:"not null comment('更新店铺主体信息的用户') VARCHAR(56)"`
	CreateUserNo      string    `xorm:"not null comment('创建店铺主体的用户') VARCHAR(56)"`
	IsDeleted         int       `xorm:"default 0 comment('是否删除 0.未删除 1.已删除') TINYINT(1)"`
	UpdateTime        time.Time `xorm:"not null default 'current_timestamp()' comment('最后更新时间') DATETIME"`
	CreateTime        time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') DATETIME"`
}
