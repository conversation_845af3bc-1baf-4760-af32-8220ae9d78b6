package omnibus_vo

// MytTokenResponse 麦芽田token响应
type MytTokenResponse struct {
	Code    int32  `json:"code"`    // 状态码 200成功
	Message string `json:"message"` // 错误信息
	Data    string `json:"data"`
	Msg     string `json:"msg"`
}

type DataToken struct {
	AccessToken  string `json:"token"`               // 访问令牌
	RefreshToken string `json:"refresh_token"`       // 刷新令牌
	ExpiresIn    int64  `json:"expire_time"`         // 访问令牌有效期(秒)
	ReExpiresIn  int64  `json:"refresh_expire_time"` // 刷新令牌有效期(秒)
}

// MytBaseResponse 麦芽田基础响应
type MytBaseResponse struct {
	Code    int32  `json:"code"`    // 状态码 200成功
	Message string `json:"message"` // 错误信息
}

type SignData struct {
	AppKey    string `json:"app_key"`
	Token     string `json:"token"`
	Timestamp int64  `json:"timestamp"`
	Data      string `json:"data"`
	Command   string `json:"command"`
	ShopId    string `json:"-"`
	RequestId string `json:"request_id"`
	Signature string `json:"signature"`
}

type GetMytTokenRequest struct {
	GrantType string `json:"grant_type"` // 授权类型 默认:shop
	Code      string `json:"code"`       // 状态码
	ShopID    string `json:"shop_id"`    // 平台方渠道ID
	Category  string `json:"category"`   // 订单分类(麦芽田枚举)
	Name      string `json:"name"`       // 门店名称
	Type      string `json:"type"`       // 店铺类型 ["waimai","shop","other"]
	Longitude string `json:"longitude"`  // 经度(国测局02标准，如高德)
	Latitude  string `json:"latitude"`   // 纬度(国测局02标准，如高德)
}
