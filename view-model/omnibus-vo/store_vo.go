package omnibus_vo

// PrintTypeResponse 打印类型响应
type PrintTypeResponse struct {
	// 打印类型(0:本地打印 1:飞鹅打印)
	WhatPrint int `json:"what_print"`
	//储值卡校验 1:不需要 2:需要
	IsCheck int `json:"is_check" xorm:"not null comment('1:不需要 2:需要') INT 'is_check'"`
}

// UpdatePrintTypeRequest 修改打印类型请求
type UpdatePrintTypeRequest struct {
	// 店铺财务编码
	FinanceCode string `json:"finance_code" `
	// 打印类型(0:本地打印 1:飞鹅打印)
	WhatPrint int `json:"what_print" `
	//储值卡校验 1:不需要 2:需要
	IsCheck int `json:"is_check" xorm:"not null comment('1:不需要 2:需要') INT 'is_check'"`
}

// DeleteEmployeeReq 删除员工请求
type DeleteEmployeeReq struct {
	// 店铺ID
	StoreId string `json:"store_id" validate:"required"`
	// 员工手机号
	Mobile string `json:"mobile" validate:"required"`
	// 组织ID
	OrgId int64 `json:"org_id" validate:"required"`
}
