package offline

// FosterPayDTO 对应 Java 的 FosterPayDTO
type FosterPayDTO struct {
	// 店铺id
	StoreId string `json:"storeId"`
	// 客户id
	CustomerId int64 `json:"customerId"`
	// 客户名称
	CustomerName string `json:"customerName"`
	// 结算寄养id
	FosterId int64 `json:"fosterId"`
	// 订单id
	OrderId int64 `json:"orderId"`
	// 订单号
	OrderNo string `json:"orderNo"`
	// 退押金方式
	PayType string `json:"payType"`
	// 押金实付金额
	Amount float64 `json:"amount"`
	// 收银员id（客户id）
	SellerId int64 `json:"sellerId"`
	// 收银员姓名
	SellerName string `json:"sellerName"`
}
