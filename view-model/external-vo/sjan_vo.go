package external_vo

// ProductInfo 产品信息数据结构
type ProductInfo struct {
	PNo     string `json:"p_no"`    // 产品编号
	Pname   string `json:"pname"`   // 产品名称
	Ptype   string `json:"ptype"`   // 产品分类
	UnitNum int    `json:"unitNum"` // 包装数量
}

// PackagingDetailItem 包装明细项
type PackagingDetailItem struct {
	Barcode  string `json:"barcode"`  // 小标
	Ubarcode string `json:"Ubarcode"` // 大标
	InDate   string `json:"in_date"`  // 采集时间
}

// PackagingTaskRequest 包装任务请求
type PackagingTaskRequest struct {
	BillNo  string                `json:"bill_no"` // 任务单号
	InDate  string                `json:"in_date"` // 任务开始时间
	PNo     string                `json:"p_no"`    // 产品编号
	Pname   string                `json:"pname"`   // 产品名称
	UnitNum int                   `json:"unitNum"` // 包装数量
	Data    []PackagingDetailItem `json:"data"`    // 明细数据
}

// OutboundDetailItem 出库明细项
type OutboundDetailItem struct {
	Barcode string `json:"barcode"` // 物流码
	PNo     string `json:"p_no"`    // 产品编号
	InDate  string `json:"in_date"` // 扫码时间
	Number  int    `json:"number"`  // 包装数量
}

// OutboundRecordRequest 出库记录请求
type OutboundRecordRequest struct {
	BillNo string               `json:"bill_no"` // 出库单号
	InDate string               `json:"in_date"` // 出库单建立时间
	CuNo   string               `json:"cu_no"`   // 代理编号
	Data   []OutboundDetailItem `json:"data"`    // 明细数据
}

// ReturnRecordItem 退货记录项
type ReturnRecordItem struct {
	Id      int    `json:"id"`      // 主键
	Barcode string `json:"barcode"` // 物流码
	PNo     string `json:"p_no"`    // 产品编号
	InDate  string `json:"in_date"` // 退货时间
	Number  int    `json:"number"`  // 包装数量
	CuNo    string `json:"cu_no"`   // 退货代理编号
}

// ReturnRecordRequest 退货记录请求
type ReturnRecordRequest struct {
	Records []ReturnRecordItem `json:"records"` // 退货记录数组
}

// AgentInfo 代理商信息数据结构
type AgentInfo struct {
	CuNo     string `json:"cu_no"`    // 代理编号
	CuName   string `json:"cu_name"`  // 代理名称
	Ctype    string `json:"ctype"`    // 代理分类
	Phone    string `json:"phone"`    // 电话
	Province string `json:"province"` // 省份
	City     string `json:"city"`     // 城市
	Address  string `json:"address"`  // 地址
}
