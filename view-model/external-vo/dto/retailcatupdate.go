package dto

// / retailCat/update 创建/更新商品分类
type RetailCatUpdateRequest struct {
	// APP方门店id 必镇
	App_poi_code string `json:"app_poi_code" form:"app_poi_code" query:"app_poi_code"`
	// 原始的商品分类id     与category_name_origin至多填写一个
	Category_code_origin string `json:"category_code_origin,omitempty" form:"category_code_origin" query:"category_code_origin"`
	//  原始的商品分类名称 与category_code_origin至多填写一个
	Category_name_origin string `json:"category_name_origin,omitempty" form:"category_name_origin" query:"category_name_origin"`
	// 商品分类id 非必镇
	Category_code string `json:"category_code,omitempty" form:"category_code" query:"category_code"`
	// 商品分类名称 必镇
	Category_name string `json:"category_name" form:"category_name" query:"category_name"`
	//  二级商品分类id  非必镇
	Secondary_category_code string `json:"secondary_category_code,omitempty" form:"secondary_category_code" query:"secondary_category_code"`
	// 二级商品分类名称 非必镇
	Secondary_category_name string `json:"secondary_category_name,omitempty" form:"secondary_category_name" query:"secondary_category_name"`
	// 商品分类的排序 非必镇
	Sequence int32 `json:"sequence,omitempty" form:"sequence" query:"sequence"`
	//  调整分类层级时的目标等级   非必镇
	Target_level int32 `json:"target_level,omitempty" form:"target_level" query:"target_level"`
	//  调整为二级分类时所属的一级分类名称  仅当target_level=2时填写
	Target_parent_name string `json:"target_parent_name,omitempty" form:"target_parent_name" query:"target_parent_name"`
	//  是否开启置顶 非必镇
	Top_flag int32 `json:"top_flag,omitempty" form:"top_flag" query:"top_flag"`
	// 置顶周期 当top_flag=1时必填，且需要与period字段同时设置。
	Weeks_time string `json:"weeks_time,omitempty" form:"weeks_time" query:"weeks_time"`
	// 置顶时段   当top_flag=1时必填，且需要与weeks_time字段同时设置
	Period string `json:"period,omitempty" form:"period" query:"period"`
}
