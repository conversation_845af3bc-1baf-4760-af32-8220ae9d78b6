package dto

// 订单详情
type ELMOrderDetail struct {
	Body OrderGetBody `json:"body"`
}

type OrderGetBody struct {
	//返回错误信息
	Error string `json:"error"`
	//返回错误码
	Errno int32 `json:"errno"`
	//返回信息
	Data OrderGetData `json:"data"`
}

type OrderGetData struct {
	//账号
	Source string `json:"source"`
	//订单信息
	Order OrderGetOrder `json:"order"`
	//订单用户信息
	User OrderGetUser `json:"user"`
	//商户信息
	Shop OrderGetShop `json:"shop"`
	//订单商品信息数组
	Products [][]OrderGetProducts `json:"products"`
	//订单优惠信息
	Discount []OrderGetDiscount `json:"discount"`
}

type OrderGetBodyData struct {
	//返回错误信息
	Error string `json:"elmError"`
	//返回错误码
	Errno int32 `json:"errno"`
	//返回信息
	Data OrderGetDatas `json:"OrderData"`
}

type OrderGetDatas struct {
	//账号
	Source string `json:"source"`
	//订单信息
	Order OrderGetOrder `json:"Order"`
	//订单用户信息
	User OrderGetUser `json:"OrderGetUser"`
	//商户信息
	Shop OrderGetShop `json:"OrderGetShop"`
	//订单商品信息数组
	Products []OrderGetProducts `json:"OrderGetProductsList"`
	//订单优惠信息
	Discount []OrderGetDiscount `json:"OrderGetDiscountsList"`
}

type OrderGetOrder struct {
	//订单ID
	OrderId string `json:"order_id"`
	//饿了么订单ID，与order_id值相同
	ElemeOrderId string `json:"eleme_order_id"`
	//订单来源 1 饿了么星选 2饿了么
	OrderFrom string `json:"order_from"`
	//是否是预约单 1即时单 2预约单
	SendImmediately int32 `json:"send_immediately"`
	//是否降级;1:是,0:否;
	//极少数订单因网络或信息交互异常，
	//导致订单部分字段（如订单金额）生成延迟，
	//此时订单会被标记为“已降级”状态，
	//需开发者重新调用查看订单详情接口获取完整订单数据。
	DownFlag int32 `json:"down_flag"`
	//订单状态
	Status int32 `json:"status"`
	//取货类型 0-外卖到家，1-用户到店自提
	BusinessType string `json:"business_type"`
	//用户到店自提取货码
	PickUpCode string `json:"pick_up_code"`
	//订单完成时间
	FinishedTime int64 `json:"finished_time"`
	//商家确认订单时间
	ConfirmTime int64 `json:"confirm_time"`
	//订单取消时间
	CancelTime string `json:"cancel_time"`
	//配送费，单位：分
	SendFee int32 `json:"send_fee"`
	//包装费，单位：分
	PackageFee int32 `json:"package_fee"`
	//优惠总金额，单位：分
	DiscountFee int32 `json:"discount_fee"`
	//商户应收金额，订单预计给商家，单位：分
	ShopFee int32 `json:"shop_fee"`
	//订单总金额，单位：分
	TotalFee int32 `json:"total_fee"`
	//用户实付金额，单位：分
	UserFee int32 `json:"user_fee"`
	//付款类型 2 在线支付
	PayType int32 `json:"pay_type"`
	//付款状态 1 未支付 2 已支付
	PayStatus int32 `json:"pay_status"`
	//是否需要发票 1 是 2 否
	NeedInvoice int32 `json:"need_invoice"`
	//发票抬头
	InvoiceTitle string `json:"invoice_title"`
	//纳税人识别号
	TaxerId string `json:"taxer_id"`
	//订单备注
	Remark string `json:"remark"`
	//创建时间
	CreateTime string `json:"create_time"`
	//物流类型
	//1 蜂鸟 2 蜂鸟自配送 3 蜂鸟众包 4 饿了么众包
	//5 蜂鸟配送 6 饿了么自配送 7 全城送 8 快递配送
	DeliveryParty string `json:"delivery_party"`
	//用户期望最早送达时间
	SendTime int64 `json:"send_time"`
	//用户期望最晚送达时间
	LatestSendTime int64 `json:"latest_send_time"`
	//佣金，单位：分
	Commission int32 `json:"commission"`
	//骑士手机号
	DeliveryPhone string `json:"delivery_phone"`
}

type OrderGetUser struct {
	//客户ID
	UserId string `json:"user_id"`
	//客户名称
	Name string `json:"name"`
	//客户所在省份
	Province string `json:"province"`
	//客户所在城市
	City string `json:"city"`
	//客户所在区
	District string `json:"district"`
	//客户电话，订单完成后48小时内可返回客户联系电话。
	//匿名订单会返回隐私小号，因小号存在有效期，如遇小号过期拨打不通，
	//可调用order.get接口重新获取。
	Phone string `json:"phone"`
	//客户地址
	Address string `json:"address"`

	//客户百度经纬度
	Coord OrderGetCoord `json:"coord"`
}

type OrderGetCoord struct {
	//送货地址百度经度
	Longitude string `json:"longitude"`
	//送货地址百度纬度
	Latitude string `json:"latitude"`
}

type OrderGetShop struct {
	//合作方门店ID
	Id string `json:"id"`
	//平台门店ID
	BaiduShopId string `json:"baidu_shop_id"`
	//门店名称
	Name string `json:"name"`
}

// 订单商品
type OrderGetProducts struct {
	//商品ID
	BaiduProductId string `json:"baidu_product_id"`
	//商品自定义ID;未设置为空;
	CustomSkuId string `json:"custom_sku_id"`
	////子订单ID，可区分同商品ID的不同属性，订单逆向操作必须字段
	SubBizOrderId string `json:"sub_biz_order_id"`
	//商品类型,1.单品 2 套餐 3 配料
	ProductType int32 `json:"product_type"`
	//商品名称
	ProductName string `json:"product_name"`
	//商品份数
	ProductAmount int32 `json:"product_amount"`
	//总重量，单位：克
	TotalWeight int32 `json:"total_weight"`
	//商品单价
	ProductPrice int32 `json:"product_price"`
	//商品总价，单位：分
	ProductFee int32 `json:"product_fee"`
	//包装费
	PackagePrice int32 `json:"package_price"`
	//包装费总价，单位：分
	PackageFee int32 `json:"package_fee"`
	//包装数量
	PackageAmount int32 `json:"package_amount"`
	//包装数量
	TotalFee int32 `json:"total_fee"`
	//商品优惠信息
	ProductSubsidy OrderProductSubsidy `json:"product_subsidy"`
}

// 商品优惠信息
type OrderProductSubsidy struct {
	//总优惠金额;单位分
	Discount int32 `json:"discount"`
	//平台承担费用;单位分;
	BaiduRate int32 `json:"baidu_rate"`
	//商户承担费用;单位分;
	ShopRate int32 `json:"shop_rate"`
	//代理商承担费用;单位分;
	AgentRate int32 `json:"agent_rate"`
	//物流承担费用;单位分;
	LogisticsRate int32 `json:"logistics_rate"`
}

// 订单优惠信息
type OrderGetDiscount struct {
	//优惠类型
	Type string `json:"type"`
	//优惠金额 单位：分
	Fee int32 `json:"fee"`
	//活动ID
	ActivityId string `json:"activity_id"`
	//平台承担费用;单位分;
	BaiduRate int32 `json:"baidu_rate"`
	//商户承担费用;单位分;
	ShopRate int32 `json:"shop_rate"`
	//代理商承担费用;单位分;
	AgentRate int32 `json:"agent_rate"`
	//物流承担费用;单位分;
	LogisticsRate int32 `json:"logistics_rate"`
	//优惠描述
	Desc string `json:"desc"`
	//优惠信息商品
	DiscountProducts []OrderDiscountProducts `json:"products"`
}

// 优惠信息商品
type OrderDiscountProducts struct {
	//商品ID
	BaiduProductId string `json:"baidu_product_id"`
	//优惠后金额
	NowPrice int32 `json:"now_price"`
}

type (
	BillRes struct {
		Body      ELMBillBody `json:"body"`
		Cmd       string      `json:"cmd"`
		Encrypt   string      `json:"encrypt"`
		Sign      string      `json:"sign"`
		Source    string      `json:"source"`
		Ticket    string      `json:"ticket"`
		Timestamp int         `json:"timestamp"`
		Traceid   string      `json:"traceid"`
		Version   string      `json:"version"`
	}
	ELMBillBody struct {
		Errno int         `json:"errno"`
		Data  ELMBillData `json:"data"`
		Error string      `json:"error"`
	}
	ELMBillData struct {
		Date        string      `json:"date"`
		BaiduShopId string      `json:"baidu_shop_id"`
		ShopId      string      `json:"shop_id"`
		OrderList   []OrderList `json:"order_list"`
	}
	OrderList struct {
		PayEntity        string         `json:"pay_entity"`
		Amount           int            `json:"amount"`
		TradeId          TradeId        `json:"trade_id"`
		OrderCreateTime  int64          `json:"order_create_time"`
		TradeCreateTime  int64          `json:"trade_create_time"`
		OrderFrom        string         `json:"order_from"`
		OrderDetailFee   OrderDetailFee `json:"order_detail_fee"`
		ResponsibleParty string         `json:"responsible_party"`
		OrderIndex       string         `json:"order_index"`
		OrderId          string         `json:"order_id"`
		PaymentDate      int            `json:"payment_date"`
		EleOrderId       string         `json:"ele_order_id"`
	}
	TradeId struct {
		AgentId    string `json:"agent_id"`
		SupplierId string `json:"supplier_id"`
	}
	OrderDetailFee struct {
		ShopInAmount                  int    `json:"shop_in_amount"`
		AdjustFee                     int    `json:"adjust_fee"`
		BusinessTypeDesc              string `json:"businessTypeDesc"`
		AdditionServicePrice          int    `json:"addition_service_price"`
		PayChannelFee                 int    `json:"pay_channel_fee"`
		TyingOrderTypeDesc            string `json:"tyingOrderTypeDesc"`
		SendFee                       int    `json:"send_fee"`
		ZhongbaoCallFee               int    `json:"zhongbao_call_fee"`
		ElemeSubsidyAmount            int    `json:"elemeSubsidyAmount"`
		ElemeRedPacket                int    `json:"elemeRedPacket"`
		ShopRate                      int    `json:"shop_rate"`
		GuaranteeFee                  int    `json:"guarantee_fee"`
		DeliveryTipAmount             int    `json:"deliveryTipAmount"`
		BizType                       string `json:"biz_type"`
		MedicalCardPay                int    `json:"medical_card_pay"`
		ProductFee                    int    `json:"product_fee"`
		UserFee                       int    `json:"user_fee"`
		RefundOrderId                 string `json:"refundOrderId"`
		OriginDeliverFee              int    `json:"originDeliverFee"`
		RtsRefundOrderId              string `json:"rts_refund_order_id"`
		MerchantItemCouponAmount      int    `json:"merchantItemCouponAmount"`
		MerchantCouponAmount          int    `json:"merchantCouponAmount"`
		MerchantRedPacket             int    `json:"merchantRedPacket"`
		PlatformRate                  int    `json:"platform_rate"`
		SlowWillPayAmount             int    `json:"slow_will_pay_amount"`
		PackageFee                    int    `json:"package_fee"`
		ColdBoxFee                    int    `json:"cold_box_fee"`
		PayChannelRate                int    `json:"pay_channel_rate"`
		ActualBaseTimeAmount          int    `json:"actualBaseTimeAmount"`
		BaseLogisticsAmount           int    `json:"baseLogisticsAmount"`
		BookTypeDesc                  string `json:"bookTypeDesc"`
		OrderBusinessType             int    `json:"orderBusinessType"`
		IsTyingOrderDesc              string `json:"isTyingOrderDesc"`
		AgentRate                     int    `json:"agent_rate"`
		MerchantCashGiftAmount        int    `json:"merchantCashGiftAmount"`
		Commission                    int    `json:"commission"`
		BillBusinessType              int    `json:"bill_business_type"`
		ElemeDeliveryCouponAmount     int    `json:"elemeDeliveryCouponAmount"`
		TyingOrderType                int    `json:"tyingOrderType"`
		ElemeCouponAmount             int    `json:"elemeCouponAmount"`
		ElemeCashGiftAmount           int    `json:"elemeCashGiftAmount"`
		MerchantDeliverySubsidyAmount int    `json:"merchantDeliverySubsidyAmount"`
		ShippingCost                  int    `json:"shipping_cost"`
		ElemeDeliverySubsidyAmount    int    `json:"elemeDeliverySubsidyAmount"`
		ActualBaseDistanceAmount      int    `json:"actualBaseDistanceAmount"`
		DeliveryType                  string `json:"deliveryType"`
		DiscountValue                 int    `json:"discount_value"`
		MerchantSubsidyAmount         int    `json:"merchantSubsidyAmount"`
		BizTypeDesc                   string `json:"biz_type_desc"`
		IsSelfDelivery                string `json:"isSelfDelivery"`
		ActualBaseLogisticsAmount     int    `json:"actual_base_logistics_amount"`
		MerchantDeliveryCouponAmount  int    `json:"merchantDeliveryCouponAmount"`
		IsTyingOrder                  int    `json:"isTyingOrder"`
	}
)

// AddressInfo 表示收货地址信息
type AddressInfo struct {
	Province string `json:"province,omitempty"` // 所在省份
	City     string `json:"city,omitempty"`     // 所在城市
	District string `json:"district,omitempty"` // 所在区
	Address  string `json:"address,omitempty"`  // 收货地址
}

// InvoiceInfo 表示发票信息
type InvoiceInfo struct {
	NeedInvoice    int    `json:"need_invoice,omitempty"`    // 是否需要发票 1是 2否
	InvoiceTitle   string `json:"invoice_title,omitempty"`   // 发票抬头
	TaxerID        string `json:"taxer_id,omitempty"`        // 纳税人识别号
	InvoiceType    int    `json:"invoice_type,omitempty"`    // 发票类型 2纸质发票 4电子发票
	InvoiceAddress string `json:"invoice_address,omitempty"` // 发票地址
}

// OrderModifyRequest 表示订单变更请求的结构体
type OrderModifyRequest struct {
	OrderID         string      `json:"order_id"`                 // 订单ID
	PlatformShopID  string      `json:"platform_shop_id"`         // 平台店铺ID
	OrderModifyType int         `json:"order_modify_type"`        // 订单变更信息类型
	OperatorRole    string      `json:"operator_role"`            // 信息变更操作者角色
	Remark          string      `json:"remark,omitempty"`         // 订单备注
	ReceiverPhone   string      `json:"receiver_phone,omitempty"` // 收货人电话
	AddressInfo     AddressInfo `json:"address_info,omitempty"`   // 收货地址信息
	InvoiceInfo     InvoiceInfo `json:"invoice_info,omitempty"`   // 发票信息
	Greeting        string      `json:"greeting,omitempty"`       // 祝福语
	SendTime        int64       `json:"send_time,omitempty"`      // 配送时间，格式：毫秒
}
type RefundOrderGoodsData struct {
	//商品唯一标识
	SkuId string `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//数量
	Quantity int32 `protobuf:"varint,2,opt,name=quantity,proto3" json:"quantity"`
	//总金额
	RefundAmount string `protobuf:"bytes,3,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount"`
	//饿了么专属字段，子订单ID，可区分同商品ID的不同属性，订单逆向操作必须字段
	SubBizOrderId string `protobuf:"bytes,4,opt,name=sub_biz_order_id,json=subBizOrderId,proto3" json:"sub_biz_order_id"`
	//退款商品名称
	GoodsName string `protobuf:"bytes,5,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	//商品sku的规格名称
	Spec string `protobuf:"bytes,6,opt,name=spec,proto3" json:"spec"`
	//商品单价  如本次部分退款是按件部分退，则此金额为单件商品sku的退款金额
	RefundPrice float32 `protobuf:"fixed32,7,opt,name=refund_price,json=refundPrice,proto3" json:"refund_price"`
	//实际支付单价 当前商品sku参加商品类活动优惠后的金额（单价），单位是元。
	RefundRealityPrice float32 `protobuf:"fixed32,8,opt,name=refund_reality_price,json=refundRealityPrice,proto3" json:"refund_reality_price"`
	// APP方商品id，即商家中台系统里商品的编码（spu_code值），字段信息限定长度不超过128个字符。
	AppFoodCode string `protobuf:"bytes,9,opt,name=app_food_code,json=appFoodCode,proto3" json:"app_food_code"`
	//京东到家标识 商品促销类型（1203满赠，6买赠，1正品）
	PromotionType int32 `protobuf:"varint,10,opt,name=promotion_type,json=promotionType,proto3" json:"promotion_type"`
	//订单商品表主键id
	OrderProductId int64 `protobuf:"varint,11,opt,name=order_product_id,json=orderProductId,proto3" json:"order_product_id"`
	//订单商品的父sku 组合商品的子商品该字段有值
	ParentSkuId string `protobuf:"bytes,12,opt,name=parent_sku_id,json=parentSkuId,proto3" json:"parent_sku_id"`
	//订单商品的父sku 组合商品的子商品该字段有值
	PlatformSkuId int64 `protobuf:"varint,13,opt,name=platform_sku_id,json=platformSkuId,proto3" json:"platform_sku_id"`
}

type PlatformChannel struct {
	ChannelId  int
	UserAgent  int
	AppChannel int
}

type GrpcContext struct {
	Channel PlatformChannel
}

type ELMSource struct {
	//订单ID
	SourceOrderId string `json:"source_order_id" form:"source_order_id" query:"source_order_id"`
}

// 订单优惠活动
type OrdersPrivilegeActive struct {
	//活动id
	Actdetailid int `json:"act_detail_id"`
	//活动优惠金额中美团承担成本
	Mtcharge float64 `json:"mt_charge"`
	//活动优惠金额中商家承担成本
	Poicharge float64 `json:"poi_charge"`
	//活动优惠金额
	Reducefee float64 `json:"reduce_fee"`
	//备注
	Remark string `json:"remark"`
	//活动类型
	Activetype int `json:"type"`
}

// 推送订单信息修改消息
type OrdersUpdate struct {
	//订单号，数据库中请用bigint(20)存储此字段。
	OrderId int64 `json:"order_id"`
	//APP方门店id
	AppPoiCode float64 `json:"app_poi_code"`
	//信息变更操作人：1-用户；2-美团骑手。
	OpRole int `json:"op_role"`
	//操作员类型，参考值：1-用户；2-商家；3-客服人员；4-BD；5-系统；6-合作中心。
	OpType int `json:"op_type"`
}
