package dto

import "eShop/proto"

// 列表记录集
type ResponseDatas struct {
	Body Bodys `json:"body"`
}

type Bodys struct {
	Errno int32                    `json:"errno"`
	Error string                   `json:"error"`
	Data  []map[string]interface{} `json:"data"`
}

// 返回单条记录集
type ResponseData struct {
	Body Body `json:"body"`
}

type Body struct {
	Errno int32                  `json:"errno"`
	Error string                 `json:"error"`
	Data  map[string]interface{} `json:"data"`
}

// 返回操作结果(Bool)
type ResponseBoolData struct {
	Body BodyBool `json:"body"`
}

type BodyBool struct {
	Errno int32  `json:"errno"`
	Error string `json:"error"`
	Data  bool   `json:"data"`
}

// 返回操作结果(String)
type ResponseStringData struct {
	Body BodyString `json:"body"`
}

type BodyString struct {
	Errno int32  `json:"errno"`
	Error string `json:"error"`
	Data  string `json:"data"`
}

// 返回操作结果(Interface)
// 某些情况。饿了么返回'body.data'类型不确定,且不关注data内容
type ResponseInterfaceData struct {
	Body BodyInterface `json:"body"`
}
type BodyInterface struct {
	Errno int32       `json:"errno"`
	Error string      `json:"error"`
	Data  interface{} `json:"data"`
}

type ResponseBase struct {
	Errno int32  `json:"errno"`
	Error string `json:"error"`
}

type ResponseBaseElm struct {
	Body struct {
		Errno interface{} `json:"errno"`
		Error string      `json:"error"`
	} `json:"body"`
}

type StockBatchRequest struct {
	ShopId      string `json:"shop_id"`
	SkuidStocks string `json:"skuid_stocks"`
	UpcStocks   string `json:"upc_stocks"`
	CustomSkuId string `json:"custom_sku_id"`
	AppChannel  int32  `json:"app_channel"`
}

// 返回商品列表操作结果
type ResponseSkuListData struct {
	Body SkuListData `json:"body"`
}

// 饿了么商品列表
type SkuListData struct {
	// todo.sean 饿了么这个字段有时候返回 string 有时候返回number,其他接口估计也有这个问题，先改这一个
	Errno interface{}             `json:"errno"`
	Error string                  `json:"error"`
	Data  proto.ElmProductListDto `json:"data"`
}

type SkuListDto struct {
	List []SkuList `json:"list"`
	// 页码
	Page int32 `json:"page"`
	// 总页数
	Pages int32 `json:"pages"`
	// 商品总数
	Total int32 `json:"total"`
}

type SkuList struct {
	// 商品自定义ID
	CustomSkuId string `json:"custom_sku_id"`
	// 库存,范围：0~9999
	LeftNum int32 `json:"left_num"`
	// 商品名称
	Name string `json:"name"`
	// 商品upc
	Upc string `json:"upc"`
	// 毛重，单位克
	Weight string `json:"weight"`
	// 市场价格，单位：分，范围：1~99999900
	MarketPrice int32 `json:"market_price"`
	// 销售价格，单位：分，范围：1~99999900
	SalePrice int32 `json:"sale_price"`
}

// 返回品牌列表操作结果
type ResponseSkuBrandData struct {
	Body SkuBrandData `json:"body"`
}

type SkuBrandData struct {
	Errno int32    `json:"errno"`
	Error string   `json:"error"`
	Data  SkuBrand `json:"data"`
}

type SkuBrand struct {
	Count      int32          `json:"count"`
	PageNum    int32          `json:"page_num"`
	PageSize   int32          `json:"page_size"`
	MaxPageNum int32          `json:"max_page_num"`
	Detail     []SkuBrandList `json:"detail"`
}

type SkuBrandList struct {
	BrandId   string `json:"brand_id"`
	BrandName string `json:"brand_name"`
}

type ElmStoreOpenCloseResponse struct {
	Body struct {
		Errno interface{} `json:"errno"` // 有时候是数字，有时候是数字字符串
		Error string      `json:"error"`
		Data  interface{} `json:"data"`
	} `json:"body"`
}

/*
*
针对单个删除判断
*/
type EleDeleteResponse struct {
	Body ReturnData `json:"body"`
}

type ReturnData struct {
	Errno int32  `json:"errno"`
	Error string `json:"error"`
}

type ElmBatchReturn struct {
	Body struct {
		Errno int `json:"errno"`
		Data  struct {
			FailList []struct {
				Errno       string `json:"errno"`
				Name        string `json:"name"`
				CustomSkuID string `json:"custom_sku_id"`
				Upc         string `json:"upc"`
				Error       string `json:"error"`
			} `json:"fail_list"`
			SuccessList []struct {
				Name        string `json:"name"`
				CustomSkuID string `json:"custom_sku_id"`
				Upc         string `json:"upc"`
			} `json:"success_list"`
			ResultList []struct {
				ItemID      int64  `json:"item_id"`
				Cat1ID      int    `json:"cat1_id"`
				Cat2ID      int    `json:"cat2_id"`
				Cat3ID      int    `json:"cat3_id"`
				Name        string `json:"name"`
				CustomSkuID string `json:"custom_sku_id"`
				Upc         string `json:"upc"`
				SkuID       string `json:"sku_id"`
			} `json:"result_list"`
		} `json:"data"`
		Error string `json:"error"`
	} `json:"body"`
	Cmd       string `json:"cmd"`
	Encrypt   string `json:"encrypt"`
	Sign      string `json:"sign"`
	Source    string `json:"source"`
	Ticket    string `json:"ticket"`
	Timestamp int    `json:"timestamp"`
	Traceid   string `json:"traceid"`
	Version   string `json:"version"`
}

type ElmCommonReturn struct {
	Body interface{} `json:"body"`
}

type AutoGenerated1 struct {
	Body struct {
		Errno int         `json:"errno"`
		Data  interface{} `json:"data"`
		Error string      `json:"error"`
	} `json:"body"`
}

// 批量获取门店详细请求参数
type ElmStoreUpdateRequest struct {
	//平台门店ID
	BaiduShopId string `json:"baidu_shop_id"`
	//门店ID
	ShopId string `json:"shop_id"`
	// 标品id，请通过
	ProductId     string `json:"product_id"`
	DeliveryAreas []struct {
		DistanceExtFee struct {
			DistanceFeeStages []struct {
				Fee   string `json:"fee"`
				End   string `json:"end,omitempty"`
				Start string `json:"start"`
			} `json:"distance_fee_stages"`
		} `json:"distance_ext_fee"`
		MaxWeight   int `json:"max_weight"`
		Coordinates []struct {
			Latitude  string `json:"latitude"`
			Longitude string `json:"longitude"`
		} `json:"coordinates"`
		DistanceExtPrice struct {
			DistancePriceStages []struct {
				Price string `json:"price"`
				Start string `json:"start"`
				End   string `json:"end,omitempty"`
			} `json:"distance_price_stages"`
		} `json:"distance_ext_price"`
		AreaType     int    `json:"area_type"`
		AgentFee     string `json:"agent_fee"`
		WeightExtFee struct {
			WeightFeeStageList []struct {
				StartWeight int    `json:"start_weight"`
				Unit        int    `json:"unit"`
				EndWeight   int    `json:"end_weight"`
				Fee         string `json:"fee"`
			} `json:"weight_fee_stage_list"`
		} `json:"weight_ext_fee"`
		UUID       string `json:"uuid"`
		TimeExtFee struct {
			TimeStageFeeStageList []struct {
				StartTime string `json:"start_time"`
				Fee       string `json:"fee"`
				EndTime   string `json:"end_time"`
			} `json:"time_stage_fee_stage_list"`
		} `json:"time_ext_fee"`
		TimeExtPrice struct {
			TimePriceStageList []struct {
				StartTime string `json:"start_time"`
				Price     string `json:"price"`
				EndTime   string `json:"end_time"`
			} `json:"time_price_stage_list"`
		} `json:"time_ext_price"`
		DeliveryPrice string `json:"delivery_price"`
		MultiPeriod   []struct {
			Start string `json:"start"`
			End   string `json:"end"`
		} `json:"multi_period,omitempty"`
	} `json:"delivery_areas"`
}

type ElmDeliveryKnight struct {
	Phone string `json:"phone"`
	Name  string `json:"name"`
}

type ELMDistributorInfo struct {
	DistributorTypeID string `json:"distributorTypeId"`
	DistributorName   string `json:"distributorName"`
	DistributorPhone  string `json:"distributorPhone"`
}

type Location struct {
	Altitude  string `json:"altitude"`
	UTC       string `json:"UTC"`
	Latitude  string `json:"latitude"`
	Longitude string `json:"longitude"`
}

// ElmCategoryPropertyListRequest 获取属性列表请求
type ElmCategoryPropertyListRequest struct {
	ShopId     string `json:"shop_id"`     // 店铺ID
	CategoryId string `json:"category_id"` // 分类ID,可选
}

// PropertyValue 属性值
type PropertyValue struct {
	// 属性值ID
	ValueId int64 `json:"value_id"`
	// 属性值名称
	ValueName string `json:"value"`
}

// PropertyInfo 属性信息
type PropertyInfo struct {
	// 属性ID
	PropertyId int64 `json:"attr_id"`
	// 属性名称
	PropertyName string `json:"attr_name"`
	// 是否必填
	Required bool `json:"required"`
	// 是否枚举属性
	EnumProp bool `json:"enum_prop"`
	// 是否排序
	SortOrder bool `json:"sort_order"`
	// 是否主要属性
	MajorProp bool `json:"major_prop"`
	// 是否输入属性
	InputProp bool `json:"input_prop"`
	// 分类ID
	CategoryId int64 `json:"category_id"`
	// 是否销售属性
	SaleProp bool `json:"sale_prop"`
	// 是否多选
	MultiSelect bool `json:"multi_select"`
	// 属性值列表
	Values []PropertyValue `json:"value_list,omitempty"`
}

// ElmCategoryPropertyListResponse 获取属性列表响应
type ElmCategoryPropertyListResponse struct {
	// 状态码
	Code int32 `json:"code"`
	// 错误信息
	Message string `json:"message"`
	// 属性列表
	Data []PropertyInfo `json:"data"`
}

// ResponsePropertyListData 饿了么属性列表响应
type ResponsePropertyListData struct {
	Body struct {
		Errno int    `json:"errno"`
		Error string `json:"error"`
		Data  []struct {
			// 属性ID
			PropertyId int64 `json:"propertyId"`
			// 属性名称
			PropertyName string `json:"propertyName"`
			// 是否必填
			Required bool `json:"required"`
			// 是否枚举属性
			EnumProp bool `json:"enumProp"`
			// 是否排序
			SortOrder bool `json:"sortOrder"`
			// 是否主要属性
			MajorProp bool `json:"majorProp"`
			// 是否输入属性
			InputProp bool `json:"inputProp"`
			// 分类ID
			CategoryId int64 `json:"categoryId"`
			// 是否销售属性
			SaleProp bool `json:"saleProp"`
			// 是否多选
			MultiSelect bool `json:"multiSelect"`
			// 属性值列表
			PropertyValues []struct {
				// 属性值ID
				ValueId int64 `json:"valueId"`
				// 属性值名称
				ValueData string `json:"valueData"`
				// 排序值
				SortOrder int `json:"sortOrder"`
			} `json:"propertyValues,omitempty"`
		} `json:"data"`
	} `json:"body"`
}
