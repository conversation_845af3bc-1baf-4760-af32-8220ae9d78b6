package dto

type OrderDetail struct {
	AppId string `json:"app_id" form:"app_id" query:"app_id"`

	//数据因内部交互异常或网络等原因延迟生成（超时），导致开发者当前获取的订单数据不完整，此时平台对订单数据缺失情况进行标记。如不完整，建议尝试重新查询。注意，平台仅对部分模块的数据完整性进行监察标记（参考incmp_modules字段）。参考值：
	//-1：有数据降级
	//0：无数据降级
	IncmpCode int32 `json:"incmp_code" form:"incmp_code" query:"incmp_code"`
	//订单状态，返回订单当前的状态。目前平台的订单状态参考值有：1-用户已提交订单；2-向商家推送订单；4-商家已确认；8-订单已完成；9-订单已取消。
	Status int32 `json:"status" form:"status" query:"status"`
	//订单号（同订单展示ID），数据库中请用bigint(20)存储此字段。
	WmOrderIdView int64 `json:"wm_order_id_view" form:"wm_order_id_view" query:"wm_order_id_view"`
	//APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
	AppPoiCode string `json:"app_poi_code" form:"app_poi_code" query:"app_poi_code"`
	//商家门店名称，即美团平台上当前订单所属门店的名称。
	WmPoiName string `json:"wm_poi_name" form:"wm_poi_name" query:"wm_poi_name"`
	//订单收货人地址，此字段为用户填写的收货地址。商家可在开发者中心->基础设置->订单订阅字段 页面订阅字段“13 收货地址”；若开启订阅，则订单数据会在此字段后追加根据经纬度反查地址的结果，并用“@#”符号分隔，如：用户填写地址@#反查结果。
	RecipientAddress string `json:"recipient_address" form:"recipient_address" query:"recipient_address"`
	//订单收货人联系电话，此字段信息可能推送真实手机号码或隐私号，即需兼容13812345678和13812345678_123456两种号码格式。
	RecipientPhone string `json:"recipient_phone" form:"recipient_phone" query:"recipient_phone"`
	//订单收货人姓名，同时包含用户在用户端选择的性别标识信息。(1)若用户没有填写姓名(老版本的用户端可能支持不填写)，此字段默认为空；商家可在开发者中心开启订阅“12 用户名称”字段，则平台会用“美团客人”自动填充此字段。(2)用户填写了收货人姓名时，此字段按用户所填全部内容正常展示，若姓名中包含特殊符号(如emoji表情)，需商家自行解析。
	RecipientName string `json:"recipient_name" form:"recipient_name" query:"recipient_name"`
	//门店配送费，单位是元。当前订单产生时该门店的配送费（商家自配送运费或美团配送运费），此字段数据为运费优惠前的原价。
	ShippingFee float64 `json:"shipping_fee" form:"shipping_fee" query:"shipping_fee"`
	//订单的实际在线支付总价，单位是元。此字段数据为用户实际支付的订单总金额，含打包袋、配送费等。
	Total float64 `json:"total" form:"total" query:"total"`
	//订单的总原价，单位是元。此字段数据为未扣减所有优惠前订单的总金额，含打包袋、配送费等。
	OriginalPrice float64 `json:"original_price" form:"original_price" query:"original_price"`
	//发票抬头，为用户填写的开发票的抬头。
	InvoiceTitle string `json:"invoice_title" form:"invoice_title" query:"invoice_title"`
	//纳税人识别号，此字段信息默认不返回，如商家支持订单开发票，可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“27 纳税人识别号”。
	TaxpayerId string `json:"taxpayer_id" form:"taxpayer_id" query:"taxpayer_id"`
	//订单创建时间，为10位秒级的时间戳，此字段为用户提交订单的时间。
	Ctime int64 `json:"ctime" form:"ctime" query:"ctime"`
	//预计送达时间。若为“立即送达”订单则推送0；若是“预订单”则推送用户选择的预计送达时间，为10位秒级的时间戳。
	DeliveryTime int64 `json:"delivery_time" form:"delivery_time" query:"delivery_time"`
	//页面开启订阅字段“35 订单预计送达时间”。关于订单预计送达时间字段的说明： (1)当用户选择订单“立即送达”(delivery_time=0)，estimate_arrival_time字段信息则为美团计算的该即时订单预计送达时间。 (2)当用户选择订单在某个特定时间送达，即为预订单。estimate_arrival_time字段与delivery_time字段信息相同，均为用户选择的订单预计送达时间。 (3)当订单为用户到店自取方式，estimate_arrival_time字段与delivery_time字段信息相同，均为用户选择的到店取货时间。
	EstimateArrivalTime int64 `json:"estimate_arrival_time" form:"estimate_arrival_time" query:"estimate_arrival_time"`
	//订单收货地址的纬度，美团使用的是高德坐标系，也就是火星坐标系，商家如果使用的是百度坐标系需要自行转换，坐标需要乘以一百万。
	Latitude float32 `json:"latitude" form:"latitude" query:"latitude"`
	//订单收货地址的经度，美团使用的是高德坐标系，也就是火星坐标系，商家如果使用的是百度坐标系需要自行转换，坐标需要乘以一百万。
	Longitude float32 `json:"longitude" form:"longitude" query:"longitude"`
	//当日订单流水号，门店每日已支付订单的流水号从1开始。
	//目前，自提订单的取货码与该订单流水号相同。
	//此字段信息默认不返回，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“15 订单流水号”。
	DaySeq int32 `json:"day_seq" form:"day_seq" query:"day_seq"`
	//订单配送方式，该字段信息默认不推送，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“22 配送方式”。商家可在开放平台的【附录】文档中对照查看logistics_code的描述，如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等。 如商家想了解自己门店的配送方式以及如何区分等情况，请咨询美团品牌经理。
	LogisticsCode string `json:"logistics_code" form:"logistics_code" query:"logistics_code"`
	//订单商品总重量（该信息默认不返回，可在开发者中心订阅），单位为克/g。
	TotalWeight int64 `json:"total_weight" form:"total_weight" query:"total_weight"`
	//订单备注信息，是用户下单时填写和选择的备注信息；同时，当隐私号服务正常且用户开启号码保护时，此字段信息中会包含收货人脱敏真实号码和隐私号码，如“收餐人隐私号 18689114387_3473，手机号 185****2032”。
	//针对鲜花绿植品类的订单，本字段中支持展示预订人联系电话，请支持接收隐私号格式。
	Caution string `json:"caution" form:"caution" query:"caution"`
	//支付类型：1-货到付款，2-在线支付。目前订单只支持在线支付，此字段推送信息为2。
	PayType int32 `json:"pay_type" form:"pay_type" query:"pay_type"`
	//取货类型：0-普通(配送),1-用户到店自取。此字段的信息默认不推送，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“28 取餐类型订阅字段”。
	PickType int32 `json:"pick_type" form:"pick_type" query:"pick_type"`
	//订单纬度的打包袋金额，单位是元。该信息默认不推送，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“39 打包袋(元)”。
	//与package_bag_money字段相比，package_bag_money_yuan字段的单位为元，其他规则相同；订阅其中一个字段即可。
	PackageBagMoneyYuan string `json:"package_bag_money_yuan" form:"package_bag_money_yuan" query:"package_bag_money_yuan"`
	//订单商品详情，其值为由list序列化得到的json字符串
	Detail string `json:"detail" form:"detail" query:"detail"`
	//订单优惠信息，其值为由list序列化得到的json字符串。
	Extras string `json:"extras" form:"extras" query:"extras"`
	//商品优惠详情
	SkuBenefitDetail string `json:"sku_benefit_detail" form:"sku_benefit_detail" query:"sku_benefit_detail"`
	//订单纬度的商家对账信息，json格式数据。该信息默认不推送，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“38 商家应收款详情(元)”。
	//与poi_receive_detail字段相比，poi_receive_detail_yuan字段里的金额类字段单位为元，其他规则相同；订阅其中一个字段即可。
	PoiReceiveDetailYuan string `json:"poi_receive_detail_yuan" form:"poi_receive_detail_yuan" query:"poi_receive_detail_yuan"`
	//是否是第三方配送平台配送,0表否，1表是）
	IsThirdShipping int32 `json:"is_third_shipping" form:"is_third_shipping" query:"is_third_shipping"`
}

// 订单商品详情
type Detail struct {
	//APP方商品id，即商家中台系统里商品的编码(spu_code值)：(1)不同门店之间商品id可以重复，同一门店内商品id不允许重复。(2)字段信息限定长度不超过128个字符。(3)如此字段信息推送的是商品名称或信息为空，则表示商家没有维护商品编码，请商家自行维护。
	AppFoodCode string `json:"app_food_code"`
	//商品名称
	FoodName string `json:"food_name"`
	//SKU码(商家的规格编码)，是商品sku唯一标识码。
	SkuID string `json:"sku_id"`
	//商品的UPC码信息，即商品包装上的UPC/EAN码编号，长度一般8位或者13位。是商家同步商品信息时维护的UPC码，同一门店内，商品UPC码不允许重复。
	Upc string `json:"upc"`
	//订单中此商品sku的购买数量
	Quantity int32 `json:"quantity"`
	//商品单价，单位是元。此字段信息默认返回活动折扣后价格，商家如有需求将价格替换为原价，可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“18 替换菜品折扣价格为原价”。
	Price float64 `json:"price"`
	//订单中当前商品sku需使用包装盒的总数量（单件商品sku需使用包装盒数量*商品售卖数量）。单件商品sku包装盒数量是商家同步商品时维护的信息。
	BoxNum float64 `json:"box_num"`
	//包装盒单价，单位是元。为订单中当前商品sku单个包装盒的价格，是商家同步商品时维护的信息。
	BoxPrice float64 `json:"box_price"`
	//商品售卖单位
	Unit string `json:"unit"`
	//商品折扣系数，目前此字段默认为1，商家无需参考此字段的信息。
	FoodDiscount float64 `json:"food_discount"`
	//商品sku单件的重量，单位是克/g。
	Weight int32 `json:"weight"`
	//表示商品sku单件的重量数值信息，最多支持两位小数。
	WeightForUnit string `json:"weight_for_unit"`
	//表示sku的重量数值单位。
	WeightUnit string `json:"weight_unit"`
	//商品sku的规格名称
	Spec  string `json:"spec"`
	Count int
}

// 平台服务费
type PoiReceiveDetailYuan struct {
	//商品分成，即平台服务费，单位为元
	FoodShareFeeChargeByPoi string `json:"foodShareFeeChargeByPoi"`
	ReconciliationExtras    string `json:"reconciliationExtras"`
}

type ReconciliationExtras struct {
	ChargeMode            int32  `json:"chargeMode"`
	PerformanceServiceFee string `json:"performanceServiceFee"`
	TechnicalServiceFee   string `json:"technicalServiceFee"`
}

// 商品优惠详情
type SkuBenefitDetail struct {
	//SKU码(商家的规格编码)，是商品sku唯一标识码
	SkuId string `json:"sku_id"`

	//商品sku所参加的全部活动优惠的总金额，单位是元。
	TotalReducePrice float64 `json:"totalReducePrice"`

	//商品sku所参加的全部活动优惠，其中美团承担成本总金额，单位是元。
	TotalPoiCharge float64 `json:"totalPoiCharge"`

	//商品sku所参加的全部活动优惠的总金额，单位是元。
	TotalMtCharge float64 `json:"totalMtCharge"`

	//商品sku所参加的全部活动优惠后的实付价（单价），单位是元。
	ActivityPrice float64 `json:"activityPrice"`

	//商品sku所参加的全部活动优惠前的原价（单价），单位是元。
	OriginPrice float64 `json:"originPrice"`

	//商品sku实付金额。
	TotalActivityPrice float64 `json:"totalActivityPrice"`
	// 优惠商品数量
	Count int32 `json:"count"`
	//参与活动详情
	WmAppOrderActDetails []WmAppOrderActDetails `json:"wmAppOrderActDetails"`
}

// 参与活动详情
type WmAppOrderActDetails struct {
	//优惠活动的活动id，适用于【ecommerce/order/getOrderActDetail】接口查询订单中此活动信息（注：目前已支持查询的活动类型，请参考该查询接口的说明）。
	ActId int64 `json:"act_id"`
	//优惠活动类型，参考值： 1-首单减；2-满减；3-抵价券；4-套餐赠；5-满赠；6-超时赔付；7-特价菜；8-首单返优惠劵；9-使用优惠劵；10-运营发优惠劵；11-提前下单减；12-满返优惠劵；13-当面付返优惠券；14-随机返优惠券；15-兑换红包；16-满减配送费；17-折扣菜；18-美团专送减；19-使用点评优惠券；20-第2份半价活动；21-会员免配送费；22-门店新用户立减；23-买赠活动；24-AB新客活动；25-减配送费；100-满减商家优惠券（下单返券）；101-使用商家优惠券；102-供应链自定义；103-进店领券（商家券）；26-满减AB；27-指定商品满减；50-微信钱包渠道首减；51-点评app渠道首减；52-美团app渠道首减；28-新客满减；29-新客满减AB；30-多阶梯满减配送费；31-满N件折；32-扫码购偶数件折扣；33-会员折扣/特价；34-买满件阶梯特价/折扣；35-组合特价/折扣；40-加价购；41-新客折扣菜；42-红包兑换, 不在B端配置，通过数据写入服务来创建；117-商品券；88-虚拟币；45-外卖拼团；43-X元M件 （可多种商品总计件数）；46-外卖加价购；118-商品折扣券；66-闪购会员折；48-拼团减配送费；53-新客专享减包装费；54-新客专享减配送费； 55-第N件优惠; 56-闪购爆品；57-新客专享减打包袋费（针对闪购新客爆品活动）；59-新客专享减配送费（针对闪购新客爆品活动）；201-会员首购优惠；202-会员限时特惠；304-减配送费劵；300-商家会员减配送费；305-联盟津贴；84-店外领券；123-好友助力。
	Type int32 `json:"type"`
	//优惠说明
	Remark string `json:"remark"`
	//美团承担成本，是商品sku参与本活动单次的成本，单位是元。
	MtCharge float64 `json:"mtCharge"`
	//商家承担成本，是商品sku参与本活动单次的成本，单位是元。
	PoiCharge float64 `json:"poiCharge"`
	//表示商品sku参与本活动的次数。
	//1.用折扣活动类型举例：如设置同一订单中此商品sku可享受折扣价格的限购数量是2件，那么用户购买2件及以上时，此处的count=2；如设置可享受折扣价的限购数量是1件，购买1件或多件时，此处count=1；如设置可享受折扣价的数量不限制，则此处count的值与商家购买此商品sku的数量相同。
	//2.用红包活动举例：如订单使用了一个美团红包，多种商品都享受了红包优惠，那么不同商品sku即使有多件数量，此处count的值均等于1，表示该商品仅参与了一次红包优惠活动。
	//3.用X元M件活动举例：设置活动为10元3件，如购买参加此活动的商品任意6件，则每种商品优惠均摊信息的wmAppOrderActDetails字段中count字段都为1。
	Count int32 `json:"count"`
}

// 美团优惠信息
type OrderDiscountExtra struct {
	ActDetailId int64   `json:"act_detail_id"`
	MtCharge    float64 `json:"mt_charge"`
	PoiCharge   float64 `json:"poi_charge"`
	ReduceFee   float64 `json:"reduce_fee"`
	Remark      string  `json:"remark"`
	Type        int     `json:"type"`
}

// 美团新增订单
type MtAddOrderRequest struct {
	//渠道id
	ChannelId int `json:"channel_id"`

	UserAgent int `json:"user_agent"`
	//订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//订单状态：0(已取消)10(默认):未付款;20:已付款;30:已完成;
	OrderStatus int32 `protobuf:"varint,2,opt,name=order_status,json=orderStatus,proto3" json:"order_status"`
	//子状态：20101(美团默认)未接单; 20102已接单; 20103配送中; 20104已送达; 20105已取货; 20106已完成; 20107已取消;  10201(电商默认)未付款; 20201待发货; 20202全部发货; 20203确认收货; 20204部分发货;
	OrderStatusChild int32 `protobuf:"varint,3,opt,name=order_status_child,json=orderStatusChild,proto3" json:"order_status_child"`
	//商户或门店id
	ShopId string `protobuf:"bytes,4,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	//商户名称
	ShopName string `protobuf:"bytes,5,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	//收件人
	ReceiverName string `protobuf:"bytes,6,opt,name=receiver_name,json=receiverName,proto3" json:"receiver_name"`
	//收件省
	ReceiverState string `protobuf:"bytes,7,opt,name=receiver_state,json=receiverState,proto3" json:"receiver_state"`
	//收件市
	ReceiverCity string `protobuf:"bytes,8,opt,name=receiver_city,json=receiverCity,proto3" json:"receiver_city"`
	//收件区
	ReceiverDistrict string `protobuf:"bytes,9,opt,name=receiver_district,json=receiverDistrict,proto3" json:"receiver_district"`
	//收件地址
	ReceiverAddress string `protobuf:"bytes,10,opt,name=receiver_address,json=receiverAddress,proto3" json:"receiver_address"`
	//收件电话
	ReceiverPhone string `protobuf:"bytes,11,opt,name=receiver_phone,json=receiverPhone,proto3" json:"receiver_phone"`
	//总优惠金额
	Privilege int32 `protobuf:"varint,12,opt,name=privilege,proto3" json:"privilege"`
	//Cod=货到付款, NoCod=非货到付款
	PayType string `protobuf:"bytes,13,opt,name=pay_type,json=payType,proto3" json:"pay_type"`
	//alipay支付宝  wxpay微信 wxpay_jsapi 微信支付JSAPI alipay_native 支付宝移动支付
	//string pay_mode = 14;
	//1支付宝  2微信
	//int32 pay_mode_int = 15;
	//收件手机
	ReceiverMobile string `protobuf:"bytes,16,opt,name=receiver_mobile,json=receiverMobile,proto3" json:"receiver_mobile"`
	//NoPay = 未付款 Payed = 已付款 Sended = 已发货 TradeSuccess = 交易成功 TradeClosed = 交易关闭 PartSend = 部分发货
	GjpStatus string `protobuf:"bytes,17,opt,name=gjp_status,json=gjpStatus,proto3" json:"gjp_status"`
	//总金额（付款金额，加上运费，减优惠金额）
	Total int32 `protobuf:"varint,18,opt,name=total,proto3" json:"total"`
	//商品总金额（未加运费，不加包装费，减优惠金额，美团不减优惠金额）
	GoodsTotal int32 `protobuf:"varint,19,opt,name=goods_total,json=goodsTotal,proto3" json:"goods_total"`
	//是否支付0否  1是
	IsPay int32 `protobuf:"varint,20,opt,name=is_pay,json=isPay,proto3" json:"is_pay"`
	//创建时间
	CreateTime string `protobuf:"bytes,21,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//支付时间
	PayTime string `protobuf:"bytes,22,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	//支付单号
	PaySn string `protobuf:"bytes,23,opt,name=pay_sn,json=paySn,proto3" json:"pay_sn"`
	//订单类型1普通订单(默认),2预订订单,3门店自提,4拼团订单,5门店配送
	OrderType int32 `protobuf:"varint,24,opt,name=order_type,json=orderType,proto3" json:"order_type"`
	//总运费
	Freight int32 `protobuf:"varint,25,opt,name=freight,proto3" json:"freight"`
	//发票信息
	Invoice string `protobuf:"bytes,26,opt,name=invoice,proto3" json:"invoice"`
	//买家留言
	BuyerMemo string `protobuf:"bytes,27,opt,name=buyer_memo,json=buyerMemo,proto3" json:"buyer_memo"`
	//卖家留言
	SellerMemo string `protobuf:"bytes,28,opt,name=seller_memo,json=sellerMemo,proto3" json:"seller_memo"`
	//1WEB2mobile3宠医云4ERP5智慧门店6有赞7阿闻宠物8阿闻商城 9美团 10饿了么 11阿闻到家
	//int32 order_from = 29;
	//1快递 2外卖 3自提
	DeliveryType int32 `protobuf:"varint,30,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type"`
	//配送备注
	DeliveryRemark string `protobuf:"bytes,31,opt,name=delivery_remark,json=deliveryRemark,proto3" json:"delivery_remark"`
	//附加优惠信息
	Extras string `protobuf:"bytes,32,opt,name=extras,proto3" json:"extras"`
	//包装费
	PackingCost int32 `protobuf:"varint,33,opt,name=packing_cost,json=packingCost,proto3" json:"packing_cost"`
	//平台服务费
	ServiceCharge int32 `protobuf:"varint,34,opt,name=service_charge,json=serviceCharge,proto3" json:"service_charge"`
	//商品
	OrderProductModel []*OrderProductModel `protobuf:"bytes,35,rep,name=OrderProductModel,proto3" json:"OrderProductModel"`
	//支付信息
	PayInfo []*OrderPayInfoModel `protobuf:"bytes,36,rep,name=PayInfo,proto3" json:"PayInfo"`
	//预计送达时间
	ExpectedTime string `protobuf:"bytes,37,opt,name=expected_time,json=expectedTime,proto3" json:"expected_time"`
	//收货地址纬度
	Latitude float64 `protobuf:"fixed64,38,opt,name=latitude,proto3" json:"latitude"`
	//收货地址经度
	Longitude float64 `protobuf:"fixed64,39,opt,name=longitude,proto3" json:"longitude"`
	//取货码
	PickupCode string `protobuf:"bytes,40,opt,name=pickup_code,json=pickupCode,proto3" json:"pickup_code"`
	//总重量
	TotalWeight int32 `protobuf:"varint,41,opt,name=total_weight,json=totalWeight,proto3" json:"total_weight"`
	//如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送
	LogisticsCode string `protobuf:"bytes,42,opt,name=logistics_code,json=logisticsCode,proto3" json:"logistics_code"`
	//会员id
	MemberId string `protobuf:"bytes,43,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	//会员名称
	MemberName string `protobuf:"bytes,44,opt,name=member_name,json=memberName,proto3" json:"member_name"`
	//member_tel
	MemberTel string `protobuf:"bytes,45,opt,name=member_tel,json=memberTel,proto3" json:"member_tel"`
	//是否订单调整
	IsAdjust int32 `protobuf:"varint,46,opt,name=is_adjust,json=isAdjust,proto3" json:"is_adjust"`
	//是否虚拟订单
	IsVirtual int32 `protobuf:"varint,47,opt,name=is_virtual,json=isVirtual,proto3" json:"is_virtual"`
	//是否拆单
	IsSplit int32 `protobuf:"varint,48,opt,name=is_split,json=isSplit,proto3" json:"is_split"`
	// 渠道订单子系统来源
	OrderChannelSrcType *OrderChannelSrcTypeModel `protobuf:"bytes,49,opt,name=orderChannelSrcType,proto3" json:"orderChannelSrcType"`
	//优惠信息
	OrderPromotion []*OrderPromotionModel `protobuf:"bytes,50,rep,name=orderPromotion,proto3" json:"orderPromotion"`
	//助力订单id（电商使用）
	PowerId int32 `protobuf:"varint,51,opt,name=power_id,json=powerId,proto3" json:"power_id"`
	//所有组合商品优惠
	CombinePrivilege int32 `protobuf:"varint,52,opt,name=combine_privilege,json=combinePrivilege,proto3" json:"combine_privilege"`
	//1.推送到腾讯有数
	IsPushTencent int32 `protobuf:"varint,53,opt,name=is_push_tencent,json=isPushTencent,proto3" json:"is_push_tencent"`
	//1.阿闻自有,2.TP代运营
	AppChannel int32 `protobuf:"varint,54,opt,name=app_channel,json=appChannel,proto3" json:"app_channel"`
	// 仓库的id信息
	WarehouseId int32 `protobuf:"varint,55,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	//交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
	OrderPayType string `protobuf:"bytes,56,opt,name=order_pay_type,json=orderPayType,proto3" json:"order_pay_type"`
	//1分享连接，2扫码 3 自己扫码自己 5 自主访问下单  商城分销用 原商城订单的dis_type字段
	DisType int32 `protobuf:"varint,57,opt,name=dis_type,json=disType,proto3" json:"dis_type"`
	//希望送货时间 商城下单用 原商城订单的receiver_date_msg字段
	ReceiverDateMsg string `protobuf:"bytes,58,opt,name=receiver_date_msg,json=receiverDateMsg,proto3" json:"receiver_date_msg"`
	//收件人座机 商城下单用 原商城订单的tel_phone字段
	TelPhone string `protobuf:"bytes,59,opt,name=tel_phone,json=telPhone,proto3" json:"tel_phone"`
	//1 小程序(阿闻智慧门店) 2:阿闻宠物(北京那边用) //3阿闻商城(自用) 商城用 原商城虚拟订单的source字段
	Source int32 `protobuf:"varint,60,opt,name=source,proto3" json:"source"`
	//原商城虚拟订单first_order字段
	FirstOrder int32 `protobuf:"varint,61,opt,name=first_order,json=firstOrder,proto3" json:"first_order"`
	//原商城虚拟订单的open_id字段
	OpenId string `protobuf:"bytes,62,opt,name=open_id,json=openId,proto3" json:"open_id"`
	//分销id 原商城虚拟订单的dis_id字段
	DisId int32 `protobuf:"varint,63,opt,name=dis_id,json=disId,proto3" json:"dis_id"`
	//收货地址id 原商城虚拟订单的address_id字段
	AddressId int32 `protobuf:"varint,64,opt,name=address_id,json=addressId,proto3" json:"address_id"`
	//订单id
	OrderId int32 `protobuf:"varint,65,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	//在线问诊信息
	DiagnoseData *DiagnoseData `protobuf:"bytes,66,opt,name=diagnose_data,json=diagnoseData,proto3" json:"diagnose_data"`
	//提交订单时用户经度
	Lng string `protobuf:"bytes,67,opt,name=lng,proto3" json:"lng"`
	//提交订单时用户纬度
	Lat string `protobuf:"bytes,68,opt,name=lat,proto3" json:"lat"`
	// 社区团购站点id，分销员id（分销店铺和下单店铺不一致，会标记为空）
	PickupStationId int32 `protobuf:"varint,69,opt,name=pickup_station_id,json=pickupStationId,proto3" json:"pickup_station_id"`
	ContractFee     int32 `protobuf:"varint,70,opt,name=contract_fee,json=contractFee,proto3" json:"contract_fee"`
	// 社区团购字段，团购id、下单联系人、手机号，地址、昵称，头像
	GroupId      int64  `protobuf:"varint,71,opt,name=GroupId,proto3" json:"GroupId"`
	GroupMobile  string `protobuf:"bytes,72,opt,name=GroupMobile,proto3" json:"GroupMobile"`
	GroupName    string `protobuf:"bytes,73,opt,name=GroupName,proto3" json:"GroupName"`
	GroupAddress string `protobuf:"bytes,76,opt,name=GroupAddress,proto3" json:"GroupAddress"`
	// 昵称
	NickName string `protobuf:"bytes,74,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	// 头像完整地址
	AvatarUrl string `protobuf:"bytes,75,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url"`
	//医疗互联网订单号\处方ID\推荐ID
	ConsultOrderSn string `protobuf:"bytes,77,opt,name=consult_order_sn,json=consultOrderSn,proto3" json:"consult_order_sn"`
	// 店铺海报推广员id
	ShopDisMemberId string `protobuf:"bytes,78,opt,name=shop_dis_member_id,json=shopDisMemberId,proto3" json:"shop_dis_member_id"`
	// 店铺海报推广来源 0默认 1从爱省钱分享的店铺推广海报
	ShopDisMemberFrom int32 `protobuf:"varint,79,opt,name=shop_dis_member_from,json=shopDisMemberFrom,proto3" json:"shop_dis_member_from"`
	// 账单订单完成时间
	BillCompletedTime string `protobuf:"bytes,80,opt,name=bill_completed_time,json=billCompletedTime,proto3" json:"bill_completed_time"`
	// 账单订单取消时间
	BillCanceledTime string `protobuf:"bytes,81,opt,name=bill_canceled_time,json=billCanceledTime,proto3" json:"bill_canceled_time"`
	//账户时间
	TradeCreatedTime string `protobuf:"bytes,82,opt,name=trade_created_time,json=tradeCreatedTime,proto3" json:"trade_created_time"`
	//入账时间
	TradePaymentTime string `protobuf:"bytes,83,opt,name=trade_payment_time,json=tradePaymentTime,proto3" json:"trade_payment_time"`
	//账单日期
	TradeTime string `protobuf:"bytes,84,opt,name=trade_time,json=tradeTime,proto3" json:"trade_time"`
	//本地生活的主体ID   `datacenter`.`organization_info` 主键ID
	OrgId int32 `protobuf:"varint,85,opt,name=org_id,json=orgId,proto3" json:"org_id"`
}

type OrderChannelSrcTypeModel struct {
	// 子系统来源(渠道api文档定义)
	SrcType string `protobuf:"bytes,1,opt,name=src_type,json=srcType,proto3" json:"src_type"`
	// 子系统来源名称(渠道api文档定义)
	SrcTypeName string `protobuf:"bytes,2,opt,name=src_type_name,json=srcTypeName,proto3" json:"src_type_name"`
}
type DiagnoseData struct {
	//宠物id
	PetId string `protobuf:"bytes,2,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//医生编号
	DoctorCode string `protobuf:"bytes,3,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//问诊项目：1-免费义诊，2-快速咨询，3-找医生
	DiagnoseProject int32 `protobuf:"varint,4,opt,name=diagnose_project,json=diagnoseProject,proto3" json:"diagnose_project"`
	//问诊形式：1-图文，2-电话，3-视频
	DiagnoseForm int32 `protobuf:"varint,5,opt,name=diagnose_form,json=diagnoseForm,proto3" json:"diagnose_form"`
	//免疫情况:1已免疫，2未免疫，3免疫不全，4免疫不详
	ImmuneStatus int32 `protobuf:"varint,6,opt,name=immune_status,json=immuneStatus,proto3" json:"immune_status"`
	//症状：0其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题,多个用英文逗号隔开（【其他】选项与具体症状关键词二选一）
	Symptom string `protobuf:"bytes,7,opt,name=symptom,proto3" json:"symptom"`
	//补充症状(选择【其他】关键词，输入框描述症状必填)
	SymptomDesc string `protobuf:"bytes,8,opt,name=symptom_desc,json=symptomDesc,proto3" json:"symptom_desc"`
	//症状出现时间：1-小于7天，2-小于1个月，3-小于3个月，4-3个月以上
	SymptomRecent int32 `protobuf:"varint,9,opt,name=symptom_recent,json=symptomRecent,proto3" json:"symptom_recent"`
	//宠物症状照片，多个用英文逗号隔开
	Image string `protobuf:"bytes,10,opt,name=image,proto3" json:"image"`
	//是否就诊过：0未就诊，1就诊过
	HaveHospital int32 `protobuf:"varint,11,opt,name=have_hospital,json=haveHospital,proto3" json:"have_hospital"`
	//就诊过的医院名称
	HaveHospitalName string `protobuf:"bytes,12,opt,name=have_hospital_name,json=haveHospitalName,proto3" json:"have_hospital_name"`
	//医生诊断结果与治疗方案
	HaveHospitalResult string `protobuf:"bytes,13,opt,name=have_hospital_result,json=haveHospitalResult,proto3" json:"have_hospital_result"`
	//历史就诊的检查照片/药品照片
	HaveHospitalImage string `protobuf:"bytes,14,opt,name=have_hospital_image,json=haveHospitalImage,proto3" json:"have_hospital_image"`
	//是否有其他病史：0否，1是
	MedicalHistory int32 `protobuf:"varint,15,opt,name=medical_history,json=medicalHistory,proto3" json:"medical_history"`
	//其他病史信息
	MedicalHistoryInfo string `protobuf:"bytes,16,opt,name=medical_history_info,json=medicalHistoryInfo,proto3" json:"medical_history_info"`
	//宠物头像
	PetAvatar string `protobuf:"bytes,17,opt,name=pet_avatar,json=petAvatar,proto3" json:"pet_avatar"`
	//宠物名称
	PetName string `protobuf:"bytes,18,opt,name=pet_name,json=petName,proto3" json:"pet_name"`
	//宠物种类大分类
	PetKindof string `protobuf:"bytes,19,opt,name=pet_kindof,json=petKindof,proto3" json:"pet_kindof"`
	//宠物种类
	PetVariety string `protobuf:"bytes,20,opt,name=pet_variety,json=petVariety,proto3" json:"pet_variety"`
	//宠物生日
	PetBirthday string `protobuf:"bytes,21,opt,name=pet_birthday,json=petBirthday,proto3" json:"pet_birthday"`
	//宠物性别 性别：1GG,2MM
	PetSex int32 `protobuf:"varint,22,opt,name=pet_sex,json=petSex,proto3" json:"pet_sex"`
	//1：已绝育 0：未绝育
	PetNeutering int32 `protobuf:"varint,23,opt,name=pet_neutering,json=petNeutering,proto3" json:"pet_neutering"`
	//用户ScrmId
	UserId string `protobuf:"bytes,24,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//金额
	Amount int32 `protobuf:"varint,25,opt,name=amount,proto3" json:"amount"`
	//问诊时长
	Duration int32 `protobuf:"varint,26,opt,name=duration,proto3" json:"duration"`
	//医生类别：1门店医生，2互联网医生
	DoctorType int32 `protobuf:"varint,27,opt,name=doctor_type,json=doctorType,proto3" json:"doctor_type"`
}

// 优惠信息
type OrderPromotionModel struct {
	//活动id
	PromotionId int32 `protobuf:"varint,1,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id"`
	//活动类型  1满减
	PromotionType int32 `protobuf:"varint,2,opt,name=promotion_type,json=promotionType,proto3" json:"promotion_type"`
	//促销活动描述
	PromotionTitle string `protobuf:"bytes,3,opt,name=promotion_title,json=promotionTitle,proto3" json:"promotion_title"`
	//商家优惠金额（分）
	PoiCharge int32 `protobuf:"varint,4,opt,name=poi_charge,json=poiCharge,proto3" json:"poi_charge"`
	//平台优惠金额（分）
	PtCharge int32 `protobuf:"varint,5,opt,name=pt_charge,json=ptCharge,proto3" json:"pt_charge"`
	//优惠金额（分）
	PromotionFee int32 `protobuf:"varint,6,opt,name=promotion_fee,json=promotionFee,proto3" json:"promotion_fee"`
}
type OrderProductModel struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	//订单id
	OrderId string `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	//主订单号
	OrderSn string `protobuf:"bytes,3,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//sku
	Sku string `protobuf:"bytes,4,opt,name=sku,proto3" json:"sku"`
	//组合商品父级sku
	ParentSkuId string `protobuf:"bytes,5,opt,name=parent_sku_id,json=parentSkuId,proto3" json:"parent_sku_id"`
	//商品id
	ProductId string `protobuf:"bytes,6,opt,name=product_id,json=productId,proto3" json:"product_id"`
	//商品名称
	ProductName string `protobuf:"bytes,7,opt,name=product_name,json=productName,proto3" json:"product_name"`
	//商品类别（1-实物商品，2-虚拟商品，3-组合商品）
	ProductType int32 `protobuf:"varint,8,opt,name=product_type,json=productType,proto3" json:"product_type"`
	//组合商品组合类型（组合商品组合类型: 0-非组合 1-实物实物 2-实物虚拟 3-虚拟虚拟）
	CombineType int32 `protobuf:"varint,9,opt,name=combine_type,json=combineType,proto3" json:"combine_type"`
	//商品编码
	BarCode string `protobuf:"bytes,10,opt,name=bar_code,json=barCode,proto3" json:"bar_code"`
	//单价
	Price int32 `protobuf:"varint,11,opt,name=price,proto3" json:"price"`
	//数量
	Number int32 `protobuf:"varint,12,opt,name=number,proto3" json:"number"`
	//规格
	Specs string `protobuf:"bytes,13,opt,name=specs,proto3" json:"specs"`
	//总金额(实际支付金额)
	PaymentTotal int32 `protobuf:"varint,14,opt,name=payment_total,json=paymentTotal,proto3" json:"payment_total"`
	//商家优惠金额
	Privilege int32 `protobuf:"varint,15,opt,name=privilege,proto3" json:"privilege"`
	//邮费
	Freight int32 `protobuf:"varint,16,opt,name=freight,proto3" json:"freight"`
	//原价
	MarkingPrice int32 `protobuf:"varint,17,opt,name=marking_price,json=markingPrice,proto3" json:"marking_price"`
	//商品图片
	Image string `protobuf:"bytes,18,opt,name=image,proto3" json:"image"`
	//发货数量
	DeliverNum int32 `protobuf:"varint,19,opt,name=deliver_num,json=deliverNum,proto3" json:"deliver_num"`
	//退货数量
	RefundNum int32 `protobuf:"varint,20,opt,name=refund_num,json=refundNum,proto3" json:"refund_num"`
	//平台优惠金额
	PrivilegePt int32 `protobuf:"varint,21,opt,name=privilege_pt,json=privilegePt,proto3" json:"privilege_pt"`
	//总优惠金额
	PrivilegeTotal int32 `protobuf:"varint,22,opt,name=privilege_total,json=privilegeTotal,proto3" json:"privilege_total"`
	//实际支付单价（美团）
	PayPrice int32 `protobuf:"varint,23,opt,name=pay_price,json=payPrice,proto3" json:"pay_price"`
	//子订单ID，可区分同商品ID的不同属性，订单逆向操作必须字段
	SubBizOrderId string `protobuf:"bytes,24,opt,name=sub_biz_order_id,json=subBizOrderId,proto3" json:"sub_biz_order_id"`
	//参与限时折扣的活动id
	PromotionId int32 `protobuf:"varint,25,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id"`
	//货号
	ArticleNumber string `protobuf:"bytes,26,opt,name=article_number,json=articleNumber,proto3" json:"article_number"`
	//商品级别促销类型(1、无优惠;2、秒杀(已经下线);3、单品直降;4、限时抢购;1202、加价购;1203、满赠(标识商品);6、买赠(买A送B，标识B);9999、表示一个普通商品参与捆绑促销，设置的捆绑类型;9998、表示一个商品参与了捆绑促销，并且还参与了其他促销类型;9997、表示一个商品参与了捆绑促销，但是金额拆分不尽,9996:组合购,8001:商家会员价,8:第二件N折,9:拼团促销)
	PromotionType int32 `protobuf:"varint,27,opt,name=promotion_type,json=promotionType,proto3" json:"promotion_type"`
	//sku商品实际支付总金额
	SkuPayTotal int32 `protobuf:"varint,28,opt,name=sku_pay_total,json=skuPayTotal,proto3" json:"sku_pay_total"`
	//虚拟商品已经核销过的数量
	UsedNum int32 `protobuf:"varint,29,opt,name=used_num,json=usedNum,proto3" json:"used_num"`
	//只有虚拟商品才有值1有效期至多少2有效期天数
	TermType int32 `protobuf:"varint,30,opt,name=term_type,json=termType,proto3" json:"term_type"`
	//如果term_type=1存时间戳如果term_type=2存多少天
	TermValue int32 `protobuf:"varint,31,opt,name=term_value,json=termValue,proto3" json:"term_value"`
	//商城订单商品主键id
	MallOrderProductId int64 `protobuf:"varint,32,opt,name=mall_order_product_id,json=mallOrderProductId,proto3" json:"mall_order_product_id"`
	//是否支持过期退款 1：是  0：否
	VirtualInvalidRefund int32 `protobuf:"varint,33,opt,name=virtual_invalid_refund,json=virtualInvalidRefund,proto3" json:"virtual_invalid_refund"`
	//是否是第三方商品信息 1：是  0：否 默认0
	IsThirdProduct int32 `protobuf:"varint,34,opt,name=is_third_product,json=isThirdProduct,proto3" json:"is_third_product"`
	// 药品仓标识（1：巨星药品仓, 0：否）
	WarehouseType int32 `protobuf:"varint,35,opt,name=warehouse_type,json=warehouseType,proto3" json:"warehouse_type"`
	//组合子商品在单个组合中的售卖数量
	GroupItemNum int32 `protobuf:"varint,36,opt,name=group_item_num,json=groupItemNum,proto3" json:"group_item_num"`
	// 是否使用了虚拟库存
	UseVirtualStock int32 `protobuf:"varint,37,opt,name=use_virtual_stock,json=useVirtualStock,proto3" json:"use_virtual_stock"`
	//商品分类名称
	ChannelCategoryName string `protobuf:"bytes,38,opt,name=channel_category_name,json=channelCategoryName,proto3" json:"channel_category_name"`
	// 是否处方药
	IsPrescribedDrug int32 `protobuf:"varint,39,opt,name=is_prescribed_drug,json=isPrescribedDrug,proto3" json:"is_prescribed_drug"`
	//vip单价
	VipPrice int32 `protobuf:"varint,40,opt,name=vip_price,json=vipPrice,proto3" json:"vip_price"`
}
type OrderPayInfoModel struct {
	PayId string `protobuf:"bytes,1,opt,name=pay_id,json=payId,proto3" json:"pay_id"`
	//订单号
	OrderSn string `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//支付单号
	PaySn string `protobuf:"bytes,3,opt,name=pay_sn,json=paySn,proto3" json:"pay_sn"`
	//1支付宝  2微信 3美团支付 4其他
	PayMode int32 `protobuf:"varint,4,opt,name=pay_mode,json=payMode,proto3" json:"pay_mode"`
	//实际支付金额
	PayAmount int32 `protobuf:"varint,5,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//支付时间
	PayTime string `protobuf:"bytes,6,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
}

type MtAddOrderResponse struct {
	//订单号
	OrderSn string `protobuf:"bytes,4,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//支付单号
	PaySn string `protobuf:"bytes,5,opt,name=pay_sn,json=paySn,proto3" json:"pay_sn"`
	//订单id
	OrderId int32 `protobuf:"varint,6,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	//在线问诊医生编号
	DoctorCode string `protobuf:"bytes,7,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
}
