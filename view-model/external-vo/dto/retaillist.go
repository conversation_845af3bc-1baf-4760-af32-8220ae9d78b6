package dto

type RetailListRequest struct {
	// APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
	AppPoiCode string `json:"app_poi_code"`
	// 分页查询偏移量，表示从第几页开始查询，需根据公式得到：页码(舍弃小数)=offset/limit+1。例如：offset=23，limit=5，根据公式计算结果表示从第5页开始查询，且每页5个商品，即本次请求结果将展示门店内第21～第25条商品数据。(2)【limit】字段有值时，此字段必填
	Offset int32 `json:"offset"`
	// 分页每页商品的数量：(1)此字段的值须为大于0的整数，最多支持200。(2)【offset】字段有值时，此字段必填
	Limit int32 `json:"limit"`
}
