package meituan

// retail/batchinitdata 批量创建/更新商品[支持商品多规格,不含删除逻辑]    请求参数
type RetailBatchinitdataRequest struct {
	// APP方门店id，即商家中台系统里门店的编码 必镇
	AppPoiCode string `protobuf:"bytes,1,opt,name=app_poi_code,json=appPoiCode,proto3" json:"app_poi_code"`
	// 当次调用的操作类型 非必镇
	OperateType int32 `protobuf:"varint,2,opt,name=operate_type,json=operateType,proto3" json:"operate_type"`
	// 多个商品数据集合的json格式数组  必镇
	FoodData []*RetailBatchinitdata `protobuf:"bytes,3,rep,name=food_data,json=foodData,proto3" json:"food_data"`
	// 店铺主体Id
	StoreMasterId int32 `protobuf:"varint,4,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
}

type RetailBatchinitdata struct {
	//APP方商品id，  必镇
	AppFoodCode string `protobuf:"bytes,1,opt,name=app_food_code,json=appFoodCode,proto3" json:"app_food_code"`
	// 单个商品需使用的打包盒数量  非必填
	BoxNum int64 `protobuf:"fixed32,2,opt,name=box_num,json=boxNum,proto3" json:"box_num"`
	// 单个打包盒的价格，单位是元，需在0-100之间 非必填
	BoxPrice float32 `protobuf:"fixed32,3,opt,name=box_price,json=boxPrice,proto3" json:"box_price"`
	//  分类名称    创建时：category_code与category_name、category_code_list、category_name_list字段必须且只能填写一个 更新时非必填
	CategoryName string `protobuf:"bytes,4,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	// 商品上下架状态，字段取值范围：0-上架，1-下架。  非必镇
	IsSoldOut int32 `protobuf:"varint,5,opt,name=is_sold_out,json=isSoldOut,proto3" json:"is_sold_out"`
	// 一个订单中此商品的最小购买量   必填
	MinOrderCount int32 `protobuf:"varint,6,opt,name=min_order_count,json=minOrderCount,proto3" json:"min_order_count"`
	// 商品名称(总称：如电风机) 创建时必填
	Name string `protobuf:"bytes,7,opt,name=name,proto3" json:"name"`
	// 商品描述  非必镇
	Description string `protobuf:"bytes,8,opt,name=description,proto3" json:"description"`
	// 商品的售卖单位 非必镇
	Unit string `protobuf:"bytes,9,opt,name=unit,proto3" json:"unit"`
	// 商品图片  非必镇
	Picture string `protobuf:"bytes,10,opt,name=picture,proto3" json:"picture"`
	// 	 商品在当前分类下的排序  非必镇
	Sequence int32 `protobuf:"varint,11,opt,name=sequence,proto3" json:"sequence"`
	// 商品价格 创建时，如不传skus信息，则本参数必填。
	Price float32 `protobuf:"fixed32,12,opt,name=price,proto3" json:"price"`
	// 美团内部商品类目id 门店启用类目属性并且传递了销售或普通属性则  1.创建时必传，2.若商品创建时未传tag_id，更新时必传(只需传一次)。 门店未启用类目属性或未传递销售属性/普通属性则非必传。
	TagId int64 `protobuf:"varint,13,opt,name=tag_id,json=tagId,proto3" json:"tag_id"`
	// 商品的品牌名称 非必镇
	ZhName string `protobuf:"bytes,14,opt,name=zh_name,json=zhName,proto3" json:"zh_name"`
	// 完整产品名称（如：非力蒲电风机）
	ProductName string `protobuf:"bytes,15,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 商品的产地 非必镇
	OriginName string `protobuf:"bytes,16,opt,name=origin_name,json=originName,proto3" json:"origin_name"`
	//  功能 非必镇
	Flavour string `protobuf:"bytes,17,opt,name=flavour,proto3" json:"flavour"`
	// 商品普通属性  若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
	CommonAttrValue []*CommonAttrValue `protobuf:"bytes,18,rep,name=common_attr_value,json=commonAttrValue,proto3" json:"common_attr_value"`
	// APP方商品的skus信息字符串，支持同时传多个sku信息  非必镇
	Skus []*SkuParam `protobuf:"bytes,19,rep,name=skus,proto3" json:"skus"`
	// 商品的图片详情 非必镇
	PictureContents string `protobuf:"bytes,20,opt,name=picture_contents,json=pictureContents,proto3" json:"picture_contents"`
	// 商品属性 非必镇
	Properties []*Propertie `protobuf:"bytes,21,rep,name=properties,proto3" json:"properties"`
	// 是否为“力荐”商品，字段取值范围：0-否， 1-是。  非必镇
	IsSpecialty int32 `protobuf:"varint,22,opt,name=is_specialty,json=isSpecialty,proto3" json:"is_specialty"`
	// 视频ID 非必镇
	VideoId int64 `protobuf:"varint,23,opt,name=video_id,json=videoId,proto3" json:"video_id"`
	// 商品限购详情  非必镇
	LimitSaleInfo *LimitSaleInfo `protobuf:"bytes,24,opt,name=limit_sale_info,json=limitSaleInfo,proto3" json:"limit_sale_info"`
	//分类ID  非必镇
	CategoryId int32 `protobuf:"varint,25,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	// 分类的code
	CategoryCode string `protobuf:"bytes,26,opt,name=category_code,json=categoryCode,proto3" json:"category_code"`
	// 商品卖点
	SellPoint string `protobuf:"bytes,27,opt,name=sell_point,json=sellPoint,proto3" json:"sell_point"`
}
