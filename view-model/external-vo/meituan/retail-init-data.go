package meituan

type RetailInitData struct {
	// APP方门店id  必镇
	AppPoiCode string `protobuf:"bytes,1,opt,name=app_poi_code,json=appPoiCode,proto3" json:"app_poi_code"`
	// 本次调用的操作类型标识：(1)字段取值范围：1-创建，2-更新；  非必镇
	OperateType int32 `protobuf:"varint,2,opt,name=operate_type,json=operateType,proto3" json:"operate_type"`
	// APP方商品id，即商家中台系统里商品的编码（spu_code值）必镇
	AppFoodCode string `protobuf:"bytes,3,opt,name=app_food_code,json=appFoodCode,proto3" json:"app_food_code"`
	// 商品名称：(1)此字段信息限定长度不超过30个字符； // 创建时必填
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	// 商品描述，  非必镇
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description"`
	//  APP方商品的skus信息字符串  非必镇
	Skus []*SkuParam `protobuf:"bytes,6,rep,name=skus,proto3" json:"skus"`
	// 商品价格，单位是元；最多支持2位小数，不能为负数；     创建时，如不传skus信息，则本参数必填。
	Price float32 `protobuf:"fixed32,7,opt,name=price,proto3" json:"price"`
	// 一个订单中此商品的最小购买量。创建商品时，min_order_count字段信息如不传则默认为1  非必镇
	MinOrderCount int32 `protobuf:"varint,8,opt,name=min_order_count,json=minOrderCount,proto3" json:"min_order_count"`
	//    商品的售卖单位  非必镇
	Unit string `protobuf:"bytes,9,opt,name=unit,proto3" json:"unit"`
	//  单个商品需使用的打包盒数量，需在0-100之间。 非必镇
	BoxNum int64 `protobuf:"fixed32,10,opt,name=box_num,json=boxNum,proto3" json:"box_num"`
	//  单个打包盒的价格，单位是元，  非必镇
	//BoxPrice float32 `protobuf:"fixed32,11,opt,name=box_price,json=boxPrice,proto3" json:"box_price"`
	// 分类id   创建时：category_code与category_name、category_code_list、category_name_list字段必须且只能填写一个 更新时非必填
	CategoryCode string `protobuf:"bytes,12,opt,name=category_code,json=categoryCode,proto3" json:"category_code"`
	//  分类名称创建时：category_name与category_code、category_code_list、category_name_list字段必须且只能填写一个 更新时非必填
	CategoryName string `protobuf:"bytes,13,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	//  分类id列表，用于同步商品多个分类信息。 创建时：category_code_list与category_name、category_code、category_name_list字
	// 段必须且只能填写一个 更新时非必填
	CategoryCodeList string `protobuf:"bytes,14,opt,name=category_code_list,json=categoryCodeList,proto3" json:"category_code_list"`
	// 分类名称列表，用于同步商品多个分类信息 创建时：category_name_list与category_name、category_code、category_code_list
	// 字段必须且只能填写一个 更新时非必填
	CategoryNameList string `protobuf:"bytes,15,opt,name=category_name_list,json=categoryNameList,proto3" json:"category_name_list"`
	//  商品上下架状态，字段取值范围：0-上架，1-下架。  非必镇
	IsSoldOut int32 `protobuf:"varint,16,opt,name=is_sold_out,json=isSoldOut,proto3" json:"is_sold_out"`
	//  商品图片：  非必镇
	Picture string `protobuf:"bytes,17,opt,name=picture,proto3" json:"picture"`
	//  商品在当前分类下的排序 非必镇
	Sequence int32 `protobuf:"varint,18,opt,name=sequence,proto3" json:"sequence"`
	// 美团内部商品类目id  门店启用类目属性并且传递了销售或普通属性则  1.创建时必传，2.若商品创建时未传tag_id，更新时必传(只需传一次)。
	// 门店未启用类目属性或未传递销售属性/普通属性则非必传。
	TagId int64 `protobuf:"varint,19,opt,name=tag_id,json=tagId,proto3" json:"tag_id"`
	// 商品的品牌名称 非必镇
	ZhName string `protobuf:"bytes,20,opt,name=zh_name,json=zhName,proto3" json:"zh_name"`
	// 商品的产地  非必镇
	OriginName string `protobuf:"bytes,21,opt,name=origin_name,json=originName,proto3" json:"origin_name"`
	//  商品的图片详情 非必镇
	PictureContents string `protobuf:"bytes,22,opt,name=picture_contents,json=pictureContents,proto3" json:"picture_contents"`
	// 商品属性 非必镇
	Properties []*Propertie `protobuf:"bytes,23,rep,name=properties,proto3" json:"properties"`
	// 是否为“力荐”商品 百必镇
	IsSpecialty int32 `protobuf:"varint,24,opt,name=is_specialty,json=isSpecialty,proto3" json:"is_specialty"`
	// 视频ID 非必镇
	VideoId int64 `protobuf:"varint,25,opt,name=video_id,json=videoId,proto3" json:"video_id"`
	// 商品普通属性  若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
	CommonAttrValue []*CommonAttrValue `protobuf:"bytes,26,rep,name=common_attr_value,json=commonAttrValue,proto3" json:"common_attr_value"`
	// 商品限购详情  非必镇
	LimitSaleInfo *LimitSaleInfo `protobuf:"bytes,27,opt,name=limit_sale_info,json=limitSaleInfo,proto3" json:"limit_sale_info"`
	// 分类ID  非必镇
	CategoryId int32 `protobuf:"varint,28,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	// 店铺主体Id
	StoreMasterId int32 `protobuf:"varint,29,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	// 商品卖点
	SellPoint string `protobuf:"bytes,30,opt,name=sell_point,json=sellPoint,proto3" json:"sell_point"`
}

type SkuParam struct {
	// 是sku唯一标识码 必镇
	SkuId string `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// sku的规格名称  建时门店启用类目属性且skus中传递了销售属性则为非必填(会自动根据销售属性组合其规格)，其余情况参考字段描述里规则。 更新商品时，本参数非必填。
	Spec string `protobuf:"bytes,2,opt,name=spec,proto3" json:"spec"`
	// 为sku的商品包装上的条形码编号，UPC/EAN码；字符长度8位或者13位 非必镇
	Upc string `protobuf:"bytes,3,opt,name=upc,proto3" json:"upc"`
	// 为sku的价格  创建时必填
	Price string `protobuf:"bytes,4,opt,name=price,proto3" json:"price"`
	// sku的库存量 创建时必填
	Stock string `protobuf:"bytes,5,opt,name=stock,proto3" json:"stock"`
	// 商品sku的售卖单位 非必镇
	Unit string `protobuf:"bytes,6,opt,name=unit,proto3" json:"unit"`
	// 一个订单中此商品的最小购买量   非必镇
	MinOrderCount int32 `protobuf:"varint,7,opt,name=min_order_count,json=minOrderCount,proto3" json:"min_order_count"`
	// 表示sku可售时间 非必镇
	AvailableTimes *AvailableTimes `protobuf:"bytes,8,opt,name=available_times,json=availableTimes,proto3" json:"available_times"`
	// 表示sku的料位码 非必镇
	LocationCode string `protobuf:"bytes,9,opt,name=location_code,json=locationCode,proto3" json:"location_code"`
	// 包装费老计费规则，表示：商品sku单件需使用打包盒的数量 非必镇
	BoxNum int64 `protobuf:"bytes,10,opt,name=box_num,json=boxNum,proto3" json:"box_num"`
	//  包装费老计费规则，表示：商品sku单个打包盒的价格，单位是元，不能为负数 非必镇
	//BoxPrice string `protobuf:"bytes,11,opt,name=box_price,json=boxPrice,proto3" json:"box_price"`
	// 包装费阶梯计价规则，表示：每M件商品收取N元包装费中的M  非必镇
	LadderBoxNum string `protobuf:"bytes,12,opt,name=ladder_box_num,json=ladderBoxNum,proto3" json:"ladder_box_num"`
	// 包装费阶梯计价规则，表示：每M件商品收取N元包装费中的N。 非必镇
	LadderBoxPrice string `protobuf:"bytes,13,opt,name=ladder_box_price,json=ladderBoxPrice,proto3" json:"ladder_box_price"`
	//  表示sku的重量   否，与weight_for_unit和weight_unit至多填写一个
	//int64 weight=12;
	// 表示sku的重量数值信息  创建时，如填写weight_unit，则weight_for_unit必填且与weight至多填写一个，否则非必填
	WeightForUnit string `protobuf:"bytes,14,opt,name=weight_for_unit,json=weightForUnit,proto3" json:"weight_for_unit"`
	// 表示sku的重量数值单位，枚举值如下： 1."克(g)" 2."千克(kg)" 3."毫升(ml)" 4."升(L)" 5."磅" 6."斤" 7."两"。
	// 创建时，如填写weight_for_unit，则weight_unit必填且与weight至多填写一个，否则非必填
	WeightUnit string `protobuf:"bytes,15,opt,name=weight_unit,json=weightUnit,proto3" json:"weight_unit"`
	// 商品销售属性 非必镇
	OpenSaleAttrValueList []*OpenSaleAttrValue `protobuf:"bytes,16,rep,name=openSaleAttrValueList,proto3" json:"openSaleAttrValueList"`
}

type Propertie struct {
	// 属性名称，字段信息限定长度不超过10个字符。最多支持传10组属性。不允许上传emoji等表情符  若有properties参数则必须填
	PropertyName string `protobuf:"bytes,1,opt,name=property_name,json=propertyName,proto3" json:"property_name"`
	// 属性值 若有properties参数则必须填
	Values               string   `protobuf:"bytes,2,opt,name=values,proto3" json:"values"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

// 商品普通属性  若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
type CommonAttrValue struct {
	// 普通属性Id
	AttrId int64 `protobuf:"varint,1,opt,name=attrId,proto3" json:"attrId"`
	// 普通属性名称
	AttrName string `protobuf:"bytes,2,opt,name=attrName,proto3" json:"attrName"`
	// 普通属性列表
	ValueList []*ValueList `protobuf:"bytes,3,rep,name=valueList,proto3" json:"valueList"`
}

// 普通属性列表
type ValueList struct {
	// 普通属性值Id
	ValueId int64 `protobuf:"varint,1,opt,name=valueId,proto3" json:"valueId"`
	// 普通属性值名称
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value"`
}

// 限购信息  非必镇
type LimitSaleInfo struct {
	// 是否限制购买数量 必镇
	LimitSale bool `protobuf:"varint,1,opt,name=limitSale,proto3" json:"limitSale"`
	// 限购规则： 1-限制下单顾客每X天限购数量，X为frequency，不传默认为1；2-限制整个周期内下单顾客限购数量 如限购开启则必填
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	// 限购循环天数： 最大31，最小1。 非必镇
	Frequency int32 `protobuf:"varint,3,opt,name=frequency,proto3" json:"frequency"`
	// 限购开始日期 如限购开启则必填
	Begin string `protobuf:"bytes,4,opt,name=begin,proto3" json:"begin"`
	// 限购数量  如限购开启则必填
	Count int32 `protobuf:"varint,5,opt,name=count,proto3" json:"count"`
	// 限购结束日期 如限购开启则必填
	End string `protobuf:"bytes,6,opt,name=end,proto3" json:"end"`
}

// 表示sku可售时间 非必镇
type AvailableTimes struct {
	Monday    string `protobuf:"bytes,1,opt,name=monday,proto3" json:"monday"`
	Tuesday   string `protobuf:"bytes,2,opt,name=tuesday,proto3" json:"tuesday"`
	Wednesday string `protobuf:"bytes,3,opt,name=wednesday,proto3" json:"wednesday"`
	Thursday  string `protobuf:"bytes,4,opt,name=thursday,proto3" json:"thursday"`
	Friday    string `protobuf:"bytes,5,opt,name=friday,proto3" json:"friday"`
	Saturday  string `protobuf:"bytes,6,opt,name=saturday,proto3" json:"saturday"`
	Sunday    string `protobuf:"bytes,7,opt,name=sunday,proto3" json:"sunday"`
}

// 商品销售属性 非必镇
type OpenSaleAttrValue struct {
	//  销售属性id，不支持自定义
	AttrId int64 `protobuf:"varint,1,opt,name=attrId,proto3" json:"attrId"`
	// 销售属性值id。当属性值录入方式为文本时，该参数无需上传
	ValueId int64 `protobuf:"varint,2,opt,name=valueId,proto3" json:"valueId"`
	//  销售属性值，支持自定义
	Value string `protobuf:"bytes,3,opt,name=value,proto3" json:"value"`
}
