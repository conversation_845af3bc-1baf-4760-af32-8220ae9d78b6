package voucher

import (
	viewmodel "eShop/view-model"
	baseVO "eShop/view-model/inventory-vo"
	"eShop/view-model/inventory-vo/inventory"
)

// VoucherType 单据类型
type VoucherType string

const (
	// 采购单
	VoucherTypePurchase VoucherType = "CG"
	// 采购入库
	VoucherTypePurchaseIn VoucherType = "RK"
	// 采购退货单
	VoucherTypePurchaseReturn VoucherType = "RCG"
	// 采购退货出库
	VoucherTypePurchaseReturnOut VoucherType = "CK"
	//盘点
	VoucherTypeInventory VoucherType = "PD"
	// 销售
	VoucherTypeSale VoucherType = "销售"
	// 退货入库
	VoucherTypeReturnIn VoucherType = "退货入库"
)

var VoucherTypeMap = map[VoucherType]int{
	VoucherTypePurchase:          1,
	VoucherTypePurchaseIn:        4,
	VoucherTypePurchaseReturn:    2,
	VoucherTypePurchaseReturnOut: 5,
	VoucherTypeInventory:         3,
}

type VoucherPageRequest struct {
	ChainId      int64  `json:"chain_id"`      // 连锁id
	TenantId     string `json:"tenant_id"`     // 门店id
	WarehouseId  int    `json:"warehouse_id"`  // 仓库id
	VoucherNo    string `json:"voucher_no"`    // 单据单号
	SkuCount     int    `json:"sku_count"`     // 库存单据sku数量
	ChangeNum    int    `json:"change_num"`    // 盈亏数量
	ChangeAmount int    `json:"change_amount"` // 盈亏总金额
	ProfitStatus int    `json:"profit_status"` // 盈利状态: 1-盈利, 2-亏损, 3-平账
	Remark       string `json:"remark"`        // 备注
	IsDeleted    int    `json:"is_deleted"`    // 删除标识
	Operator     string `json:"operator"`      // 操作人
	Query        string `json:"query"`         // 查询条件
	Status       int    `json:"status"`        // 库存单据状态: 1-草稿, 2-待处理, 3-部分完成, 4-已完成, 5-已作废, 6-已取消,
	baseVO.PageRequest
	baseVO.TimeQueryRequest
}

type VoucherResponse struct {
	Id            int    `json:"id"`             // 主键
	ChainId       int64  `json:"chain_id"`       // 连锁id
	TenantId      string `json:"tenant_id"`      // 门店id
	WarehouseId   int    `json:"warehouse_id"`   // 仓库id
	VoucherNo     string `json:"voucher_no"`     // 单据单号
	SkuCount      int    `json:"sku_count"`      // 库存单据sku数量
	ChangeNum     int    `json:"change_num"`     // 盈亏数量
	ChangeAmount  int    `json:"change_amount"`  // 盈亏总金额
	ProfitStatus  int    `json:"profit_status"`  // 盈利状态: 1-盈利, 2-亏损, 3-平账
	Remark        string `json:"remark"`         // 备注
	IsDeleted     int    `json:"is_deleted"`     // 删除标识:0未删除,1已删除
	Operator      string `json:"operator"`       // 操作人
	Name          string `json:"name"`           // 库存单据名称
	Status        int    `json:"status"`         // 库存单据状态: 1-草稿, 2-待处理, 3-部分完成, 4-已完成, 5-已作废, 6-已取消,
	VoucherTime   string `json:"voucher_time"`   // 单据时间
	WarehouseName string `json:"warehouse_name"` // 仓库名称
	UpdatedTime   string `json:"updated_time"`   // 更新时间
}

type VoucherCreateCommand struct {
	ChainId     int64       `json:"chain_id"`     // 连锁id
	StoreId     string      `json:"store_id"`     // 店铺id
	WarehouseId int         `json:"warehouse_id"` // 仓库id
	Name        string      `json:"name"`         // 库存单据名称
	VoucherType VoucherType `json:"voucher_type"` // 单据类型
	Operator    string      `json:"operator"`     // 操作人
}

type VoucherUpdateCommand struct {
	Id                 int    `json:"id"`                   // 主键
	Remark             string `json:"remark"`               // 备注
	VoucherOperateType int    `json:"voucher_operate_type"` // 操作类型: 1-取消, 2-保存草稿, 3-完成盘点, 4-清空
	IsCheckChange      int    `json:"is_check_change"`      // 是否检测库存变动: 0-否, 1-是
	IsUseLatestNum     int    `json:"is_use_latest_num"`    // 是否更新库存: 0-否, 1-是
}

type VoucherDetailBatchCreateCommand struct {
	Id      int             `json:"voucher_id"` // 库存单据id
	Details []VoucherDetail `json:"details"`
}

type VoucherDetailUpdateCommand struct {
	Id        int    `json:"id"`         // 库存单据id
	ActualNum int    `json:"actual_num"` // 盘点实存,不填时传-1
	Remark    string `json:"remark"`     // 备注
}

type VoucherDetail struct {
	SkuId       int    `json:"sku_id"`       // sku id
	ProductName string `json:"product_name"` // 商品名称
	Remark      string `json:"remark"`       // 备注
	Quantity    int    `json:"quantity"`     //数量
}

type VoucherDetailPageRequest struct {
	ChainId           int64  `json:"chain_id"`            // 连锁id
	TenantId          string `json:"tenant_id"`           // 门店id
	WarehouseId       int    `json:"warehouse_id"`        // 仓库id
	VoucherId         int    `json:"voucher_id"`          // 单据id
	ProductId         int    `json:"product_id"`          // 商品id
	SkuId             int    `json:"sku_id"`              // sku id
	ProfitStatus      int    `json:"profit_status"`       // 盈利状态: 1-盈利, 2-亏损, 3-平账
	Remark            string `json:"remark"`              // 备注
	Query             string `json:"query"`               // 查询条件
	BarCode           string `json:"bar_code"`            // 条形码
	ProductCategoryId string `json:"product_category_id"` // 商品分类ID
	baseVO.PageRequest
}

type DetailStatisticsResponse struct {
	TotalNum       int `json:"total_num"`        // 库存单据总数
	DoneTotalNum   int `json:"done_total_num"`   // 已完成库存单据数
	UndoneTotalNum int `json:"undone_total_num"` // 未完成库存单据数
}

type VoucherDetailResponse struct {
	Id            int    `json:"id"`              // 主键
	ChainId       int64  `json:"chain_id"`        // 连锁id
	StoreId       string `json:"store_id"`        // 门店id
	VoucherId     int    `json:"voucher_id"`      // 单据id
	ProductId     int    `json:"product_id"`      // 商品id
	SkuId         int    `json:"sku_id"`          // sku id
	TotalNum      int    `json:"total_num"`       // 当前系统库存
	AvgCostPrice  int    `json:"avg_cost_price"`  // 当前平均成本价(分)
	ActualNum     int    `json:"actual_num"`      // 库存单据库存
	ChangeNum     int    `json:"change_num"`      // 盈亏数量
	ChangeAmount  int    `json:"change_amount"`   // 盈亏总金额(分)
	ProfitStatus  int    `json:"profit_status"`   // 盈利状态: 1-盈利, 2-亏损, 3-平账
	TotalNumAfter int    `json:"total_num_after"` // 库存单据后总库存
	//FreezeNumAfter    int                              `json:"freeze_num_after"`    // 库存单据后锁定库存
	//AvailableNumAfter int                              `json:"available_num_after"` // 库存单据后可用库存
	//TotalAmountAfter  int                              `json:"total_amount_after"`  // 库存单据后总成本价(分)
	Remark           string `json:"remark"`             // 备注
	TotalNumSnapshot int    `json:"total_num_snapshot"` // 总库存快照
	WarehouseName    string `json:"warehouse_name"`     // 总库存快照
	//ProductCategoryId string                           `json:"product_category_id"` // 商品分类ID
	ProductSku inventory.ProductSkuInfoResponse `json:"product_sku" xorm:"-"` // 商品信息
}

// VoucherQueryParams 单据列表页查询参数
type VoucherQueryParams struct {
	viewmodel.BasePageHttpRequest
	ChainId          int64  `json:"chain_id"`           // 连锁id
	TenantId         string `json:"tenant_id"`          // 门店id
	WarehouseId      int    `json:"warehouse_id"`       // 仓库id
	VoucherNo        string `json:"voucher_no"`         // 单据单号
	Status           int    `json:"status"`             // 单据状态
	SourceType       int    `json:"source_type"`        // 单据来源
	VoucherType      int    `json:"voucher_type"`       // 单据类型
	VoucherTime      string `json:"voucher_time"`       // 单据日期
	SkuInfo          string `json:"sku_info"`           // sku信息
	SupplierInfo     string `json:"supplier_info"`      // 供应商信息
	VoucherTimeType  int    `json:"voucher_time_type"`  // 单据日期类型: 1-单据日期, 2-采购日期, 3-预计到货时间, 4-退货时间
	VoucherTimeStart string `json:"voucher_time_start"` // 单据日期开始
	VoucherTimeEnd   string `json:"voucher_time_end"`   // 单据日期结束
}

// VoucherWithDetailResponse 单据详情页数据
type VoucherWithDetailResponse struct {
	voucher VoucherPageResponse
	details []VoucherDetailResponse
}

// VoucherPageResponse 单据列表页的单行数据
type VoucherPageResponse struct {
	// 单据信息
	Id int `json:"id"`
	//子单据id,当前列表为采购单或采购入库单时，该字段是入库单Id；当前列表为采购退货单或采购退货出库单时，该字段是出库单Id
	ChildId int `json:"child_id"`
	// 子单据单号
	ChildVoucherNo string `json:"child_voucher_no"`
	// 单据单号
	VoucherNo string `json:"voucher_no"`
	// 连锁信息
	ChainId int64 `json:"chain_id"`
	// 门店信息
	StoreId string `json:"store_id"`
	// 门店名称
	StoreName string `json:"store_name"`
	// 仓库信息
	WarehouseId int `json:"warehouse_id"`
	// 仓库名称
	WarehouseName string `json:"warehouse_name"`
	// 供应商信息
	SupplierId int `json:"supplier_id"`
	// 供应商名称
	SupplierName string `json:"supplier_name"`
	// 单据名称
	Name string `json:"name"`
	// 单据类型
	VoucherType int `json:"voucher_type"`
	// 单据状态
	Status int `json:"status"`
	// 申请单据sku种类
	ExpectSkuNum int `json:"expect_sku_num"`
	// 申请单据sku商品数量
	ExpectSkuCount int `json:"expect_sku_count"`
	// 申请单据总金额
	ExpectTotalAmount int `json:"expect_total_amount"`
	// 真实单据sku种类
	ActualSkuNum int `json:"actual_sku_num"`
	// 真实单据sku商品数量
	ActualSkuCount int `json:"actual_sku_count"`
	// 真实单据总金额
	ActualTotalAmount int `json:"actual_total_amount"`
	// 待处理单据sku种类
	WaitSkuNum int `json:"wait_sku_num"`
	// 待处理单据sku商品数量
	WaitSkuCount int `json:"wait_sku_count"`
	// 待处理单据总金额
	WaitTotalAmount int `json:"wait_total_amount"`
	// 单据来源
	SourceType int `json:"source_type"`
	// 盈亏状态
	ProfitStatus int `json:"profit_status"`
	// 盈亏数量
	ChangeNum int `json:"change_num"`
	// 盈亏金额
	ChangeAmount int `json:"change_amount"`
	// 备注
	Remark string `json:"remark"`
	// 采购日期
	PurchaseTime string `json:"purchase_time"`
	// 预计到货时间
	DeliveryTime string `json:"delivery_time"`
	// 退货时间
	ReturnTime string `json:"return_time"`
	// 删除标识
	IsDeleted int `json:"is_deleted"`
	// 操作人
	Operator string `json:"operator"`
	// 单据日期
	VoucherTime string `json:"voucher_time"`
	// 创建时间
	CreatedTime string `json:"created_time"`
	// 修改时间
	UpdatedTime string `json:"updated_time"`
	// 单据商品详情
	Details []VoucherPageProductDetail `json:"details"`
}

type VoucherPageProductDetail struct {
	VoucherId      int    `json:"voucher_id"`      // 单据id
	SkuId          int    `json:"sku_id"`          // sku id
	ProductId      int    `json:"product_id"`      // 商品id
	ProductName    string `json:"product_name"`    // 商品名称
	ProductImg     string `json:"product_img"`     // 商品图片
	ExpectQuantity int    `json:"expect_quantity"` // 预计数量
}

// VoucherSaveCommand 保存各种单据的 不包含盘点
type VoucherSaveCommand struct {
	Id           int             `json:"id"`            // 单据Id
	Pid          int             `json:"pid"`           // 上级id
	Name         string          `json:"name"`          // 库存单据名称
	VoucherType  VoucherType     `json:"voucher_type"`  // 单据类型
	SourceType   int             `json:"source_type"`   // 单据来源 1-连锁采购, 2-店铺自采, 3-代运营采购
	Status       int             `json:"status"`        // 单据状态 1-草稿, 2-待处理, 3-部分完成, 4-已完成, 5-已作废, 6-已取消
	ShopId       int             `json:"shop_id"`       // 店铺id
	WarehouseId  int             `json:"warehouse_id"`  // 仓库id
	SupplierId   int             `json:"supplier_id"`   // 供应商
	Remark       string          `json:"remark"`        // 备注
	PurchaseTime string          `json:"purchase_time"` // 采购日期
	DeliveryTime string          `json:"delivery_time"` // 预计到货时间
	ReturnTime   string          `json:"return_time"`   // 退货时间
	Details      []ProductDetail `json:"details"`       // 单据详情
}

// ProductDetail 单据中商品详情
type ProductDetail struct {
	ProductId int    `json:"product_id"` // 商品id
	SkuId     int    `json:"sku_id"`     // sku id
	Quantity  int    `json:"quantity"`   // 数量
	Price     int    `json:"price"`      // 单价,单位分
	Amount    int    `json:"amount"`     // 金额,单位分
	Reason    string `json:"reason"`     // 原因
}

type StocktakingOperateResponse struct {
	OperateResult    string                               `json:"operate_result"`    // 结果:"OK"-完成操作， "NUM_CHANGE"-实际库存发生变更
	StocktakingModel string                               `json:"stocktaking_model"` // 盘点方式,NOT_BLIND明盘,BLIND盲盘
	ChangeNumDetails []StocktakingDetailChangeNumResultVO `json:"change_num_details"`
}

type StocktakingDetailChangeNumResultVO struct {
	Id               int    `json:"id"`                 // 主键
	ProductId        int    `json:"product_id"`         // 商品id
	SkuId            int    `json:"sku_id"`             // sku id
	ProductName      string `json:"product_name"`       // 商品名称
	ProductImg       string `json:"product_img"`        // 商品图片
	TotalNum         int    `json:"total_num"`          // 当前系统库存
	TotalNumSnapshot int    `json:"total_num_snapshot"` // 总库存快照
	ActualNum        int    `json:"actual_num"`         // 盘点库存
}

type StocktakingSaveVO struct {
	VoucherId int                       `json:"voucher_id"` // 盘点单id
	Details   []StocktakingDetailSaveVO `json:"details"`    // 盘点单详情
}

type StocktakingDetailSaveVO struct {
	Id               int    `json:"id"`                 // 盘点明细ID
	ProductId        int    `json:"product_id"`         // 商品id
	SkuId            int    `json:"sku_id"`             // sku id
	TotalNumSnapshot int    `json:"total_num_snapshot"` // 总库存快照
	ActualNum        int    `json:"actual_num"`         // 盘点实存
	ChangeNum        int    `json:"change_num"`         // 实际盈亏数量
	Remark           string `json:"remark"`             // 备注
}
