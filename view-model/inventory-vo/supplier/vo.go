package supplier

import (
	viewmodel "eShop/view-model"
)

type SupplierType int

const (
	// 线下供应商
	SupplierType_OfflineSupplier SupplierType = 1
	//	线上供应商
	SupplierType_OnlineSupplier SupplierType = 2
	// 1688供应商
	SupplierType_1688Supplier SupplierType = 3
)

type Supplier struct {
	Id              int          `json:"id"`                // 供应商ID
	Name            string       `json:"name"`              // 供应商名称
	Address         string       `json:"address"`           // 供应商地址
	Type            SupplierType `json:"type"`              // 供应商类型
	UnifiedSocialId string       `json:"unified_social_id"` // 统一社会信用编码
	PurchasePeriod  int          `json:"purchase_period"`   // 采购周期(天)
	DueDays         int          `json:"due_days"`          // 到货天数
	ContactPerson   string       `json:"contact_person"`    // 联系人
	ContactMethod   string       `json:"contact_method"`    // 联系方式
	BankAccount     string       `json:"bank_account"`      // 银行账号
	BankName        string       `json:"bank_name"`         // 开户银行
	AccountName     string       `json:"account_name"`      // 开户名称
	Status          int       `json:"status"`            // 状态Status
	Source          int       `json:"source"`            // 来源 1.店铺自建；2.连锁创建
	Url             string       `json:"url"`               // 供应商官网
	CreatedAt       string       `json:"created_at"`        // 创建时间
	UpdatedAt       string       `json:"updated_at"`        // 更新时间
	CreatedTime     string       `json:"created_time"`      // 创建时间
	UpdatedTime     string       `json:"updated_time"`      // 更新时间
}

// CreateCommand 创建供应商命令
type CreateCommand struct {
	Name            string       `json:"name"`              // 供应商名称
	Address         string       `json:"address"`           // 供应商地址
	Type            SupplierType `json:"type"`              // 供应商类型
	UnifiedSocialID string       `json:"unified_social_id"` // 统一社会信用编码
	PurchasePeriod  int          `json:"purchase_period"`   // 采购周期(天)
	DueDays         int          `json:"due_days"`          // 到货天数
	ContactPerson   string       `json:"contact_person"`    // 联系人
	ContactMethod   string       `json:"contact_method"`    // 联系方式
	BankAccount     string       `json:"bank_account"`      // 银行账号
	BankName        string       `json:"bank_name"`         // 开户银行
	AccountName     string       `json:"account_name"`      // 开户名称
	Source          int          `json:"source"`            // 来源 1.店铺自建；2.连锁创建
	Url             string       `json:"url"`               // 供应商官网
}

// UpdateCommand 更新供应商命令
type UpdateCommand struct {
	ID              int          `json:"id"`                // 供应商ID
	Name            string       `json:"name"`              // 供应商名称
	Address         string       `json:"address"`           // 供应商地址
	Type            SupplierType `json:"type"`              // 供应商类型
	UnifiedSocialID string       `json:"unified_social_id"` // 统一社会信用编码
	PurchasePeriod  int          `json:"purchase_period"`   // 采购周期(天)
	DueDays         int          `json:"due_days"`          // 到货天数
	ContactPerson   string       `json:"contact_person"`    // 联系人
	ContactMethod   string       `json:"contact_method"`    // 联系方式
	BankAccount     string       `json:"bank_account"`      // 银行账号
	BankName        string       `json:"bank_name"`         // 开户银行
	AccountName     string       `json:"account_name"`      // 开户名称
	Source          int          `json:"source"`            // 来源 1.店铺自建；2.连锁创建
	Url             string       `json:"url"`               // 供应商官网
}

// EnableCommand 启用供应商命令
type EnableCommand struct {
	ID int `json:"id"` // 供应商ID
}

// DisableCommand 禁用供应商命令
type DisableCommand struct {
	ID int `json:"id"` // 供应商ID
}

// QueryParams 查询参数
type QueryParams struct {
	viewmodel.BasePageHttpRequest
	SupplierInfo    string `json:"supplier_info"`    // 供应商信息
	Type            string `json:"type"`            // 供应商类型
	Source          int `json:"source"`          // 来源
	Status          int `json:"status"`          // 状态
}
