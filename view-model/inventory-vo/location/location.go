package location

import (
	vo "eShop/view-model/inventory-vo/warehouse"
)

// CreateCommand 创建库位命令
type CreateCommand struct {
	Code   string `json:"code"`   // 库位编码
	Name   string `json:"name"`   // 库位名称
	Status string `json:"status"` // 状态
}

// UpdateCommand 更新库位命令
type UpdateCommand struct {
	ID     int    `json:"id"`     // 库位ID
	Name   string `json:"name"`   // 库位名称
	Status string `json:"status"` // 状态
}

// BindProductCommand 绑定商品命令
type BindProductCommand struct {
	LocationID int `json:"location_id"` // 库位ID
	SKUID      int `json:"sku_id"`      // SKU ID
}

// QueryParams 查询参数
type QueryParams struct {
	WarehouseID int    `json:"warehouse_id"` // 仓库id
	Code        string `json:"code"`         // 库位编码
	Name        string `json:"name"`         // 库位名称
	ProductInfo string `json:"product_info"` // 商品信息:商品名称、条码、Id、skuId等
	Page        int    `json:"page"`         // 页码
	PageSize    int    `json:"page_size"`    // 每页大小
}

// Status 库位状态
type Status string

const (
	StatusEnabled  Status = "启用"
	StatusDisabled Status = "禁用"
)

// Location 库位信息
type Location struct {
	Id          int             `json:"id" xorm:"pk autoincr"`    // 主键ID
	ChainId     int64           `json:"chain_id"`                 // 连锁ID
	StoreId     string          `json:"store_id"`                 // 门店ID
	WarehouseId int             `json:"warehouse_id"`             // 仓库ID
	Code        string          `json:"code"`                     // 库位编码
	CreatedTime string          `json:"created_time"`             // 创建时间
	UpdatedTime string          `json:"updated_time"`             // 修改时间
	Products    LocationProduct `json:"products" xorm:"extends"`  // 库位商品
	Warehouse   vo.WarehouseVO  `json:"warehouse" xorm:"extends"` // 仓库
}

// LocationProduct 库位商品
type LocationProduct struct {
	//ID           int       `json:"id" xorm:"pk autoincr"`          // 主键ID
	//ChainID      int       `json:"chain_id"`                       // 连锁ID
	//StoreID      int       `json:"store_id"`                       // 门店ID
	//WarehouseID  int       `json:"warehouse_id"`                   // 仓库ID
	//LocationID   int       `json:"location_id"`                    // 库位ID
	ProductID    int    `json:"product_id" xorm:"'product_id'"` // 商品ID
	SKUID        int    `json:"sku_id" xorm:"'sku_id'"`         // SKU ID
	BarCode      string `json:"bar_code"`                       // 条形码
	ProductName  string `json:"product_name"`                   // 商品名称
	ProductImage string `json:"product_image"`                  // 商品图片
	ProductSpec  string `json:"product_spec"`                   // 商品规格
	CreatedBy    int    `json:"created_by"`                     // 创建人
	CreatedTime  string `json:"created_time"`                   // 创建时间
	UpdatedBy    int    `json:"updated_by"`                     // 修改人
	UpdatedTime  string `json:"updated_time"`                   // 修改时间
}
