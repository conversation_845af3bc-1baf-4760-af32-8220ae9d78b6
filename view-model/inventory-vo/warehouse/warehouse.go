package warehouse

// WarehouseVO 仓库视图对象
type WarehouseVO struct {
	Id                  int    `json:"id"`                    // 仓库ID
	ChainId             int64  `json:"chain_id"`              // 连锁ID
	StoreId             string `json:"store_id"`              // 门店ID
	StoreName           string `json:"store_name"`            // 门店名称
	OperationType       int    `json:"operation_type"`        // 运营类型(1：独立运营 , 2：代运营模式)
	WarehouseName       string `json:"warehouse_name"`        // 仓库名称(取店名，如"深圳三美连锁店铺1")
	Category            int    `json:"category"`              // 仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓，5-前置仓虚拟仓，6-门店新零售仓)
	CategoryName        string `json:"category_name"`         // 仓库类型名称
	Address             string `json:"address"`               // 地址(如"广东省深圳市")
	Lat                 string `json:"latitude"`              // 纬度
	Lng                 string `json:"longitude"`             // 经度
	AuthorizedChainId   int64  `json:"authorized_chain_id"`   // 代运营组织ID
	AuthorizedChainName string `json:"authorized_chain_name"` // 代运营组织名称
	ChannelId           string `json:"channel_id"`            // 1小程序，2美团，3饿了么，4京东，100门店
}

type QueryRequest struct {
	StoreId string `json:"storeId"` // 店铺Id
}
