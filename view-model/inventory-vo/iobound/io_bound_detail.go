package iobound

import (
	baseVO "eShop/view-model/inventory-vo"
	inventoryVO "eShop/view-model/inventory-vo/inventory"
)

type IoBoundDetailPageRequest struct {
	ChainId            int64  `json:"chain_id"`             // 连锁id
	StoreId            string `json:"store_id"`             // 门店id
	WarehouseId        int    `json:"warehouse_id"`         // 仓库id
	BoundId            int    `json:"bound_id"`             // 出入库id
	BoundNo            string `json:"bound_no"`             // 出入库单号
	BoundType          int    `json:"bound_type"`           // 出库/入库类型
	ItemType           int    `json:"item_type"`            // 单据类型
	ItemDetailRefId    string `json:"item_detail_ref_id"`   // 出入库关联的单据详情id
	ProductId          int    `json:"product_id"`           // 商品id
	SkuId              int    `json:"sku_id"`               // sku id
	ProductName        string `json:"product_name"`         // 商品名称
	ProductType        int    `json:"product_type"`         // 商品类型
	BarCode            string `json:"bar_code"`             // 条形码
	Operator           string `json:"operator"`             // 操作人
	Query              string `json:"query"`                // 查询条件
	ProductCategoryId  int    `json:"product_category_id"`  // 商品分类id
	ProductCategoryIds []int  `json:"product_category_ids"` // 商品分类id集合
	baseVO.PageRequest
	baseVO.TimeQueryRequest
	baseVO.SortRequest
}

type IoBoundDetailResponse struct {
	Id                  int                                `json:"id"`                    // 主键
	ChainId             int64                              `json:"chain_id"`              // 连锁id
	StoreId             string                             `json:"store_id"`              // 门店id
	WarehouseId         int                                `json:"warehouse_id"`          // 仓库id
	WarehouseName       string                             `json:"warehouse_name"`        // 仓库名称
	BoundId             int                                `json:"bound_id"`              // 出入库id
	BoundNo             string                             `json:"bound_no"`              // 出入库单号
	BoundType           int                                `json:"bound_type"`            // 出库/入库类型
	ItemType            int                                `json:"item_type"`             // 单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 11. 初始化入库, 12.自用出库
	ItemRefId           int                                `json:"item_ref_id"`           // 出入库关联的单据id
	ItemRefNo           string                             `json:"item_ref_no"`           // 出入库关联的单据编号
	ItemRefType         int                                `json:"item_ref_type"`         // 出入库关联的单据类型:1.无，2.采购单，3.订单，4.盘点单，5.其他
	ItemDetailRefId     string                             `json:"item_detail_ref_id"`    // 出入库关联的单据详情id
	ProductId           int                                `json:"product_id"`            // 商品id
	SkuId               int                                `json:"sku_id"`                // sku id
	ProductName         string                             `json:"product_name"`          // 商品名称
	ProductType         int                                `json:"product_type"`          // 商品类型
	BarCode             string                             `json:"bar_code"`              // 条形码
	ProductCategoryPath string                             `json:"product_category_path"` // 商品分类路径
	AvgCostPrice        int                                `json:"avg_cost_price"`        // 出入库前平均成本价
	IoPrice             int                                `json:"io_price"`              // 出入库单价
	IoCount             int                                `json:"io_count"`              // 出入库数量
	IoAmount            int                                `json:"io_amount"`             // 出入库小计
	RealIoAmount        int                                `json:"real_io_amount"`        // 实际出入库小计
	GrossMargin         int                                `json:"gross_margin"`          // 毛利
	TotalNumAfter       int                                `json:"total_num_after"`       // 变更后总库存
	FreezeNumAfter      int                                `json:"freeze_num_after"`      // 变更后锁定库存
	AvailableNumAfter   int                                `json:"available_num_after"`   // 变更后可用库存
	TotalAmountAfter    int                                `json:"total_amount_after"`    // 变更后总成本
	Operator            string                             `json:"operator"`              // 操作人
	Remark              string                             `json:"remark"`                // 备注
	CreatedTime         string                             `json:"created_time"`          // 创建时间
	UpdatedTime         string                             `json:"updated_time"`          // 更新时间
	ProductSku          inventoryVO.ProductSkuInfoResponse `json:"product_sku" xorm:"-"`  // 商品信息
}
