package petai_vo

type PetInfo struct {
	Id          int    `json:"id"`           //更新宠物信息 传这个id
	UserInfoId  string `json:"user_info_id"` //用户ID作为唯一标示即eshop.user_info.user_info_id
	PetInfoId   string `json:"pet_info_id"`  //宠物唯一标识
	PetId       string `json:"pet_id"`       //宠物ID作为唯一标示，若授权了同scrm_organization_db.t_scrm_pet_info.pet_id，若未授权则为空
	PetAvatar   string `json:"pet_avatar"`   //宠物头像
	PetName     string `json:"pet_name"`     //宠物昵称
	PetBirthday string `json:"pet_birthday"` //宠物生日
	PetHomeDay  string `json:"pet_home_day"` //宠物到家日期
	PetSex      int    `json:"pet_sex"`      //宠物性别 0未知 1公 2母
	PetSexStr   string `json:"pet_sex_str"`  //宠物性别 0未知 1公 2母
	PetKindof   int    `json:"pet_kindof"`   //品种 -1未知
	PetVariety  int    `json:"pet_variety"`  //种类 -1 未知

	PetLong         int    `json:"pet_long"`          //宠物体长,单位mm
	PetWeight       int    `json:"pet_weight"`        //宠物体重,单位g
	PetNeutering    int    `json:"pet_neutering"`     //绝育 -1未知,0未绝育,1已绝育
	PetNeuteringStr string `json:"pet_neutering_str"` //绝育 ：未绝育,已绝育
	PetVaccinated   int    `json:"pet_vaccinated"`    //疫苗 -1未知,0未接种,1已接种
	PetDeworming    int    `json:"pet_deworming"`     //驱虫  -1未知,0未驱虫,1 已驱虫
	PetHeight       int    `json:"pet_height"`        //宠物体高,单位mm
	PetSource       int    `json:"pet_source"`        //宠物来源 0：子龙 1：小暖 2：瑞鹏 3:小程序
	PetStatus       int    `json:"pet_status"`        //宠物状态 0正常,1死亡,2走失,4送人,8隐藏

	PetRemark        string `json:"pet_remark"`         //用户备注
	PetVarietyStr    string `json:"pet_variety_str"`    //种类
	PetKindofStr     string `json:"pet_kindof_str"`     //品种
	DogLicenceCode   string `json:"dog_licence_code"`   //养犬许可证号码
	DogVaccinateCode string `json:"dog_vaccinate_code"` //犬类免疫证号码
	FaceId           string `json:"face_id"`            //宠物faceId
	PetCode          string `json:"pet_code"`           //宠物鼻纹识别宠物code

	SendTime         string `json:"send_time"`          //发送时间
	InsuranceFaceId  string `json:"insurance_face_id"`  //保险-宠物faceId
	EnsureCard       bool   `json:"ensure_card"`        //是否有身份证
	AgeStr           string `json:"age_str"`            //年龄
	AgeConversionStr string `json:"age_conversion_str"` //年龄转换
	PetFlower        string `json:"pet_flower"`         //宠物花色
	FlowerCode       string `json:"flower_code"`        //花色编码

}

type PetInfoListReq struct {
	UserInfoId string `json:"user_info_id"` //用户ID作为唯一标示即eshop.user_info.user_info_id(前端不用传)
	PageIndex  int    `json:"page_index"`   //页码
	PageSize   int    `json:"page_size"`    //每页条数
	PetInfoId  string `json:"pet_info_id"`  //宠物id
}

type PetInfoListResp struct {
	PetInfoList []*PetInfo `json:"pet_info_list"`
}

type PetHealth struct {
	Id            int    `json:"id" xorm:"not null pk autoincr INT(11)"`   //主键(更新是传这个id)
	PetInfoId     string `json:"pet_info_id" xorm:"not null VARCHAR(32)"`  //宠物id即user_pet_info.pet_info_id
	PetId         string `json:"pet_id" xorm:"not null VARCHAR(255)"`      // 宠物ID作为唯一标示，若授权了同scrm_organization_db.t_scrm_pet_info.pet_id，若未授权则为空
	Type          string `json:"type" xorm:"not null VARCHAR(16)"`         // 类别类别（免疫：200000084，驱虫：200000085，绝育：200000086，洗牙：200000087）
	OperationTime string `json:"operation_time" xorm:"not null DATETIME"`  //接种时间/驱虫时间
	StructName    string `json:"struct_name" xorm:"not null VARCHAR(32)"`  // 医院名称即接诊机构
	ProductCode   string `json:"product_code" xorm:"not null VARCHAR(20)"` // 产品编码
	ProductName   string `json:"product_name" xorm:"not null VARCHAR(50)"` // 产品名称
	UserInfoId    string `json:"user_info_id" xorm:"not null VARCHAR(32)"` //用户id(前端不用传)
}
type PetHealthListReq struct {
	Type      string `json:"type" xorm:"not null VARCHAR(16)"`        // 类别类别（免疫：200000084，驱虫：200000085，绝育：200000086，洗牙：200000087）
	PetInfoId string `json:"pet_info_id" xorm:"not null VARCHAR(32)"` //宠物id即user_pet_info.pet_info_id

}

type PetVaccinate struct {
	Id            int    `json:"id" xorm:"not null pk autoincr INT(11)"`     //主键(更新是传这个id)
	PetInfoId     string `json:"pet_info_id" xorm:"not null VARCHAR(32)"`    //宠物id即user_pet_info.id
	PetId         string `json:"pet_id" xorm:"not null VARCHAR(255)"`        // 宠物ID作为唯一标示，若授权了同scrm_organization_db.t_scrm_pet_info.pet_id，若未授权则为空
	Type          int    `json:"type" xorm:"not null TINYINT(4)"`            // 1疫苗记录 2驱虫记录 3口腔 4体检 5洗护 6体况评分 7三围 8体重 101宠物病史记录表 102症状自诊 103报告解读 104(生活环境(多选,英文逗号隔开) 喂养方式(多选,英文逗号隔开) 喂食模式 洗澡频率 运动方式 运动时长) 105过敏史 106肌肉评分   petai-v2.3.0 把宠物基本信息整合为一条文本信息
	OperationDate string `json:"operation_date" xorm:"not null DATETIME"`    // 接种日期/驱虫日期/病史记录日期
	ShopName      string `json:"shop_name" xorm:"not null VARCHAR(255)"`     // 接诊机构/就诊医院
	ProductName   string `json:"product_name" xorm:"not null VARCHAR(1000)"` // 产品名称/疾病诊断
	ProductCode   string `json:"product_code" xorm:"not null VARCHAR(20)"`   // 产品编码
	Category      string `json:"category"`                                   // 类型（核心疫苗：200000088，狂犬疫苗：200000089，核心疫苗抗体检测：200000091,狂犬疫苗抗体检测:200000092,抗体检测：200000090）
	Number        int    `json:"number" xorm:"not null INT(11)"`             // 第几针 1-首次免疫 2-二次免疫 3-尾次免疫 4-年度免疫
	NumberOf      int    `json:"number_of" xorm:"not null TINYINT(4)"`       // 0：未知，1：首次免疫，2：二次免疫，3：尾次免疫，4:年度免疫
	RecordPhoto   string `json:"record_photo" xorm:"not null VARCHAR(255)"`  // 记录拍照
	//TScrmPetVaccinateRecordId int    `json:"t_scrm_pet_vaccinate_record_id" xorm:"not null INT(11)"` // dc_customer.t_scrm_pet_vaccinate_record.id
	DataSource       int    `json:"data_source"`       //数据来源：1-AI问答  2-自建 3-scrm系统(前端不用传)
	TreatmentOutcome string `json:"treatment_outcome"` // 治愈情况
	ThirdNo          string `json:"third_no"`          // 第三方唯一标识当type为1,2时,scrm_organization_db.t_scrm_pet_health_record; type为3,4,5,6,7,8时dc_customer.t_scrm_pet_vaccinate_record.id;type为101时是SCMR病史记录唯一标识
	DeleteReason     string `json:"delete_reason"`     // 删除原因
	AllergicReaction string `json:"allergic_reaction"` // 过敏反应 petai-v2.3.0 把宠物基本信息整合为一条文本信息

}

type PetVaccinateListReq struct {
	Type           int    `json:"type" xorm:"not null TINYINT(4)"`         // 1疫苗记录 2驱虫记录 3口腔 4体检 5洗护 6体况评分 7三围 8体重 101宠物病史记录表 102症状自诊 103报告解读
	PetInfoId      string `json:"pet_info_id" xorm:"not null VARCHAR(32)"` //宠物id即user_pet_info.pet_info_id
	PageIndex      int    `json:"page_index"`                              //页码
	PageSize       int    `json:"page_size"`                               //每页条数
	ConversationId int    `json:"conversation_id"`                         // 会话id
	SectionUuid    string `json:"section_uuid"`                            // 话题uuid即petai_message.section_uuid
}

type PetVaccinateDeleteReq struct {
	Id           int    `json:"id" xorm:"not null pk autoincr INT(11)"` //主键(更新是传这个id)
	DeleteReason string `json:"delete_reason"`                          //删除原因
}

type PetInfoEditStatusReq struct {
	PetInfoId string `json:"pet_info_id" xorm:"not null pk autoincr VARCHAR(32)"` //主键(更新是传这个id)
	PetStatus int    `json:"pet_status" xorm:"not null TINYINT(4)"`               //宠物状态 0正常,1死亡,2走失,4送人,8隐藏
}

type NewPetRecordDateRes struct {
	//创建时间
	CreateTime string `protobuf:"bytes,1,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//体况拍照记录
	RecordPhoto string `protobuf:"bytes,2,opt,name=record_photo,json=recordPhoto,proto3" json:"record_photo"`
	//产品名称/BCS评分/三围/体重
	ProductName string `protobuf:"bytes,3,opt,name=product_name,json=productName,proto3" json:"product_name"`
	//接种日期/驱虫日期
	OperationDate string `protobuf:"bytes,4,opt,name=operation_date,json=operationDate,proto3" json:"operation_date"`
	//记录类型 0 绝育  1疫苗记录 2驱虫记录 3口腔 4体检 5洗护 6体况评分 7三围 8体重
	RecordType int64 `protobuf:"varint,5,opt,name=record_type,json=recordType,proto3" json:"record_type"`
	//红点展示
	Show bool `protobuf:"varint,6,opt,name=show,proto3" json:"show"`
	//距离3天提醒
	Remind bool `protobuf:"varint,7,opt,name=remind,proto3" json:"remind"`
	//天数
	DayNum int32 `protobuf:"varint,8,opt,name=day_num,json=dayNum,proto3" json:"day_num"`
}
type PetRecordDate struct {
	Id            int    `json:"id"`
	PetInfoId     string `json:"pet_info_id"`
	PetId         string `json:"pet_id"`
	RecordType    int64  `json:"record_type"`
	OperationDate string `json:"operation_date"`
	RecordPhoto   string `json:"record_photo"`
	ProductName   string `json:"product_name"`
	CreateTime    string `json:"create_time"`
	Number        int    `json:"number"`
	Category      string `json:"category"`
}
type GetNewPetDateReq struct {
	PetInfoId string `json:"pet_info_id" xorm:"not null VARCHAR(32)"` //宠物id即user_pet_info.id
}

type ShowPetNeuteringData struct {
	PetBirthday  string `json:"pet_birthday"`
	PetNeutering int    `json:"pet_neutering"`
	PetKindof    int32  `json:"pet_kindof"`
}

type GetPetVaccinateLatestListReq struct {
	PetInfoId string `json:"pet_info_id" xorm:"not null VARCHAR(32)"` //宠物id即user_pet_info.id

}
