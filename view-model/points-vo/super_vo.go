package points_vo

import (
	po "eShop/domain/points-po"
	"eShop/infra/converter"
)

// SaveVO
type SaveVO interface {
	ToEntity(vo SaveVO) (any, error)
}

type SuperSaveVO[E po.Entity] struct {
}

func (v SuperSaveVO[E]) ToEntity(vo SaveVO) (any, error) {
	return convertToEntity[E](vo)
}

// UpdateVO
type UpdateVO interface {
	ToEntity(vo UpdateVO) (any, error)
}

type SuperUpdateVO[E po.Entity] struct {
}

func (v SuperUpdateVO[E]) ToEntity(vo UpdateVO) (any, error) {
	return convertToEntity[E](vo)
}

// QueryVO
type QueryVO interface {
	GetCurrent() int
	GetSize() int
	GetOrderBy() string
	GetExportType() int
}

type SuperQueryVO[E po.Entity] struct {
	Current int    `json:"current" validate:"required"`                                    // 当前页码
	Size    int    `json:"size" validate:"required"`                                       // 每页大小
	Sort    string `json:"sort" validate:"required"`                                       // 排序字段 默认id
	Order   string `json:"order" validate:"required,ascii,lowercase=ascii,oneof=asc desc"` // 排序规则 默认正序 asc-正序 desc-倒序
}

func (v SuperQueryVO[E]) GetCurrent() int {
	return v.Current
}

func (v SuperQueryVO[E]) GetSize() int {
	return v.Size
}

func (v SuperQueryVO[E]) GetOrderBy() string {
	sortField := v.Sort
	if len(sortField) == 0 {
		var e E
		sortField = e.TableName() + ".id"
	}
	if len(v.Order) == 0 {
		v.Order = "asc"
	}
	return sortField + " " + v.Order
}

func (v SuperQueryVO[E]) GetExportType() int {
	return 0
}

// ResultVO
type ResultVO interface {
	FromEntity(entity any) (any, error)
}

type SuperResultVO[ResultVO any] struct {
}

func (v SuperResultVO[ResultVO]) FromEntity(entity any) (any, error) {
	return converter.Convert[ResultVO](entity)
}

func convertToEntity[T any](vo any) (T, error) {
	entity, err := converter.DeepConvert[T](vo)
	if err != nil {
		return entity, err
	}
	return entity, nil
}

// IdVO id请求参数
type IdVO[T po.Id] struct {
	Id T `json:"id" validate:"required"` // id主键
}
