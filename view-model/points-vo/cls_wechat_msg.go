package points_vo

type SendSubscribeMessageReq struct {
	//访问密钥
	AccessKey string `json:"access_key" validate:"required"`
	//用户id
	ScrmUserId string `json:"scrm_user_id" validate:"required"`
	//订阅类型：user_level会员等级通知、integral积分通知、voucher优惠券通知、register预约挂号通知、queuing医院挂号排队通知
	SubscribeType string `json:"subscribe_type" validate:"required"`
	//微信消息key：register-confirm预约确认提醒、register-tobe-confirm预约待确认通知、register-store-remind预约到店提醒、register-queuing预约队列
	TemplateKey string `json:"template_key" validate:"required"`
	//时间戳，秒
	Timestamp string `json:"timestamp" validate:"required"`
	//签名:md5(ScrmUserId+SubscribeType+TemplateKey+AccessKey+Timestamp)
	Sign string `json:"sign" validate:"required"`
	//通知信息参数 []struct{type string, value string}，如：[{"type":"string","value":"aa"},{"type":"int","value":"11"},,{"type":"float","value":"10.2"}]
	Values []*MessageValue `json:"values"`
	//小程序页面跳转参数
	PageParams string `json:"page_params"`
}

type MessageValue struct {
	Type  string `json:"type"`
	Value string `json:"value"`
}
