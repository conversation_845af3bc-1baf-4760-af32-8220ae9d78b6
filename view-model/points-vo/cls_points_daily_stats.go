package points_vo

import (
	po "eShop/domain/points-po"
	task_type "eShop/infra/enum"
	"time"
)

// ClsPointsDailyStatsSaveVO 用于创建积分每日统计的视图对象
type ClsPointsDailyStatsSaveVO struct {
	SuperSaveVO[po.ClsPointsDailyStats]
	StatDate     time.Time `json:"stat_date" validate:"required"`
	IssueTotal   int       `json:"issue_total" validate:"min=0"`
	IssueBlky    int       `json:"issue_blky" validate:"min=0"`
	IssueSzld    int       `json:"issue_szld" validate:"min=0"`
	ConsumeTotal int       `json:"consume_total" validate:"min=0"`
	ConsumeBlky  int       `json:"consume_blky" validate:"min=0"`
	ConsumeSzld  int       `json:"consume_szld" validate:"min=0"`
	ExpiredTotal int       `json:"expired_total" validate:"min=0"`
	ExpiredBlky  int       `json:"expired_blky" validate:"min=0"`
	ExpiredSzld  int       `json:"expired_szld" validate:"min=0"`
}

// ClsPointsDailyStatsUpdateVO 用于更新积分每日统计的视图对象
type ClsPointsDailyStatsUpdateVO struct {
	SuperUpdateVO[po.ClsPointsDailyStats]
}

// ClsPointsDailyStatsQueryVO 用于查询积分每日统计的视图对象
type ClsPointsDailyStatsQueryVO struct {
	SuperQueryVO[po.ClsPointsDailyStats]
	StatDate  string `json:"stat_date" query:"stat_date:eq"`
	StartTime string `json:"start_time" query:"stat_date:gte"`
	EndTime   string `json:"end_time" query:"stat_date:lte"`
}

func (v ClsPointsDailyStatsQueryVO) GetExportType() int {
	return task_type.ClsPointsDailyStatExport
}

// ClsPointsDailyStatsResultVO 用于返回积分每日统计结果的视图对象
type ClsPointsDailyStatsResultVO struct {
	SuperResultVO[ClsPointsDailyStatsResultVO]
	Id           int    `json:"id"`
	StatDate     string `json:"stat_date"`
	IssueTotal   int    `json:"issue_total"`
	IssueBlky    int    `json:"issue_blky"`
	IssueSzld    int    `json:"issue_szld"`
	ConsumeTotal int    `json:"consume_total"`
	ConsumeBlky  int    `json:"consume_blky"`
	ConsumeSzld  int    `json:"consume_szld"`
	ExpiredTotal int    `json:"expired_total"`
	ExpiredBlky  int    `json:"expired_blky"`
	ExpiredSzld  int    `json:"expired_szld"`
}

// ClsPointsStatVO 用于返回积分统计数据的视图对象
type ClsPointsStatVO struct {
	TotalIssuePoints   int `json:"total_issue_points"`   // 累计发放总额
	TotalConsumePoints int `json:"total_consume_points"` // 累计消耗总额
	TotalExpiredPoints int `json:"total_expired_points"` // 累计失效总额
	TodayIssuePoints   int `json:"today_issue_points"`   // 今日发放总数
	TodayConsumePoints int `json:"today_consume_points"` // 今日消耗总数
}
