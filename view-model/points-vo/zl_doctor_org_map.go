package points_vo

import (
	po "eShop/domain/points-po"
)

// ZlDoctorOrgMapQueryVO 医生组织映射查询VO
type ZlDoctorOrgMapQueryVO struct {
	SuperQueryVO[po.ZlDoctorOrgMap]
	UserId            string `json:"user_id" query:"user_id:eq"`                           // 员工id
	CoSocialReditCode string `json:"co_social_redit_code" query:"co_social_redit_code:eq"` // 统一社会信用代码
}

// ClsWorkerDistributorAddReq 创建店员分销员请求
type ClsWorkerDistributorAddReq struct {
	OrgId            int64  `json:"org_id"`             // 组织ID
	MemberId         int64  `json:"member_id"`          // 会员ID
	Name             string `json:"name"`               // 名称
	RealName         string `json:"real_name"`          // 真实姓名
	Mobile           string `json:"mobile"`             // 手机号
	HeadImage        string `json:"head_image"`         // 头像
	SocialCodeImage  string `json:"social_code_image"`  // 社会信用代码图片
	SocialCreditCode string `json:"social_credit_code"` // 社会信用代码

	// 企业相关信息
	EnterpriseId   int64  `json:"enterprise_id"`   // 企业ID
	EnterpriseName string `json:"enterprise_name"` // 企业名称
	Province       string `json:"province"`        // 省份
	City           string `json:"city"`            // 城市
	District       string `json:"district"`        // 区域
	Address        string `json:"address"`         // 地址

	// 详情相关信息
	HospitalName string `json:"hospital_name"` // 医院名称
	Professional string `json:"professional"`  // 职业
	Specialize   string `json:"specialize"`    // 专业
}
