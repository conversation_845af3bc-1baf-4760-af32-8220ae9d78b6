package points_vo

import (
	po "eShop/domain/points-po"
)

type ClsDisAddrSaveVO struct {
	SuperSaveVO[po.ClsDisAddr]
	DisId        int    `json:"dis_id" validate:"required"`
	Province     string `json:"province" validate:"required"`
	City         string `json:"city" validate:"required"`
	District     string `json:"district" validate:"required"`
	Ress         string `json:"ress" validate:"required"`
	Name         string `json:"name" validate:"required"`
	Phone        string `json:"phone" validate:"required"`
	EncryptPhone string `json:"encrypt_phone"` // 加密后的手机号
}

type ClsDisAddrUpdateVO struct {
	SuperUpdateVO[po.ClsDisAddr]
	Id        int    `json:"id" validate:"required"`
	DisId     int    `json:"dis_id"`
	Province  string `json:"province"`
	City      string `json:"city"`
	District  string `json:"district"`
	Ress      string `json:"ress"`
	Name      string `json:"name"`
	Phone     string `json:"phone"`
	IsDefault int    `json:"is_default"`
}

type ClsDisAddrQueryVO struct {
	SuperQueryVO[po.ClsDisAddr]
	DisId int `json:"dis_id" query:"eq"`
}

type ClsDisAddrResultVO struct {
	SuperResultVO[ClsDisAddrResultVO]
	Id        int    `json:"id"`
	DisId     int    `json:"dis_id"`
	Province  string `json:"province"`
	City      string `json:"city"`
	District  string `json:"district"`
	Ress      string `json:"ress"`
	Name      string `json:"name"`
	Phone     string `json:"phone"`
	IsDefault int    `json:"is_default"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}
