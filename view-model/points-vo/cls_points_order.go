package points_vo

import (
	po "eShop/domain/points-po"
	task_type "eShop/infra/enum"
)

// ClsPointsOrderSaveVO 用于创建积分订单的视图对象
type ClsPointsOrderSaveVO struct {
	SuperSaveVO[po.ClsPointsOrder]     // 嵌入通用的保存VO
	DisId                          int `json:"dis_id" validate:"required"`
	GoodsId                        int `json:"goods_id" validate:"required"`
	Quantity                       int `json:"quantity" validate:"required,min=1"`
	AddressId                      int `json:"address_id"`
	PaymentMethod                  int `json:"payment_method" validate:"required"` // 默认1-积分
	OrderSource                    int `json:"order_source" validate:"required"`   // 默认1-宠利扫小程序
	OrderType                      int `json:"order_type" validate:"required"`     // 默认1-积分兑换
}

// ClsPointsOrderUpdateVO 用于更新积分订单的视图对象
type ClsPointsOrderUpdateVO struct {
	SuperUpdateVO[po.ClsPointsOrder]        // 嵌入通用的更新VO
	Id                               int    `json:"id" validate:"required"`
	Remark                           string `json:"remark"`               // 订单备注
	ExpressCompanyCode               string `json:"express_company_code"` // 快递公司编码
	ExpressCompany                   string `json:"express_company"`      // 快递公司名称
	ExpressNo                        string `json:"express_no"`           // 快递单号
	Status                           int    `json:"status"`               // 订单状态：1-待发货，2-已发货，3-已完成
}

// ClsPointsOrderQueryVO 用于查询积分订单的视图对象
type ClsPointsOrderQueryVO struct {
	SuperQueryVO[po.ClsPointsOrder]        // 嵌入通用的查询VO
	DisId                           int    `json:"dis_id" query:"cls_points_order.dis_id:eq"`
	OrderNo                         string `json:"order_no" query:"cls_points_order.order_no:eq"`
	Phone                           string `json:"phone" query:"dis_distributor.encrypt_mobile:like"`            // 手机号
	GoodsName                       string `json:"goods_name" query:"cls_points_order.goods_name:like"`          // 商品名称
	GoodsType                       int    `json:"goods_type" query:"cls_points_order.goods_type:eq"`            // 商品类型: 1-实物商品，2-虚拟商品
	Status                          int    `json:"status" query:"cls_points_order.status:eq"`                    // 订单状态：1-待发货，2-已发货，3-已完成
	OrderTimeStart                  string `json:"order_time_start" query:"cls_points_order.order_time:gte"`     // 兑换时间开始
	OrderTimeEnd                    string `json:"order_time_end" query:"cls_points_order.order_time:lte"`       // 兑换时间结束
	OrderSource                     int    `json:"order_source" query:"cls_points_order.order_source:eq"`        // 订单来源: 1-宠利扫小程序
	ExpressTime                     string `json:"express_time"`                                                 // 发货时间
	ExpressTimeStart                string `json:"express_time_start" query:"cls_points_order.express_time:gte"` // 发货时间开始
	ExpressTimeEnd                  string `json:"express_time_end" query:"cls_points_order.express_time:lte"`   // 发货时间结束
	GoodsId                         int    `json:"goods_id" query:"cls_points_order.goods_id:eq"`
}

func (v ClsPointsOrderQueryVO) GetExportType() int {
	return task_type.ClsPointOrderExport
}

type ClsPointsOrderResultVO struct {
	SuperResultVO[ClsPointsOrderResultVO] `xorm:"extends"`
	Id                                    int    `json:"id"`
	OrderNo                               string `json:"order_no"` // 订单号，唯一
	DisId                                 int    `json:"dis_id"`
	GoodsId                               int    `json:"goods_id"`
	GoodsName                             string `json:"goods_name"`
	GoodsType                             int    `json:"goods_type"`   // 商品类型: 1-实物商品，2-虚拟商品
	MarketPrice                           int    `json:"market_price"` // 商品市场价
	PointsPrice                           int    `json:"points_price"` // 商品积分价
	ImageUrl                              string `json:"image_url"`    // 商品图片URL
	PointsCost                            int    `json:"points_cost"`
	Quantity                              int    `json:"quantity"`
	Status                                int    `json:"status"` // 订单状态：1-待发货，2-已发货，3-已完成
	AddressId                             int    `json:"address_id"`
	AddrName                              string `json:"addr_name"`
	AddrPhone                             string `json:"addr_phone"`
	AddrEncryptPhone                      string `json:"addr_encrypt_phone"` // 加密后的手机号
	Province                              string `json:"province"`
	City                                  string `json:"city"`
	District                              string `json:"district"`
	Ress                                  string `json:"ress"` // 收货地址
	Remark                                string `json:"remark"`
	ExpressCompany                        string `json:"express_company"`
	ExpressNo                             string `json:"express_no"`
	ExpressTime                           string `json:"express_time"`
	PaymentMethod                         int    `json:"payment_method"`
	OrderTime                             string `json:"order_time"`
	OrderSource                           int    `json:"order_source"`  // 订单来源: 1-宠利扫小程序
	OrderType                             int    `json:"order_type"`    // 订单类型:1-积分兑换
	CompleteTime                          string `json:"complete_time"` // 完成时间
	MemberId                              int    `json:"member_id"`
	MemberName                            string `json:"member_name"`
	MemberMobile                          string `json:"member_mobile"`
	MemberEncryptMobile                   string `json:"member_encrypt_mobile"`
}
