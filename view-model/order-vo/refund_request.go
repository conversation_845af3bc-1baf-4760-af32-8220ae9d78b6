package order_vo

// RefundRequest 退款请求参数
type RefundRequest struct {
	// 订单号
	OrderSn string `json:"order_sn"`
	// 退回方式 1:原路退回 2:指定退回
	RefundType int `json:"refund_type"`
	// 指定退回时，选择退款方式: 1-自有微信 2-自有支付宝 3-现金 4-其它 5-次卡
	RefundMethod int `json:"refund_method"`
	// 退款原因
	RefundReason string `json:"refund_reason"`
	// 备注
	Remark string `json:"remark"`
	// 退款类型 1:全部退款 2:部分退款
	Type int `json:"type"`
	// 退货类型 1为仅退款,2为退款退货'
	ReType int `json:"re_type"`
	// 退款商品信息,部分退款时必填
	Products []*RefundProduct `json:"products"`
	// 退卡卡号
	CardNo string `json:"card_no"`
	// 是否退卡 储值卡-100 次卡-102
	IsRefundCard int `json:"is_refund_card"`
	// 退卡金额（分）
	RefundCardAmount int `json:"refund_card_amount"`
	// 收银员
	Operator string `json:"operator"`
}

type RefundProduct struct {
	// 订单商品ID
	OrderProductId int `json:"order_product_id"`
	// 退款数量
	RefundNum int `json:"refund_num"`
	// 退款金额(分)
	RefundAmount int `json:"refund_amount"`
}

// ProcessReturnRequest 处理退货请求
type ProcessReturnRequest struct {
	// 退款单号
	RefundSn string `json:"refund_sn" binding:"required"`
	// 退款商品信息
	Products []*ProcessReturnProduct `json:"products" binding:"required"`
	// 备注
	Remark string `json:"remark"`
}

type ProcessReturnProduct struct {
	// 退款商品记录ID
	Id int `json:"id" binding:"required"`
	// 实际退回数量
	ActualReturnNum int `json:"actual_return_num" binding:"gte=0"`
	// 备注
	Remark string `json:"remark"`
}

// RefundReturnListRequest 退货记录列表请求参数
type RefundReturnListRequest struct {
	// 页码
	Page int `json:"page"`
	// 每页数量
	PageSize int `json:"page_size"`
	// 状态 1待处理 2已处理
	Status int `json:"status"`
	// 订单编号
	OrderSn string `json:"order_sn"`
	// 店铺ID
	ShopId string `json:"shop_id"`
	// 退款单号
	RefundSn string `json:"refund_sn"`
	// 手机号
	Mobile string `json:"mobile"`
}

// RefundReturnListResponse 退货记录列表响应
type RefundReturnListResponse struct {
	// 总数
	Total int64 `json:"total"`
	// 列表数据
	List []*RefundReturnListItem `json:"list"`
}

// RefundReturnListItem 退货记录列表项
type RefundReturnListItem struct {
	// 退款单号
	RefundSn string `json:"refund_sn"` // 退款单号
	// 订单编号
	OrderSn string `json:"order_sn"` // 订单编号
	// 是否已处理 0-未处理 1-已处理
	IsPicking int `json:"is_picking"` // 是否已处理
	// 操作人名称
	Operator string `json:"operator"` // 操作人
	// 退款金额,单位元
	RefundAmount float64 `json:"refund_amount"` // 退款金额
	// 创建时间,格式:2006-01-02 15:04:05
	CreateTime string `json:"create_time"` // 创建时间
	// 计划退回的数量
	TotalQuantity int `json:"total_quantity"` // 计划退回的数量
	// 实际退款数量
	TotalRefundCount int `json:"total_refund_count"` // 总退款数量
	// 退款商品列表
	Products []*RefundReturnProductItem `json:"products"` // 商品列表
	// 取货码
	PickupCode string `json:"pickup_code"`
	//处理时间
	ProcessTime string `json:"process_time"`
	// 渠道
	ChannelId int `json:"channel_id"`
	// 仓库id
	WarehouseId int `json:"warehouse_id"`
	// 仓库名称
	WarehouseName string `json:"warehouse_name"`
}

// RefundReturnProductItem 退货商品项
type RefundReturnProductItem struct {
	// 退款商品id
	Id int `json:"id"`
	// 商品名称
	ProductName string `json:"product_name"`
	// 退货数量
	Quantity int `json:"quantity"`
	// 实际退回数量
	ActualReturnNum int `json:"actual_return_num"`
	// 处理时间
	ProcessTime string `json:"process_time"`
	// 备注
	Remark string `json:"remark"`
	// 规格
	Specs string `json:"specs"`
	// 库位
	LocationCode string `json:"location_code"`
	// 商品原优惠单价
	ProductPrice int `json:"product_price"`
	// 商品原价
	MarkingPrice int `json:"marking_price"`
	// 条形码
	Barcode string `json:"bar_code"`
	// 图片
	Image string `json:"image"`
}
