package order_vo

type GetDeliveryMoneyRequest struct {
	//用户所属店铺ID
	ShopId string `protobuf:"bytes,1,opt,name=shopId,proto3" json:"shopId"`
	//目的地坐标X 不传递或传递0，不计算运费
	DestinationX float64 `protobuf:"fixed64,3,opt,name=destinationX,proto3" json:"destinationX"`
	//目的地坐标Y 不传递或传递0，不计算运费
	DestinationY float64 `protobuf:"fixed64,4,opt,name=destinationY,proto3" json:"destinationY"`
	// 重量
	TotalWeight float64 `protobuf:"fixed64,6,opt,name=totalWeight,proto3" json:"totalWeight"`
}

type PromotionCalcResponse struct {
	// 代码 非 200 取 message 错误信息
	Code int32 `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 总运费金额,以分为单位
	UpetDjMoneyByMinUnit int32 `protobuf:"varint,7,opt,name=upetDjMoneyByMinUnit,proto3" json:"upetDjMoneyByMinUnit"`
}
