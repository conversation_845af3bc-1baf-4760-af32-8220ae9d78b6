package distribution_vo

// DistributorSwlmCpsListRes 扣除佣金订单列表响应
type DistributorSwlmCpsListRes struct {
	// 主键id
	SwlmCpsId int `json:"swlm_cps_id"`
	// 分销员会员id
	DisMemberId int `json:"dis_member_id"`
	// 物流码
	Swlm string `json:"swlm"`
	// 订单编号
	OrderSn string `json:"order_sn"`
	// 扣减佣金金额(单位分)
	Amount int `json:"amount"`
	// 扣减佣金状态：0-默认, 1-未扣款，2-已扣款,3-扣款中
	Status int `json:"status"`
	// 是否自平账
	IsSelfBalance int `json:"is_self_balance"`
	// 主体id
	OrgId int `json:"org_id"`
	// 店铺id
	ShopId int `json:"shop_id"`
	// 商品id
	GoodsId int `json:"goods_id"`
	// 商品名称
	GoodsName string `json:"goods_name"`
	//码包所属企业：1北京百林康源 2-深圳利都
	OrgType int `json:"org_type"`
	// 下单时间
	AddTime string `json:"add_time"`
	// 分销员id
	DisId int `json:"dis_id"`
}
