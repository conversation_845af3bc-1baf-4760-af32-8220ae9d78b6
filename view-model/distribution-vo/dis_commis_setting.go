package distribution_vo

import (
	viewmodel "eShop/view-model"
)

type GetDisCommisSettingPageListReq struct {
	viewmodel.BasePageHttpRequest
	OrgId       int    `json:"org_id"`
	ChannelName string `json:"channel_name"`
}

type DisCommisSettingInfo struct {
	Id                int    `json:"id"`
	CustomerChannelId int    `json:"customer_channel_id"`
	ChannelPath       string `json:"channel_path"`
	// 分销佣金比例(示例：佣金比例是5%，这里存5)
	DisCommisRate float64 `json:"dis_commis_rate" xorm:"not null default 0.00 comment('分销佣金比例(示例：佣金比例是5%，这里存5)') DECIMAL(4,2)"`
	// 佣金设置：1默认设置，0自定义佣金比例
	IsDefault int `json:"is_default" xorm:"not null default 0 comment('佣金设置：1默认设置，0自定义佣金比例') INT"`
	// 更新时间
	UpdateTime string `json:"update_time" xorm:"default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment('更新时间') DATETIME updated"`
}

type GetDisCommisSettingPageListResp struct {
	viewmodel.BasePageHttpResponse
	Data []DisCommisSettingInfo `json:"data"`
}

type AddDisCommisSettingReq struct {
	// 客户渠道id
	ChannelCustomerId int `json:"channel_customer_id" validate:"required" label:"客户渠道id"`
	// 分销佣金比例
	DisCommisRate float64 `json:"dis_commis_rate"`
	// 佣金设置：1默认设置，0自定义佣金比例
	IsDefault int `json:"is_default"`
	// 主体id
	OrgId int `json:"org_id"`
}

type AddDisCommisSettingResp struct {
	viewmodel.BaseHttpResponse
	Data AddDisCommisSettingData `json:"data"`
}

type AddDisCommisSettingData struct {
	// 佣金设置id
	DisCommisSettingId int `json:"dis_commis_setting_id"`
	// 客户渠道路径
	ChannelPath string `json:"channel_path"`
	// 分销佣金比例
	DisCommisRate float64 `json:"dis_commis_rate"`
	// 佣金设置：1默认设置，0自定义佣金比例
	IsDefault int `json:"is_default"`
}

type EditDisCommisSettingReq struct {
	// 佣金设置id
	DisCommisSettingId int `json:"dis_commis_setting_id" validate:"required" label:"佣金设置id"`
	// 分销佣金比例
	DisCommisRate float64 `json:"dis_commis_rate" label:"分销佣金比例"`
	// 佣金设置：1默认设置，0自定义佣金比例
	IsDefault int `json:"is_default"`
	// 主体id
	OrgId int `json:"org_id"`
}

type EditDisCommisSettingResp struct {
	viewmodel.BaseHttpResponse
	Data EditDisCommisSettingData `json:"data"`
}

type DeleteDisCommisSettingReq struct {
	// 佣金设置id
	DisCommisSettingId int `json:"dis_commis_setting_id" validate:"required" label:"佣金设置id"`
	// 主体id
	OrgId int `json:"org_id"`
}
type DeleteDisCommisSettingResp struct {
	viewmodel.BaseHttpResponse
}
type EditDisCommisSettingData struct {
	// 佣金设置id
	DisCommisSettingId int `json:"dis_commis_setting_id"`
	// 客户渠道路径
	ChannelPath string `json:"channel_path"`
	// 分销佣金比例
	DisCommisRate float64 `json:"dis_commis_rate"`
	// 佣金设置：1默认设置，0自定义佣金比例
	IsDefault int `json:"is_default"`
	// 旧的分销佣金比例
	OldDisCommisRate float64 `json:"old_dis_commis_rate"`
	// 旧的佣金设置：1默认设置，0自定义佣金比例
	OldIsDefault int `json:"old_is_default"`
}

type CustomerChannelGlobalSettingReq struct {
	// 主体id
	OrgId int `json:"org_id"`
	// 佣金比例
	DisCommisRate float64 `json:"dis_commis_rate" label:"佣金比例"`
}
type CustomerChannelGlobalSettingResp struct {
	viewmodel.BaseHttpResponse
}

type GetCustomerChannelListReq struct {
	OrgId int `json:"org_id"`
}

type GetCustomerChannelListResp struct {
	viewmodel.BaseHttpResponse
	Data []CustomerChannelListData `json:"data"`
}

type CustomerChannelListData struct {
	// 自增id
	Id int `json:"id"`
	// 渠道层级：1为一级渠道,2为二级渠道
	Level int `json:"level"`
	// 企业销售渠道类型
	ChannelType int `json:"channel_type"`
	// 企业销售渠道类型名称
	ChannelName string `json:"channel_name"`
	// 渠道路径
	ChannelPath string `json:"channel_path"`
	// 父节点
	Parent int `json:"parent"`
	// 创建时间
	CreateTime string `json:"create_time"`
	// 更新时间
	UpdateTime string `json:"update_time"`
	// 子节点
	Children []CustomerChannelListData `json:"children" xorm:"-"`
}
type GetCustomerChannelGlobalSettingResp struct {
	viewmodel.BaseHttpResponse
	Data string `json:"data"`
}
