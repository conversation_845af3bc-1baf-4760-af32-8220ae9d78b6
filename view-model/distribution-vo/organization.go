package distribution_vo

import (
	"eShop/view-model"
)

type OrgPageReq struct {
	//状态 0：未启用 1：启用
	IsUse int `json:"is_use"`
	//组织名称
	OrgName string `json:"org_name"`

	viewmodel.BasePageHttpRequest
}

type OrgPageResp struct {
	viewmodel.BasePageHttpResponse
	Data []OrgPageData `json:"data"`
}

type OrgPageData struct {
	//组织id
	Id int `json:"id"`
	//组织名称
	OrgName string `json:"org_name"`
	//ACP编码
	ZlOrgId string `json:"zl_org_id"`
	//电商店铺id
	DsOrgId string `json:"ds_org_id"`
	//创建时间
	CreateTime string `json:"create_time"`
	//修改时间
	UpdateTime string `json:"update_time"`
	//状态 0：未启用 1：启用
	IsUse int `json:"is_use"`
}

type OrgAddReq struct {
	//组织名称
	OrgName string `json:"org_name" validate:"add:required;upd:required"`
	//ACP编码
	ZlOrgId string `json:"zl_org_id" validate:"add:required;upd:required"`
	//电商店铺id
	DsOrgId string `json:"ds_org_id"`
	//小程序appid
	WxAppid string `json:"wx_appid"`
	//小程序实物电银商户号
	MiniProgramMchId string `json:"mini_program_mch_id"`
	//小程序虚拟电银商户号
	MiniVProgramMchId string `json:"mini_v_program_mch_id"`
	//App实物商户号
	AppMchId string `json:"app_mch_id"`
	//App虚拟商户号
	AppVMchId string `json:"app_v_mch_id"`
}

type OrgEditReq struct {
	//组织id
	Id int `json:"id"  validate:"upd:required"`
	OrgAddReq
}

type OrgIsUseReq struct {
	//组织id
	Id int `json:"id"  validate:"required,min=1"`
	//启用状态:0-未启用 1-启用(默认-1)
	IsUse int `json:"is_use" validate:"min=0,max=1"`
}

type OrgDetailResp struct {
	Data OrgDetailData `json:"data"`
	viewmodel.BaseHttpResponse
}

type OrgDetailData struct {
	//组织id
	Id int `json:"id"`
	//组织名称
	OrgName string `json:"org_name"`
	//ACP编码
	ZlOrgId string `json:"zl_org_id"`
	//电商店铺id
	DsOrgId string `json:"ds_org_id"`
	//小程序appid
	WxAppid string `json:"wx_appid"`
	//小程序实物电银商户号
	MiniProgramMchId string `json:"mini_program_mch_id"`
	//小程序虚拟电银商户号
	MiniVProgramMchId string `json:"mini_v_program_mch_id"`
	//App实物商户号
	AppMchId string `json:"app_mch_id"`
	//App虚拟商户号
	AppVMchId string `json:"app_v_mch_id"`
}
