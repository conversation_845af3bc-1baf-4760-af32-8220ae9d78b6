package distribution_vo

import viewmodel "eShop/view-model"

type OperateLogPageReq struct {
	//业务模块标识：1-分销员，2-业务员，3-分销商品 4-线下企业 5-物流码 6-客户渠道佣金设置
	ModuleType int `json:"module_type"`
	//业务数据的id
	FromId string `json:"from_id"`
	//是否导出 1-是
	IsExport int `json:"is_export"`
	viewmodel.BasePageHttpRequest
}

type OperateLogPageResp struct {
	Data []OperateLogPageData `json:"data"`

	viewmodel.BasePageHttpResponse
}

type OperateLogPageData struct {
	//主键id
	Id int `json:"id"`
	//操作时间
	CreateTime string `json:"create_time"`
	//操作类型: module_type=5(1-查询物流码 2-扣除佣金 3-恢复物流码)
	Type int `json:"type"`
	//操作详情
	Description string `json:"description"`
	//操作人
	UserName string `json:"user_name"`
	//业务模块标识 业务模块标识:1-分销员，2-业务员，3-分销商品 4-线下企业 5-物流码
	ModuleType int `json:"module_type"`
}

type OperateLogReq struct {
	ModuleType  int    `json:"module_type"`
	Type        int    `json:"type"`
	FromId      string `json:"from_id"`
	Description string `json:"description"`
}
