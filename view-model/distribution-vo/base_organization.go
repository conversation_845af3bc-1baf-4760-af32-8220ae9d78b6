package distribution_vo

import (
	"eShop/view-model"
	"time"
)

type BaseOrganization struct {
	Id                       uint      `json:"id" xorm:"pk autoincr not null comment('ID') INT 'id'"`
	TenantNo                 int       `json:"tenant_no" xorm:"not null default -1 comment('租户编码（属于哪个租户）') INT 'tenant_no'"`
	Code                     string    `json:"code" xorm:"not null default '' comment('租户机构编码') VARCHAR(50) 'code'"`
	Name                     string    `json:"name" xorm:"not null default '' comment('租户机构名称') VARCHAR(50) 'name'"`
	Alias                    string    `json:"alias" xorm:"not null default '' comment('租户机构助记码（别名）') VARCHAR(50) 'alias'"`
	AreaId                   int       `json:"area_id" xorm:"not null default -1 comment('租户机构区域id') INT 'area_id'"`
	ClassificationId         int       `json:"classification_id" xorm:"not null default -1 comment('机构分类id') INT 'classification_id'"`
	Type                     int       `json:"type" xorm:"not null default -1 comment('机构属性（类别）分为【0：总部】【1：分支机构】，【2：加盟门店】，:3：直营门店】，:4：仓库】【5：二级机构】【6：三级机构】') INT 'type'"`
	PropertyId               int       `json:"property_id" xorm:"not null default -1 comment('机构属性') INT 'property_id'"`
	ParentId                 int       `json:"parent_id" xorm:"not null default -1 comment('父级机构id') INT 'parent_id'"`
	ContactName              string    `json:"contact_name" xorm:"not null default '' comment('联系人姓名') VARCHAR(50) 'contact_name'"`
	ContactPhone             string    `json:"contact_phone" xorm:"not null default '' comment('联系人电话') VARCHAR(20) 'contact_phone'"`
	Address                  string    `json:"address" xorm:"not null default '' comment('机构地址') VARCHAR(50) 'address'"`
	DeliveryCycle            string    `json:"delivery_cycle" xorm:"not null default '' comment('配送周期（当机构属性选择仓库的时候存在）') VARCHAR(50) 'delivery_cycle'"`
	DeliveryDays             string    `json:"delivery_days" xorm:"not null default '' comment('送货天数（当机构属性选择仓库的时候存在）') VARCHAR(50) 'delivery_days'"`
	LevelId                  int       `json:"level_id" xorm:"not null default -1 comment('机构等级id（当机构属性选择直营门店的时候存在）') INT 'level_id'"`
	DeliveryWarehouseId      int       `json:"delivery_warehouse_id" xorm:"not null default -1 comment('配送仓库id(当选择为直营门店的时候必选)') INT 'delivery_warehouse_id'"`
	FloatRatio               string    `json:"float_ratio" xorm:"not null default '' comment('门店进货价浮动比例 产品说不允许小数 0-10,0表示没有浮动；10表示上浮100%（当机构属性选择直营门店的时候存在）') VARCHAR(10) 'float_ratio'"`
	NcCode                   string    `json:"nc_code" xorm:"not null default '' comment('NC编码') VARCHAR(50) 'nc_code'"`
	CreateAccount            string    `json:"create_account" xorm:"not null default '' comment('创建人账号') VARCHAR(50) 'create_account'"`
	ModifyAccount            string    `json:"modify_account" xorm:"not null default '' comment('更新人账号') VARCHAR(50) 'modify_account'"`
	CreateTime               time.Time `json:"create_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime               time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
	HuilianyiPushStatus      int8      `json:"huilianyi_push_status" xorm:"not null default 0 comment('启用汇联易推送:0禁用|1启用') TINYINT(1) 'huilianyi_push_status'"`
	MiddlePlatformPushStatus int8      `json:"middle_platform_push_status" xorm:"not null default 0 comment('是否线上开票（推送中台开票）: 0 关 / 1 开') TINYINT(1) 'middle_platform_push_status'"`
	RuipengOmsSyncFlag       int8      `json:"ruipeng_oms_sync_flag" xorm:"not null default 0 comment('新瑞鹏自研OMS同步标识( 0 未同步 / 1 已同步 / 2 同步失败 / 3 更新未同步 / 4更新同步失败 )') TINYINT 'ruipeng_oms_sync_flag'"`
	LogisticsFeeIssueStatus  int8      `json:"logistics_fee_issue_status" xorm:"not null default 0 comment('是否有开物流费发票资质: 0 关 / 1 开') TINYINT(1) 'logistics_fee_issue_status'"`
	ParentType               int       `json:"parent_type" xorm:"not null default 1 comment('上级机构类型:1-润合,2-宜嘉(注:总部为0)') INT 'parent_type'"`
	TaxId                    string    `json:"tax_id" xorm:"default 'null' comment('税号') VARCHAR(50) 'tax_id'"`
	AccountName              string    `json:"account_name" xorm:"default 'null' comment('账户名称') VARCHAR(200) 'account_name'"`
	AccountBank              string    `json:"account_bank" xorm:"default 'null' comment('开户银行') VARCHAR(200) 'account_bank'"`
	AccountNumber            string    `json:"account_number" xorm:"default 'null' comment('开户账号') VARCHAR(200) 'account_number'"`
	CreateName               string    `json:"create_name" xorm:"default 'null' comment('创建人') VARCHAR(50) 'create_name'"`
	ModifyName               string    `json:"modify_name" xorm:"default 'null' comment('更新人') VARCHAR(50) 'modify_name'"`
	OrganizationType         int       `json:"organization_type" xorm:"default 'null' comment('机构类型(0:润合贸易 1：润合电商2：自有品牌3:宜嘉总部4：安加商)') INT 'organization_type'"`
}

type BaseOrganizationRes struct {
	viewmodel.BaseHttpResponse
	//业务员列表数据
	Data []BaseOrganization `json:"data"`
}
