package distribution_vo

import (
	"eShop/view-model"
	"fmt"
)

type StatsSalespersonDaily struct {
	Id int `json:"id" xorm:"pk autoincr not null comment('主键') BIGINT 'id'"`
	// 日期
	StatDate string `json:"stat_date" xorm:"not null comment('日期') DATE 'stat_date'"`
	// 业务员ID
	SalesmanId int `json:"salesman_id" xorm:"not null comment('业务员ID') BIGINT 'salesman_id'"`
	// 业务员姓名
	SalesmanName string `json:"salesman_name" xorm:"not null default '' comment('业务员姓名') VARCHAR(100) 'salesman_name'"`
	// 所属组织
	Organization string `json:"organization" xorm:"not null comment('所属组织') VARCHAR(100) 'organization'"`
	// 服务企业数
	ServiceEnterpriseCount int `json:"service_enterprise_count" xorm:"not null default 0 comment('服务企业数') INT 'service_enterprise_count'"`
	// 分销店铺数
	ShopCount int `json:"shop_count" xorm:"not null default 0 comment('分销店铺数') INT 'shop_count'"`
	// 服务分销员数
	ServiceDistributorCount int `json:"service_distributor_count" xorm:"not null default 0 comment('服务分销员数') INT 'service_distributor_count'"`
	// 老板数
	BossCount int `json:"boss_count" xorm:"not null default 0 comment('老板数') INT 'boss_count'"`
	// 拓客分销员数
	CustomerCount int `json:"customer_count" xorm:"not null default 0 comment('拓客分销员数') INT 'customer_count'"`
	// 成交企业数
	TransEnterpriseCount int `json:"trans_enterprise_count" xorm:"not null default 0 comment('成交企业数') INT 'trans_enterprise_count'"`
	// 分销成交单数
	TransCount int `json:"trans_count" xorm:"not null default 0 comment('分销成交单数') INT 'trans_count'"`
	// 分销成交金额(分)
	TransAmount int `json:"trans_amount" xorm:"not null default 0 comment('分销成交金额(分)') INT 'trans_amount'"`
	// 分销佣金(分)
	Commission int `json:"commission" xorm:"not null default 0 comment('分销佣金(分)') INT 'commission'"`
	// 分销商品成交单数
	ProductTransCount int `json:"product_trans_count" xorm:"not null default 0 comment('分销商品成交单数') INT 'product_trans_count'"`
	// 分销商品成交金额(分)
	ProductTransAmount int `json:"product_trans_amount" xorm:"not null default 0 comment('分销商品成交金额(分)') INT 'product_trans_amount'"`
	// 分销商品佣金(分)
	ProductCommission int `json:"product_commission" xorm:"not null default 0 comment('分销商品佣金(分)') INT 'product_commission'"`
	// 分销保险成交单数
	InsuranceTransCount int `json:"insurance_trans_count" xorm:"not null default 0 comment('分销保险成交单数') INT 'insurance_trans_count'"`
	// 分销保险成交金额(分)
	InsuranceTransAmount int `json:"insurance_trans_amount" xorm:"not null default 0 comment('分销保险成交金额(分)') INT 'insurance_trans_amount'"`
	// 分销保险佣金(分)
	InsuranceCommission int `json:"insurance_commission" xorm:"not null default 0 comment('分销保险佣金(分)') INT 'insurance_commission'"`
	// 创建时间
	CreateTime string `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	// 更新时间
	UpdateTime string `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
}

type StatsSalespersonPageReq struct {
	// 业务员
	SalesmanName string `json:"salesman_name"`
	// 所属组织
	Organization string `json:"organization"`
	// 时间范围-开始时间
	StartDate string `json:"start_date"`
	// 时间范围-结束时间
	EndDate string `json:"end_date"`
	// 分页信息
	viewmodel.BasePageHttpRequest
}

type StatsSalespersonPageResp struct {
	viewmodel.BasePageHttpResponse
	Data []StatsSalespersonPageData `json:"data"`
}

type StatsSalespersonPageData struct {
	//业务员ID
	SalesmanId string `json:"salesman_id"`
	//业务员姓名
	SalesmanName string `json:"salesman_name"`
	//业务员手机号
	Phone string `json:"phone"`
	//所属组织
	Organization string `json:"organization"`
	//服务企业数
	ServiceEnterpriseCount int `json:"service_enterprise_count"`
	//服务分销店铺
	ShopCount int `json:"shop_count"`
	//服务分销员
	ServiceDistributorCount int `json:"service_distributor_count"`
	//拓客分销员数
	CustomerCount int `json:"customer_count"`
	//成交企业数
	TransEnterpriseCount int `json:"trans_enterprise_count"`
	//分销成交单数
	TransCount int `json:"trans_count"`
	//分销成交金额
	TransAmount int `json:"trans_amount"`
	//分销佣金
	Commission int `json:"commission"`
	//分销商品成交企业数
	ProductEnterpriseCount int `json:"product_enterprise_count"`
	//分销商品成交单数
	ProductTransCount int `json:"product_trans_count"`
	//分销商品成交金额
	ProductTransAmount int `json:"product_trans_amount"`
	//分销商品佣金
	ProductCommission int `json:"product_commission"`
	//分销保险成交企业数
	InsuranceEnterpriseCount int `json:"insurance_enterprise_count"`
	//分销保险成交单数
	InsuranceTransCount int `json:"insurance_trans_count"`
	//分销保险成交金额
	InsuranceTransAmount int `json:"insurance_trans_amount"`
	//分销保险佣金
	InsuranceCommission int `json:"insurance_commission"`
}

type GetStatsSalespersonReq struct {
	// 业务员ID
	SalesmanId int `json:"salesman_id" validate:"required"`
	// 曲线图数据类型：1-分销单数，2-分销金额，3-企业数，4-分销员数
	GraphType int `json:"graph_type" validate:"required"`
	// 时间范围-开始时间
	StartDate string `json:"start_date" validate:"required"`
	// 时间范围-结束时间
	EndDate string `json:"end_date" validate:"required"`
	// 主体id
	OrgId int `json:"org_id"`
}

type StatsSalespersonSummaryResp struct {
	viewmodel.BaseHttpResponse
	Data StatsSalespersonSummaryData `json:"data"`
}

type StatsSalespersonSummaryData struct {
	// 分销金额
	TotalSales int `json:"total_sales"`
	// 分销单数
	TotalOrderNum int `json:"total_order_num"`
	// 分销佣金
	TotalCommission int `json:"total_commission"`
	// 服务企业数
	ServiceEntNum int `json:"service_ent_num"`
	// 分销店铺
	ShopNum int `json:"shop_num"`
	// 服务分销员
	ServiceDisNum int `json:"service_dis_num"`
	// 服务老板
	BossCount int `json:"boss_count"`
	// 拓客分销员
	TuokeDisNum int `json:"tuoke_dis_num"`
}

type StatsDisGraphResp struct {
	viewmodel.BaseHttpResponse
	Data []StatsDisGraphData `json:"data"`
}

type StatsDisGraphData struct {
	// 日期
	StatDate string `json:"stat_date"`
	// 取值
	Num int `json:"num"`
}

type StatsDisTrendResp struct {
	viewmodel.BaseHttpResponse
	Data StatsDisTrendData `json:"data"`
}

type StatsDisTrendData struct {
	// 总计（商品+保险）数据
	TotalDisStat TotalOrderDisStat `json:"total_dis_stat"`
	// 商品数据
	ProductDisStat OrderDisStat `json:"product_dis_stat"`
	// 保险数据
	InsureDisStat OrderDisStat `json:"insure_dis_stat"`
}

type TotalOrderDisStat struct {
	// 新增企业
	ServiceEntCount int `json:"service_ent_count"`
	// 新增企业环比
	ServiceEntCountRatio string `json:"service_ent_count_ratio"`
	// 新增分销店铺
	ShopCount int `json:"shop_count"`
	// 新增分销店铺环比
	ShopCountRatio string `json:"shop_count_ratio"`
	// 新增分销员
	ServiceDisCount int `json:"service_dis_count"`
	// 新增分销员环比
	ServiceDisCountRatio string `json:"service_dis_count_ratio"`
	// 分销单数
	TransCount int `json:"trans_count"`
	// 分销单数环比
	TransCountRatio string `json:"trans_count_ratio"`
	// 分销金额
	TransAmount int `json:"trans_amount"`
	// 分销金额环比
	TransAmountRatio string `json:"trans_amount_ratio"`
	// 分销佣金
	Commission int `json:"commission"`
	// 分销佣金环比
	CommissionRatio string `json:"commission_ratio"`
}

type OrderDisStat struct {
	// 分销单数
	TransCount int `json:"trans_count"`
	// 分销单数环比
	TransCountRatio string `json:"trans_count_ratio"`
	// 分销金额
	TransAmount int `json:"trans_amount"`
	// 分销金额环比
	TransAmountRatio string `json:"trans_amount_ratio"`
	// 新增企业
	TransEntCount int `json:"trans_ent_count"`
	// 新增企业环比
	TransEntCountRatio string `json:"trans_ent_count_ratio"`
	// 分销佣金
	Commission int `json:"commission"`
	// 分销佣金环比
	CommissionRatio string `json:"commission_ratio"`
}

type StatDisUnit struct {
	// 统计数据
	num int `json:"num"`
	// 环比比例
	ratio string `json:"ratio"`
}

func GetStatDisUint(curNum, preNum int) StatDisUnit {
	if preNum == 0 {
		return StatDisUnit{
			num:   curNum,
			ratio: "--",
		}
	}
	if curNum == 0 {
		return StatDisUnit{
			num:   0,
			ratio: "-100%",
		}
	}
	ratio := float64(curNum-preNum) / float64(preNum) * 100
	return StatDisUnit{
		num:   curNum,
		ratio: fmt.Sprintf("%.2f%%", ratio),
	}
}

type OrderDisStatDto struct {
	// 新增企业
	ServiceEnterpriseCount int `json:"service_enterprise_count"`
	// 新增分销店铺
	ShopCount int `json:"shop_count"`
	// 新增分销员
	ServiceDistributorCount int `json:"service_distributor_count"`
	// 分销单数
	TransCount int `json:"trans_count"`
	// 分销金额
	TransAmount int `json:"trans_amount"`
	// 成交企业
	TransEnterpriseCount int `json:"trans_enterprise_count"`
	// 分销佣金
	Commission int `json:"commission"`
	// 商品分销单数
	ProductTransCount int `json:"product_trans_count"`
	// 商品分销金额
	ProductTransAmount int `json:"product_trans_amount"`
	// 商品成交企业
	ProductEnterpriseCount int `json:"product_enterprise_count"`
	// 商品分销佣金
	ProductCommission int `json:"product_commission"`
	// 保险分销单数
	InsuranceTransCount int `json:"insurance_trans_count"`
	// 保险分销金额
	InsuranceTransAmount int `json:"insurance_trans_amount"`
	// 保险成交企业
	InsuranceEnterpriseCount int `json:"insurance_enterprise_count"`
	// 保险分销佣金
	InsuranceCommission int `json:"insurance_commission"`
}

type StatsDisGoodsReq struct {
	viewmodel.BasePageHttpRequest
	// 开始日期
	StartDate string `json:"start_date"`
	// 结束日期
	EndDate string `json:"end_date"`
	// 主体(前端不用传)
	OrgId int64 `json:"org_id"`
	// 业务员id
	SalesmanId int `json:"salesman_id" validate:"required"`
	// 商品名称
	GoodsName string `json:"goods_name"`
	// 排序:1-分销件数升序 2-分销件数降序 3-分销金额升序 4-分销金额降序 5-分销佣金升序 6-分销佣金降序
	OrderBy int `json:"order_by"`
}

type StatsDisGoodsResp struct {
	viewmodel.BasePageHttpResponse
	Data []StatsDisCenterGoods `json:"data"`
}
