package distribution_vo

import (
	"eShop/view-model"
)

type OutHospitalListReq struct {
	HospitalName string `json:"hospital_name"`
	CityName     string `json:"city_name"`
}
type OutHospitalListRes struct {
	viewmodel.BaseHttpResponse
	Data []S `json:"data"`
}
type S struct {
	Shop string `json:"shop"` //医院名称
}

type ScrmLeadsAutonavi struct {
	Id         int    `json:"id"  `
	UnKey      string `json:"un_key"`
	Shop       string `json:"shop"`        //店铺
	Tel        string `json:"tel"`         //电话
	Province   string `json:"province"`    //省
	City       string `json:"city"`        //市
	District   string `json:"district"`    //区
	Address    string `json:"address"`     //详细地址
	Longitude  string `json:"longitude"`   //纬度
	Latitude   string `json:"latitude"`    //纬度
	CreateTime string `json:"create_time"` //创建时间
	UpdateTime string `json:"update_time"` //更新时间
	JsonStr    string `json:"json_str"`
}
