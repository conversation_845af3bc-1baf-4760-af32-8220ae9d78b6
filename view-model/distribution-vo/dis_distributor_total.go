package distribution_vo

import (
	"time"
)

type DisDistributorTotal struct {
	Id                     int       `json:"id" xorm:"pk autoincr not null comment('ID') INT 'id'"`
	OrgId                  int       `json:"org_id" xorm:"not null default 0 comment('主体ID') INT 'org_id'"`
	DisId                  int       `json:"dis_id" xorm:"not null comment('分销员id') INT 'dis_id'"`
	MemberId               int       `json:"member_id" xorm:"not null comment('分销员用户id') INT 'member_id'"`
	ShopId                 int       `json:"shop_id" xorm:"default 0 comment('分销店铺') INT 'shop_id'"`
	OrderNum               int       `json:"order_num" xorm:"default 0 comment('分销单数') INT 'order_num'"`
	TotalSales             int       `json:"total_sales" xorm:"default 0 comment('分销销售额(分)') INT 'total_sales'"`
	SettledCommission      int       `json:"settled_commission" xorm:"default 0 comment('已结佣金(分)') INT 'settled_commission'"`
	UnsettledCommission    int       `json:"unsettled_commission" xorm:"not null default 0 comment('未结佣金(分)') INT 'unsettled_commission'"`
	InsOrderNum            int       `json:"ins_order_num" xorm:"default 0 comment('保险分销单数') INT 'ins_order_num'"`
	InsTotalSales          int       `json:"ins_total_sales" xorm:"default 0 comment('保险分销销售额(分)') INT 'ins_total_sales'"`
	InsSettledCommission   int       `json:"ins_settled_commission" xorm:"default 0 comment('保险已结佣金(分)') INT 'ins_settled_commission'"`
	InsUnsettledCommission int       `json:"ins_unsettled_commission" xorm:"not null default 0 comment('保险未结佣金(分)') INT 'ins_unsettled_commission'"`
	WithdrawSuccess        int       `json:"withdraw_success" xorm:"default 0 comment('提现成功(分)') INT 'withdraw_success'"`
	WithdrawApply          int       `json:"withdraw_apply" xorm:"default 0 comment('提现申请(分)') INT 'withdraw_apply'"`
	WaitWithdraw           int       `json:"wait_withdraw" xorm:"default 0 comment('待提现(分)') INT 'wait_withdraw'"`
	TotalCustomer          int       `json:"total_customer" xorm:"default 0 comment('累计客户数') INT 'total_customer'"`
	CreateTime             time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime             time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
	OrderPayNum            int       `json:"order_pay_num" xorm:"default 0 comment('已支付分销单数') INT 'order_pay_num'"`
	TotalPaySales          int       `json:"total_pay_sales" xorm:"default 0 comment('已支付分销销售额(分)') INT 'total_pay_sales'"`
	InsOrderPayNum         int       `json:"ins_order_pay_num" xorm:"default 0 comment('已支付保险分销单数') INT 'ins_order_pay_num'"`
	InsTotalPaySales       int       `json:"ins_total_pay_sales" xorm:"default 0 comment('已支付保险分销销售额(分)') INT 'ins_total_pay_sales'"`
}
