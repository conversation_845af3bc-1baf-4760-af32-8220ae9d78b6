package distribution_vo

import (
	"eShop/view-model"
)

type StatsSkuCityDaily struct {
	// 商品SKU
	Skuid int32 `json:"skuid"`
	// 省份
	Province string `json:"province"`
	// 城市
	City string `json:"city"`
	// 商品名称
	ProductName string `json:"product_name"`
	// 下单客户数
	OrderCustomerCount int32 `json:"order_customer_count"`
	// 下单件数
	OrderQuantity int32 `json:"order_quantity"`
	// 下单金额(分）
	OrderAmount int32 `json:"order_amount"`
	// 成交客户数
	DealCustomerCount int32 `json:"deal_customer_count"`
	// 成交件数
	DealQuantity int32 `json:"deal_quantity"`
	// 成交金额(分)
	DealAmount int32 `json:"deal_amount"`
	// 分销成交客户数
	DistDealCustomerCount int32 `json:"dist_deal_customer_count"`
	// 分销成交件数
	DistDealQuantity int32 `json:"dist_deal_quantity"`
	// 分销成交金额(分)
	DistDealAmount int32 `json:"dist_deal_amount"`
}

type StatsSkuCityDailyPageReq struct {
	// 商品名称
	ProductName string `json:"product_name"`
	// 商品SKU
	Skuid int32 `json:"skuid"`
	// 时间范围-开始时间
	StartDate string `json:"start_date"`
	// 时间范围-结束时间
	EndDate string `json:"end_date"`
	// 导了类型 15-商品数据导出 16-商品下单区域分析导出
	Type int8 `json:"type"`
	// 分页
	viewmodel.BasePageHttpRequest
}

type StatsSkuCityDailyPageRes struct {
	viewmodel.BasePageHttpResponse
	Data []StatsSkuCityDaily `json:"data"`
}
