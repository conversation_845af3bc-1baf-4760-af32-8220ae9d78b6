package distribution_vo

import (
	viewmodel "eShop/view-model"
	"time"
)

type Shop struct {
	Id                     int64     `json:"id" xorm:"pk autoincr not null comment('自增id') BIGINT 'id'"`                                             //自增id
	OrgId                  int       `json:"org_id" xorm:"not null default 0 comment('所属主体id') INT 'org_id'"`                                        //所属主体id
	EnterpriseId           string    `json:"enterprise_id" xorm:"not null default 0 comment('企业R1编码即scrm_enterprise.id') BIGINT 'enterprise_id'"`    //企业R1编码即scrm_enterprise.id
	ShopName               string    `json:"shop_name" xorm:"not null default '' comment('线上小店名称') VARCHAR(127) 'shop_name'"`                        //线上小店名称
	HeadImage              string    `json:"head_image" xorm:"not null default '' comment('小店头像') VARCHAR(511) 'head_image'"`                        //小店头像
	Welcome                string    `json:"welcome" xorm:"not null default '' comment('欢迎语') VARCHAR(255) 'welcome'"`                               //欢迎语
	OrderNum               int       `json:"order_num" xorm:"not null default 0 comment('分销订单数') INT 'order_num'"`                                   //分销订单数
	Sales                  int       `json:"sales" xorm:"not null default 0 comment('分销销售额') INT 'sales'"`                                           //分销销售额
	OrderPayNum            int       `json:"order_pay_num" xorm:"not null default 0 comment('分销支付订单数') INT 'order_pay_num'"`                         //分销支付订单数
	PaySales               int       `json:"pay_sales" xorm:"not null default 0 comment('分销支付销售额') INT 'pay_sales'"`                                 //分销支付销售额
	IsMain                 int       `json:"is_main" xorm:"not null default 0 comment('是否主店 0-初始，1-是，2否') INT 'is_main'"`                            //是否主店 0-初始，1-是，2否
	SettledCommission      int       `json:"settled_commission" xorm:"default 0 comment('已结佣金(分)') INT 'settled_commission'"`                        //已结佣金(分)
	UnsettledCommission    int       `json:"unsettled_commission" xorm:"not null default 0 comment('未结佣金(分)') INT 'unsettled_commission'"`           //未结佣金(分)
	InsSettledCommission   int       `json:"ins_settled_commission" xorm:"not null default 0 comment('保险已结佣金(分)') INT 'ins_settled_commission'"`     //保险已结佣金(分)
	InsUnsettledCommission int       `json:"ins_unsettled_commission" xorm:"not null default 0 comment('保险未结佣金(分)') INT 'ins_unsettled_commission'"` //保险未结佣金(分)
	WithdrawSuccess        int       `json:"withdraw_success" xorm:"default 0 comment('提现成功(分)') INT 'withdraw_success'"`                            //提现成功(分)
	WithdrawApply          int       `json:"withdraw_apply" xorm:"default 0 comment('提现申请(分)') INT 'withdraw_apply'"`                                //提现申请(分)
	WaitWithdraw           int       `json:"wait_withdraw" xorm:"default 0 comment('待提现(分)') INT 'wait_withdraw'"`                                   //待提现(分)
	IsSettedShop           int       `json:"is_setted_shop" xorm:"not null default 0 comment('是否已经设置线上小店:0-初始，1-是，2-否') INT 'is_setted_shop'"`       //是否已经设置线上小店:0-初始，1-是，2-否
	RegisteredSalesperson  string    `json:"registered_salesperson" xorm:"default 0 comment('注册业务员') INT 'registered_salesperson'"`                  //注册业务员
	CreateTime             time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`                  //创建时间
	UpdateTime             time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`                  //更新时间
	SaasShopId             string    `json:"saas_shop_id" xorm:"default '' comment('saas店铺id') varchar(50) 'saas_shop_id'"`
}

// 扩展shop查询字段
type ShopExt struct {
	Shop                     `xorm:"extends"`
	ScrmEnterpriseId         int64  `json:"scrm_enterprise_id" xorm:"not null default 0 comment('企业id') BIGINT 'scrm_enterprise_id'"`               //企业id
	EnterpriseName           string `json:"enterprise_name" xorm:"not null default '' comment('企业名称') VARCHAR(127) 'enterprise_name'"`              //企业名称
	DisEnterpriseName        string `json:"dis_enterprise_name" xorm:"not null default '' comment('线下企业名称') VARCHAR(127) 'dis_enterprise_name'"`    //线下企业名称
	EnterpriseType           int    `json:"enterprise_type" xorm:"not null default 0 comment('企业类型') INT 'enterprise_type'"`                        //企业类型 （0企业 1个人）
	EnterpriseStatus         int    `json:"enterprise_status" xorm:"not null default 0 comment('企业状态') INT 'enterprise_status'"`                    //企业状态 （0停用 1启用）
	DistributorName          string `json:"distributor_name" xorm:"not null default '' comment('分销员姓名') VARCHAR(127) 'distributor_name'"`           //老板姓名
	DistributorMobile        string `json:"distributor_mobile" xorm:"not null default '' comment('分销员带*手机号') VARCHAR(127) 'distributor_mobile'"`    //老板带*手机号
	EncryptMobile            string `json:"encrypt_mobile" xorm:"not null default '' comment('分销员密文手机号') VARCHAR(127) 'encrypt_mobile'"`            //老板密文手机号
	SalespersonNames         string `json:"salesperson_names" xorm:"not null default '' comment('业务员名称') VARCHAR(127) 'salesperson_names'"`         //所属业务员
	RegSalespersonName       string `json:"reg_salesperson_name" xorm:"not null default '' comment('注册业务员名称') VARCHAR(127) 'reg_salesperson_name'"` //注册业务员
	DisNum                   int    `json:"dis_num" xorm:"not null default 0 comment('分销员数量') INT 'dis_num'"`                                       //分销员数量
	TotalCustomer            int    `json:"total_customer" xorm:"not null default 0 comment('总客户数') INT 'total_customer'"`                          //总客户数
	EffectiveDisNum          int    `json:"effective_dis_num" xorm:"not null default 0 comment('有效分销员数量') INT 'effective_dis_num'"`                 //有效分销员数量
	Province                 string `json:"province" xorm:"not null default '' comment('省') VARCHAR(127) 'province'"`
	City                     string `json:"city" xorm:"not null default '' comment('市') VARCHAR(127) 'city'"`
	District                 string `json:"district" xorm:"not null default '' comment('区') VARCHAR(127) 'district'"`
	SettledCommissionTotal   int    `json:"settled_commission_total" xorm:"not null default 0 comment('已结佣金总额(分)') INT 'settled_commission_total'"`     //已结佣金总额(分)
	UnsettledCommissionTotal int    `json:"unsettled_commission_total" xorm:"not null default 0 comment('未结佣金总额(分)') INT 'unsettled_commission_total'"` //未结佣金总额(分)
	CommissionTotal          int    `json:"commission_total" xorm:"not null default 0 comment('佣金总额(分)') INT 'commission_total'"`                       //佣金总额(分)
	SocialCreditCode         string `json:"social_credit_code" xorm:"default '' comment('统一社会信用代码') VARCHAR(50) 'social_credit_code'"`
	SocialCodeImage          string `json:"social_code_image" xorm:"default '' comment('统一社会信用代码图片') VARCHAR(255) 'social_code_image'"`
	EncryptIdCard            string `json:"encrypt_id_card" xorm:"not null default '' comment('加密身份证号码') VARCHAR(100) 'encrypt_id_card'"`
	IdCard                   string `json:"id_card" xorm:"not null default '' comment('身份证号码') VARCHAR(100) 'id_card'"`
	IdcardFront              string `json:"idcard_front" xorm:"not null default '' comment('分销员身份证正面') VARCHAR(255) 'idcard_front'"`
	IdcardReverse            string `json:"idcard_reverse" xorm:"not null default '' comment('分销员身份证反面') VARCHAR(255) 'idcard_reverse'"`
	DisRole                  int    `json:"dis_role" xorm:"not null default 0 comment('分销员角色 0-初始值 1-老板 2-店员') INT 'dis_role'"`
	//分销员姓名
	Name string `json:"name"`
	//业务员姓名
	SalespersonName string `json:"salesperson_name"`
	Mobile          string `json:"mobile"`
	SourceType      int8   `json:"source_type" xorm:"default 0 comment('来源类型：0-润合云店，1-润合SAAS') TINYINT 'source_type'"` //来源类型：0-润合云店，1-润合SAAS
	DataSource      int    `json:"data_source" xorm:"default 1 comment('来源：1、自建   2 云订货') TINYINT 'data_source'"`      //来源：1、自建   2 云订货
	DisEnterpriseId int    `json:"dis_enterprise_id"`                                                                  //宠利扫企业id
}

// 获取店铺信息
type DisShopReq struct {
	//店铺ID
	ShopId int `json:"shop_id" required:"true"`
}

// 获取saas店铺信息
type DisSaasShopReq struct {
	//saas店铺id
	SaasShopId string `json:"saas_shop_id" required:"true"`
	//分销员手机号
	Mobile string `json:"mobile"`
	//请求来源 1-mis 2-saas
	SourceType int `json:"source_type"`
}

// 店铺信息返回
type SaasShopRes struct {
	viewmodel.BaseHttpResponse
	Data ShopExt `json:"data"`
}

// 店铺信息编辑
type DisShopUpdateReq struct {
	//店铺ID
	Id int `json:"id" xorm:"pk autoincr not null comment('自增id') BIGINT 'id'"`
	//线上小店名称
	ShopName string `json:"shop_name" xorm:"not null default '' comment('线上小店名称') VARCHAR(127) 'shop_name'"`
	//小店头像
	HeadImage string `json:"head_image" xorm:"not null default '' comment('小店头像') VARCHAR(511) 'head_image'"`
	//欢迎语
	Welcome string `json:"welcome" xorm:"not null default '' comment('欢迎语') VARCHAR(255) 'welcome'"`
}

// 店铺信息返回
type DisShopRes struct {
	viewmodel.BaseHttpResponse
	Data Shop `json:"data"`
}

type GetShopDetailReq struct {
	ShopId int64 `json:"shop_id"` //分销店铺id
}

// 店铺列表返回
type DisShopListRes struct {
	viewmodel.BasePageHttpResponse
	Data []ShopExt `json:"data"`
}

type DisShopListReq struct {
	//分销状态 1-启用 2-禁用
	Status int8 `json:"status"`
	//主体id
	OrgId int `json:"org_id"`
	//分销店铺id
	ShopId int `json:"shop_id"`
	//分销店铺设置
	IsSettedShop int `json:"is_setted_shop"`
	//查询条件
	Where string `json:"where"`
	//查询条件的类型（1-老板Id 2-老板姓名、3-老板手机号、4-所属业务员、5-所属业务员Id、6-所属业务员手机号）
	WhereType int `json:"where_type"`
	//查询内容
	WhereContent string `json:"content"`
	//查询条件,企业名称和r1编码
	EnterpriseName string `json:"enterprise_name"`
	//企业ID  ，用于小程序接口，销售业绩界面查询
	EnterpriseId string `json:"enterprise_id"`
	viewmodel.BasePageHttpRequest
}
