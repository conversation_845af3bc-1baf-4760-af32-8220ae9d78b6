package distribution_vo

// SecurityCodeVerifyReq 防伪码验证请求
type SecurityCodeVerifyReq struct {
	// 防伪码
	Code string `json:"code" validate:"required"`
	// 会员ID
	MemberId int `json:"member_id"`
	// 	查询方式 1: 手动输入 2: 扫码查询
	VerifyType int `json:"verify_type" validate:"required"`
	// 来源：1-首页-扫一扫,2-微信扫一扫,3-个人中心查验真伪 4-公众号
	Source int `json:"source" validate:"required"`
	// 组织ID
	OrgId int `json:"org_id"`
	// 客户端IP
	ClientIP string `json:"client_ip"`
	// 地理位置
	Location string `json:"location"`
	// 用户ID
	UserId string `json:"user_id"`
	// 微信OpenID
	OpenId string `json:"open_id"`
	// 手机号
	Mobile string `json:"mobile"`
	// msg_state 201-未能验证防伪信息(扫一扫) 202-未找防伪信息(手动输入) 203-未找到商品备案信息
	MsgState int `json:"msg_state"`
}

// VerifyTimeInfo 验证时间信息
type VerifyTimeInfo struct {
	Name       string `json:"name"`        // 查询次数名称，如"首次查询时间"
	VerifyTime string `json:"verify_time"` // 查询时间
}

// SecurityCodeVerifyResp 防伪码验证响应
type SecurityCodeVerifyResp struct {
	IsValid      bool             `json:"is_valid"`       // 是否有效的防伪码
	Message      string           `json:"message"`        // 验证消息
	VerifyCount  int              `json:"verify_count"`   // 验证次数
	IsFirstQuery bool             `json:"is_first_query"` // 是否首次查询
	VerifyTimes  []VerifyTimeInfo `json:"verify_times"`   // 验证时间列表，包含名称和时间
	Warning      string           `json:"warning"`        // 警告信息
	BrandType    int              `json:"brand_type"`     // 品牌类型: 1-贵族 2-宠儿香 3-科学喵
	// msg_state 201-未能验证防伪信息(扫一扫) 202-未找防伪信息(手动输入) 203-未找到商品备案信息
	MsgState int `json:"msg_state"`
}

// SecurityCodeVerifyResponse API响应结构
type SecurityCodeVerifyResponse struct {
	Code    int                    `json:"code"`
	Message string                 `json:"message"`
	Data    SecurityCodeVerifyResp `json:"data"`
}

// UpdateUserInfoReq 更新用户信息请求
type UpdateUserInfoReq struct {
	OpenId string `json:"open_id"`
	UserId string `json:"user_id"`
	Mobile string `json:"mobile"`
}

