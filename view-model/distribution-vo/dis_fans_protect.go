package distribution_vo

import (
	"eShop/view-model"
)

type DistributorFansProtect struct {
	HasProtect int `json:"has_protect"  validate:"required"` //有无粉丝关系保护期，0-默认，1-有，2-无
	ProtectDay int `json:"protect_day"`                      //有效期多少天后自动解绑（无粉丝关系保护期时， 该字段值为0）
	OrgId      int `json:"org_id"`                           //主体id
}

type GetDisFansProtectSettingRes struct {
	viewmodel.BaseHttpResponse
	Data DistributorFansProtect `json:"data"`
}
