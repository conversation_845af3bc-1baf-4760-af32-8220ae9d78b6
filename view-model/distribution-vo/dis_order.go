package distribution_vo

import viewmodel "eShop/view-model"

type GetDisOrderListReq struct {
	viewmodel.BasePageHttpRequest
	// 订单状态：-1-全部，0-已取消，10-待付款;20-待发货;30-待收货;40:已完成
	OrderState int `json:"order_state"`
	// 所属主体id
	OrgId int `json:"org_id"`
	// 所属店铺
	ShopId int `json:"shop_id"`
	// 所属组织
	OrgName string `json:"org_name"`
	// 物流码
	LogisticsCode string `json:"logistics_code"`
	//查询条件的类型（enterprise_name=线下企业,order_sn=订单编号，distributor_id=分销员id，distributor_name=分销员名称,buyer_mobile=买家手机号，buyer_name=买家账号,salesman_id=业务员id，salesman_name=业务员姓名）
	WhereType string `json:"where_type"`
	// 查询条件值-开始时间
	Where string `json:"where"`
	// 查询条件值-开始时间
	WhereType2 string `json:"where_type2"`
	// 查询条件值-开始时间
	WhereStart string `json:"where_start"`
	// 查询条件值-结束时间
	WhereEnd string `json:"where_end"`
	// 码包所属企业：1北京百林康源 2-深圳利都
	OrgType int `json:"org_type"`
	// 是否扣除佣金：1-扣除佣金，2-不扣除佣金
	DeductCommission int `json:"deduct_commission"`
}

type GetDisOrderListRes struct {
	viewmodel.BasePageHttpResponse
	Data []DisOrderListView `json:"data"`
}

// 下单时间 电商表是整型，这里也需要是整型， 金额是浮点型这里也要是浮点型
type DisOrderList struct {
	// 订单id
	OrderId int `json:"order_id"`
	// 订单编号
	OrderSn string `json:"order_sn"`
	// 订单生成时间
	OrderAddTime int `json:"order_add_time"`
	// 订单完成时间
	OrderFinishTime int `json:"order_finish_time"`
	// 订单状态：0-已取消，10-待付款;20-待发货;30-待收货;40:已完成;50:部分发货
	OrderState int `json:"order_state"`
	// 订单总价格(单位元)
	OrderAmount float64 `json:"order_amount"`
	// 退款金额(单位元)
	RefundAmount float64 `json:"refund_amount"`
	// 运费
	ShippingFee float64 `json:"shipping_fee"`
	// 所属店铺名称
	ShopName string `json:"shop_name"`
	// 订单分销金额(单位元)
	OrderDisAmount float64 `json:"order_dis_amount"`
	// 买家id
	BuyerId int `json:"buyer_id"`
	// 买家手机号
	BuyerPhone string `json:"buyer_phone"`
	// 买家加密手机号
	EncryptMobile string `json:"encrypt_mobile"`
	// 买家账号
	BuyerName string `json:"buyer_name"`
	// 分销员名称
	DistributorName string `json:"distributor_name"`
	// 分销员id
	DistributorId int `json:"distributor_id"`
	// 业务员姓名（多个用逗号隔开）
	SalespersonName string `json:"salesperson_name"`
	// 业务员id/名字（多个用逗号隔开）
	SalespersonIdName string `json:"salesperson_id_name"`
	// 线下企业编码
	EnterpriseId int64 `json:"enterprise_id"`
	// 线下企业名称
	EnterpriseName string `json:"enterprise_name"`
	// 所属店铺
	StoreId int `json:"store_id"`
	// 分销类型 1分享连接，2扫码 3 自己扫码自己 5 自主访问下单
	DisType int `json:"dis_type"`
	// 注册业务员id/名字
	TuokeSalespersonIdName string `json:"tuoke_salesperson_id_name"`
	// 物流码
	LogisticsCode string `json:"logistics_code"`
	// 物流码状态 1 已使用 0-已恢复
	SwlmStatus int `json:"swlm_status"`
	// 类型   1：体系内   0：体系外
	InSystem int `json:"in_system"`
	// 扣除的佣金
	DisCommisAmount int `json:"dis_commis_amount"`
	// 分销员角色
	DisRole int `json:"dis_role"`
	// 线下企业名称
	DisEnterpriseName string `json:"dis_enterprise_name"`
	// 码包所属企业：1北京百林康源 2-深圳利都
	OrgType int `json:"org_type"`
	// 分销员渠道路径
	CustomerChannelPath string `json:"customer_channel_path"`
	// 分销员渠道路径id
	CustomerChannelId int `json:"customer_channel_id"`
}

// 下单时间 电商表是整型，这里也需要是整型， 金额是浮点型这里也要是浮点型
type ExportDisOrderList struct {
	// 订单id
	OrderId int `json:"order_id"`
	// 订单编号
	OrderSn string `json:"order_sn"`
	// 订单生成时间
	OrderAddTime int `json:"order_add_time"`
	// 订单完成时间
	OrderFinishTime int `json:"order_finish_time"`
	// 订单状态：0-已取消，10-待付款;20-待发货;30-待收货;40:已完成;50:部分发货
	OrderState int `json:"order_state"`
	// 订单总价格(单位元)
	OrderAmount float64 `json:"order_amount"`
	// 退款金额(单位元)
	RefundAmount float64 `json:"refund_amount"`
	// 运费
	ShippingFee float64 `json:"shipping_fee"`
	// 所属店铺名称
	ShopName string `json:"shop_name"`
	// 订单分销金额(单位元)
	OrderDisAmount float64 `json:"order_dis_amount"`
	// 买家id
	BuyerId int `json:"buyer_id"`
	// 买家手机号
	BuyerPhone string `json:"buyer_phone"`
	// 买家加密手机号
	EncryptMobile string `json:"encrypt_mobile"`
	// 买家账号
	BuyerName string `json:"buyer_name"`
	// 分销员名称
	DistributorName string `json:"distributor_name"`
	// 分销员id
	DistributorId int `json:"distributor_id"`
	// 业务员姓名（多个用逗号隔开）
	SalespersonName string `json:"salesperson_name"`
	// 业务员id/名字（多个用逗号隔开）
	SalespersonIdName string `json:"salesperson_id_name"`
	// 线下企业编码
	EnterpriseId int64 `json:"enterprise_id"`
	// 线下企业名称
	EnterpriseName string `json:"enterprise_name"`
	// 所属店铺
	StoreId int `json:"store_id"`
	// 分销类型 1分享连接，2扫码 3 自己扫码自己 5 自主访问下单
	DisType int `json:"dis_type"`
	// 注册业务员id/名字
	TuokeSalespersonIdName string `json:"tuoke_salesperson_id_name"`
	// 商品skuid
	GoodsId int `json:"goods_id"`
	// 商品名称
	GoodsName string `json:"goods_name"`
	// 商品数量
	GoodsNum int `json:"goods_num"`
	// 商品佣金比例
	DisCommisRate float64 `json:"dis_commis_rate"`
	// 商品支付金额
	GoodsPayPrice float64 `json:"goods_pay_price"`
	// 商品佣金
	CommisAmount float64 `json:"commis_amount"`
	// 物流码
	LogisticsCode string `json:"logistics_code"`
	// 箱码
	XiangMa string `json:"xiang_ma"`
	// 物流码状态 1 已使用 2 已恢复
	SwlmStatus int `json:"swlm_status"`
	// 扣除的佣金
	DisCommisAmount int `json:"dis_commis_amount"`
	// 企业销售渠道
	CustomerChannelPath string `json:"customer_channel_path"`
	// 绑定的线下企业
	DisEnterpriseName string `json:"dis_enterprise_name"`
	// 码包所属企业：1北京百林康源 2-深圳利都
	OrgType int `json:"org_type"`
}

type DisOrderListView struct {
	// 电商自营店铺id
	StoreId int `json:"store_id"`
	// 订单编号
	OrderSn string `json:"order_sn"`
	// 订单生成时间
	OrderAddTime string `json:"order_add_time"`
	// 订单完成时间
	OrderFinishTime string `json:"order_finish_time"`
	// 订单状态：0-已取消，10-待付款;20-待发货;30-待收货;40:已完成;50:部分发货
	OrderState int `json:"order_state"`
	// 订单总价格(单位分)
	OrderAmount int `json:"order_amount"`
	// 退款金额(单位分)
	RefundAmount int `json:"refund_amount"`
	// 运费（单位分）
	ShippingFee int `json:"shipping_fee"`
	// 所属店铺名称
	ShopName string `json:"shop_name"`
	// 订单分销金额(单位分)
	OrderDisAmount int `json:"order_dis_amount"`
	// 买家id
	BuyerId int `json:"buyer_id"`
	// 买家手机号
	BuyerPhone string `json:"buyer_phone"`
	// 买家加密手机号
	EncryptMobile string `json:"encrypt_mobile"`
	// 买家账号
	BuyerName string `json:"buyer_name"`
	// 分销员名称
	DistributorName string `json:"distributor_name"`
	// 分销员id
	DistributorId int `json:"distributor_id"`
	// 业务员姓名
	SalespersonName string `json:"salesperson_name"`
	// 业务员id/名字（多个用逗号隔开）
	SalespersonIdName string `json:"salesperson_id_name"`
	// 线下企业名称
	EnterpriseName string `json:"enterprise_name"`
	// 线下企业编码
	EnterpriseId int64 `json:"enterprise_id"`
	// 分销类型 1分享连接，2扫码 3 自己扫码自己 5 自主访问下单
	DisType int `json:"dis_type"`
	// 注册业务员id/名字
	TuokeSalespersonIdName string `json:"tuoke_salesperson_id_name"`
	// 商品skuid
	GoodsId int `json:"goods_id"`
	// 商品名称
	GoodsName string `json:"goods_name"`
	// 商品数量
	GoodsNum int `json:"goods_num"`
	// 商品佣金比例
	DisCommisRate float64 `json:"dis_commis_rate"`
	// 商品支付金额
	GoodsPayPrice float64 `json:"goods_pay_price"`
	// 商品佣金
	CommisAmount float64 `json:"commis_amount"`
	// 物流码
	LogisticsCode string `json:"logistics_code"`
	// 物流码状态 1 已使用 2 已恢复
	SwlmStatus int `json:"swlm_status"`
	// 箱码
	XiangMa string `json:"xiang_ma"`
	// 类型   1：体系内   0：体系外
	InSystem int `json:"in_system"`
	// 扣除的佣金
	DisCommisAmount int `json:"dis_commis_amount"`
	// 码包所属企业：1北京百林康源 2-深圳利都
	OrgType int `json:"org_type"`
	// 分销员渠道路径
	CustomerChannelPath string `json:"customer_channel_path"`
	// 分销员渠道路径id
	CustomerChannelId int `json:"customer_channel_id"`
	// 绑定的线下企业
	DisEnterpriseName string `json:"dis_enterprise_name"`
}
type GetDisOrderListApiReq struct {
	viewmodel.BasePageHttpRequest
	//flag=0 代表的是分销订单 ， flag=1 代表是店铺分销订单
	Flag int `json:"flag"`
	//分销员在电商表的upet_member表的member_id(前端不用传)
	MemberId int `json:"member_id"`
	//店员电商会员id
	DyMemberId int `json:"dy_member_id"`
	//订单状态：0-全部，1-待完成（即待付款、待发货、待收货），2-已取消 ，3-已完成
	OrderState int `json:"order_state"`
	//所属主体id
	OrgId int `json:"org_id"`
	//所属店铺
	ShopId int `json:"shop_id"`
	//搜索：订单编号、买家手机号、商品名称
	SearchValue string `json:"search_value"`
	//下单开始时间(示例：2024-04-22 00:00:00)
	AddTimeEnd string `json:"add_time_end"`
	//下单结束时间(示例：2024-04-22 23:59:59)
	AddTimeStart string `json:"add_time_start"`
	//结算状态：0-全部,1-待结算，2-已结算
	SettStatus int `json:"sett_status"`
	//saas店铺id
	SaasShopId string `json:"saas_shop_id"`
	//查询条件
	Where string `json:"where"`
	//查询条件的类型（id=分销员id，name=分销员姓名，mobile=分销员手机号，belong_salesman_id=所属业务员ID，belong_salesman_name=所属业务员，belong_salesman_mobile=业务员手机号，tuoke_salesman_name=注册业务员ID，tuoke_salesman_name=注册业务员）
	WhereType string `json:"where_type"`
	// 统计类型：0-不需要统计  1统计总销售额
	StatsType int `json:"stats_type"`
	// 是否展示百林康源医生角色时分销订单数据
	IsShowBLKYDoctorOrder bool `json:"is_show_blky_doctor_order"`
}

type GetDisOrderListApiRes struct {
	viewmodel.BasePageHttpResponse
	//合计订单金额（分）
	TotalSale int `json:"total_sale"`
	// 宠利扫统计总登记佣金(总佣金-扣除佣金) 分
	RealCommission int            `json:"real_commission"`
	Data           []DisOrderData `json:"data"`
}

type DisOrderData struct {
	//订单id
	OrderId int `json:"order_id"`
	//订单编号
	OrderSn string `json:"order_sn"`
	//订单状态：0-已取消，10-待付款;20-待发货;30-待收货;40:已完成;50:部分发货
	OrderState int `json:"order_state"`
	//退款状态:0是无退款,1是部分退款,2是全部退款
	RefundState int `json:"refund_state"`
	//物流码(分销码)
	LogisticsCode string `json:"logistics_code"`
	//订单状态文本
	OrderStateText string `json:"order_state_text"`
	//订单生成时间(时间戳)
	OrderAddTime int `json:"order_add_time"`
	//订单完成时间（时间戳）
	OrderFinishTime int `json:"order_finish_time"`
	//所属店铺名称
	ShopName string `json:"shop_name"`
	//商品列表
	GoodsList []*GoodsInfoExt `json:"goods_list"`
	//结算状态
	SettStatusText string `json:"sett_status_text"`
	//分销实付
	PayAmount int `json:"pay_amount"`
	//有退款，退款金额 分
	RefundAmount int `json:"refund_amount"`
	//佣金总计
	Commission int `json:"commission"`
	//订单金额 分
	OrderAmount int `json:"order_amount"`
	// 订单分销金额 分(或宠利扫登记佣金)
	DisOrderAmount int `json:"dis_order_amount"`
	// 扣除佣金
	DeductCommission int `json:"deduct_commission"`
	// 扣除佣金时间
	DeductCommissionTime string `json:"deduct_commission_time"`
	//分销员名称
	DisName string `json:"dis_name"`
}

type GoodsInfo struct {
	//订单id
	OrderId int `json:"order_id"`
	//商品名称
	GoodsName string `json:"goods_name"`
	//商品图片
	GoodsImage string `json:"goods_image"`
	//商品规格
	GoodsSpec string `json:"goods_spec"`
	//商品数量
	GoodsNum int `json:"goods_num"`
	//商品单价（单位元）
	GoodsPrice float64 `json:"goods_price"`
	//分销佣金比例(示例：10.00%显示的是10.00)
	DisCommisRate float64 `json:"dis_commis_rate"`
	//佣金（单位元）
	DisComm float64 `json:"dis_comm" xorm:"-"`
	//是否分销商品 1是 0否
	IsDis int `json:"is_dis"`
}

// 扩展GoodsInfo
type GoodsInfoExt struct {
	GoodsInfo `xorm:"extends"`
	//有退款，退款金额 分
	RefundAmount int `json:"refund_amount"`
	//订单商品id
	OrderGoodsId int `json:"order_goods_id"`
}

type OrderSettedReq struct {
	Data []SettedData `json:"data" validate:"required,dive"`
}

type SettedData struct {
	// 所属主体id
	OrgId int `json:"org_id" validate:"required"`
	// 订单编号
	OrderNo string `json:"order_no"  validate:"required"`
	// 下单时间
	OrderTime string `json:"order_time" `
	// 订单完成时间
	OrderFinishTime string `json:"order_finish_time"`
	// 商品id
	GoodsId int `json:"goods_id"  validate:"required"`
	// 商品名称
	GoodsName string `json:"goods_name" `
	// 支付金额(分)
	PayAmount int `json:"pay_amount"`
	// 分销佣金比例(示例：佣金比例是5%，这里存5)
	CommissionRate float64 `json:"commission_rate"`
	// 分销员id
	MemberId int `json:"member_id"  validate:"required"`
	// 店铺id
	ShopId int `json:"shop_id"`
}
