package distribution_vo

import (
	viewmodel "eShop/view-model"
	"time"
)

type BlkyQueryCodeRequest struct {
	//物流码 or 箱码
	Code string `json:"code"`
	//类型 1-百林康源 2-深圳利都
	OrgType int `json:"org_type" validate:"required,min=1,max=2"`
	//是否导出 1-是
	IsExport int `json:"is_export"`
}

type BlkyQueryCodeResponse struct {
	viewmodel.BaseHttpResponse
	//列表数据
	Data BlkyQueryCodeData `json:"data"`
}

type BlkyQueryCodeData struct {
	//箱码
	CaseCode string `json:"case_code"`

	Data []LogisticsCodes `json:"data"`
}

type QueryData struct {
	//店铺id
	ShopId int `json:"shop_id"`
	//箱号id
	Sdxtm string `json:"sdxtm"`
	//物流码
	Swlm string `json:"swlm"`
	//物流码状态
	State bool `json:"state"`
	//更新时间
	UpdateTime time.Time `json:"update_time"`
	//商品实际支付价元 单价*数量
	GoodsPayPrice float64 `json:"goods_pay_price"`
	//佣金比例
	DisCommisRate float64 `json:"dis_commis_rate"`
	//分销员Id
	DisMemberId int `json:"dis_member_id"`
	//商品名称
	GoodsName string `json:"goods_name"`
	// 分销员名称
	Name string `json:"name"`
	// 医院名称
	HospitalName string `json:"hospital_name"`
	// 省
	Province string `json:"province"`
	//市
	City string `json:"city"`
	//手机号带*
	Mobile string `json:"mobile"`
	//加密手机号
	EncryptMobile string `json:"encrypt_mobile"`
	//佣金状态码
	CommissStatus int `json:"commiss_status"`
	//物流码状态
	SwlmStatus int `json:"swlm_status"`
	//可提现金额(分)
	WaitWithdraw int `json:"wait_withdraw"`
	// 店铺可提现金额(分)
	ShopWaitWithdraw int `json:"shop_wait_withdraw"`
	//订单号
	OrderSn string `json:"order_sn"`
	//已扣除佣金（分）
	DisCommisAmount int `json:"dis_commis_amount"`
	// 注册企业名称 enterprise_name
	EnterpriseName string `json:"enterprise_name"`
	// 企业所在省
	EnterpriceProvince string `json:"enterprice_province"`
	// 企业所在市
	EnterpriceCity string `json:"enterprice_city"`
}

type LogisticsCodes struct {
	//物流码
	LogisticsCodes string `json:"logistics_codes"`

	//是否未使用
	Unused bool `json:"unused"`

	//使用详情
	UsedDetail ProductUsedDetail `json:"used_detail"`
}

type ProductUsedDetail struct {
	//使用时间
	Time string `json:"time"`
	//医生名称
	DoctorName string `json:"doctor_name"`
	//区域名称
	AreaName string `json:"area_name"`
	//医院名称
	HospitalName string `json:"hospital_name"`
	//商品名称
	ProductName string `json:"product_name"`
	//佣金
	Commission int `json:"commission"`
	// 税后佣金
	AfterTaxCommission int `json:"after_tax_commission"`
	//联系电话
	Mobile string `json:"mobile"`
	//加密手机号
	EncryptMobile string `json:"encrypt_mobile"`
	//待提现金额(分)
	WaitWithdraw int `json:"wait_withdraw"`
	// 税后待提现金额(分)
	AfterTaxWaitWithdraw int `json:"after_tax_wait_withdraw"`
	//物流码恢复状态（0-可操作 1-置灰不可操作）
	SwlmStatus int `json:"swlm_status"`
	//扣除佣金操作状态 0-可操作 1-置灰不可操作
	CommissStatus int `json:"commiss_status"`
	//物流码
	Swlm string `json:"swlm"`
	//订单号
	OrderSn string `json:"order_sn"`
	//注册企业名称
	EnterpriseName string `json:"enterprise_name"`
}

type XcodeImportReq struct {
	//导入类型
	Type int `json:"type"`
	//导入文件地址url
	OperationFileUrl string `json:"operation_file_url"`
}

type CommissionWithdrawReq struct {
	//物流码
	Code string `json:"code" validate:"required"`
	//提现金额(元）
	DisCommisRate float64 `json:"dis_commis_rate" validate:"required"`
	//订单号
	OrderSn string `json:"order_sn" validate:"required"`
	//归属企业 1-北京百林康源 2-深圳利都
	OrgType int `json:"org_type" validate:"required"`
}

type CommissionWithdraw struct {
	//店铺id
	ShopId int
	//分销员id
	DisId int
	//主体Id
	OrgId int
	//订单商品佣金（分）
	Commission int
	//分销员可提现金额（分）
	WaitWithdraw int
	// 店铺可提现金额（分）
	ShopWaitWithdraw int `json:"shop_wait_withdraw"`
	// 分销员名称
	Name string
	// 订单号
	OrderSn string
	//带*手机号
	Mobile string
	//手机号加密
	EncryptMobile string
	//物流码
	LogisticsCode string
	//物流码状态
	SwlmStatus int
	//分销员用户id
	MemberId int
}

type SwlmImportReq struct {
	//导入类型 1-箱码 2-物流码
	Type int `json:"type"`
	//物流码
	Code string `json:"code" validate:"required"`
	//0~1佣金比例
	CommissionRate float64 `json:"commission_rate" validate:"required"`
	//箱码
	Sdxtm string
	//归属企业 1-北京百林康源 2-深圳利都
	OrgType int `json:"org_type" validate:"required"`
}

type BlkyCodeListRes struct {
	viewmodel.BaseHttpResponse
	//列表数据
	Data BlkyCodeListData `json:"data"`
}

type BlkyCodeListData struct {
	//查询结果
	Result string `json:"result"`
	// 已扫码登记列表
	UseData []ProductUsedDetail `json:"use_data"`
	//未扫码登记列表
	UnuseData []Xkucun `json:"unuse_data"`
	// 是否存在记录
	IsExist bool `json:"is_exist"`
}
type Xkucun struct {
	Swlm string `json:"swlm"`
}

type CommissionWithdrawLog struct {
	//分销员id
	DisId int
	//订单商品佣金（分）
	Commission int
	//分销员可提现金额（分）
	WaitWithdraw int
	// 分销员名称
	Name string
	// 订单号
	OrderSn string
	//带*手机号
	Mobile string
	//手机号加密
	EncryptMobile string
	//物流码
	LogisticsCode string
	// 本次扣除佣金是否恢复物流码
	IsRestore bool
}

type DelDataReq struct {
	//类型 1-分销员管理 2-分销订单 3-结算管理 4-提现管理
	Type int `json:"type" validate:"min=1,max=4"`
	//来源id 1-id 2-order_sn 3-id 4-id
	FromId string `json:"from_id" validate:"required"`
}

type SyncCodeDateReq struct {
	//类型 1-分销码,营销码 2-防伪码
	FileType string `json:"file_type"`
	//时间戳
	Timestamp string `json:"timestamp"`
	//文件地址
	FileUrl string `json:"file_url"`
	//批次号,用于防止数据重传
	BatchNum string `json:"batch_num"`
	//签名，用于验证请求的合法性
	Sign string `json:"sign"`
}
