package payment

import (
	"eShop/infra/log"
	"reflect"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"xorm.io/xorm"
)

func TestDianyinPayService_OrderRefund(t *testing.T) {
	type fields struct {
		refundUrl string
	}
	type args struct {
		orderInfo map[string]interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *RefundResult
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				orderInfo: map[string]interface{}{
					"trade_no":      "20250208143050734983785669497913",
					"refund_amount": 100,
					"refund_sn":     "1234567890",
					"appId":         5,
				},
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := OrderRefund(tt.args.orderInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderRefund() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderRefund() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDianyinPayService_HandleScanPayRefund(t *testing.T) {
	// 初始化数据库连接和session
	engine, err := xorm.NewEngine("mysql", "user:password@tcp(localhost:3306)/db_name?charset=utf8")
	if err != nil {
		t.Fatal(err)
	}
	session := engine.NewSession()
	defer session.Close()

	type fields struct {
		refundUrl   string
		callbackUrl string
	}
	type args struct {
		TradeNo      string
		RefundSn     string
		RefundAmount int
		OldOrderSn   string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *RefundResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				TradeNo:      "20250208143050734983785669497913",
				RefundSn:     "123456789",
				RefundAmount: 100,
				OldOrderSn:   "",
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := HandleScanPayRefund(session, tt.args.OldOrderSn, tt.args.RefundSn, tt.args.RefundAmount)
			if (err != nil) != tt.wantErr {
				t.Errorf("HandleScanPayRefund() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("HandleScanPayRefund() got = %v, want %v", got, tt.want)
			}
		})
	}
}
