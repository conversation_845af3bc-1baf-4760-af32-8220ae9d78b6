package payment

import (
	order_po "eShop/domain/order-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"encoding/json"
	"fmt"
	"time"
	"xorm.io/xorm"
)

type RefundRequest struct {
	//1：阿闻，2：子龙，3：R1，4：互联网,5：saas
	AppId int `json:"app_id"`
	//退款金额（分）
	RefundAmount int `json:"refund_amount" `
	//支付中心订单号
	TradeNo string `json:"trade_no"`
	//商户退款订单号
	RefundId string `json:"refund_id"`
	//签名
	Sign string `json:"sign"`
	//客户端 IP
	ClientIP string `json:"client_ip"`
	//后台回调地址
	NotifyUrl string `json:"notify_url"`
	//商户私有域
	BackParam string `json:"back_param"`
	//扩展信息
	ExtendInfo string `json:"extend_info"`
	//时间戳
	Timestamp int64 `json:"timestamp"`
}

type RefundResponse struct {
	Code    int                       `json:"code"`
	Message string                    `json:"message"`
	Data    StandardPayRefundResponse `json:"data"`
}

type StandardPayRefundResponse struct {
	//退款金额 单位：分
	RefundAmount int64 `json:"refund_amount"`
	//支付中心退款流水号
	RefundTradeNo string `json:"refund_trade_no"`
	//第三方退款流水号
	ThirdRefundNo string `json:"third_refund_no"`
	//结果 0：未退款 1：退款成功 2：退款处理中 3：退款失败
	Result string `json:"result"`
	//退款返回信息
	ResultMsg string `json:"result_msg"`
	//支付中心流水号
	TradeNo string `json:"trade_no"`
}

type RefundResult struct {
	State bool                      `json:"state"`
	Data  StandardPayRefundResponse `json:"data,omitempty"`
	Msg   string                    `json:"msg,omitempty"`
}

var (
	//RefundUrl   = config.Get("pay_echo_domain") + "/pay/pay-api/pay/refund"
	//CallbackUrl = config.Get("pay_echo_domain") + "/order-api/order/offlinenotify"
	RefundUrl   = "http://127.0.0.1:7035/pay/pay-api/pay/refund"
	CallbackUrl = "http://127.0.0.1:7040/order-api/order/offlinenotify"
	//RefundUrl   = "https://awen.uat.rvet.cn/pay/pay-api/pay/refund"
	//CallbackUrl = "https://awen.uat.rvet.cn/order-api/order/offlinenotify"
	// RefundUrl   = "http://127.0.0.1:7035/pay/pay-api/pay/refund"
	// CallbackUrl = "https://awen.uat.rvet.cn/order-api/order/offlinenotify"
	AppId = 7
)

// OrderRefund 处理退款请求
func OrderRefund(orderInfo map[string]interface{}) (response *RefundResponse, err error) {
	// 构建请求参数
	req := &RefundRequest{}
	// 检查退款单号
	if refundSn, ok := orderInfo["refund_sn"].(string); !ok || refundSn == "" {
		response.Message = "退款单号不能为空"
		return response, nil
	} else {
		req.RefundId = refundSn
	}

	// 设置其他参数
	req.TradeNo = orderInfo["trade_no"].(string)
	req.RefundAmount = orderInfo["refund_amount"].(int)
	req.NotifyUrl = CallbackUrl
	req.ClientIP = getClientIP()
	req.Timestamp = time.Now().UnixMilli()
	if extendInfo, ok := orderInfo["extendInfo"].(string); ok {
		req.ExtendInfo = extendInfo
	}

	if appId, ok := orderInfo["appId"].(int); ok {
		req.AppId = appId
	} else {
		req.AppId = AppId
	}
	jsonBytes, err := json.Marshal(req)
	if err != nil {
		return response, err
	}
	// 生成签名
	req.Sign, err = utils.MakeStandardPaySign(jsonBytes, int32(req.AppId)) // 默认普通商户类型
	if err != nil {
		return response, err
	}

	// 发送请求
	respBody, err := utils.HttpPost(RefundUrl, []byte(utils.JsonEncode(req)), "")
	if err != nil {
		return response, err
	}

	// 解析响应
	if err = json.Unmarshal(respBody, &response); err != nil {
		return nil, err
	}

	// 记录日志
	log.Info("订单退款", map[string]interface{}{
		"params": req,
		"res":    response,
		"action": "OrderRefund",
	})

	// 处理响应
	return response, nil
}

// 获取客户端IP
func getClientIP() string {
	return "127.0.0.1"
}

// HandleScanPayRefund 处理扫码支付退款
func HandleScanPayRefund(session *xorm.Session, ParentOrderSn, RefundSn string, RefundAmount int) (*RefundResponse, error) {
	// 1. 根据订单号获取TradeNo
	orderPayNotify := &order_po.OrderPayNotify{}
	TradeNo, err := orderPayNotify.GetTradeNoByOrderSn(session, ParentOrderSn)
	// 构建退款请求参数
	orderInfo := map[string]interface{}{
		"trade_no":      TradeNo,
		"refund_sn":     RefundSn,
		"refund_amount": RefundAmount,
	}

	log.Infof("扫码支付退款,请求参数：%v", orderInfo)
	// 调用退款接口
	result, err := OrderRefund(orderInfo)
	if err != nil {
		log.Errorf("扫码支付退款,参数: %v,异常：%s", orderInfo, err.Error())
		return nil, err
	}

	// 处理退款结果
	if result.Code != 200 {
		log.Errorf("扫码支付退款,参数: %v,失败：%s", orderInfo, utils.JsonEncode(result))
		return nil, fmt.Errorf("扫码支付退款,失败: %s", result.Message)
	}
	if result.Data.Result != "1" && result.Data.Result != "2" {
		log.Errorf("扫码支付退款,参数: %v,失败：%s", orderInfo, utils.JsonEncode(result))
		return nil, fmt.Errorf("扫码支付退款,失败: %s", result.Message)
	}
	// 检查退款状态
	if result.Data.Result != "1" && result.Data.Result != "2" {
		// 根据 Result 状态码给出具体的错误提示
		var statusMsg string
		switch result.Data.Result {
		case "0":
			statusMsg = "未退款"
		case "3":
			statusMsg = "退款失败"
		default:
			statusMsg = "未知状态"
		}

		errMsg := fmt.Sprintf("扫码支付退款失败 - 状态: %s, 原因: %s", statusMsg, result.Data.ResultMsg)
		log.Errorf("扫码支付退款,参数: %v, %s", orderInfo, errMsg)

		// 如果 ResultMsg 为空，则使用通用错误信息
		if result.Data.ResultMsg == "" {
			errMsg = fmt.Sprintf("扫码支付退款失败 - %s: %s", statusMsg, result.Message)
		}

		return nil, fmt.Errorf(errMsg)
	}
	return result, nil
}
