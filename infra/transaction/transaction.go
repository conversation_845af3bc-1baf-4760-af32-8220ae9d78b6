// infra/transaction/transaction.go
package transaction

import (
	"eShop/infra/errors"
	"eShop/services/common"

	"xorm.io/xorm"
)

// TransactionManager 事务管理器
type TransactionManager interface {
	Required(session *xorm.Session, fn func(*xorm.Session) error) error
	RequiresNew(session *xorm.Session, fn func(*xorm.Session) error) error
	NotSupported(session *xorm.Session, fn func(*xorm.Session) error) error
	DbDmMdm() *xorm.Engine
}

// transactionManager 事务管理器实现
type transactionManager struct {
	common.BaseService
}

// NewTransactionManager 创建事务管理器
func NewTransactionManager() TransactionManager {
	return &transactionManager{}
}

func (tm *transactionManager) DbDmMdm() *xorm.Engine {
	return tm.BaseService.DbDmMdm()
}

// Required 实现 REQUIRED 传播行为
func (tm *transactionManager) Required(session *xorm.Session, fn func(*xorm.Session) error) error {
	if session != nil && session.IsInTx() {
		return fn(session)
	}
	return tm.WithNewTransaction(fn)
}

// RequiresNew 实现 REQUIRES_NEW 传播行为
func (tm *transactionManager) RequiresNew(session *xorm.Session, fn func(*xorm.Session) error) error {
	return tm.WithNewTransaction(fn)
}

// NotSupported 实现 NOT_SUPPORTED 传播行为
// 这个方法确保提供的函数总是在一个非事务性的、新的 session 中运行。
// 它会忽略传入的 session 参数（如果存在），并总是从引擎创建一个新的 session。
func (tm *transactionManager) NotSupported(session *xorm.Session, fn func(*xorm.Session) error) error {
	// 确保引擎被初始化
	tm.Begin()
	defer tm.Close()

	// 忽略传入的 session，总是创建一个新的 session 来保证非事务性执行
	newSession := tm.Engine.NewSession()
	defer newSession.Close()

	return fn(newSession)
}

// WithNewTransaction 在新事务中执行
func (tm *transactionManager) WithNewTransaction(fn func(*xorm.Session) error) error {
	tm.Begin()
	defer tm.Close()

	session := tm.Engine.NewSession()
	if err := session.Begin(); err != nil {
		return errors.NewInternalError("开启事务失败: " + err.Error())
	}

	// 添加 panic 恢复
	defer func() {
		if r := recover(); r != nil {
			session.Rollback()
			panic(r) // 重新抛出 panic
		}
	}()

	if err := fn(session); err != nil {
		session.Rollback()
		return err
	}

	if err := session.Commit(); err != nil {
		tm.Session.Rollback()
		return errors.NewInternalError("提交事务失败: " + err.Error())
	}

	return nil
}
