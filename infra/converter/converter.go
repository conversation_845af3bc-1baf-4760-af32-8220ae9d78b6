package converter

import (
	"eShop/infra/utils"
	"reflect"
	"time"
)

// Convert 通用的对象转换函数
// 将源对象(source)转换为目标类型(T)的新实例
func Convert[T any](source interface{}) (T, error) {
	var target T
	if source == nil {
		return target, nil
	}

	sourceVal := reflect.ValueOf(source)
	targetVal := reflect.ValueOf(&target).Elem()

	// 如果源对象是指针，获取其指向的实际值
	if sourceVal.Kind() == reflect.Ptr {
		if sourceVal.IsNil() {
			return target, nil
		}
		sourceVal = sourceVal.Elem()
	}

	// 遍历目标结构体的字段
	for i := 0; i < targetVal.NumField(); i++ {
		targetField := targetVal.Field(i)
		targetFieldType := targetVal.Type().Field(i)

		// 在源结构体中查找同名字段
		if sourceField := sourceVal.FieldByName(targetFieldType.Name); sourceField.IsValid() {
			// 如果字段类型相同，直接赋值
			if sourceField.Type() == targetField.Type() {
				targetField.Set(sourceField)
			} else if sourceField.Type() == reflect.TypeOf(time.Time{}) && targetField.Type() == reflect.TypeOf("") {
				// 如果来源字段类型是time.Time，则转换为字符串
				targetField.Set(reflect.ValueOf(sourceField.Interface().(time.Time).Format("2006-01-02 15:04:05")))
			} else if targetField.Type() == reflect.TypeOf(time.Time{}) && sourceField.Type() == reflect.TypeOf("") {
				// 如果目标字段类型是time.Time，则尝试将字符串转换为time.Time
				time, err := utils.ParseTime(sourceField.Interface().(string))
				if err != nil {
					continue
				}
				targetField.Set(reflect.ValueOf(time))
			}
		} else {

		}
	}

	return target, nil
}

// ConvertSlice 转换切片中的所有对象
func ConvertSlice[T any](sources interface{}) ([]T, error) {
	var result []T

	if sources == nil {
		return result, nil
	}

	sourcesVal := reflect.ValueOf(sources)
	if sourcesVal.Kind() == reflect.Ptr {
		if sourcesVal.IsNil() {
			return result, nil
		}
		sourcesVal = sourcesVal.Elem()
	}

	if sourcesVal.Kind() != reflect.Slice {
		return result, nil
	}

	result = make([]T, sourcesVal.Len())
	for i := 0; i < sourcesVal.Len(); i++ {
		item, err := Convert[T](sourcesVal.Index(i).Interface())
		if err != nil {
			return nil, err
		}
		result[i] = item
	}

	return result, nil
}

// DeepConvert 深拷贝
func DeepConvert[T any](source interface{}) (T, error) {
	var target T
	if source == nil {
		return target, nil
	}

	sourceVal := reflect.ValueOf(source)
	targetVal := reflect.ValueOf(&target).Elem()

	// 如果源对象是指针，获取其指向的实际值
	if sourceVal.Kind() == reflect.Ptr {
		if sourceVal.IsNil() {
			return target, nil
		}
		sourceVal = sourceVal.Elem()
	}

	err := deepCopy(sourceVal, targetVal)
	if err != nil {
		return target, err
	}

	return target, nil
}

// deepCopy 递归处理深层复制
func deepCopy(sourceVal, targetVal reflect.Value) error {
	// 处理指针类型
	if targetVal.Kind() == reflect.Ptr {
		// 如果目标是nil指针，需要先初始化
		if targetVal.IsNil() {
			targetVal.Set(reflect.New(targetVal.Type().Elem()))
		}
		return deepCopy(sourceVal, targetVal.Elem())
	}

	if sourceVal.Kind() == reflect.Ptr {
		if sourceVal.IsNil() {
			return nil
		}
		return deepCopy(sourceVal.Elem(), targetVal)
	}

	// 遍历目标结构体的字段
	for i := 0; i < targetVal.NumField(); i++ {
		targetField := targetVal.Field(i)
		targetFieldType := targetVal.Type().Field(i)

		// 如果是嵌入式字段，需要特殊处理
		if targetFieldType.Anonymous {
			err := deepCopy(sourceVal, targetField)
			if err != nil {
				return err
			}
			continue
		}

		// 首先尝试直接从源结构体获取同名字段
		sourceField := findField(sourceVal, targetFieldType.Name)

		// 如果找到了对应的字段，进行转换
		if sourceField.IsValid() {
			err := copyField(sourceField, targetField)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// findField 在结构体中查找字段（包括嵌套字段）
func findField(val reflect.Value, fieldName string) reflect.Value {
	// 直接尝试获取字段
	field := val.FieldByName(fieldName)
	if field.IsValid() {
		return field
	}

	// 遍历所有字段寻找嵌入式结构体
	for i := 0; i < val.NumField(); i++ {
		embedField := val.Field(i)
		embedType := val.Type().Field(i)

		// 检查是否是嵌入式字段或者字段名匹配
		if embedType.Anonymous || embedType.Name == fieldName {
			var fieldVal reflect.Value

			// 处理指针类型的嵌入式字段
			if embedField.Kind() == reflect.Ptr {
				if embedField.IsNil() {
					continue
				}
				fieldVal = findField(embedField.Elem(), fieldName)
			} else if embedField.Kind() == reflect.Struct {
				// 先检查当前字段名是否匹配
				if embedType.Name == fieldName {
					return embedField
				}
				// 检查嵌入式结构体中是否有目标字段
				if f := embedField.FieldByName(fieldName); f.IsValid() {
					return f
				}
				// 如果没有，继续递归查找
				fieldVal = findField(embedField, fieldName)
			}

			if fieldVal.IsValid() {
				return fieldVal
			}
		}
	}

	return reflect.Value{}
}

// copyField 复制字段值
func copyField(sourceField, targetField reflect.Value) error {
	// 如果字段不可设置，直接返回
	if !targetField.CanSet() {
		return nil
	}

	sourceType := sourceField.Type()
	targetType := targetField.Type()

	// 处理基础类型的直接赋值
	if sourceType == targetType {
		targetField.Set(sourceField)
		return nil
	}

	// 处理指针类型
	if targetField.Kind() == reflect.Ptr {
		if targetField.IsNil() {
			targetField.Set(reflect.New(targetField.Type().Elem()))
		}
		return copyField(sourceField, targetField.Elem())
	}

	if sourceField.Kind() == reflect.Ptr {
		if sourceField.IsNil() {
			return nil
		}
		return copyField(sourceField.Elem(), targetField)
	}

	// 处理时间类型转换
	if targetType == reflect.TypeOf(time.Time{}) {
		if sourceType == reflect.TypeOf("") {
			timeStr := sourceField.String()
			if timeStr == "" {
				return nil
			}
			t, err := utils.ParseTime(timeStr)
			if err != nil {
				return err
			}
			targetField.Set(reflect.ValueOf(t))
			return nil
		}
	}

	// 处理字符串类型转换
	if sourceType == reflect.TypeOf(time.Time{}) && targetType == reflect.TypeOf("") {
		t := sourceField.Interface().(time.Time)
		if t.IsZero() {
			targetField.Set(reflect.ValueOf(""))
		} else {
			targetField.Set(reflect.ValueOf(t.Format("2006-01-02 15:04:05")))
		}
		return nil
	}

	// 处理结构体类型
	if sourceField.Kind() == reflect.Struct && targetField.Kind() == reflect.Struct {
		return deepCopy(sourceField, targetField)
	}

	return nil
}
