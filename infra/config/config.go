package config

import (
	"eShop/infra/httpx"
	"eShop/infra/security"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"time"
)

type SignInDataResponse struct {
	AppId    string `json:"app_id"`
	Token    string `json:"token"`
	SignText string `json:"sign_text"`
}

type Value struct {
	AppId         int       `json:"app_id"`
	AppName       string    `json:"app_name"`
	KeyId         int       `json:"key_id"`
	KeyName       string    `json:"key_name"`
	KeyValue      string    `json:"key_value"`
	VersionId     int       `json:"version_id"`
	EnvId         int       `json:"env_id"`
	EnvCode       string    `json:"env_code"`
	IsEncryption  bool      `json:"is_encryption"`
	LastVisitTime time.Time `json:"last_visit_time"`
}

var localConfig map[string]Value

var httpClient *httpx.HttpClient

var token string
var appId string

var envCode string

func InitConfig(address, appName, envName string) {
	httpClient = httpx.NewHttpClient(address)
	signIn(appName, envName)
}

func Get(key string) string {
	var result string
	//if httpClient == nil {
	result = config.GetString(key)
	//} else {
	//	result = getConfig(key)
	//}
	return result
}

func getConfig(key string) string {

	var heads = make(map[string]string)
	heads["Token"] = token
	responseTxt, err := httpClient.Get(fmt.Sprintf("config-service/get?app_name=%s&env_code=%s&key=%s", appId, envCode, key), heads)
	if err != nil {
		return ""
	}
	fmt.Println(responseTxt)
	return responseTxt
}

func signIn(appName, envName string) {
	var bodyStr = `{
			"app_id": "%s",
			"sign_text": "%s",
			"environment": "%s"
		}`

	var signText = security.SignData(appName)
	responseStr, err := httpClient.Post("config-service/sign", nil, fmt.Sprintf(bodyStr, appName, signText, envName))
	if err != nil {
		panic(err)
	}

	var sidResponse SignInDataResponse
	err = json.Unmarshal([]byte(responseStr), &sidResponse)
	if err != nil {
		panic(err)
	}

	check, err := security.Verify(security.MyPublicKey, sidResponse.Token, sidResponse.SignText)
	if err != nil {
		panic(err)
	}

	if !check {
		panic(errors.New("配置服务下发的token未通过验签"))
	}

	appId = appName
	envCode = envName
	token = sidResponse.Token
}
