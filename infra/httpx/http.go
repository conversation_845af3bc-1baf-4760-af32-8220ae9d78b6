package httpx

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"net/http"
)

type HttpClient struct {
	BaseUrl string
	client  *http.Client
}

// NewHttpClient creates a new HTTP client with custom settings.
func NewHttpClient(url string) *HttpClient {
	return &HttpClient{
		client:  &http.Client{},
		BaseUrl: url,
	}
}

// Get sends a GET request to the specified URL and returns response body.
func (h *HttpClient) Get(path string, heads map[string]string) (string, error) {
	req, err := http.NewRequest("GET", fmt.Sprintf("%s/%s", h.BaseUrl, path), nil)

	if len(heads) > 0 {
		for k, v := range heads {
			req.Header.Set(k, v)
		}
	}

	resp, err := h.client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// Post sends a POST request to the specified URL with provided body and returns response body.
func (h *HttpClient) Post(path string, heads map[string]string, body string) (string, error) {
	req, err := http.NewRequest("POST", fmt.Sprintf("%s/%s", h.BaseUrl, path), bytes.NewBuffer([]byte(body)))
	req.Header.Set("Content-Type", "application/json")

	if len(heads) > 0 {
		for k, v := range heads {
			req.Header.Set(k, v)
		}
	}

	resp, err := h.client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	resBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(resBody), nil
}
