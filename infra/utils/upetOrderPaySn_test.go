package utils

import "testing"

func TestUPetPaySN_Generate(t *testing.T) {
	type fields struct {
		MemberId int32
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				MemberId: 103931,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := UPetPaySN{
				MemberId: tt.fields.MemberId,
			}
			if got := s.Generate(); got != tt.want {
				t.<PERSON>("Generate() = %v, want %v", got, tt.want)
			}
		})
	}
}
