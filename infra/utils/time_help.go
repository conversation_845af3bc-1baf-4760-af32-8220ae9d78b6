package utils

import (
	"errors"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
)

const (
	DateTimeLayout  = "2006-01-02 15:04:05"
	DateTimeLayout2 = "2006/01/02 15:04:05"
	DateLayout      = "2006-01-02"
	DateLayout2     = "2006/01/02"
)

var ChinaLocation = time.FixedZone("Asia/Shanghai", 8*60*60)

// 获取当前标准格式时间
func GetTimeNow(time2 ...time.Time) string {
	if len(time2) == 0 {
		return time.Now().Format(DateTimeLayout)
	}

	return time2[0].Format(DateTimeLayout)
}

// 获取上个月的第一天和最后一天
func GetLastMonthFirstLast() (firstDay, lastDay string) {
	now := time.Now()
	lastMonth := now.AddDate(0, -1, 0)
	firstDay = lastMonth.Format(DateLayout)
	lastDay = now.Format(DateLayout)
	return
}

func GetLastCycleDate(start, end string) (s, e time.Time, err error) {
	sTime, err := time.ParseInLocation(DateLayout, start, time.Local)
	if err != nil {
		return
	}
	eTime, err := time.ParseInLocation(DateLayout, end, time.Local)
	if err != nil {
		return
	}

	// 备注 ： 结束日期是可以等于开始日期的
	if eTime.Before(sTime) {
		err = errors.New("结束日期不能小于开始日期")
		return
	}
	// 昨天
	if eTime.Equal(sTime) {
		s = sTime.AddDate(0, 0, -1)
		e = s
		return

	}
	// 上一周
	lastWeek := eTime.Sub(sTime)
	days := 6 * 24 * time.Hour
	if lastWeek == days {
		s = sTime.AddDate(0, 0, -7)
		e = eTime.AddDate(0, 0, -7)
		return
	}
	// 上一个月
	if eTime.Sub(sTime) > 6*24*time.Hour && eTime.Sub(sTime) < 35*24*time.Hour {
		s = time.Date(sTime.Year(), sTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
		e = sTime.AddDate(0, 0, -1)
		return
	}
	// 上一年
	if eTime.Sub(sTime) > 35*24*time.Hour {
		s = time.Date(sTime.Year()-1, 1, 1, 0, 0, 0, 0, time.Local)
		e = sTime.AddDate(0, 0, -1)
		return
	}

	// d := int(eTime.Sub(sTime).Hours()/24) + 1
	// s = sTime.AddDate(0, 0, -d)
	// e = sTime.Add(-1)
	return
}
func CalProportion(cur, last int) string {
	if last == 0 {
		return "--"
	}
	tmp, _ := decimal.NewFromFloat(float64(100 * (cur - last))).Div(decimal.NewFromFloat(float64(last))).Round(2).Float64()
	return fmt.Sprintf("%g%%", tmp)

}

// 获取统计日期
// 入参date: 日期,  （示例：2024-06-26）
func GetStatDate(start, end string) (s, e time.Time, err error) {

	if len(start) == 0 && len(end) == 0 {
		// 若入参日期为空， 则只需统计一天前的数据
		dayAgo := time.Now().AddDate(0, 0, -1).Format(DateLayout)
		dayAgoT, _ := time.ParseInLocation(DateLayout, dayAgo, time.Local)
		s, e = dayAgoT, dayAgoT
		return
	}
	if len(start) > 0 && len(end) == 0 {
		s, err = time.ParseInLocation(DateLayout, start, time.Local)
		if err != nil {
			return
		}
		e = s
		return
	}
	if len(start) > 0 && len(end) > 0 {
		s, err = time.ParseInLocation(DateLayout, start, time.Local)
		if err != nil {
			return
		}
		e, err = time.ParseInLocation(DateLayout, end, time.Local)
		if err != nil {
			return
		}
		if e.Before(s) {
			err = errors.New("结束日期不能小于开始日期")
			return
		}

	}
	return
}

// 查询最近N天日期
func GetDate(day int) (s, e string) {
	s = time.Now().AddDate(0, 0, -day).Format("2006-01-02")
	e = time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	return
}

// 获取当月的第一天日期和当月的最后一天
func GetMonthFirstLast() (firstDay, lastDay string) {
	thisYear, thisMonth, _ := time.Now().Date()
	// 当月的第一天
	firstTime, err := time.ParseInLocation(DateLayout, fmt.Sprintf("%d-%02d-01", thisYear, int(thisMonth)), time.Local)
	if err != nil {
		return
	}
	firstDay = firstTime.Format(DateLayout)
	// 下个月的第一天
	nextMonthFirst := firstTime.AddDate(0, +1, 0)
	lastDay = nextMonthFirst.Add(-1).Format(DateLayout)
	return
}

// 日期字符串转成时间戳
func Date2Timestamp(date string) int64 {
	t, _ := time.ParseInLocation(DateLayout, date, time.Local)
	return t.Unix()
}

func AddDate2Timestamp(datestr string) int64 {
	t, _ := time.ParseInLocation(DateLayout, datestr, time.Local)
	// 加一天
	t = t.AddDate(0, 0, 1)
	// 将时间对象转换为时间戳
	timestamp := t.Unix()
	return timestamp
}

// ParseTime 解析时间字符串为时间对象
func ParseTime(timeStr string) (time.Time, error) {
	return time.ParseInLocation("2006-01-02 15:04:05", timeStr, ChinaLocation)
}

// FormatTime 格式化时间对象为字符串
func FormatTime(t time.Time) string {
	// 判断时间是否为零值
	if t.IsZero() {
		return ""
	}
	return t.In(ChinaLocation).Format("2006-01-02 15:04:05")
}
