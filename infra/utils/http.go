package utils

import (
	"bytes"
	"crypto/md5"
	"crypto/tls"
	"eShop/infra/config"
	"eShop/infra/log"
	"eShop/infra/pkg/util/cache"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

const (
	tokenKey                = "offline:token"
	GetTokenUrl             = "/api/auth/anyone/getNewToken?userId=10001"
	OfflineProductUrl       = "/api/product/product"
	OfflineUpdateProductUrl = "/api/product/product/productUpdate"
	OfflineAddProductUrl    = "/api/product/product/productSave"
	OfflineDelByIdsUrl      = "/api/product/baseProduct/delByIdList"
)

func GetToken() (string, error) {
	// 尝试从 Redis 中获取 token
	redis := cache.GetRedisConn()
	token := redis.Get(tokenKey).Val()
	if token != "" {
		return token, nil
	}

	// Redis 中不存在 token，或者获取失败，则重新获取
	//增加请求次数，超过三次就不请求了
	// count := redis.Incr("offline_token_request_count").Val()
	// if count > 3 {
	// 	return "", errors.New("请求次数过多，请稍后再试")
	// }
	token, err := GetOfflineToken()
	if err != nil {
		return "", err
	}
	// 将新的 token 存储到 Redis 中，设置过期时间为 29 天
	redis.Set(tokenKey, token, 29*24*time.Hour)

	return token, nil
}

func GetOfflineToken() (string, error) {
	headers := "ApplicationId|1&Authorization|bGFtcF93ZWI6bGFtcF93ZWJfc2VjcmV0"
	respBody, err := HttpApi("GET", GetTokenUrl, headers, nil)
	log.Infof(fmt.Sprintf("请求新线下门店的获取token接口， 数据返回：%s", string(respBody)))
	if err != nil {
		log.Error("请求新线下门店的获取token接口，get请求失败，地址："+GetTokenUrl+",err=", err.Error())
		return "", err
	} else {
		var result map[string]interface{}
		err = json.Unmarshal(respBody, &result)
		if err != nil {
			log.Error("请求新线下门店的获取token接口,解析token失败，err=", err.Error())
			return "", err
		}
		if result["code"].(float64) == 0 {
			token := result["data"].(map[string]interface{})["token"].(string)
			return token, nil
		} else {
			log.Error("请求新线下门店的获取token接口，返回错误，地址："+GetTokenUrl+",err=", result["errorMsg"].(string))
			return "", errors.New(result["errorMsg"].(string))
		}
	}
}

// url ： /base/area/all
// dataJson : 数据对象转化成json字符串
func HttpPost(url string, dataJson []byte, Headers string) ([]byte, error) {
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(dataJson))
	client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	req.Header.Set("Content-Type", "application/json")

	if len(Headers) > 0 {
		strlist := strings.Split(Headers, "&")
		for i := 0; i < len(strlist); i++ {
			v := strlist[i]
			valuelist := strings.Split(v, "|")
			req.Header.Set(valuelist[0], valuelist[1])
		}
	}
	//for k, v := range BjSignMap(url) {
	//	req.Header.Set(k, v)
	//}

	res, err := client.Do(req)
	if err != nil {
		log.Error(err)
		return []byte(""), err
	}
	defer res.Body.Close()
	body, err := ioutil.ReadAll(res.Body)
	return body, err
}

// https://openapi.qcc.com/dataApi
// qcc:
//
//	appKey: 3d03e2879d9f4a39a764c685764eac1c
//	secret: 47C56D0AC436EA2FE9B7113F523F7851
//
// 根据社会信用代码查企查查 查出企业名称
func GetEnterpriseNameBySocialCreditCode(socialCreditCode string) (string, error) {
	// 构建请求URL和参数
	baseURL := "https://api.qichacha.com/EnterpriseInfo/Verify"
	appKey := "3d03e2879d9f4a39a764c685764eac1c"
	secretKey := "47C56D0AC436EA2FE9B7113F523F7851"

	// 生成时间戳和Token
	timespan := fmt.Sprintf("%d", time.Now().Unix())
	tokenStr := appKey + timespan + secretKey
	h := md5.New()
	h.Write([]byte(tokenStr))
	token := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))

	// 构建完整URL
	fullURL := fmt.Sprintf("%s?key=%s&searchKey=%s", baseURL, appKey, socialCreditCode)

	// 创建请求
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("Token", token)
	req.Header.Set("Timespan", timespan)
	log.Info(fmt.Sprintf("根据社会信用代码查企查查 查出企业名称 fullURL：%s, token：%s, timespan：%s", fullURL, token, timespan))

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	log.Info(fmt.Sprintf("根据社会信用代码查企查查 查出企业名称 响应：%s", string(body)))
	// 解析响应
	var result struct {
		Status  string `json:"Status"`
		Message string `json:"Message"`
		Result  struct {
			VerifyResult int `json:"VerifyResult"`
			Data         struct {
				Name string `json:"Name"`
			} `json:"Data"`
		} `json:"Result"`
	}

	if err := json.Unmarshal(body, &result); err != nil {
		return "", err
	}

	// 检查响应状态
	if result.Status != "200" {
		return "", fmt.Errorf("企查查接口调用失败: %s", result.Message)
	}

	// 检查验证结果
	if result.Result.VerifyResult != 1 {
		return "", errors.New("未找到企业信息")
	}

	return result.Result.Data.Name, nil
}

// 根据电话号码查询企业信息
func GetEnterpriseInfoByPhone(phone string) (string, error) {
	// 构建请求URL和参数
	baseURL := "https://api.qichacha.com/FuzzySearch/GetList"
	appKey := "3d03e2879d9f4a39a764c685764eac1c"
	secretKey := "47C56D0AC436EA2FE9B7113F523F7851"

	// 生成时间戳和Token
	timespan := fmt.Sprintf("%d", time.Now().Unix())
	tokenStr := appKey + timespan + secretKey
	h := md5.New()
	h.Write([]byte(tokenStr))
	token := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))

	// 构建完整URL
	fullURL := fmt.Sprintf("%s?key=%s&searchKey=%s", baseURL, appKey, phone)

	// 创建请求
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("Token", token)
	req.Header.Set("Timespan", timespan)
	log.Info(fmt.Sprintf("根据电话号码查询企业信息 fullURL：%s, token：%s, timespan：%s", fullURL, token, timespan))

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	log.Info(fmt.Sprintf("根据电话号码查询企业信息响应：%s", string(body)))

	// 解析响应
	var result struct {
		Status  string `json:"Status"`
		Message string `json:"Message"`
		Result  struct {
			Result []struct {
				Name       string `json:"Name"`
				CreditCode string `json:"CreditCode"`
				OperName   string `json:"OperName"`
				Status     string `json:"Status"`
				Address    string `json:"Address"`
			} `json:"Result"`
		} `json:"Result"`
	}

	if err := json.Unmarshal(body, &result); err != nil {
		return "", err
	}

	// 检查响应状态
	if result.Status != "200" {
		return "", fmt.Errorf("企查查接口调用失败: %s", result.Message)
	}

	// 检查是否有查询结果
	if len(result.Result.Result) == 0 {
		return "", errors.New("未找到企业信息")
	}

	// 返回第一个匹配的企业信息
	enterprise := result.Result.Result[0]
	return enterprise.Name, nil
	// return fmt.Sprintf("企业名称：%s\n统一社会信用代码：%s\n法定代表人：%s\n企业状态：%s\n注册地址：%s",
	// 	enterprise.Name,
	// 	enterprise.CreditCode,
	// 	enterprise.OperName,
	// 	enterprise.Status,
	// 	enterprise.Address), nil
}

func BjSignMap(url string) map[string]string {
	domainUrl := strings.Split(url, "//")[1]
	baseUrl := strings.Split(domainUrl, "/")[0]
	method := strings.Split(url, baseUrl)[1]
	Timestamp := strconv.Itoa(int(time.Now().Unix()))
	sign := fmt.Sprintf("AppId=%s&Secret=%s&Url=%s&Timestamp=%s&Version=%s", config.Get("bj.auth.appid"), config.Get("bj.auth.secret"), method, Timestamp, config.Get("bj.auth.version"))
	h := md5.New()
	h.Write([]byte(sign))
	md5sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	arr := make(map[string]string)
	arr["focus-auth-appid"] = config.Get("bj.auth.appid")
	arr["focus-auth-userid"] = "0"
	arr["focus-auth-username"] = "0"
	arr["focus-auth-version"] = config.Get("bj.auth.version")
	arr["focus-auth-url"] = method
	arr["focus-auth-timestamp"] = Timestamp
	arr["focus-auth-sign"] = md5sign
	return arr
}

func HttpGet(url string, Headers string, MapHeaders map[string]string) ([]byte, error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	if len(Headers) > 0 {
		strList := strings.Split(Headers, "&")
		for i := 0; i < len(strList); i++ {
			v := strList[i]
			valueList := strings.Split(v, "|")
			req.Header.Set(valueList[0], valueList[1])
		}
	}
	//循环处理请求头信息
	for k, v := range MapHeaders {
		req.Header.Set(k, v)
	}
	client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	res, err := client.Do(req)
	if err != nil {
		log.Info(err)
		return []byte(""), err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	return body, err
}

// HttpApi performs an HTTP request with various options
func HttpApi(method, url, Headers string, body interface{}) ([]byte, error) {
	logPrefix := fmt.Sprintf("HttpApi:url=%s，method=%s", url, method)

	log.Infof("%s, Headers=%s, body=%s", logPrefix, Headers, JsonEncode(body))
	var reqBody []byte
	var err error

	if body != nil {
		switch t := body.(type) {
		case string:
			reqBody = []byte(t)
		case []byte:
			reqBody = t
		case map[string]interface{}:
			reqBody, err = json.Marshal(body)
			if err != nil {
				return nil, err
			}
		default:
			return nil, errors.New("unsupported body type")
		}
	}
	apiUrl := config.Get("offline_url")
	if apiUrl == "" {
		apiUrl = "https://pets-saas-pre.rvet.cn"
	}
	newUrl := fmt.Sprintf("%s%s", apiUrl, url)
	req, err := http.NewRequest(method, newUrl, bytes.NewReader(reqBody))
	if err != nil {
		log.Error(err)
		return nil, err
	}

	// Set headers
	req.Header.Set("ApplicationId", "1")
	req.Header.Set("Authorization", "bGFtcF93ZWI6bGFtcF93ZWJfc2VjcmV0")
	if len(Headers) > 0 {
		strList := strings.Split(Headers, "&")
		for i := 0; i < len(strList); i++ {
			v := strList[i]
			valueList := strings.Split(v, "|")
			req.Header.Set(valueList[0], valueList[1])
		}
	}
	// 优先拿header头的门店id\连锁id,没有头就拿token
	if url != GetTokenUrl {
		token, err := GetToken()
		if err != nil {
			log.Error(logPrefix, "获取token错误：", err.Error())
			return nil, err
		}
		req.Header.Set("token", token)
	}

	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{Timeout: time.Second * 15, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	res, err := client.Do(req)
	log.Info(logPrefix, "数据返回：", JsonEncode(res))
	if err != nil {
		log.Info(logPrefix, err)
		return nil, err
	}
	defer res.Body.Close()

	return ioutil.ReadAll(res.Body)
}

// 上传至七牛云
func UploadToQiNiu(bodyByte []byte) (url string, err error) {
	body_buf := bytes.NewBuffer(bodyByte)

	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)

	fileName := strconv.Itoa(int(time.Now().UnixNano())) + "_小程序码.png"
	fileWriter, _ := bodyWriter.CreateFormFile("file", fileName)

	io.Copy(fileWriter, body_buf)
	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()

	// 上传文件
	path := config.Get("file-upload-url") + "/fss/up"
	resp, err := http.Post(path, contentType, bodyBuffer)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	resp_body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	var result uploadResult
	err = json.Unmarshal(resp_body, &result)
	if err != nil {
		return "", err
	}
	if len(result.Url) == 0 {
		return "", errors.New(result.Err)
	}
	return result.Url, nil
}

// HttpPostForm 发送表单格式的 POST 请求
func HttpPostForm(urlStr string, data url.Values) ([]byte, error) {
	// 创建请求
	req, err := http.NewRequest("POST", urlStr, strings.NewReader(data.Encode()))
	client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	if err != nil {
		return nil, err
	}
	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	// 读取响应
	return io.ReadAll(resp.Body)
}
