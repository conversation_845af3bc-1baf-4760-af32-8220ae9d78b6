package utils

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

// AliExpress 阿里云物流查询配置
type AliExpress struct {
	AppCode string // API密钥
	Host    string // API域名
	Path    string // API路径
}

// NewAliExpress 创建阿里云物流查询实例
func NewAliExpress() *AliExpress {
	return &AliExpress{
		AppCode: "f49313bfe47545a99ea301b6de278bf2",
		Host:    "https://wuliu.market.alicloudapi.com",
		Path:    "/kdi",
	}
}

// ExpressResult 物流查询结果
type ExpressResult struct {
	Status string       `json:"status"` // 0:正常查询 201:快递单号错误 203:快递公司不存在 204:快递公司识别失败 205:没有信息 207:该单号被限制
	Msg    string       `json:"msg"`
	Result *ExpressInfo `json:"result"`
}

type ExpressInfo struct {
	Number         string      `json:"number"`         // 快递单号
	Type           string      `json:"type"`           // 快递公司编码
	List           []TrackInfo `json:"list"`           // 物流轨迹
	DeliveryStatus string      `json:"deliverystatus"` // 0：快递收件 1：在途中 2：正在派件 3：已签收 4：派送失败 5：疑难件 6：退件签收
	IsSign         string      `json:"issign"`         // 1：是否签收
	ExpName        string      `json:"expName"`        // 快递公司名称
	ExpSite        string      `json:"expSite"`        // 快递公司官网
	ExpPhone       string      `json:"expPhone"`       // 快递公司电话
	Courier        string      `json:"courier"`        // 快递员或快递站
	CourierPhone   string      `json:"courierPhone"`   // 快递员电话
	UpdateTime     string      `json:"updateTime"`     // 快递轨迹信息最新时间
	TakeTime       string      `json:"takeTime"`       // 发货到收货消耗时长
	Logo           string      `json:"logo"`           // 快递公司LOGO
}

// TrackInfo 物流轨迹信息
type TrackInfo struct {
	Time   string `json:"time"`   // 轨迹时间
	Status string `json:"status"` // 轨迹信息
}

// QueryExpress 查询物流信息
// compnayCode: 快递公司编码
// expressCode: 快递单号
// phoneEnd: 手机号后四位
func (a *AliExpress) QueryExpress(compnayCode, expressNo, phoneEnd string) (*ExpressResult, error) {
	// 处理快递单号
	expressNo = fmt.Sprintf("%s:%s", strings.TrimSpace(expressNo), phoneEnd)

	// 转换快递公司代码
	compnayCode = convertExpressCode(compnayCode)

	// 构建请求URL
	query := fmt.Sprintf("no=%s&type=%s", expressNo, compnayCode)
	url := fmt.Sprintf("%s%s?%s", a.Host, a.Path, query)

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %v", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "APPCODE "+a.AppCode)

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		if strings.Contains(err.Error(), "no such host") {
			return nil, fmt.Errorf("URL地址错误: %v", err)
		}
		return nil, fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return nil, a.handleAPIError(resp)
	}

	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response failed: %v", err)
	}

	// 解析响应数据
	var result ExpressResult
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("parse response failed: %v, body: %s", err, string(body))
	}

	return &result, nil
}

// convertExpressCode 转换快递公司代码
func convertExpressCode(code string) string {
	codeMap := map[string]string{
		"SF":   "SFEXPRESS",
		"YD":   "YUNDA",
		"DBL":  "DEPPON",
		"XFEX": "XFEXPRESS",
		"UC":   "UC56",
	}
	if newCode, ok := codeMap[code]; ok {
		return newCode
	}
	return code
}

// APIError API错误信息
type APIError struct {
	Code    int    // HTTP状态码
	Message string // 错误信息
}

func (e *APIError) Error() string {
	return fmt.Sprintf("API Error (HTTP %d): %s", e.Code, e.Message)
}

// handleAPIError 处理API错误
func (a *AliExpress) handleAPIError(resp *http.Response) error {
	errorMsg := resp.Header.Get("X-Ca-Error-Message")
	if errorMsg == "" {
		return &APIError{
			Code:    resp.StatusCode,
			Message: "Unknown API error",
		}
	}

	var msg string
	switch {
	case resp.StatusCode == 400 && errorMsg == "Invalid AppCode `not exists`":
		msg = "AppCode错误"
	case resp.StatusCode == 400 && errorMsg == "Invalid Url":
		msg = "请求的Method、Path或者环境错误"
	case resp.StatusCode == 400 && errorMsg == "Invalid Param Location":
		msg = "参数错误"
	case resp.StatusCode == 403 && errorMsg == "Unauthorized":
		msg = "服务未被授权（或URL和Path不正确）"
	case resp.StatusCode == 403 && errorMsg == "Quota Exhausted":
		msg = "套餐包次数用完"
	case resp.StatusCode == 403 && errorMsg == "Api Market Subscription quota exhausted":
		msg = "套餐包次数用完，请续购套餐"
	default:
		msg = fmt.Sprintf("其他错误: %s", errorMsg)
	}

	return &APIError{
		Code:    resp.StatusCode,
		Message: msg,
	}
}
