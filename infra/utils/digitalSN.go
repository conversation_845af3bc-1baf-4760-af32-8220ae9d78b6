package utils

import (
	"fmt"
	"math/rand"
	"time"

	"github.com/spf13/cast"
)

type digitalSN struct {
}

const (
	// 定义字符集，去掉了容易混淆的字符(0,O,1,I,l等)
	charset = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ"
)

// GenerateRandomString 生成指定长度的随机字符串
// 通过组合时间戳、随机数和自定义字符集来确保高唯一性
func GenerateRandomString(length int) string {
	// 获取当前时间戳(纳秒级)作为种子
	timestamp := time.Now().UnixNano()

	// 使用时间戳作为种子初始化随机数生成器
	source := rand.NewSource(timestamp)
	r := rand.New(source)

	// 创建一个字节切片来存储结果
	result := make([]byte, length)

	// 前4位使用时间戳的后4位(转换为字符集对应的字符)
	timeBytes := []byte(fmt.Sprintf("%d", timestamp))
	for i := 0; i < 4 && i < length; i++ {
		timeIndex := timeBytes[len(timeBytes)-4+i] % byte(len(charset))
		result[i] = charset[timeIndex]
	}

	// 剩余位数使用随机字符填充
	for i := 4; i < length; i++ {
		result[i] = charset[r.Intn(len(charset))]
	}

	// 随机打乱整个字符串
	for i := len(result) - 1; i > 0; i-- {
		j := r.Intn(i + 1)
		result[i], result[j] = result[j], result[i]
	}

	return string(result)
}

// GenerateRandomInt 生成指定范围内的随机整数
// 通过组合时间戳和随机数来确保高唯一性
// min和max定义了生成随机数的范围（包含）
func GenerateRandomInt(min, max int64) int64 {
	// min = 1000000000
	// max = 9999999999
	// 获取当前时间戳(纳秒级)作为种子
	timestamp := time.Now().UnixNano()

	// 使用时间戳作为种子初始化随机数生成器
	source := rand.NewSource(timestamp)
	r := rand.New(source)

	// 确保min小于max
	if min > max {
		min, max = max, min
	}

	// 生成指定范围内的随机数
	// 使用时间戳的后几位作为基础值
	baseNum := timestamp % 1000000 // 取时间戳的后6位

	// 将基础值映射到指定范围内
	rangeSize := max - min + 1
	result := min + (baseNum+int64(r.Int63n(rangeSize)))%rangeSize

	return result
}

func (s digitalSN) Generate() string {
	rand.Seed(int64(HashInt(GetGuid32())))
	return cast.ToString(rand.Intn(999999999) + 1000000000)
}
