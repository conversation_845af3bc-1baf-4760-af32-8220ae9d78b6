package utils

import (
	"bytes"
	"eShop/infra/config"
	"eShop/infra/pkg/util/cache"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"xorm.io/xorm"
)

// UploadFile 上传文件需要的信息
// 文件路径方法 reader, err := os.Open(fileName)
// excel方式 b,err := file.WriteToBuffer();reader = bytes.NewReader(b.Bytes())
type UploadFile struct {
	Name   string
	Reader io.Reader
}

// UploadQiNiuResponse 上传到七牛响应
type UploadQiNiuResponse struct {
	FileName string
	Size     int
	Url      string
	Error    string
}

// UploadExcelToQiNiu 上传excel文件到七牛云 流的方式
func UploadExcelToQiNiu(file *excelize.File, name string) (url string, err error) {
	b, err := file.WriteToBuffer()
	if err != nil {
		return
	}
	if len(name) < 1 {
		name = GetGuid36() + ".xlsx"
	}
	uf := &UploadFile{
		Name:   name,
		Reader: bytes.NewReader(b.Bytes()),
	}

	return uf.ToQiNiu()
}

// ToQiNiu 上传文件到七牛云
func (uf *UploadFile) ToQiNiu() (url string, err error) {
	if len(uf.Name) < 1 {
		return "", errors.New("文件名称不能为空")
	}

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, _ := writer.CreateFormFile("file", uf.Name)
	if _, err = io.Copy(part, uf.Reader); err != nil {
		return
	}
	if err = writer.Close(); err != nil {
		return
	}

	//TODO 先用马元彪的，等封装好后改
	host := config.Get("file-upload-url")
	if len(host) == 0 {
		host = "https://api.rp-pet.com"
	}
	// host = "http://awen.uat.rvet.cn"
	httpResp, err := http.Post(host+"/fss/newup", writer.FormDataContentType(), body)
	if err != nil {
		return "", errors.New(host + "/fss/newup " + "请求出错 " + err.Error())
	}
	defer httpResp.Body.Close()

	resBody, err := ioutil.ReadAll(httpResp.Body)
	if err != nil {
		return
	}

	res := new(UploadQiNiuResponse)
	if err = json.Unmarshal(resBody, res); err != nil {
		return "", errors.New("解析响应body出错 " + err.Error())
	}
	if httpResp.StatusCode >= 400 {
		if len(res.Error) == 0 {
			res.Error = httpResp.Status
		}
		return "", errors.New("请求出错 " + res.Error)
	}

	res.Url = strings.Replace(res.Url, "http://", "https://", 1)

	return res.Url, nil
}

// 七牛上传结果
type QiNiuUploadResult struct {
	Url string `json:"url"`
	Err string `json:"error"`
}

// 生成32位Guid字符串
func GetGuid32() string {
	return strings.ReplaceAll(GetGuid36(), "-", "")
}

// 生成36位Guid字符串
func GetGuid36() string {
	return uuid.New().String()
}

// 将处理失败的信息导入excel上传至七牛云
func ExportHandleErr(errList [][]string) (url string, err error) {
	f := excelize.NewFile()

	for i, list := range errList {
		for j, e := range list {
			colName := ""
			if j < 26 {
				colName = string(rune(65 + j))
			} else {
				first := string(rune(65 + j/26 - 1))
				second := string(rune(65 + j%26))
				colName = first + second
			}
			f.SetCellValue("Sheet1", colName+strconv.Itoa(i+1), e)
		}
	}
	f.Save()
	file, err := f.WriteToBuffer()
	if err != nil {
		return "", err
	}

	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)

	fileName := cast.ToString(time.Now().UnixNano()) + "_错误导出.xlsx"
	fileWriter, _ := bodyWriter.CreateFormFile("file", fileName)
	io.Copy(fileWriter, file)
	//测试地址
	//path := "https://awen.uat.rvet.cn/fss/up"
	path := config.Get("file-upload-url") + "/fss/up"

	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()
	// 上传文件
	resp, _ := http.Post(path, contentType, bodyBuffer)

	defer resp.Body.Close()

	resp_body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	var result uploadResult
	err = json.Unmarshal(resp_body, &result)
	if err != nil {
		return "", err
	}
	if len(result.Url) == 0 {
		return "", errors.New(result.Err)
	}
	return result.Url, nil
}

type uploadResult struct {
	Url string `json:"url"`
	Err string `json:"error"`
}

// 生成各种编号（订单，售后单，etc...）
func GetSn(kind string, db *xorm.Engine, number ...int) []string {
	loop := 1
	if len(number) > 0 && number[0] > 0 {
		loop = number[0]
	}
	var snArr []string
	redis := cache.GetRedisConn()
	for i := 0; i < loop; i++ {
		snArr = append(snArr, NewSN(kind, db, redis).Generate())
	}

	return snArr
}

// GetPickupCode 获取取货码
// storeId: 门店ID
func GetPickupCode(storeId string) string {
	redis := cache.GetRedisConn()
	today := time.Now().Format("20060102")
	keyCode := fmt.Sprintf("store:pickup:%s:%s", storeId, today)
	// 使用 INCR 命令原子性地获取自增数字
	num := redis.Incr(keyCode).Val()
	if num == 1 {
		// 如果是第一次设置，设置24小时过期时间
		redis.Expire(keyCode, 24*time.Hour)
	}

	// 格式化为4位数字，不足前面补0
	return cast.ToString(num)
}

func GenerateCardNo() string {
	redis := cache.GetRedisConn()
	keyCode := "card:number"
	// 使用 INCR 命令原子性地获取自增数字
	num := redis.Incr(keyCode).Val()
	// 格式化为16位数字，不足前面补0
	numStr := cast.ToString(num)
	for len(numStr) < 16 {
		numStr = "0" + numStr
	}
	numStr = "1" + numStr
	return cast.ToString(numStr)
}
