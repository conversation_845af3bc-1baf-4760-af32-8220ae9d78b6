package utils

import (
	"crypto/sha1"
	"eShop/infra/log"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
	"unicode"
)

type FeieYunService struct {
}

//https://help.feieyun.com/home/<USER>/zh;nav=1-1
//var (
//	USER = "xxxxxxxxx" //必填，飞鹅云后台注册的账号名
//	UKEY = "xxxxxxxxx"  //必填，飞鹅云后台注册账号后生成的UKEY
//	//SN = "xxxxxxxxx"      //必填，打印机编号，必须要在管理后台里手动添加打印机或者通过API添加之后，才能调用API
//	URL = "http://api.feieyun.cn/Api/Open/" //不需要修改
//)

type PrintOrderResponse struct {
	Msg                string `json:"msg"`
	Ret                int    `json:"ret"`
	Data               string `json:"data"`
	ServerExecutedTime int    `json:"serverExecutedTime"`
}

type AddprinterResponse struct {
	Msg  string `json:"msg"`
	Ret  int    `json:"ret"`
	Data struct {
		Ok      []interface{} `json:"ok"`
		No      []string      `json:"no"`
		NoGuide []interface{} `json:"noGuide"`
	} `json:"data"`
	ServerExecutedTime int `json:"serverExecutedTime"`
}

type FeieyunConfig struct {
	User string
	Ukey string
	Url  string
}

func (f FeieYunService) InitFeieyunConfig() FeieyunConfig {
	return FeieyunConfig{
		User: config.GetString("feieyun_user"),
		Ukey: config.GetString("feieyun_ukey"),
		Url:  config.GetString("feieyun_url"),
	}
}

func (f FeieYunService) Addprinter(snlist string) (err error) {

	config := f.InitFeieyunConfig()
	itime := time.Now().Unix()
	stime := strconv.FormatInt(itime, 10)
	sig := f.SHA1(config.User + config.Ukey + stime) //生成签名

	client := http.Client{}
	postValues := url.Values{}
	postValues.Add("user", config.User)              //账号名
	postValues.Add("stime", stime)                   //当前时间的秒数，请求时间
	postValues.Add("sig", sig)                       //签名
	postValues.Add("apiname", "Open_printerAddlist") //固定
	postValues.Add("printerContent", snlist)         //打印机

	res, _ := client.PostForm(config.Url, postValues)
	data, _ := ioutil.ReadAll(res.Body)
	fmt.Println(string(data)) //服务器返回的JSON字符串，建议要当做日志记录起来
	res.Body.Close()

	var addprinterResponse AddprinterResponse
	if err = json.Unmarshal(data, &addprinterResponse); err != nil {
		return
	}

	if addprinterResponse.Ret != 0 {
		return errors.New(addprinterResponse.Msg)
	}

	if len(addprinterResponse.Data.No) > 0 {
		return errors.New(addprinterResponse.Data.No[0])
	}

	return nil
}

func (f FeieYunService) SHA1(str string) string {
	s := sha1.Sum([]byte(str))
	strsha1 := hex.EncodeToString(s[:])
	return strsha1
}

// 将字符串s分割成每个元素最多包含n长度单位
func (f FeieYunService) SplitStringByNum(s string, n int) (str []string) {
	sNum := 0
	sStr := ""

	for k, r := range s {
		// 检查字符是否是中文字符,一些特殊的符号也算占二个长度单位
		if unicode.Is(unicode.Scripts["Han"], r) || strings.Contains("（）【】", string(r)) {
			// 如果是中文字符，则视为2个长度单位
			sNum += 2
		} else {
			// 如果不是中文字符，则视为1个长度单位
			sNum += 1
		}
		sStr += string(r)

		if sNum >= n || k == len(s)-1 {
			str = append(str, sStr)
			sNum = 0
			sStr = ""
		}

	}
	if sStr != "" {
		str = append(str, sStr)
	}

	return
}

func (f FeieYunService) PrintOrder(sn string, content string) (err error) {
	//标签说明：
	//单标签:
	//"<BR>"为换行,"<CUT>"为切刀指令(主动切纸,仅限切刀打印机使用才有效果)
	//"<LOGO>"为打印LOGO指令(前提是预先在机器内置LOGO图片),"<PLUGIN>"为钱箱或者外置音响指令
	//成对标签：
	//"<CB></CB>"为居中放大一倍,"<B></B>"为放大一倍,"<C></C>"为居中,<L></L>字体变高一倍
	//<W></W>字体变宽一倍,"<QR></QR>"为二维码,"<BOLD></BOLD>"为字体加粗,"<RIGHT></RIGHT>"为右对齐
	//拼凑订单内容时可参考如下格式
	//根据打印纸张的宽度，自行调整内容的格式，可参考下面的样例格式

	//content := "<CB>测试打印</CB><BR>"
	//content += "名称　　　　　 单价  数量 金额<BR>"
	//content += "--------------------------------<BR>"
	//content += "饭　　　　　 　10.0   10  10.0<BR>"
	//content += "炒饭　　　　　 10.0   10  10.0<BR>"
	//content += "蛋炒饭　　　　 10.0   100 100.0<BR>"
	//content += "鸡蛋炒饭　　　 100.0  100 100.0<BR>"
	//content += "西红柿炒饭　　 1000.0 1   100.0<BR>"
	//content += "西红柿蛋炒饭　 100.0  100 100.0<BR>"
	//content += "西红柿鸡蛋炒饭 15.0   1   15.0<BR>"
	//content += "备注：加辣<BR>"
	//content += "--------------------------------<BR>"
	//content += "合计：xx.0元<BR>"
	//content += "送货地点：广州市南沙区xx路xx号<BR>"
	//content += "联系电话：13888888888888<BR>"
	//content += "订餐时间：2014-08-08 08:08:08<BR>"
	//content += "<QR>http://www.dzist.com</QR>"

	config := f.InitFeieyunConfig()
	itime := time.Now().Unix()
	stime := strconv.FormatInt(itime, 10)
	sig := f.SHA1(config.User + config.Ukey + stime) //生成签名

	client := http.Client{}
	postValues := url.Values{}
	postValues.Add("user", config.User)        //账号名
	postValues.Add("stime", stime)             //当前时间的秒数，请求时间
	postValues.Add("sig", sig)                 //签名
	postValues.Add("apiname", "Open_printMsg") //固定
	postValues.Add("sn", sn)                   //打印机编号
	postValues.Add("content", content)         //打印内容
	postValues.Add("times", "1")               //打印次数

	res, _ := client.PostForm(config.Url, postValues)
	data, _ := ioutil.ReadAll(res.Body)
	//fmt.Println(string(data)) //服务器返回的JSON字符串，建议要当做日志记录起来
	log.Info(fmt.Sprintf("飞鹅云打印,订单号：%s,返回内容是：%s", sn, string(data)))
	res.Body.Close()

	var printOrderResponse PrintOrderResponse
	if err = json.Unmarshal(data, &printOrderResponse); err != nil {
		return
	}

	if printOrderResponse.Ret != 0 {
		return errors.New(printOrderResponse.Msg)
	}

	return
}
