package utils

import (
	"eShop/infra/cache"
	"eShop/infra/config"
	"eShop/infra/log"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"eShop/view-model"
	"errors"
	"fmt"
	"math/rand"
	"strings"
	"time"

	openapi "github.com/alibabacloud-go/darabonba-openapi/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v2/client"
	"github.com/spf13/cast"
)

const (
	SmsSignName              = "润合云店"
	RpMillionsVerifyCodeKey  = "RpMillions:SmsVerifyCode:"
	RpMillionsVerifyCountKey = "RpMillions:SmsVerifyCodeCount:"
)

// 创建阿里云短信发送客户端
func newAliyunSmsClient() (*dysmsapi20170525.Client, error) {
	conf := &openapi.Config{}
	key := config.Get("SmsAccessKeyId")
	secret := config.Get("SmsAccessKeySecret")
	conf.SetAccessKeyId(key)
	conf.SetAccessKeySecret(secret)
	return dysmsapi20170525.NewClient(conf)
}

// 发送阿里云短信
func sendAliyunSms(phoneNumbers, signName, templateCode, templateParam string) (*dysmsapi20170525.SendSmsResponse, error) {
	client, err := newAliyunSmsClient()
	if err != nil {
		log.Error("创建阿里云短信客户端失败，error: ", err.Error())
		return nil, err
	}
	sendSmsRequest := &dysmsapi20170525.SendSmsRequest{}
	sendSmsRequest.SetPhoneNumbers(phoneNumbers)
	sendSmsRequest.SetSignName(signName)
	sendSmsRequest.SetTemplateCode(templateCode)
	sendSmsRequest.SetTemplateParam(templateParam)
	re, err := client.SendSms(sendSmsRequest)
	if err != nil {
		log.Error("发送短信失败，error: ", err.Error())
		return nil, err
	}
	if re.Body == nil {
		log.Error("发送短信失败，respBody: ", re.Body)
		return nil, errors.New("发送短信失败 body nil")
	}
	return re, err
}

func CheckCode(mobile, code string, org_id string) error {

	isOpen := cast.ToInt32(config.Get("is_open_master_key"))

	if code == "688123" && isOpen == 1 {
		return nil
	}
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])

	codeKey := RpMillionsVerifyCodeKey + org_id + ":" + mobile //
	getcode := mCache.Get("", codeKey)

	if getcode[0] != code {
		return errors.New("验证码错误")
	}
	// 通过验证则删除
	mCache.Delete("", codeKey)
	return nil
}

func SendCode(par viewmodel.BaseUserRequest) error {

	templateCode := "SMS_465337627"

	//redis := cache.GetRedisConn()
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])

	//获取次数限制当天内10次
	checkCountKey := RpMillionsVerifyCountKey + cast.ToString(par.OrgId) + ":" + par.Mobile //
	todayRemainSecond := TodayRemainSecond()
	intSecond := time.Duration(todayRemainSecond)
	setOk := mCache.TrySave("", checkCountKey, 1, time.Second*intSecond)
	var count int64 = 1
	if !setOk {
		mCache.AtomicIncr("", checkCountKey)

		val := mCache.Get("", checkCountKey)
		count = cast.ToInt64(val[0])
		if count >= 10 {
			return errors.New("频繁获取验证码，24小时后再尝试")
		}
	}

	// 发送验证码
	code := GenValidateCode(6)
	templateParam := fmt.Sprintf(`{"code":"%s"}`, code)
	re, err := sendAliyunSms(par.Mobile, SmsSignName, templateCode, templateParam)
	if err != nil {
		log.Error("发送短信失败err", err)
		return err
	}
	if *(re.Body.Code) != "OK" {
		if *(re.Body.Code) == "isv.BUSINESS_LIMIT_CONTROL" {
			errors.New("您发送的太频繁，请稍后再发")
		} else {
			errors.New("发送失败")
			log.Error("发送短信失败，", *(re.Body.Code), "，err：", *(re.Body.Message), "，templateParam：", templateParam)
		}
		return errors.New("发送失败")
	}
	log.Infof("发送短信验证码成功:%s %v", par.Mobile, *(re.Body.Message))
	codeKey := RpMillionsVerifyCodeKey + cast.ToString(par.OrgId) + ":" + par.Mobile
	mCache.Save("", codeKey, code, time.Duration(20)*time.Minute)
	if setOk {
		mCache.AtomicIncr("", checkCountKey) //次数加1
	}
	return nil
}

// 随机验证码
func GenValidateCode(width int) string {
	numeric := [10]byte{0, 1, 2, 3, 4, 5, 6, 7, 8, 9}
	r := len(numeric)
	rand.Seed(time.Now().UnixNano())

	var sb strings.Builder
	for i := 0; i < width; i++ {
		fmt.Fprintf(&sb, "%d", numeric[rand.Intn(r)])
	}
	return sb.String()
}

// 获取当天结束剩余秒数
func TodayRemainSecond() float64 {
	todayLast := time.Now().Format("2006-01-02") + " 23:59:59"

	todayLastTime, _ := time.ParseInLocation("2006-01-02 15:04:05", todayLast, time.Local)

	remainSecond := time.Duration(todayLastTime.Unix()-time.Now().Local().Unix()) * time.Second

	return remainSecond.Seconds()
}
