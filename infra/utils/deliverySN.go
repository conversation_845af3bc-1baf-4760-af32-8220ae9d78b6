package utils

import (
	"eShop/infra/log"
	"math/rand"

	"github.com/go-redis/redis"
	"github.com/spf13/cast"
	"xorm.io/xorm"
)

type deliverySN struct {
	redis *redis.Client
	db    *xorm.Engine
}

func (s deliverySN) Generate() string {
	preKey := "order-center:delivery-sn-number"

	isOk := s.redis.Exists(preKey)
	if isOk.Val() <= 0 {
		var deviverySn string
		_, err := s.db.Table("order_delivery_record").Select("max(`delivery_id`)").Get(&deviverySn)
		if err != nil {
			log.Error("查询数据库最大配送id异常, " + err.Error())
			panic(err)
		}
		s.redis.SetNX(preKey, deviverySn, 0)
	}

	var deviverySn int64
	ran := rand.Intn(899) + 100
	deviverySn, err := s.redis.IncrBy(preKey, cast.ToInt64(ran)).Result()
	if err != nil {
		panic(err)
	}
	nowstr := cast.ToString(deviverySn)
	nowstr = nowstr[0:len(nowstr)-1] + "0"
	return cast.ToString(nowstr)
}
