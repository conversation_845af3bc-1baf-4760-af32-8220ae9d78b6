package utils

import (
	"eShop/infra/log"
	"math/rand"

	"github.com/go-redis/redis"
	"github.com/spf13/cast"
	"xorm.io/xorm"
)

type orderSN struct {
	redis *redis.Client
	db    *xorm.Engine
}

func (s orderSN) Generate() string {
	preKey := "order-center:order-sn-number"
	isOk := s.redis.Exists(preKey)
	if isOk.Val() <= 0 {
		var orderSn string
		_, err := s.db.Table("order_main").Select("max(`order_sn`)").Get(&orderSn)
		if err != nil {
			log.Error("查询数据库最大订单号异常, " + err.Error())
			panic(err)
		}
		s.redis.SetNX(preKey, orderSn, 0)
	}

	ran := rand.Intn(899) + 100
	var orderSn int64
	orderSn, err := s.redis.IncrBy(preKey, int64(ran)).Result()
	if err != nil {
		panic(err)
	}

	return cast.ToString(orderSn)
}
