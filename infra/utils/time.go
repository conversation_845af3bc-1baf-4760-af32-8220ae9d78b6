package utils

import (
	"fmt"
	"time"
)

// GetTimeDiff 计算两个时间的差值，返回格式化的时间差字符串
func GetTimeDiff(endTime, startTime string) string {
	end, _ := time.Parse("2006-01-02 15:04:05", endTime)
	start, _ := time.Parse("2006-01-02 15:04:05", startTime)

	diff := end.Sub(start)
	days := int(diff.Hours() / 24)
	hours := int(diff.Hours()) % 24
	minutes := int(diff.Minutes()) % 60

	return fmt.Sprintf("%d天%d小时%d分", days, hours, minutes)
}
