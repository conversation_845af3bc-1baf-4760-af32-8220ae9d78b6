package utils

import (
	"eShop/infra/config"
	"strconv"

	"github.com/spf13/cast"
)

const (
	//from content-type
	ContentTypeToForm     = "application/x-www-form-urlencoded"
	ContentTypeToFormUtf8 = "application/x-www-form-urlencoded;charset=UTF-8"
	//json content-type
	//ContentTypeToJson = "application/json;charset=UTF-8"
	//byte content-type
	//ContentTypeToByte = "multipart/form-data"
)

var (
	//美团对接地址
	MtUrl = config.Get("MtUrl")
	//MtUrl = "https://waimaiopen.meituan.com/api/v1/"
	//美团 app secret
	MtAppSecret = config.Get("MtAppSecret") //"7989c5bdfe4e4a49f8b60079a70e7738"
	//美团分配给APP方的id
	//MtAppId = config.Get("MtAppId")//int64(4889)
	MtAppId, _ = strconv.ParseInt(config.Get("MtAppId"), 10, 64)

	TPMtAppId = cast.ToInt64(config.Get("tp-mt-app-id"))
	//美团 app secret
	TPMtAppSecret = config.Get("tp-mt-app-secret") //"7989c5bdfe4e4a49f8b60079a70e7738"

)

var (
	//美配对接地址
	MpUrl = config.Get("MpUrl") //"https://peisongopen.meituan.com/api/"
	//美配 app secret
	MpAppSecret = config.Get("MpAppSecret") //"v[X=$/zXed&nc<79gA;BN._FE)P`U#X4O<k$OE2G)B7{zhF6)e4h!IR1pk?%fGr;" //"v[X=$/zXed&nc<79gA;BN._FE)P`U#X4O<k$OE2G)B7{zhF6)e4h!IR1pk?%fGr;"

	MpAppKey = config.Get("MpAppKey") //"d15816c2dc244a438d99d27587ee1a14"
)

var (
	//美配自由达 app secret
	MpFreeAppSecret = config.Get("mp-free-secret") //"GdD!kfXL6T9$h!wk|RcFd]:<7n/|%Qz0hP54Xy]OKWk0FdH//RQSt:+NM9Ja<8l?"

	MpFreeAppKey = config.Get("mp-free-app-key") //"8627dd29f7594617a761ef9b9f3a5ca9"
)

var (
	// ElmUrl 饿了么对接地址
	ElmUrl = config.Get("ElmUrl") //"https://api-be.ele.me/"
	//ElmSecret 饿了么secret
	ElmSecret = config.Get("ElmSecret")
	//ElmSource 饿了么合作方账号
	ElmSource = config.Get("ElmSource") //"60948"
	//TPElmSource 饿了么TP代运营SOURCE
	TPElmSource = config.Get("tp-elm-app-id") //"60948"  "49101183"
	//TPElmSecret 饿了么TP代运营 app secret
	TPElmSecret = config.Get("tp-elm-app-secret") //"7989c5bdfe4e4a49f8b60079a70e7738" "4v9oaeNkYC"
)

var (
	//闪送地址
	IssUrl = config.Get("IssUrl") //测试："http://open.s.bingex.com" 生产：http://open.ishansong.com
	//闪送appId
	IssClientId = config.Get("IssClientId") // 测试："ssEVZY1OAkBUiTa7q"
	//闪送秘钥
	IssAppSecret = config.Get("IssAppSecret") //测试："iLwrJhwC9bMWKzfyIpxNH286yhsAS7Yt"
	//商家id
	IssShopId = config.Get("IssShopId") //测试"20000000000001103"
)

// 微信视频号
var (
	// 对接地址
	WeiXinUrl = config.Get("weixin_url") // https://api.weixin.qq.com
)

// 微盟对接地址
var (
	WeiMengApiUrl       = config.Get("weimob.api.url")
	WeiMengClientId     = config.Get("weimob.api.client_id")
	WeiMengClientSecret = config.Get("weimob.api.client_secret")
)

var (
	DaDaApiUrl = config.Get("dadaApiUrl")
	//蜂鸟对接地址
	FnApiUrl = config.Get("FnApiUrl")
)

// 达达的方法名称对应的地址
const (
	//查询运费
	QueryDeliverFee = "/api/order/queryDeliverFee"
	//查询运费后下单
	AddAfterQuery = "/api/order/addAfterQuery"
	//取消订单
	FormalCancel = "/api/order/formalCancel"
	//查询订单详情
	Query = "/api/order/status/query"
	//查询骑手位置
	QueryPosition = "/api/order/transporter/position"
	//商户确认物品已归还
	ConfirmGoods = "/api/order/confirm/goods"
)

// 蜂鸟
const (
	//获取token
	FnToken = "/openapi/token"
	//刷新token
	FnRefreshToken = "/openapi/refreshToken"
	//蜂鸟存token的Key
	redisTokenKey = "FnTokenKey"
	//蜂鸟存用于刷新的token
	redisFreshTokenKey = "FnFreshTokenKey"
	//门店配送范围接口
	ChainstoreRange = "/v3/invoke/chainstoreRange"
	//预下单，查询配送费
	FnPreCreateOrder = "/v3/invoke/preCreateOrder"
	//蜂鸟正式下单
	FnAddAfterQuery = "/v3/invoke/createOrder"
	//蜂鸟正式下单
	FnGetOrderDetail = "/v3/invoke/getOrderDetail"
	//获取可用订单取消原因接口
	FnGetCancelReasonList = "/v3/invoke/getCancelReasonList"
	//订单预取消
	FnPreCancelOrder = "/v3/invoke/preCancelOrder"
	//订单取消
	FnCancelOrder = "/v3/invoke/cancelOrder"
	//查询骑手信息
	FnGetKnightInfo = "/v3/invoke/getKnightInfo"
)
