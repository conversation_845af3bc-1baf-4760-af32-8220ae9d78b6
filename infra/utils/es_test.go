package utils

import "testing"

func TestCreateESIndex(t *testing.T) {
	type args struct {
		orgId string
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{name: "创建ES"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			CreateESIndex("3")
		})
	}
}

func TestCreateESIndex1(t *testing.T) {
	type args struct {
		orgId string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "创建ES索引"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := CreateESIndex("3"); (err != nil) != tt.wantErr {
				t.Errorf("CreateESIndex() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
