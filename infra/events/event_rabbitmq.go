package events

import (
	"eShop/infra/config"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"time"

	amqp "github.com/streadway/amqp"
)

// RabbitMQ RabbitMQ客户端
type RabbitMQ struct {
	conn    *amqp.Connection
	channel *amqp.Channel
}

// NewRabbitMQ 创建RabbitMQ客户端
func NewRabbitMQ() (*RabbitMQ, error) {
	url := config.Get("mq.oneself")

	// 配置连接参数
	config := amqp.Config{
		// 每10秒发送一次心跳，保持连接活跃
		Heartbeat: 10 * time.Second,

		// 设置语言环境
		Locale: "en_US",

		// 为连接添加标识，方便在 RabbitMQ 管理界面识别
		Properties: amqp.Table{
			"connection_name": "eshop-service",
		},

		// 设置连接超时时间为10秒
		Dial: func(network, addr string) (net.Conn, error) {
			return net.DialTimeout(network, addr, 10*time.Second)
		},
	}
	// 使用新的配置建立连接
	conn, err := amqp.DialConfig(url, config)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to RabbitMQ: %v", err)
	}

	ch, err := conn.Channel()
	if err != nil {
		return nil, fmt.Errorf("failed to open channel: %v", err)
	}

	return &RabbitMQ{
		conn:    conn,
		channel: ch,
	}, nil
}

// Close 关闭连接
func (r *RabbitMQ) Close() {
	if r.channel != nil {
		r.channel.Close()
	}
	if r.conn != nil {
		r.conn.Close()
	}
}

// PublishEvent 发布事件
func (r *RabbitMQ) PublishEvent(exchange, routingKey string, event interface{}) error {
	// 确保exchange存在
	err := r.channel.ExchangeDeclare(
		exchange,
		"topic", // type
		true,    // durable
		false,   // auto-deleted
		false,   // internal
		false,   // no-wait
		nil,     // arguments
	)
	if err != nil {
		return fmt.Errorf("failed to declare exchange: %v", err)
	}

	// 序列化事件
	body, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal event: %v", err)
	}

	// 发布消息
	err = r.channel.Publish(
		exchange,   // exchange
		routingKey, // routing key
		false,      // mandatory
		false,      // immediate
		amqp.Publishing{
			ContentType: "application/json",
			Body:        body,
		},
	)
	if err != nil {
		return fmt.Errorf("failed to publish event: %v", err)
	}

	return nil
}

// ConsumeEvents 消费事件
func (r *RabbitMQ) ConsumeEvents(exchange, queueName, routingKey string, handler func([]byte) error) error {
	// 确保exchange存在
	err := r.channel.ExchangeDeclare(
		exchange,
		"topic", // type
		true,    // durable
		false,   // auto-deleted
		false,   // internal
		false,   // no-wait
		nil,     // arguments
	)
	if err != nil {
		return fmt.Errorf("failed to declare exchange: %v", err)
	}

	// 确保队列存在
	q, err := r.channel.QueueDeclare(
		queueName, // name
		true,      // durable
		false,     // delete when unused
		false,     // exclusive
		false,     // no-wait
		nil,       // arguments
	)
	if err != nil {
		return fmt.Errorf("failed to declare queue: %v", err)
	}

	// 绑定队列到exchange
	err = r.channel.QueueBind(
		q.Name,     // queue name
		routingKey, // routing key
		exchange,   // exchange
		false,      // no-wait
		nil,        // arguments
	)
	if err != nil {
		return fmt.Errorf("failed to bind queue: %v", err)
	}

	// 开始消费消息
	msgs, err := r.channel.Consume(
		q.Name, // queue
		"",     // consumer
		false,  // auto-ack
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	if err != nil {
		return fmt.Errorf("failed to register a consumer: %v", err)
	}

	go func() {
		for d := range msgs {
			if err := handler(d.Body); err != nil {
				log.Printf("Error handling message: %v", err)
				d.Nack(false, true) // 消息处理失败，重新入队
			} else {
				d.Ack(false) // 消息处理成功，确认消息
			}
		}
	}()

	return nil
}
