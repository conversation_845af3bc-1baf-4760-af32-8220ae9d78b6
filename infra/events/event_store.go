package events

import (
	"encoding/json"
	"time"

	"xorm.io/xorm"
)

// EventRecord 事件记录
type EventRecord struct {
	ID          int64  `xorm:"pk autoincr"`
	EventType   string `xorm:"index"`
	AggregateID string `xorm:"index"`
	Version     int    `xorm:"version"`
	Data        string
	Timestamp   time.Time `xorm:"created"`
}

// XORMEventStore XORM实现的事件存储
type XORMEventStore struct {
	engine *xorm.Engine
}

// NewXORMEventStore 创建XORM事件存储
func NewXORMEventStore(engine *xorm.Engine) (*XORMEventStore, error) {
	// 确保表存在
	err := engine.Sync2(new(EventRecord))
	if err != nil {
		return nil, err
	}

	return &XORMEventStore{
		engine: engine,
	}, nil
}

// SaveEvent 保存事件
func (s *XORMEventStore) SaveEvent(event Event) error {
	data, err := json.Marshal(event)
	if err != nil {
		return err
	}

	record := &EventRecord{
		EventType:   event.GetEventType(),
		AggregateID: event.GetAggregateID(),
		Version:     event.GetVersion(),
		Data:        string(data),
		Timestamp:   event.GetTimestamp(),
	}

	_, err = s.engine.Insert(record)
	return err
}

// GetEvents 获取事件
func (s *XORMEventStore) GetEvents(aggregateID string) ([]Event, error) {
	var records []EventRecord
	err := s.engine.Where("aggregate_id = ?", aggregateID).Find(&records)
	if err != nil {
		return nil, err
	}

	var events []Event
	for _, record := range records {
		var event BaseEvent
		if err := json.Unmarshal([]byte(record.Data), &event); err != nil {
			return nil, err
		}
		events = append(events, &event)
	}

	return events, nil
}
