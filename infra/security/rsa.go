package security

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/hex"
	"encoding/pem"
	"errors"
	"io/ioutil"
)

var (
	MyPublicKey  *rsa.PublicKey
	AppPublicKey *rsa.PublicKey
	PrivateKey   *rsa.PrivateKey
)

func InitPEM(priveteKeyFile, myPublicKeyFile string, appPublicKeyFile string) {

	if len(priveteKeyFile) > 0 {
		privateKeyPEM, err := ioutil.ReadFile(priveteKeyFile)
		if err != nil {
			panic(err)
		}

		PrivateKey, err = parsePrivateKeyFromPEM(privateKeyPEM)
		if err != nil {
			panic(err)
		}
	}

	if len(myPublicKeyFile) > 0 {
		// 读取公钥文件
		publicKeyPEM, err := ioutil.ReadFile(myPublicKeyFile)
		if err != nil {
			panic(err)
		}

		MyPublicKey, err = parsePublicKeyFromPEM(publicKeyPEM)
		if err != nil {
			panic(err)
		}
	}

	if len(appPublicKeyFile) > 0 {
		// 读取公钥文件
		appPublicKeyPEM, err := ioutil.ReadFile(appPublicKeyFile)
		if err != nil {
			panic(err)
		}

		AppPublicKey, err = parsePublicKeyFromPEM(appPublicKeyPEM)
		if err != nil {
			panic(err)
		}
	}
}

// Encrypt 公钥加密
func Encrypt(pubKey *rsa.PublicKey, data string) string {
	// 加密
	ciphertext, err := rsa.EncryptOAEP(sha256.New(), rand.Reader, pubKey, []byte(data), nil)
	if err != nil {
		panic(err)
	}
	return hex.EncodeToString(ciphertext)
}

// Decrypt 私钥解密
func Decrypt(ciphertext string) string {
	cipherData, _ := hex.DecodeString(ciphertext)
	// 解密
	plaintext, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, PrivateKey, cipherData, nil)
	if err != nil {
		panic(err)
	}
	return string(plaintext)
}

// SignData 私钥签名
func SignData(message string) string {
	hashed := sha256.Sum256([]byte(message))
	signature, _ := rsa.SignPKCS1v15(nil, PrivateKey, crypto.SHA256, hashed[:])
	result := hex.EncodeToString(signature)
	return result
}

// Verify 公钥验签
func Verify(pubKey *rsa.PublicKey, message string, signature string) (bool, error) {
	signatureData, _ := hex.DecodeString(signature)
	hashed := sha256.Sum256([]byte(message))
	err := rsa.VerifyPKCS1v15(pubKey, crypto.SHA256, hashed[:], signatureData)
	if err != nil {
		return false, err
	} else {
		return true, nil
	}
}

// 私钥证书转换
func parsePrivateKeyFromPEM(data []byte) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode(data)
	if block == nil {
		return nil, errors.New("failed to decode PEM block")
	}
	return x509.ParsePKCS1PrivateKey(block.Bytes)
}

// 公钥证书转换
func parsePublicKeyFromPEM(data []byte) (*rsa.PublicKey, error) {
	block, _ := pem.Decode(data)
	if block == nil {
		panic("failed to parse PEM block containing the public key")
	}
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	pubKey := pub.(*rsa.PublicKey)
	return pubKey, err
}
