package enum

// `task_content` tinyint(1) NOT NULL DEFAULT '0' COMMENT '任务内容:1:业务员导入 2:业务员导出 3:分销订单导出 4:分销订单佣金结算导出 5-提现导出 6-导入打款记录',
// 任务列表 任务类型 task_list.task_content
const (
	TaskContentSalesmanImport            = 1   //业务员导入
	TaskContentSalesmanExport            = 2   //业务员导出
	TaskContentDisOrderExport            = 3   //分销订单导出
	TaskContentDisSettlementExport       = 4   //分销订单佣金结算导出
	TaskContentDisWithdrawExport         = 5   //提现导出
	TaskContentDisWithdrawImport         = 6   // 导入打款记录
	TaskContentDistributorExport         = 7   // 分销员导出
	TaskContentDisGoodsImport            = 8   // 分销商品导入
	TaskContentDisGoodsExport            = 9   // 分销商品导出
	TaskContentDisInsureSettleExport     = 10  // 保单分销结算导出
	TaskContentShopExport                = 11  // 线下企业导出
	TaskContentBlkyProductExport         = 12  //百林康源商品物流码导出
	StatsShopExport                      = 13  //润合云店店铺数据-报表导出
	StatsSalespersonExport               = 14  //润合云店业务员统计数据导出
	TaskContentStatsSkuExport            = 15  //商品数据导出
	TaskContentStatsCitySkuExport        = 16  //商品区域数据导出
	TaskContentStoreProductExport        = 17  //saas门店商品导出
	TaskContentChainProductExport        = 18  //saas连锁商品导出
	TaskContentChainProductImport        = 19  //saas连锁商品导入
	TaskContentProductBatchUp            = 20  //门店商品操作批量上架导入
	TaskContentProductBatchDown          = 21  //门店商品操作批量下架导入
	TaskContentProductBatchDel           = 22  //门店商品操作批量删除导入
	TaskContentProductBatchPush          = 23  //门店商品操作批量铺品导入
	TaskContentProductBatchPrice         = 24  //门店商品操作批量调价导入
	TaskContentDisOrderDetailExport      = 25  //分销订单列表导出订单数据（含商品明细）
	TaskContentStoreServiceImport        = 26  //门店服务导入
	TaskContentStoreALIVEImport          = 27  //门店活体导入
	TaskContentInventoryLocationImport   = 28  //库存库位导入
	TaskContentInventoryLocationExport   = 29  //库存库位导出
	TaskContentProductBatchStock         = 30  //门店商品操作批量库存同步导入
	TaskContentDisWithdrawOrderExport    = 31  //提现订单数据导出
	TaskContentBlkyCodeImport            = 100 //百林康源物流码导入
	TaskContentSzldProductExport         = 101 //深圳利都商品物流码导出
	TaskContentClsVerifyDetailExport     = 102 //防伪码查询明细导出
	TaskContentUserListExport            = 103 //客户列表导出
	TaskContentEmployeePerformanceExport = 104 //员工业绩导出
	ClsPointOrderImport                  = 105 //宠利扫积分订单导入
	ClsPointOrderExport                  = 106 //宠利扫积分订单导出
	ClsPointsRuleImport                  = 107 //宠利扫积分规则导入
	ClsPointsDailyStatExport             = 108 //宠利扫积分每日统计导出
	ClsPointsFlowExport                  = 109 //宠利扫积分流水导出
	TaskContentPetInviteExport           = 110 //宠物邀请导出
	TaskContentPetContestantExport       = 111 //参加用户列表导出
	TaskContentPetStatExport             = 112 //数据监测导出
	TaskContentPetPrizeExport            = 113 //奖品列表导出
)

// 宠物saas的异步任务
var EshopTaskMap = map[int64]int64{
	TaskContentStoreProductExport:        0,
	TaskContentChainProductExport:        0,
	TaskContentChainProductImport:        0,
	TaskContentProductBatchUp:            0,
	TaskContentProductBatchDown:          0,
	TaskContentProductBatchDel:           0,
	TaskContentProductBatchPush:          0,
	TaskContentProductBatchPrice:         0,
	TaskContentStoreServiceImport:        0, //门店服务导入
	TaskContentStoreALIVEImport:          0, //门店活体导入
	TaskContentInventoryLocationImport:   0, //库存库位导入
	TaskContentInventoryLocationExport:   0, //库存库位导出
	TaskContentEmployeePerformanceExport: 0, //员工业绩导出
}

var TaskContentMap = map[int]string{
	TaskContentSalesmanImport:            "业务员导入",
	TaskContentSalesmanExport:            "业务员导出",
	TaskContentDisOrderExport:            "分销订单导出",
	TaskContentDisSettlementExport:       "分销订单佣金结算导出",
	TaskContentDisWithdrawExport:         "提现导出",
	TaskContentDisWithdrawImport:         "导入打款记录",
	TaskContentDistributorExport:         "分销员导出",
	TaskContentDisGoodsImport:            "分销商品导入",
	TaskContentDisGoodsExport:            "分销商品导出",
	TaskContentDisInsureSettleExport:     "保单分销结算导出",
	TaskContentShopExport:                "企业列表导出",
	TaskContentBlkyProductExport:         "百林康源商品物流码导出",
	StatsShopExport:                      "润合云店店铺数据-报表导出",
	StatsSalespersonExport:               "润合云店业务员统计数据导出",
	TaskContentStatsSkuExport:            "商品数据导出",
	TaskContentStatsCitySkuExport:        "商品区域数据导出",
	TaskContentStoreProductExport:        "saas门店商品导出",
	TaskContentChainProductExport:        "saas连锁商品导出",
	TaskContentChainProductImport:        "saas连锁商品导入",
	TaskContentProductBatchUp:            "店铺批量上架",
	TaskContentProductBatchDown:          "店铺批量下架",
	TaskContentProductBatchDel:           "店铺批量删除",
	TaskContentProductBatchPush:          "店铺商品铺品",
	TaskContentProductBatchPrice:         "店铺批量调价",
	TaskContentDisOrderDetailExport:      "导出订单数据（含商品明细）",
	TaskContentStoreServiceImport:        "门店服务导入",
	TaskContentStoreALIVEImport:          "门店活体导入",
	TaskContentInventoryLocationImport:   "库存库位导入",
	TaskContentBlkyCodeImport:            "百林康源物流码导入",
	TaskContentSzldProductExport:         "深圳利都商品物流码导出",
	TaskContentInventoryLocationExport:   "库存库位导出",
	TaskContentUserListExport:            "客户列表导出",
	TaskContentClsVerifyDetailExport:     "防伪码查询明细导出",
	TaskContentDisWithdrawOrderExport:    "提现订单数据导出",
	TaskContentEmployeePerformanceExport: "员工业绩导出",
	ClsPointOrderImport:                  "宠利扫积分订单导入",
	ClsPointOrderExport:                  "宠利扫积分订单导出",
	ClsPointsRuleImport:                  "宠利扫积分规则导入",
	ClsPointsDailyStatExport:             "宠利扫积分每日统计导出",
	ClsPointsFlowExport:                  "宠利扫积分流水导出",
	TaskContentPetInviteExport:           "宠物邀请导出",
	TaskContentPetContestantExport:       "参加用户列表导出",
	TaskContentPetStatExport:             "数据监测导出",
	TaskContentPetPrizeExport:            "奖品列表导出",
}

// `task_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '任务状态:1:未开始;2:进行中;3:已完成 4:失败',
// 任务列表 任务状态 task_list.task_status
const (
	TaskStatusNotStart = 1 //未开始
	TaskStatusing      = 2 //进行中
	TaskStatusFinished = 3 //已完成
	TaskStatusFailed   = 4 //失败
)

var TaskStatusMap = map[int]string{
	TaskStatusNotStart: "未开始",
	TaskStatusing:      "进行中",
	TaskStatusFinished: "已完成",
	TaskStatusFailed:   "失败",
}

const (
	TaskQueue             = "sz-eshop-task"
	TaskBlkyBoxxCodeQueue = "sz-eshop-task-code"

	//异步任务队列
	AsyncQueue = "sz-eshop-async"
)
