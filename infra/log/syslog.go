package log

import (
	"time"
)

type Syslog struct {
	Id             int       `xorm:"not null pk autoincr comment('Id') INT(11)"`
	Appid          string    `xorm:"default 'NULL' comment('系统标识id（可以是项目名之类）') VARCHAR(50)"`
	Level          string    `xorm:"default 'NULL' comment('日志级别') VARCHAR(10)"`
	Logger         string    `xorm:"default 'NULL' comment('日志logger') VARCHAR(200)"`
	Message        string    `xorm:"default 'NULL' comment('日志内容') TEXT"`
	RequestId      string    `xorm:"default 'NULL' comment('全局请求id') VARCHAR(36)"`
	Ip             string    `xorm:"default 'NULL' comment('请求来源ip') VARCHAR(50)"`
	Createdatetime time.Time `xorm:"default 'current_timestamp()' TIMESTAMP"`
	EsIndexPrefix  string    `xorm:"-"`
}
