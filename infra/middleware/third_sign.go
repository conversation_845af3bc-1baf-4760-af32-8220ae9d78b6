package middleware

import (
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"eShop/infra/log"
	viewmodel "eShop/view-model"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

const (
	secretKey = "52132e92d85005fd13e2a2bd89b7bed4"
)

// 授权列表
var allowList = map[string]string{
	"brins": "Lqxt3uVZz3zZ5s8DpauPl6gvJGAcWUtp",
}

type SignatureData struct {
	Uri         string
	Timestamp   int64
	NonceString string
	ContentMd5  string
	Secret      string
}

func ThirdSign(handle http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		resp := viewmodel.BaseHttpResponse{
			Code: 400,
		}
		//env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))

		//测试不加签名验证
		//if env != "" && env != "dev" {
		data, err := parseSignature(r)
		if err != nil {
			resp.Message = err.Error()
			bytes, _ := json.Marshal(resp)
			w.Write(bytes)
			return
		}

		mac := hmac.New(sha1.New, []byte(data.Secret))
		mac.Write([]byte(fmt.Sprintf("%s\n%s\n%d\n%s\n%s\n", r.Method, data.Uri, data.Timestamp, data.NonceString, data.ContentMd5)))
		signature := base64.StdEncoding.EncodeToString(mac.Sum(nil))

		if r.Header.Get("a-signature") != signature {
			log.Error("第三方调用接口，检测到签名对不上 head.sign=", r.Header.Get("a-signature"), ", signature=", signature)
			resp.Message = "签名错误"
			bytes, _ := json.Marshal(resp)
			w.Write(bytes)
			return
		}
		//}

		handle.ServeHTTP(w, r)
	})
}

func parseSignature(r *http.Request) (sd *SignatureData, err error) {
	sd = new(SignatureData)
	appId := r.Header.Get("a-id")
	if len(appId) == 0 {
		return nil, errors.New("请求头AppId不能为空")
	}

	if secret, has := allowList[appId]; !has {
		return nil, errors.New("AppId不存在")
	} else {
		sd.Secret = secret
	}

	sd.Timestamp, _ = strconv.ParseInt(r.Header.Get("a-timestamp"), 0, 0)
	if sd.Timestamp == 0 {
		return nil, errors.New("请求头时间戳不存在或解析错误")
	}
	dffTime := time.Now().Unix() - sd.Timestamp
	// 5分钟以前 或者1分钟以后
	if dffTime > 300 || dffTime < -60 {
		return nil, errors.New("请求头时间戳不在有效范围内")
	}

	sd.NonceString = r.Header.Get("a-nonce-string")
	if len(sd.NonceString) < 16 {
		return nil, errors.New("请求头随机字符串至少16位")
	}

	if len(r.Header.Get("a-signature")) == 0 {
		return nil, errors.New("请求头签名不能为空")
	}

	sd.Uri = "/eshop" + r.URL.Path

	queryParams := r.URL.Query()
	if len(queryParams) > 0 {
		var queryKeys []string
		// key升序后
		for k, _ := range queryParams {
			queryKeys = append(queryKeys, k)
		}
		sort.Strings(queryKeys)
		for i, key := range queryKeys {
			if i == 0 {
				sd.Uri += "?" + key + "=" + queryParams.Get(key)
			} else {
				sd.Uri += "&" + key + "=" + queryParams.Get(key)
			}
		}
	}

	// 请求body
	if r.Body != nil { // Read
		reqBody, _ := io.ReadAll(r.Body)
		r.Body = io.NopCloser(bytes.NewBuffer(reqBody)) // Reset
		if len(reqBody) > 0 {
			h := md5.New()
			h.Write(reqBody)
			sd.ContentMd5 = strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
		}
	}

	return
}

// GetSignature 获取电商签名
// 签名算法：
// 1. 对参数按key进行字典序排序
// 2. 拼接成 key=value& 格式的字符串，过滤空值
// 3. 去掉最后的 & 符号
// 4. 对拼接字符串进行MD5加密
// 5. 在MD5结果后拼接密钥
// 6. 再次进行MD5加密并转大写
//
// 参数:
//   - param: 需要签名的参数字典
//   - secretKey: 签名密钥
//
// 返回:
//   - 生成的签名字符串（大写）
func GetMallSignature(param map[string]string) string {
	// 对参数按键名排序
	keys := make([]string, 0, len(param))
	for key := range param {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	// 拼接参数字符串
	var str strings.Builder
	for _, key := range keys {
		val := param[key]
		if val != "" {
			str.WriteString(key + "=" + val + "&")
		}
	}

	// 去除末尾&
	paramStr := strings.TrimSuffix(str.String(), "&")

	// 第一次MD5 + API密钥 + 第二次MD5并转大写
	firstMd5 := fmt.Sprintf("%x", md5.Sum([]byte(paramStr)))
	finalStr := firstMd5 + secretKey
	return strings.ToUpper(fmt.Sprintf("%x", md5.Sum([]byte(finalStr))))
}
