package app

const (
	DelimiterForStoreMaster = "|||"
	// 饿了么店铺主体信息  key:storeMasterId  value:appid ||| appsecret
	StoreMasterElmRedisKey = "datacenter:store:master-info-elm:"
	// 美团店铺主体信息 key:storeMasterId value:appid ||| appsecret
	StoreMasterMtRedisKey = "datacenter:store:master-info-mt:"
	// 京东到家店铺主体信息 key:storeMasterId value:appid ||| appsecret
	StoreMasterJddjRedisKey = "datacenter:store:master-info-jddj:"
	// 京东到家店铺主体token key:storeMasterId value:apptoken
	StoreMasterJddjTokenRedisKey = "datacenter:store:master-info-jddj-token:"

	// 饿了么店铺主体Id  key:appId   value:storeMasterId
	StoreMasterIdElmRedisKey = "datacenter:store:master-id-elm:"
	// 美团店铺主体Id key:appId  value:storeMasterId
	StoreMasterIdMtRedisKey = "datacenter:store:master-id-mt:"
	// 京东到家店铺主体Id key:appId  value:storeMasterId
	StoreMasterIdJddjRedisKey = "datacenter:store:master-id-jddj:"
)

const (
	Channel_ELM  = 2
	Channel_MT   = 3
	Channel_JDDJ = 4
)

// 渠道的应用配置
type ChannelAppConfig struct {
	AppId     string
	AppSecret string
	// AppToken 只有京东到家渠道才有
	AppToken string
}

type WarehouseDeliveryConfig struct {
	Id           int    `json:"id" xorm:"pk autoincr not null comment('主键') INT(11) 'id'"`
	DeliveryName string `json:"delivery_name" xorm:"not null comment('配送类型名称') VARCHAR(100) 'delivery_name'"`
	AppKey       string `json:"app_key" xorm:"default 'null' comment('app_key') VARCHAR(100) 'app_key'"`
	AppSecret    string `json:"app_secret" xorm:"default 'null' comment('app_secret') VARCHAR(100) 'app_secret'"`
	SourceId     string `json:"source_id" xorm:"default 'null' comment('source_id') VARCHAR(100) 'source_id'"`
	DeliveryType int    `json:"delivery_type" xorm:"default 'null' comment('//配送类型默认美配 0 美配 1 闪送 2自配 3达达 4蜂鸟 5顺风') INT(11) 'delivery_type'"`
	ShopNo       string `json:"shop_no" xorm:"default 'null' comment('配送门店编码') VARCHAR(100) 'shop_no'"`
	Code         string `json:"code" xorm:"default 'null' comment('蜂鸟的授权code') VARCHAR(100) 'code'"`
	RefreshToken string `json:"refresh_token" xorm:"default 'null' comment('用来刷新的token') VARCHAR(100) 'refresh_token'"`
}
