package cache

import (
	"eShop/infra/config"
	"eShop/infra/log"
	"eShop/infra/pkg/util"
	"encoding/json"

	"time"

	"github.com/go-redis/redis"
	"github.com/spf13/cast"
)

var (
	redisHandle  *redis.Client
	redisHandle8 *redis.Client
)

func SetupDB() {
	redisHandle = GetRedisConn()
	redisHandle8 = GetRedisConn8()
}

func CloseDB() {
	redisHandle.Close()
	redisHandle8.Close()
}

// 获取redis集群客户端
func GetRedisConn() *redis.Client {
	var db = cast.ToInt(config.Get("redis.DB"))
	var addr = config.Get("redis.Addr")
	var pwd = config.Get("redis.Password")

	startTime := time.Now()
	defer func() {
		if p := recover(); p != nil {
			log.Errorf("GetRedisConn panic:%+v  redisDns:%d|%s|%s 耗时:%s \n %s", p, db, addr, pwd, time.Since(startTime), util.PanicTrace())
			panic(p)
		}
	}()

	if redisHandle != nil {
		_, err := redisHandle.Ping().Result()
		if err == nil {
			return redisHandle
		}
	}

	redisHandle = redis.NewClient(&redis.Options{
		Addr:         addr,
		Password:     pwd,
		DB:           db,
		MinIdleConns: 10,
		IdleTimeout:  60,
	})
	_, err := redisHandle.Ping().Result()
	if err != nil {
		tmp, _ := json.Marshal(redisHandle.PoolStats())
		log.Error("redis连接错误，", err, "，PoolStats：", string(tmp))
		panic(err)
	}

	return redisHandle
}

// 新逍宠那边用的redis库是db8
func GetRedisConn8() *redis.Client {
	if redisHandle8 != nil {
		_, err := redisHandle8.Del("").Result()
		if err == nil {
			return redisHandle8
		}
	}
	var db = cast.ToInt(config.Get("redis.DB8"))
	var addr = config.Get("redis.Addr")
	var pwd = config.Get("redis.Password")

	redisHandle8 = redis.NewClient(&redis.Options{
		Addr:         addr,
		Password:     pwd,
		DB:           db,
		MinIdleConns: 10,
		IdleTimeout:  60,
	})
	/*_, err := redisHandle.Ping().Result()
	if err != nil {
		panic(err)
	}*/

	return redisHandle8
}

// redis 加锁10秒
func RedisLock(key string, expireTime int) bool {
	redis := GetRedisConn()
	lock := redis.Set(key, "1", time.Duration(expireTime)*time.Second)
	if lock.Err() != nil {
		return false
	}
	defer redis.Del(key)
	return true
}
