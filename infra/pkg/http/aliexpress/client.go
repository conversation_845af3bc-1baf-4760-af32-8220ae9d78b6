package aliexpress

import (
	"bytes"
	"eShop/infra/config"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	glog "eShop/infra/log"
)

const (
	URI       = "https://wuliu.market.alicloudapi.com/kdi"
	KeyPrefix = "external:aliexpress:"
	SFEXPRESS = "SFEXPRESS"
)

// 快递公司字母简写映射
var typeMap = map[string]string{
	"SF":   SFEXPRESS,
	"YD":   "YUNDA",
	"DBL":  "DEPPON",
	"XFEX": "XFEXPRESS",
	"UC":   "UC56",
	"YZPY": "EMS",
}

type Response struct {
	Status string  `json:"status"`
	Msg    string  `json:"msg"`
	Result *Result `json:"result"`
}
type Node struct {
	Time   string `json:"time"`
	Status string `json:"status"`
}
type Result struct {
	Number         string  `json:"number"`
	Type           string  `json:"type"`
	List           []*Node `json:"list"`
	Deliverystatus string  `json:"deliverystatus"`
	Issign         string  `json:"issign"`
	ExpName        string  `json:"expName"`
	ExpSite        string  `json:"expSite"`
	ExpPhone       string  `json:"expPhone"`
	Courier        string  `json:"courier"`
	CourierPhone   string  `json:"courierPhone"`
	UpdateTime     string  `json:"updateTime"`
	TakeTime       string  `json:"takeTime"`
	Logo           string  `json:"logo"`
}

var (
	client  *http.Client
	appcode string
)

func init() {
	client = &http.Client{
		Timeout: time.Second * 60,
	}
	//appcode = "f49313bfe47545a99ea301b6de278bf2"
}

// 阿里云物流接口
// link https://market.aliyun.com/products/56928004/cmapi021863.html?spm=5176.2020520132.101.7.60847218Ixv1Z4#sku=yuncode1586300000
type kdi struct {
	t     string //快递公司字母简写
	no    string //快递单号
	phone string //手机号码
}

func newKdi(code string, shippingCode string, phone string) *kdi {
	return &kdi{t: code, no: shippingCode, phone: phone}
}

func (k *kdi) buildUrl() string {
	return URI + "?no=" + k.getNo() + "&type=" + k.getType()
}

func (k *kdi) getType() string {
	t, ok := typeMap[k.t]
	if !ok {
		t = k.t
	}
	return t
}

func (k *kdi) getNo() string {
	if k.getType() == SFEXPRESS && k.phone != "" {
		return k.no + ":" + k.phone[len(k.phone)-4:]
	}
	return k.no
}

func (k *kdi) getCache() ([]byte, error) {
	redis := cache.GetRedisConn()
	//defer redis.Close()
	return redis.Get(KeyPrefix + k.getNo()).Bytes()
}

func (k *kdi) setCache(data []byte) {
	redis := cache.GetRedisConn()
	//defer redis.Close()
	redis.Set(KeyPrefix+k.getNo(), string(data), time.Second*3600)
}

func (k *kdi) query() (*Response, error) {
	res := &Response{}
	val, err := k.getCache()
	setCache := false
	if err != nil || len(val) == 0 {
		val, _, err = do("GET", k.buildUrl(), nil)
		setCache = true
	}
	if err != nil || len(val) == 0 {
		return nil, fmt.Errorf("物流信息查询失败")
	}
	err = json.Unmarshal(val, res)
	if err == nil {
		if setCache {
			k.setCache(val)
		}
		return res, err
	} else {
		return nil, errors.New("解析响应body出错 " + err.Error())
	}
}

func Info(code, shipCode, phone string) (*Response, error) {
	k := newKdi(code, shipCode, phone)
	return k.query()
}

func do(method string, path string, data map[string]interface{}) (resBody []byte, contentType string, err error) {
	body, _ := json.Marshal(data)
	req, err := http.NewRequest(method, path, bytes.NewBuffer(body))

	defer func() {
		// 出错时记录请求日志
		if err != nil {
			glog.Error(fmt.Sprintf("调用阿里物流接口(%s)出错，返回内容%s:%s，接口参数:%s", path, err.Error(), string(resBody), utils.JsonEncode(data)))
		}
	}()

	if err != nil {
		return
	}

	// 请求前置处理，设置签名
	beforeRequest(req)
	httpResp, err := client.Do(req)
	if err != nil {
		return
	}
	defer httpResp.Body.Close()
	if httpResp.StatusCode >= 400 {
		err = errors.New("请求出错 " + httpResp.Status)
		return
	}

	contentType = httpResp.Header.Get("Content-Type")
	resBody, err = ioutil.ReadAll(httpResp.Body)
	return
}

// 请求库前置处理，添加请求头
func beforeRequest(req *http.Request) {
	appcode = config.Get("express.ali.appcode")
	req.Header.Set("Authorization", "APPCODE "+appcode)
}
