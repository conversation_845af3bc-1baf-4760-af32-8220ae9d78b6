package code

var MsgFlags = map[int]string{
	Success:                 "OK",
	ErrorCommon:             "错误",
	NetWorkError:            "网络错误",
	OrderStatusChanged:      "订单状态已变更",
	OrderStatusInconsistent: "订单状态不一致",
	OrderExisted:            "订单已存在",
	OrderNotExist:           "订单不存在",
	ShopNotExist:            "店铺未找到",
}

func GetMsg(code int) string {
	msg, ok := MsgFlags[code]
	if ok {
		return msg
	}
	return MsgFlags[ErrorCommon]
}
