package jwtauth

import "testing"

func TestVerifySign(t *testing.T) {
	type args struct {
		sign      string
		timestamp string
		url       string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				sign:      "D5D295AB76DF3FB3DDEDF49AFE21F479",
				timestamp: "1687704619",
				url:       "https://awen.uat.rvet.cn/external-app/api/sjan/product/add",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			JwtInit()
			if err := VerifySign(tt.args.sign, tt.args.timestamp, tt.args.url); (err != nil) != tt.wantErr {
				t.Errorf("VerifySign() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
