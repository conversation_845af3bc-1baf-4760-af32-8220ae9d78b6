package utils

import (
	"eShop/infra/config"
	"eShop/infra/log"
	"errors"
	"fmt"
	"net"
	"runtime"
	"time"

	"github.com/spf13/cast"
	"github.com/streadway/amqp"
)

// 开启一个mq链接
func NewMqConn() *amqp.Connection {
	url := config.Get("mq.oneself")

	// 配置连接参数
	config := amqp.Config{
		// 每10秒发送一次心跳，保持连接活跃
		Heartbeat: 10 * time.Second,

		// 设置语言环境
		Locale: "en_US",

		// 为连接添加标识，方便在 RabbitMQ 管理界面识别
		Properties: amqp.Table{
			"connection_name": "eshop-service",
		},

		// 设置连接超时时间为10秒
		Dial: func(network, addr string) (net.Conn, error) {
			return net.DialTimeout(network, addr, 10*time.Second)
		},
	}

	// 使用新的配置建立连接
	conn, err := amqp.DialConfig(url, config)
	if err != nil {
		log.Errorf("RabbitMQ dial fail. err : %v", err)
		return nil
	}
	return conn
}

func NewMqChannel(conn *amqp.Connection) *amqp.Channel {
	channel, err := conn.Channel()
	if err != nil {
		panic("RabbitMQ Get Channel fail. err : " + err.Error())
	}
	return channel
}

// ***************************************MQ********************************************
// 发布推送消息到RabbitMQ，参数：队列名和内容
func PublishRabbitMQ(queue, content, exchange string) bool {
	defer func() {
		if errPanic := recover(); errPanic != nil {
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER] %v %s\n", errPanic, stack[:length])
		}
	}()

	log.Info(fmt.Sprintf("推送MQ开始！队列名，%s，内容：%s，Exchange：%s", queue, content, exchange))
	//开启连接
	conn := NewMqConn()
	defer conn.Close()
	ch := NewMqChannel(conn)
	defer ch.Close()

	// 定义交换"direct"、"fanout"、"topic"和"headers"
	if err := ch.ExchangeDeclare(exchange, amqp.ExchangeDirect, true, false, false, false, nil); nil != err {
		log.Error("RabbitMQ ExchangeDeclare fail, err : ", err)
		return false
	}
	// name,durable,delete when unused,exclusive,no-wait,arguments
	q, err := ch.QueueDeclare(queue, true, false, false, false, nil)
	if err != nil {
		log.Error("RabbitMQ QueueDeclare fail, err : ", q.Name, err)
		return false
	}
	err = ch.QueueBind(queue, queue, exchange, false, nil)
	if err != nil {
		log.Error("RabbitMQ QueueBind fail, err : ", err)
		return false
	}

	_publishing := amqp.Publishing{
		ContentType:  "text/plain",
		Body:         []byte(content),
		DeliveryMode: amqp.Persistent, // Transient 性能好，但是会丢数据。故用 Persistent
	}
	//a.b.c.d.e 为发布key,以.分割；
	if err = ch.Publish(exchange, queue, false, false, _publishing); nil != err {
		log.Error("RabbitMQ Publish fail, err : ", err)
		return false
	}
	log.Info(fmt.Sprintf("推送MQ结束！队列名，%s，内容：%s，Exchange：%s", queue, content, exchange))
	return true
}

// 发送可自定义参数消息
func PublishRabbitMQV2(routeKey, exchange, content string, amqpParams ...interface{}) (err error) {
	defer func() {
		if errPanic := recover(); errPanic != nil {
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER] %v %s\n", errPanic, stack[:length])

			err = errors.New("异常：" + fmt.Sprintln(errPanic))
		}
	}()

	log.Infof("推送MQ开始！Exchange：%s，route，%s，内容：%s, %v", exchange, routeKey, content, amqpParams)
	//开启连接
	conn := NewMqConn()
	defer conn.Close()
	ch := NewMqChannel(conn)
	defer ch.Close()

	_publishing := amqp.Publishing{
		ContentType:  "text/plain",
		Body:         []byte(content),
		DeliveryMode: amqp.Persistent, // Transient 性能好，但是会丢数据。故用 Persistent
	}

	//设置过期时间
	if len(amqpParams) > 0 {
		_publishing.Expiration = cast.ToString(amqpParams[0])
	}

	if err = ch.Publish(exchange, routeKey, false, false, _publishing); nil != err {
		log.Error("RabbitMQ Publish fail, err : ", err)
	}
	log.Infof("PublishRabbitMQV2 推送MQ结束 Exchange：%s，route，%s，内容：%s, %v", exchange, routeKey, content, amqpParams)
	return
}

func PublishRabbitMQV1(routeKey, exchange, content string, amqpParams ...interface{}) (err error) {
	defer func() {
		if errPanic := recover(); errPanic != nil {
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER] %v %s\n", errPanic, stack[:length])

			err = errors.New("异常：" + fmt.Sprintln(errPanic))
		}
	}()

	log.Infof("推送MQ开始！Exchange：%s，route，%s，内容：%s, %v", exchange, routeKey, content, amqpParams)
	//开启连接
	conn := NewMqConn()
	defer conn.Close()
	ch := NewMqChannel(conn)
	defer ch.Close()

	_publishing := amqp.Publishing{
		ContentType:  "text/plain",
		Body:         []byte(content),
		DeliveryMode: amqp.Persistent, // Transient 性能好，但是会丢数据。故用 Persistent
	}

	//设置过期时间
	if len(amqpParams) > 0 {
		//_publishing.Expiration = cast.ToString(amqpParams[0])

		_publishing.Headers = amqp.Table{"x-delay": cast.ToInt32(amqpParams[0])}
	}

	if err = ch.Publish(exchange, routeKey, false, false, _publishing); nil != err {
		log.Error("RabbitMQ Publish fail, err : ", err)
	}

	return
}

// 订阅mq消息
func SubscribeRabbitMQ(queue, exchange string) ([]byte, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Error(err)
		}
	}()

	//log.Info(fmt.Sprint("订阅MQ开始！队列名，%s，Exchange：%s", queue, exchange))
	//开启链接
	conn := NewMqConn()
	defer conn.Close()
	ch := NewMqChannel(conn)
	defer ch.Close()

	if err := ch.Qos(1, 0, false); err != nil {
		log.Error("RabbitMQ ExchangeDeclare fail, err : ", err)
	}

	err := ch.ExchangeDeclare(exchange, "direct", true, false, false, false, nil)
	if err != nil {
		log.Error("RabbitMQ ExchangeDeclare fail, err : ", err)
		return nil, err
	}
	// name// durable// delete when unused // exclusive// no-wait // arguments
	q, err := ch.QueueDeclare(queue, true, false, false, false, nil)
	if err != nil {
		log.Error("RabbitMQ QueueDeclare fail, err : ", q.Name, err)
		return nil, err
	}
	err = ch.QueueBind(queue, queue, exchange, false, nil)
	if err != nil {
		log.Error("RabbitMQ QueueBind fail, err : ", q.Name, err)
		return nil, err
	}

	if delivery, err := ch.Consume(queue, queue, false, false, false, false, nil); err != nil {
		log.Error("RabbitMQ Consume fail, err : ", q.Name, err)
		return nil, err
	} else {
		for d := range delivery {
			func() {
				defer func() {
					if err := recover(); err != nil {
						log.Error(err)
					}
				}()
				//todo 处理业务逻辑
				fmt.Println(d)

			}()
		}
	}
	return nil, nil
}

func Consume(queue, key, exchange string, fun func(request amqp.Delivery) (response string, err error)) {
	for {
		// 尝试建立连接
		conn := NewMqConn()
		if conn == nil {
			log.Error("RabbitMQ连接失败，5秒后重试")
			time.Sleep(5 * time.Second)
			continue
		}
		defer conn.Close()

		ch := NewMqChannel(conn)
		if ch == nil {
			log.Error("Channel创建失败，5秒后重试")
			time.Sleep(5 * time.Second)
			continue
		}
		defer ch.Close()

		// 先声明队列
		_, err := ch.QueueDeclare(
			queue, // name
			true,  // durable
			false, // delete when unused
			false, // exclusive
			false, // no-wait
			nil,   // arguments
		)
		if err != nil {
			log.Error("QueueDeclare失败：", err)
			time.Sleep(5 * time.Second)
			continue
		}

		// 然后绑定队列
		err = ch.QueueBind(queue, key, exchange, false, nil)
		if err != nil {
			log.Error("QueueBind失败：", err)
			time.Sleep(5 * time.Second)
			continue
		}

		if err := ch.Qos(1, 0, false); err != nil {
			log.Error("设置Qos失败：", err)
			time.Sleep(5 * time.Second)
			continue
		}

		// 监听连接状态
		connClose := make(chan *amqp.Error)
		conn.NotifyClose(connClose)
		chClose := make(chan *amqp.Error)
		ch.NotifyClose(chClose)

		delivery, err := ch.Consume(queue, queue, false, false, false, false, nil)
		if err != nil {
			log.Error("设置消费者失败：", err)
			time.Sleep(5 * time.Second)
			continue
		}

		// 消费消息
		for {
			select {
			case err := <-connClose:
				log.Warningf("RabbitMQ连接断开: %s, 准备重连", err)
				goto RECONNECT
			case err := <-chClose:
				log.Warningf("Channel关闭: %s, 准备重连", err)
				goto RECONNECT
			case d, ok := <-delivery:
				if !ok {
					log.Warning("delivery channel closed, 准备重连")
					goto RECONNECT
				}
				func() {
					defer func() {
						if err := recover(); err != nil {
							log.Error(err)
						}
					}()
					fun(d)
				}()
			}
		}
	RECONNECT:
		time.Sleep(5 * time.Second)
	}
}
