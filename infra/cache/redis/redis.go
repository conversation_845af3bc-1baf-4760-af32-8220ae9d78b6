package myredis

import (
	"eShop/infra/config"

	"time"

	"github.com/go-redis/redis"
	"github.com/spf13/cast"
)

type RedisObj struct {
	Client *redis.Client
}

func (r RedisObj) Get(key string) (string, error) {
	if v, err := r.Client.Get(key).Result(); err == redis.Nil {
		return "", nil
	} else {
		return v, err
	}
}
func (r RedisObj) Set(key string, value interface{}, expiration time.Duration) (string, error) {
	return r.Client.Set(key, value, expiration).Result()

}
func (r RedisObj) HGet(key, field string) (string, error) {
	return r.Client.HGet(key, field).Result()
}

func (r RedisObj) HSet(key, field string, value interface{}) (bool, error) {
	return r.Client.HSet(key, field, value).Result()
}

func (r RedisObj) Incr(key string) (int64, error) {
	cmd := r.Client.Incr(key)
	return cmd.Result()
}
func (r RedisObj) SetNX(key string, value interface{}, expiration time.Duration) (bool, error) {
	cmd := r.Client.SetNX(key, value, expiration)
	return cmd.Result()
}

var redisHandle *redis.Client

// 连接池勿关闭
// 获取redis集群客户端
func GetRedisConn() *redis.Client {

	var db = cast.ToInt(config.Get("redis.DB"))
	var addr = config.Get("redis.Addr")
	var pwd = config.Get("redis.Password")

	if redisHandle != nil {
		//_, err := redisHandle.Ping().Result()
		//换一种探活的模式
		_, err := redisHandle.Del("").Result()
		//glog.Info("redis connections: ", redisHandle.PoolStats().TotalConns)
		if err == nil {
			return redisHandle
		} else {
			redisHandle = redis.NewClient(&redis.Options{
				Addr:         addr,
				Password:     pwd,
				DB:           db,
				MinIdleConns: 28,
				IdleTimeout:  30,
				PoolSize:     512,
				MaxConnAge:   30 * time.Second,
			})
			return redisHandle
		}

	} else {
		redisHandle = redis.NewClient(&redis.Options{
			Addr:         addr,
			Password:     pwd,
			DB:           db,
			MinIdleConns: 28,
			IdleTimeout:  30,
			PoolSize:     512,
			MaxConnAge:   30 * time.Second,
		})
		return redisHandle
	}

}
