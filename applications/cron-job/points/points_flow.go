package points

import (
	"eShop/infra/log"
	service "eShop/services/points-service"
	cachekey "eShop/services/points-service/enum"
	"time"
)

// 积分-流水操作

// 开单积分进账
func SalesIn() {
	lock(cachekey.SalesIn, func() {
		s := service.NewClsPointsFlowService()
		log.Info("积分-开单数据转积分进账开始：" + time.Now().Format("2006-01-02 00:00:00"))
		err := s.SalesIn(nil, "")
		if err != nil {
			log.Error("积分-开单数据转积分进账失败", err)
		}
	})
}

// 积分过期
func PointsExpire() {
	lock(cachekey.PointsExpire, func() {
		s := service.NewClsPointsFlowService()
		log.Info("积分-积分过期开始：" + time.Now().Format("2006-01-02 00:00:00"))
		err := s.PointsExpire(nil, "")
		if err != nil {
			log.Error("积分-积分过期失败", err)
		}
	})
}
