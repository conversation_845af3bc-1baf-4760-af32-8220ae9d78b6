package distribution

//分销系统 定时任务
import (
	"eShop/infra/utils"
	"eShop/services/distribution-service/services"
	omnibus_services "eShop/services/omnibus-service/services"

	"github.com/robfig/cron/v3"
)

func InitTask() {
	c := cron.New(cron.WithSeconds())

	disSettlementService1 := services.DisSettlementService{}
	disSettlementService2 := services.DisSettlementService{}
	disSaleManService1 := services.DisSalesmanService{}
	disSaleManService2 := services.DisSalesmanService{}
	outService := services.OutService{}
	// 初始化短信服务
	smsService := omnibus_services.SmsService{}

	//订单完成时， 写入一条待结算数据（该定时器每天0点30分执行）
	if utils.IsProEnv() {
		c.AddFunc("0 10 0 * * *", disSettlementService1.InsertSettlementData)
	} else {
		c.AddFunc("0 0/3 * * * *", disSettlementService1.InsertSettlementData)
	}

	//佣金结算，将待结算状态 改为 已结算状态(该定时器每天4点30分执行)
	if utils.IsProEnv() {
		c.AddFunc("0 30 4 * * *", disSettlementService2.ChangeSettlementStatus)
	} else {
		c.AddFunc("0 0/3 * * * *", disSettlementService2.ChangeSettlementStatus)
	}

	//添加业务员拓展数据  每分钟检测一次是否有需要添加的
	c.AddFunc("0 0/1 * * * *", disSaleManService1.SetSaleManBarCode)

	//跑业务员数据
	c.AddFunc("0 0 0/6 * * *", disSaleManService2.SetSaleManData)

	// 每天5点30跑一次： 让eshop.scrm_leads_autonavi表里的数据写入到es
	c.AddFunc("0 30 5 * * *", outService.UpdateOutHospitalToEs)

	//每天同步shr，设置分销员是体系内还是体系外
	c.AddFunc("0 20 5 * * *", disSaleManService1.SetdistributorInSystem)

	// 每天中午12点检查并通知即将过期的优惠券
	if utils.IsProEnv() {
		c.AddFunc("0 0 12 * * *", func() { smsService.NotifyExpireCoupons() })
		c.AddFunc("0 0 12 * * *", func() { smsService.NotifyExpireCards(1) })
		c.AddFunc("0 0 12 * * *", func() { smsService.NotifyExpireCards(2) })

	} else {
		// 测试环境每5分钟执行一次，要有标识已发送
		c.AddFunc("0 0 12 * * *", func() { smsService.NotifyExpireCoupons() })
		//c.AddFunc("0 0 12 * * *", func() { smsService.NotifyExpireCards(1) })
		//c.AddFunc("0 0 12 * * *", func() { smsService.NotifyExpireCards(2) })
	}

	c.Start()
}
