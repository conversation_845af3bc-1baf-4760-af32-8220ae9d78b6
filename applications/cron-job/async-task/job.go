package async_task

//分销系统 定时任务
import (
	service "eShop/services/base-service"
	"eShop/services/marketing-service/services"

	"github.com/robfig/cron/v3"
)

func InitTask() {
	c := cron.New(cron.WithSeconds())

	//添加业务员拓展数据  每分钟检测一次是否有需要添加的
	c.AddFunc("0/2 * * * * *", InsertMq)
	//发奖品
	c.AddFunc("0/2 * * * * *", GrantRankingPrizes)
	// 每1分钟统计一次投票用户数
	c.AddFunc("0 0/1 * * * *", StatVoteUserCountPerDay)
	c.Start()
}

func InsertMq() {
	// 插入mq
	s := service.AsyncService{}
	s.InsertMq()

}

// 发放奖品
func GrantRankingPrizes() {
	// 到期处理奖品发放
	prizeService := services.NewPetPrizeService()
	prizeService.GrantRankingPrizes()

}

// 新增：统计投票用户数定时任务
func StatVoteUserCountPerDay() {
	// 获取全局数据库引擎

	statService := services.PetStatService{}
	_ = statService.StatVoteUserCountPerDay()
}
