package main

import (
	async_task "eShop/applications/cron-job/async-task"
	"eShop/applications/cron-job/distribution"
	"eShop/applications/cron-job/points"
	"eShop/infra/cache"
	"eShop/infra/config"
	"eShop/infra/log"
	"eShop/infra/utils"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"fmt"
	"net/http"
	"time"

	"github.com/go-chi/chi"
)

func main() {
	sh, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = sh

	cache.CacheSources[cache_source.EShop] = cache.Address(config.Get("redis.PassAddr"))
	log.Init()
	//初始化
	utils.InitClient()
	r := chi.NewRouter()
	// 使用 Recoverer 中间件处理所有路由的 panic

	distribution.InitTask()
	distribution.InitStatsTask()
	async_task.InitTask()
	points.InitTask()
	fmt.Println("cron-job 启动成功! 端口 8146")
	http.ListenAndServe(":8146", r)

}
