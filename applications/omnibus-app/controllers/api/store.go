package api

import (
	marketing_po "eShop/domain/marketing-po"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	marketing_service "eShop/services/marketing-service/services"
	_service "eShop/services/omnibus-service/services"
	viewmodel "eShop/view-model"
	marketing_vo "eShop/view-model/marketing-vo"
	vo "eShop/view-model/omnibus-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// 获取门店列表
// @Summary -门店信息-获取门店列表
// @Tags 宠物连锁SAAS-小程序
// @Accept  json
// @Produce  json
// @Param StoreListReq body vo.StoreListReq true " "
// @Success 200 {object} vo.StoreListRes
// @Failure 400 {object} vo.StoreListRes
// @Router /omnibus-app/api/store/list [Post]
func StoreList(w http.ResponseWriter, r *http.Request) {
	resp := vo.StoreListRes{}
	resp.Code = 400
	req, err := utils.Bind[vo.StoreListReq](r)
	req.OrgId = cast.ToInt(r.Header.Get("org_id"))
	if err != nil {
		log.Error("获取店铺信息，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取店铺信息，参数解析失败：%s", err.Error())
	} else {
		s := _service.StoreService{}

		res, err := s.StoreList(req)
		if err != nil {
			log.Error("获取店铺信息：err=" + err.Error())
			resp.Message = err.Error()
		} else {
			resp.Code = 200
			resp.Data = res
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// 获取门店活动列表
// @Summary -门店信息-获取门店活动
// @Tags 宠物连锁SAAS-小程序
// @Accept  json
// @Produce  json
// @Param StorePromotionListReq body vo.StorePromotionListReq true " "
// @Success 200 {object} vo.StorePromotionListRes
// @Failure 400 {object} vo.StorePromotionListRes
// @Router /omnibus-app/api/store/promotion-list [Post]
func StorePromotionList(w http.ResponseWriter, r *http.Request) {
	out := vo.StorePromotionListRes{}
	out.Code = 400

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	} else if jwtInfo.TenantId == "" {
		out.Message = "门店ID不能为空"
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	s := marketing_service.ActivityService{}

	out.Data.MarketingActivity, _, err = s.GetActivityList(&marketing_vo.ActivityListReq{
		StoreId: jwtInfo.TenantId,
		Status:  marketing_po.ActivityStatusRunning,
	})
	if err != nil {
		log.Error("获取店铺满减和特价活动信息：err=" + err.Error())
		out.Message = fmt.Sprintf("获取店铺满减和特价活动信息：%s", err.Error())
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return

	}
	couponService := marketing_service.CouponService{}
	req := marketing_vo.CouponListReq{
		StoreId: jwtInfo.TenantId,
		Status:  marketing_po.StatusRunning,
		BasePageHttpRequest: viewmodel.BasePageHttpRequest{
			PageIndex: 1,
			PageSize:  1000,
		},
	}
	out.Data.MarketingCoupon, _, err = couponService.GetCouponList(&req)
	if err != nil {
		log.Error("获取店铺优惠券活动信息：err=" + err.Error())
		out.Message = fmt.Sprintf("获取店铺优惠券活动信息：%s", err.Error())
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return

	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	w.Write(out2)
}
