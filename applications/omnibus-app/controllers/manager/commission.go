package manager

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/omnibus-service/services"
	distribution_vo "eShop/view-model/distribution-vo"
	omnibus_vo "eShop/view-model/omnibus-vo"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/go-chi/chi/v5"
	"github.com/spf13/cast"
)

type CommissionController struct {
	service services.CommissionService
}

func NewCommissionController(service services.CommissionService) *CommissionController {
	return &CommissionController{
		service: service,
	}
}

func (c *CommissionController) RegisterRoutes(r chi.Router) {
	r.Route("/omnibus-app/manager/commission", func(r chi.Router) {
		r.Get("/list", c.GetCommissionList)
		r.Get("/detail/{id}", c.GetCommissionDetail)
		r.Post("/create", c.CreateCommission)
		r.Post("/update", c.UpdateCommission)
		r.Post("/status", c.UpdateCommissionStatus)
		r.Get("/employees", c.GetEmployeeList)
		r.Post("/employees", c.UpdateEmployees)
		r.Post("/performance", c.GetEmployeePerformance)
		r.Post("/performance/detail", c.GetEmployeePerformanceDetail)
	})
}

// GetCommissionList 获取提成设置列表
// @Summary 提成设置-列表
// @Description 获取提成设置列表
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param GetCommissionSetupListReq body omnibus_vo.GetCommissionSetupListReq true "提成设置列表查询参数"
// @Success 200 {object} omnibus_vo.GetCommissionSetupListResp
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/commission/list [get]
func (c *CommissionController) GetCommissionList(w http.ResponseWriter, r *http.Request) {
	var req omnibus_vo.GetCommissionSetupListReq
	req, err := utils.Bind[omnibus_vo.GetCommissionSetupListReq](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}
	// 获取登录信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败:", err)
		response.BadRequest(w, "未登录或登录已过期")
		return
	}
	req.StoreId = jwtInfo.TenantId
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	// 调用服务
	result, err := c.service.GetCommissionSetupList(r.Context(), req)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, result.List, int(result.Total))
}

// GetCommissionDetail 获取提成设置详情
// @Summary 提成设置-详情
// @Description 获取提成设置详情
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param id path int true "提成设置ID"
// @Success 200 {object} omnibus_vo.CommissionSetupDetailResp
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/commission/detail/{id} [get]
func (c *CommissionController) GetCommissionDetail(w http.ResponseWriter, r *http.Request) {
	// 获取路径参数
	idStr := chi.URLParam(r, "id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(w, "无效的ID参数")
		return
	}

	// 调用服务
	result, err := c.service.GetCommissionSetupById(r.Context(), id)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, result)
}

// CreateCommission 创建提成设置
// @Summary 提成设置-创建
// @Description 创建提成设置
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param CreateCommissionSetupReq body omnibus_vo.CreateCommissionSetupReq true "创建提成设置请求参数"
// @Success 200 {object} omnibus_vo.CommissionSetupResp
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/commission/create [post]
func (c *CommissionController) CreateCommission(w http.ResponseWriter, r *http.Request) {
	// 获取登录信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败:", err)
		response.BadRequest(w, "未登录或登录已过期")
		return
	}

	req, err := utils.Bind[omnibus_vo.CreateCommissionSetupReq](r)
	if err != nil {
		response.BadRequest(w, "请求参数解析失败:"+err.Error())
		return
	}
	if len(req.Details) == 0 {
		response.BadRequest(w, "提成明细不能为空")
		return
	}

	// 验证提成明细
	for _, detail := range req.Details {
		if detail.CommissionRate <= 0 {
			response.BadRequest(w, "提成比例必须大于0")
			return
		}

		// 验证提成类型和范围类型的组合是否合法
		if detail.ScopeType == 2 {
			// 仅商品提成、服务提成、寄养提成和活体提成支持指定范围
			if detail.CommissionType != 1 && detail.CommissionType != 2 &&
				detail.CommissionType != 3 && detail.CommissionType != 4 {
				response.BadRequest(w, "该提成类型不支持指定范围")
				return
			}

			// 检查目标对象
			if len(detail.Products) == 0 {
				response.BadRequest(w, "指定范围提成必须选择商品")
				return
			}
		}
	}

	req.ChainId = cast.ToInt64(jwtInfo.ChainId)
	req.StoreId = jwtInfo.TenantId
	req.Operator = jwtInfo.UserName
	req.OperatorId = cast.ToInt64(jwtInfo.CustomerId)
	// 调用服务
	result, err := c.service.CreateCommissionSetup(r.Context(), req)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, result)
}

// UpdateCommission 更新提成设置
// @Summary 提成设置-更新
// @Description 更新提成设置
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param UpdateCommissionSetupReq body omnibus_vo.UpdateCommissionSetupReq true "更新提成设置请求参数"
// @Success 200 {object} omnibus_vo.CommissionSetupResp
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/commission/update [post]
func (c *CommissionController) UpdateCommission(writer http.ResponseWriter, request *http.Request) {
	// 获取登录信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败:", err)
		response.BadRequest(writer, "未登录或登录已过期")
		return
	}

	req, err := utils.Bind[omnibus_vo.UpdateCommissionSetupReq](request)
	if err != nil {
		response.BadRequest(writer, "请求参数解析失败:"+err.Error())
		return
	}

	if len(req.Details) == 0 {
		response.BadRequest(writer, "提成明细不能为空")
		return
	}

	// 验证提成明细
	for _, detail := range req.Details {
		if detail.CommissionRate <= 0 {
			response.BadRequest(writer, "提成比例必须大于0")
			return
		}

		// 验证提成类型和范围类型的组合是否合法
		if detail.ScopeType == 2 {
			// 仅商品提成、服务提成、寄养提成和活体提成支持指定范围
			if detail.CommissionType != 1 && detail.CommissionType != 2 &&
				detail.CommissionType != 3 && detail.CommissionType != 4 {
				response.BadRequest(writer, "该提成类型不支持指定范围")
				return
			}

			// 检查目标对象
			if len(detail.Products) == 0 {
				response.BadRequest(writer, "指定范围提成必须选择商品")
				return
			}
		}
	}
	req.ChainId = cast.ToInt64(jwtInfo.ChainId)
	req.StoreId = jwtInfo.TenantId
	// 设置操作人信息
	req.Operator = jwtInfo.UserName
	req.OperatorId = cast.ToInt64(jwtInfo.CustomerId)

	// 调用服务
	err = c.service.UpdateCommissionSetup(request.Context(), req)
	if err != nil {
		response.BadRequest(writer, err.Error())
		return
	}

	response.Success(writer)
}

// UpdateCommissionStatus 更新提成设置状态
// @Summary 提成设置-更新状态
// @Description 更新提成设置状态
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param UpdateCommissionSetupStatusReq body omnibus_vo.UpdateCommissionSetupStatusReq true "更新提成设置状态请求参数"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/commission/status [post]
func (c *CommissionController) UpdateCommissionStatus(writer http.ResponseWriter, request *http.Request) {
	// 获取登录信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败:", err)
		response.BadRequest(writer, "未登录或登录已过期")
		return
	}

	// 使用Bind解析请求参数
	req, err := utils.Bind[omnibus_vo.UpdateCommissionSetupStatusReq](request)
	if err != nil {
		response.BadRequest(writer, "请求参数校验失败: "+err.Error())
		return
	}
	req.Operator = jwtInfo.UserName
	req.OperatorId = cast.ToInt64(jwtInfo.CustomerId)
	// 调用服务
	err = c.service.ChangeCommissionSetupStatus(request.Context(), req)
	if err != nil {
		response.BadRequest(writer, err.Error())
		return
	}

	response.Success(writer)
}

// GetEmployeeList 获取员工列表
// @Summary 提成设置-获取员工列表
// @Description 获取员工列表，包含选中状态和提成活动名称
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param GetEmployeeListReq query omnibus_vo.GetEmployeeListReq true "获取员工列表请求参数"
// @Success 200 {object} omnibus_vo.GetEmployeeListResp
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/commission/employees [get]
func (c *CommissionController) GetEmployeeList(w http.ResponseWriter, r *http.Request) {
	// 获取登录信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败:", err)
		response.BadRequest(w, "未登录或登录已过期")
		return
	}
	// 解析请求参数
	req, err := utils.Bind[omnibus_vo.GetEmployeeListReq](r)
	if err != nil {
		response.BadRequest(w, "请求参数校验失败: "+err.Error())
		return
	}
	req.SourceChainId = cast.ToInt64(jwtInfo.SourceChainId)
	req.TenantId = cast.ToInt64(jwtInfo.TenantId)
	// 调用服务
	result, err := c.service.GetEmployeeList(r.Context(), req)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, result.List, int(result.Total))
}

// UpdateEmployees 更新提成设置关联的员工
// @Summary 提成设置-更新员工
// @Description 更新提成设置关联的员工
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param UpdateCommissionEmployeesReq body omnibus_vo.UpdateCommissionEmployeesReq true "更新提成员工请求参数"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/commission/employees [post]
func (c *CommissionController) UpdateEmployees(w http.ResponseWriter, r *http.Request) {
	// 获取登录信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败:", err)
		response.BadRequest(w, "未登录或登录已过期")
		return
	}

	// 解析请求参数
	req, err := utils.Bind[omnibus_vo.UpdateCommissionEmployeesReq](r)
	if err != nil {
		response.BadRequest(w, "请求参数校验失败: "+err.Error())
		return
	}

	// 设置操作人信息
	req.Operator = jwtInfo.UserName
	req.OperatorId = cast.ToInt64(jwtInfo.CustomerId)
	req.StoreId = jwtInfo.TenantId

	// 调用服务
	err = c.service.UpdateCommissionEmployees(r.Context(), req)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// GetEmployeePerformance 获取员工业绩列表
// @Summary 提成设置-获取员工业绩列表
// @Description 获取员工业绩列表
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param GetPerformanceListReq query omnibus_vo.GetPerformanceListReq true "获取员工业绩列表请求参数"
// @Success 200 {object} omnibus_vo.GetPerformanceListResp
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/commission/performance [post]
func (c *CommissionController) GetEmployeePerformance(w http.ResponseWriter, r *http.Request) {
	// 获取登录信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败:", err)
		response.BadRequest(w, "未登录或登录已过期")
		return
	}

	// 解析请求参数
	req, err := utils.Bind[omnibus_vo.GetPerformanceListReq](r)
	if err != nil {
		response.BadRequest(w, "请求参数校验失败: "+err.Error())
		return
	}
	req.SourceChainId = cast.ToInt64(jwtInfo.SourceChainId)
	req.StoreId = cast.ToInt64(jwtInfo.TenantId)

	// 调用服务
	result, total, err := c.service.GetEmployeePerformance(req)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	// 导出异步处理
	if req.IsExport == 1 {
		par, _ := json.Marshal(req)
		var task distribution_vo.TaskList
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(r.Header.Get("org_id"))
		task.TaskContent = 104
		s := common.TaskListService{}
		err := s.CreatTask(r, task)
		if err != nil {
			log.Error("导出员工业绩：err=" + err.Error())
			response.BadRequest(w, fmt.Sprintf("导出员工业绩：%s", err.Error()))
			return
		} else {
			response.Success(w)
			return
		}
	}

	response.SuccessWithPage(w, result, int(total))
}

// GetEmployeePerformanceDetail 获取员工业绩详情
// @Summary 提成设置-获取员工业绩详情
// @Description 获取员工业绩详情
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param GetPerformanceDetailReq query omnibus_vo.GetPerformanceDetailReq true "获取员工业绩详情请求参数"
// @Success 200 {object} omnibus_vo.GetPerformanceDetailResp
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/commission/performance/detail [post]
func (c *CommissionController) GetEmployeePerformanceDetail(w http.ResponseWriter, r *http.Request) {
	// 获取登录信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败:", err)
		response.BadRequest(w, "未登录或登录已过期")
		return
	}
	// 解析请求参数
	req, err := utils.Bind[omnibus_vo.GetPerformanceDetailReq](r)
	if err != nil {
		response.BadRequest(w, "请求参数校验失败: "+err.Error())
		return
	}
	req.SourceChainId = cast.ToInt64(jwtInfo.SourceChainId)
	req.StoreId = cast.ToInt64(jwtInfo.TenantId)

	// 如果是导出，则不分页
	if req.IsExport == 1 {
		// 设置一个特殊值（例如 0）表示不分页，服务层会处理
		req.PageIndex = 1
		req.PageSize = 0
	}

	// 调用服务
	result, err := c.service.GetEmployeePerformanceDetail(req)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, result)
}
