package manager

import (
	"context"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	"eShop/services/omnibus-service/services"
	viewmodel "eShop/view-model"
	vo "eShop/view-model/omnibus-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// 获取门店信息
// @Summary -门店信息-获取门店信息
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param StoreGetReq query vo.StoreGetReq true " "
// @Success 200 {object} vo.StoreGet
// @Failure 400 {object} vo.StoreGet
// @Router /omnibus-app/manager/store/info [Post]
func StoreInfo(w http.ResponseWriter, r *http.Request) {
	resp := vo.StoreGetRes{}
	resp.Code = 400
	req, err := utils.Bind[vo.StoreGetReq](r)
	req.OrgId = cast.ToInt(r.Header.Get("org_id"))
	if err != nil {
		log.Error("获取店铺信息，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取店铺信息，参数解析失败：%s", err.Error())
	} else {
		s := services.StoreService{}

		res, err := s.StoreInfo(req)
		if err != nil {
			log.Error("获取店铺信息：err=" + err.Error())
			resp.Message = err.Error()
		} else {
			resp.Code = 200
			resp.Data = res
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// StoreEdit
// @Summary 门店信息-编辑门店信息
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param StoreEditReq body vo.StoreEditReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/store/edit [Post]
func StoreEdit(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400
	req, err := utils.Bind[vo.StoreEditReq](r)
	req.OrgId = cast.ToInt(r.Header.Get("org_id"))
	if err != nil {
		log.Error("获取店铺信息，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取店铺信息，参数解析失败：%s", err.Error())
	} else {
		s := services.StoreService{}

		err := s.StoreThirdRelationEdit(req)
		if err != nil {
			log.Error("获取店铺信息：err=" + err.Error())
			resp.Message = err.Error()
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// 获取门店列表
// @Summary 门店信息-门店列表
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param GetStoreListReq query vo.GetStoreListReq true " "
// @Success 200 {object} vo.GetStoreListRes
// @Failure 400 {object} vo.GetStoreListRes
// @Router /omnibus-app/manager/store/list [Post]
func GetStoreList(writer http.ResponseWriter, r *http.Request) {

	out := vo.GetStoreListRes{}
	out.Code = 400

	req, err := utils.Bind[vo.GetStoreListReq](r)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	req.ChainId = cast.ToInt64(jwtInfo.ChainId)
	req.UserId = cast.ToInt64(jwtInfo.UserId)
	if cast.ToInt(jwtInfo.TenantId) > 0 {
		req.StoreId = cast.ToInt(jwtInfo.TenantId)
	}
	server := services.StoreService{}
	if out.Data, err = server.GetStoreList(req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// 获取门店列表
// @Summary 门店信息-已下发门店列表
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param GetDistriStoreListReq query vo.GetDistriStoreListReq true " "
// @Success 200 {object} vo.GetDistriStoreListRes
// @Failure 400 {object} vo.GetDistriStoreListRes
// @Router /omnibus-app/manager/store/distrilist [Post]
func GetDistriStoreList(w http.ResponseWriter, r *http.Request) {
	resp := vo.GetDistriStoreListRes{}
	resp.Code = 400
	req, err := utils.Bind[vo.GetDistriStoreListReq](r)
	if err != nil {
		log.Error("获取已下发店铺列表，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取已下发店铺列表，参数解析失败：%s", err.Error())
	} else {
		s := services.StoreService{}
		resp.Data, err = s.GetDistriStoreList(req)

		if err != nil {
			log.Error("获取已下发店铺列表：err=" + err.Error())
			resp.Message = err.Error()
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// 获取履约配置
// @Summary 门店信息-履约配置
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param GetWarehouseConfigReq query vo.GetWarehouseConfigReq true " "
// @Success 200 {object} vo.GetWarehouseConfigResp
// @Failure 400 {object} vo.GetWarehouseConfigResp
// @Router /omnibus-app/manager/warehouse/config [Post]
func GetWarehouseConfig(w http.ResponseWriter, r *http.Request) {
	resp := vo.GetWarehouseConfigResp{}
	resp.Code = 400

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		resp.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(resp)
		w.Write(out2)
		return
	}

	req, err := utils.Bind[vo.GetWarehouseConfigReq](r)
	if err != nil {
		log.Error("获取门店的履约配置，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取门店的履约配置，参数解析失败：%s", err.Error())
	} else {
		req.StoreId = jwtInfo.TenantId
		s := services.StoreService{}
		resp.Data, err = s.GetWarehouseConfig(req)

		if err != nil {
			log.Error("获取门店的履约配置：err=" + err.Error())
			resp.Message = err.Error()
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// 履约配置
// @Summary 门店信息-履约配置
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param AddOrEditWarehouseReq query vo.AddOrEditWarehouseReq true " "
// @Success 200 {object} vo.AddOrEditWarehouseResp
// @Failure 400 {object} vo.AddOrEditWarehouseResp
// @Router /omnibus-app/manager/warehouse/edit [Post]
func AddOrEditWarehouse(w http.ResponseWriter, r *http.Request) {
	resp := vo.AddOrEditWarehouseResp{}
	resp.Code = 400
	req, err := utils.Bind[vo.AddOrEditWarehouseReq](r)
	if err != nil {
		log.Error("新增或编辑门店的履约配置，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("新增或编辑门店的履约配置，参数解析失败：%s", err.Error())
	} else {
		vErr := validate.Validate(req, "")
		if vErr != nil {
			log.Errorf("新增或编辑门店的履约配置-参数校验失败：err=%s", utils.InterfaceToJSON(vErr))
			resp.Message = fmt.Sprintf("参数校验失败%s", vErr)
			out2, _ := json.Marshal(resp)
			w.Write(out2)
			return
		}

		jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
		if err != nil {
			log.Error("获取登录信息失败: err", err.Error())
			resp.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
			out2, _ := json.Marshal(resp)
			w.Write(out2)
			return
		}
		req.StoreId = jwtInfo.TenantId
		s := services.StoreService{}
		err = s.AddOrEditWarehouse(req)
		if err != nil {
			log.Error("编辑门店的履约配置失败，err=" + err.Error())
			resp.Message = "编辑门店的履约配置失败" + err.Error()
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// SaveDeliveryConfig 保存配送方式配置
// @Summary 门店信息-保存配送方式配置
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param DeliveryConfigReq body vo.DeliveryConfigReq true "配送方式配置请求"
// @Success 200 {object} vo.DeliveryConfigRes
// @Failure 400 {object} vo.DeliveryConfigRes
// @Router /omnibus-app/manager/store/delivery/save [post]
func SaveDeliveryConfig(w http.ResponseWriter, r *http.Request) {
	resp := vo.DeliveryConfigRes{}
	resp.Code = 400

	req, err := utils.Bind[vo.DeliveryConfigReq](r)
	if err != nil {
		log.Error("保存配送方式配置，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("保存配送方式配置，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 获取JWT信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		resp.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	s := services.StoreService{}
	req.OrgId = cast.ToInt(r.Header.Get("org_id"))
	req.FinanceCode = cast.ToString(jwtInfo.TenantId)
	err = s.SaveDeliveryConfig(req)
	if err != nil {
		log.Error("保存配送方式配置失败：err=", err.Error())
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// GetDeliveryConfig 获取配送方式配置
// @Summary 门店信息-获取配送方式配置
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param GetDeliveryConfigReq body vo.GetDeliveryConfigReq true "获取配送方式配置请求"
// @Success 200 {object} vo.GetDeliveryConfigRes
// @Failure 400 {object} vo.GetDeliveryConfigRes
// @Router /omnibus-app/manager/store/delivery/get [post]
func GetDeliveryConfig(w http.ResponseWriter, r *http.Request) {
	resp := vo.GetDeliveryConfigRes{}
	resp.Code = 400

	req, err := utils.Bind[vo.GetDeliveryConfigReq](r)
	if err != nil {
		log.Error("获取配送方式配置，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取配送方式配置，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 获取JWT信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		resp.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	req.FinanceCode = jwtInfo.TenantId
	req.OrgId = cast.ToInt(r.Header.Get("org_id"))
	s := services.StoreService{}
	data, err := s.GetDeliveryConfig(req)
	if err != nil {
		log.Error("获取配送方式配置失败：err=", err.Error())
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	resp.Code = 200
	resp.Data = data
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// GetPrintType 获取打印类型
// @Summary 获取打印类型
// @Description 获取店铺打印类型(0:本地打印 1:飞鹅打印)
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param UpdatePrintTypeRequest body omnibus_vo.UpdatePrintTypeRequest true "修改打印类型请求"
// @Success 200 {object} omnibus_vo.PrintTypeResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/store/print-type [POST]
func GetPrintType(w http.ResponseWriter, r *http.Request) {
	// 获取财务编码
	var req vo.UpdatePrintTypeRequest
	req, err := utils.Bind[vo.UpdatePrintTypeRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}
	c := services.StoreService{}
	// 调用服务层获取打印类型
	res, err := c.GetPrintType(context.Background(), req.FinanceCode)
	if err != nil {
		log.Error("获取打印类型失败:", err)
		response.InternalError(w, "获取打印类型失败")
		return
	}

	response.SuccessWithData(w, vo.PrintTypeResponse{
		WhatPrint: res.WhatPrint,
		IsCheck:   res.IsCheck,
	})
}

// UpdatePrintType 修改打印类型
// @Summary 修改打印类型
// @Description 修改店铺打印类型(0:本地打印 1:飞鹅打印)
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param UpdatePrintTypeRequest body omnibus_vo.UpdatePrintTypeRequest true "修改打印类型请求"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/store/update-print [post]
func UpdatePrintType(w http.ResponseWriter, r *http.Request) {
	// 参数绑定和校验
	var req vo.UpdatePrintTypeRequest
	req, err := utils.Bind[vo.UpdatePrintTypeRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}

	c := services.StoreService{}
	// 调用服务层修改打印类型
	if err := c.UpdatePrintType(context.Background(), req); err != nil {
		log.Error("修改打印类型失败:", err)
		response.InternalError(w, "修改打印类型失败")
		return
	}

	response.Success(w)
}
