package manager

import (
	"context"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/omnibus-service/services"
	viewmodel "eShop/view-model"
	omnibus_vo "eShop/view-model/omnibus-vo"
	"encoding/json"

	"github.com/spf13/cast"

	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-playground/validator/v10"
)

type SmsController struct {
	service  services.SmsService
	validate *validator.Validate
}

func NewSmsController(service services.SmsService) *SmsController {
	return &SmsController{
		service:  service,
		validate: validator.New(),
	}
}

func (c *SmsController) RegisterRoutes(r chi.Router) {
	r.Route("/omnibus-app/manager/sms", func(r chi.Router) {
		r.Get("/config", c.GetSmsConfig)
		r.Post("/config/save", c.SaveSmsConfig)
		r.Post("/refund/list", c.GetRefundSmsList)
		r.Post("/orders", c.QuerySmsOrders)
		r.Post("/records", c.QuerySmsSendRecords)
		r.Post("/order/create", c.CreateSmsOrder)
		r.Post("/record/create", c.CreateSmsSendRecord)
		r.Get("/order/detail", c.GetSmsOrderDetail)
		r.Post("/report", c.SmsReport)
		r.Get("/refund/detail", c.GetRefundSmsDetail)
		r.Post("/refund/audit", c.AuditRefundSms)
		r.Post("/verify/send", c.SendVerifyCode)
		r.Post("/verify/check", c.VerifyCode)
		r.Post("/refund/apply", c.ApplySmsRefund)
		r.Post("/send", c.SendMessage)
		r.Post("/read-all", c.ReadAllMessages)
		r.Post("/task-do", c.taskDo)
	})
}

// GetSmsConfig 获取短信配置
// @Summary 设置中心-短信配置
// @Description 获取短信配置
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Success 200 {object} omnibus_vo.SmsConfigResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/config [get]
func (c *SmsController) GetSmsConfig(w http.ResponseWriter, r *http.Request) {
	// 获取登录信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败:", err)
		response.BadRequest(w, "未登录或登录已过期")
		return
	}

	storeId := jwtInfo.TenantId
	ctx := context.Background()
	// 直接获取转换后的视图模型数据
	items, err := c.service.GetSmsConfigs(ctx, storeId)
	if err != nil {
		log.Error("获取短信配置失败:", err)
		response.InternalError(w, "获取短信配置失败")
		return
	}

	response.SuccessWithData(w, items)
}

// SaveSmsConfig 保存短信配置
// @Summary 设置中心-短信配置
// @Description 保存短信配置
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param SmsConfigRequest body omnibus_vo.SmsConfigRequest true "短信配置提交参数"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/config/save [post]
func (c *SmsController) SaveSmsConfig(w http.ResponseWriter, r *http.Request) {
	// 获取登录信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败:", err)
		response.BadRequest(w, "未登录或登录已过期")
		return
	}

	// 参数绑定和校验
	var req omnibus_vo.SmsConfigRequest
	req, err = utils.Bind[omnibus_vo.SmsConfigRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}

	// 手动校验必填字段
	if err := c.validate.Struct(req); err != nil {
		log.Error("参数校验失败:", err)
		response.BadRequest(w, "参数校验失败:"+err.Error())
		return
	}

	// 对每个配置项进行预处理和验证
	for i, item := range req.Data {
		// 如果启用，则需要验证设置是否正确
		if item.IsEnabled {
			// 对于需要设置过期天数的配置类型
			if item.ConfigType == "store_card_expire" || item.ConfigType == "time_card_expire" || item.ConfigType == "coupon_expire" {
				// 若启用，则必须设置日期
				if !item.IsSetDate {
					log.Error("短信配置验证失败: 当启用配置时，必须设置日期")
					response.BadRequest(w, "当启用"+item.ConfigName+"时，必须设置提前提醒天数")
					return
				}

				// 验证过期天数是否在1-7天范围内
				if item.ExpireDays < 1 || item.ExpireDays > 7 {
					log.Error("短信配置验证失败: 过期天数必须在1-7天范围内")
					response.BadRequest(w, item.ConfigName+"的提前提醒天数必须在1-7天范围内")
					return
				}
			} else {
				// 其他类型的配置不需要设置过期天数
				item.IsSetDate = false
				item.ExpireDays = 0
			}
		} else {
			// 如果未启用，不需要设置日期
			item.IsSetDate = false
			item.ExpireDays = 0
		}

		// 更新处理后的数据
		req.Data[i] = item
	}

	req.StoreId = jwtInfo.TenantId

	// 直接传递请求对象给服务层处理
	if err := c.service.BatchSaveSmsConfigs(context.Background(), req); err != nil {
		log.Error("保存短信配置失败:", err)
		response.BadRequest(w, "保存短信配置失败")
		return
	}

	// 记录操作日志
	log.Info("用户保存短信配置:", jwtInfo.UserId, "店铺ID:", req.StoreId)

	response.Success(w)
}

// GetRefundSmsList 获取退单短信列表
// @Summary 退单短信-列表
// @Description 获取退单短信列表
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param SmsRefundListRequest body omnibus_vo.SmsRefundListRequest true "退单短信列表查询参数"
// @Success 200 {object} omnibus_vo.SmsRefundListResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/refund/list [post]
func (c *SmsController) GetRefundSmsList(w http.ResponseWriter, r *http.Request) {
	var req omnibus_vo.SmsRefundListRequest
	req, err := utils.Bind[omnibus_vo.SmsRefundListRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取列表数据
	data, total, err := c.service.GetSmsRefundList(context.Background(), req)
	if err != nil {
		log.Error("获取退单短信列表失败:", err)
		response.InternalError(w, "获取退单短信列表失败")
		return
	}

	resp := omnibus_vo.SmsRefundListResponse{
		BasePageHttpResponse: viewmodel.BasePageHttpResponse{
			Code:    200,
			Message: "success",
			Total:   int(total),
		},
		Data: data,
	}

	response.SuccessWithPage(w, resp.Data, resp.Total)
}

// QuerySmsOrders 查询短信订单
// @Summary 查询短信订单
// @Description 查询短信订单
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param SmsOrderQueryRequest body omnibus_vo.SmsOrderQueryRequest true "创建短信订单参数"
// @Success 200 {object} omnibus_vo.SmsOrderResponse
// @Failure 400 {object} omnibus_vo.SmsOrderResponse
// @Router /omnibus-app/manager/sms/orders [post]
func (c *SmsController) QuerySmsOrders(w http.ResponseWriter, r *http.Request) {
	// 获取登录信息
	//jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	//if err != nil {
	//	log.Error("获取登录信息失败:", err)
	//	response.BadRequest(w, "未登录或登录已过期")
	//	return
	//}

	// 参数绑定和校验
	var req omnibus_vo.SmsOrderQueryRequest
	req, err := utils.Bind[omnibus_vo.SmsOrderQueryRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}

	// 设置店铺ID
	//req.StoreId = jwtInfo.TenantId

	// 调用服务层查询
	resp, err := c.service.QuerySmsOrders(context.Background(), req)
	if err != nil {
		log.Error("查询短信订单失败:", err)
		response.InternalError(w, "查询短信订单失败")
		return
	}

	response.SuccessWithPage(w, resp.Data, resp.Total)
}

// QuerySmsSendRecords 查询短信发送记录
// @Summary 查询短信发送记录
// @Description 查询短信发送记录
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param SmsSendRecordQueryRequest body omnibus_vo.SmsSendRecordQueryRequest true "查询发送记录参数"
// @Success 200 {object} omnibus_vo.SmsSendRecordResponse
// @Failure 400 {object} omnibus_vo.SmsSendRecordResponse
// @Router /omnibus-app/manager/sms/records [post]
func (c *SmsController) QuerySmsSendRecords(w http.ResponseWriter, r *http.Request) {
	// 获取登录信息
	//jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	//if err != nil {
	//	log.Error("获取登录信息失败:", err)
	//	response.BadRequest(w, "未登录或登录已过期")
	//	return
	//}

	// 参数绑定和校验
	var req omnibus_vo.SmsSendRecordQueryRequest
	req, err := utils.Bind[omnibus_vo.SmsSendRecordQueryRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}

	// 设置店铺ID
	//req.StoreId = jwtInfo.TenantId

	// 调用服务层查询
	resp, err := c.service.QuerySmsSendRecords(context.Background(), req)
	if err != nil {
		log.Error("查询短信发送记录失败:", err)
		response.InternalError(w, "查询短信发送记录失败")
		return
	}

	response.SuccessWithPage(w, resp.Data, resp.Total)
}

// CreateSmsOrder 创建短信订单
// @Summary 创建短信订单
// @Description 创建短信订单
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param SmsOrderCreateRequest body omnibus_vo.SmsOrderCreateRequest true "创建短信订单参数"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/order/create [post]
func (c *SmsController) CreateSmsOrder(w http.ResponseWriter, r *http.Request) {
	// 获取登录信息
	//jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	//if err != nil {
	//	log.Error("获取登录信息失败:", err)
	//	response.BadRequest(w, "未登录或登录已过期")
	//	return
	//}

	// 参数绑定和校验
	var req omnibus_vo.SmsOrderCreateRequest
	req, err := utils.Bind[omnibus_vo.SmsOrderCreateRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}

	// 手动校验必填字段
	if err := c.validate.Struct(req); err != nil {
		log.Error("参数校验失败:", err)
		response.BadRequest(w, "参数校验失败:"+err.Error())
		return
	}

	//req.StoreId = jwtInfo.TenantId
	//req.Operator = jwtInfo.UserName
	//req.OperatorId = cast.ToInt64(jwtInfo.UserId)

	// 调用服务层创建订单
	if err := c.service.CreateSmsOrder(context.Background(), req); err != nil {
		log.Error("创建短信订单失败:", err)
		response.InternalError(w, "创建短信订单失败")
		return
	}

	resp := viewmodel.BaseHttpResponse{
		Code:    200,
		Message: "success",
	}

	response.SuccessWithData(w, resp)
}

// CreateSmsSendRecord 创建短信发送记录
// @Summary 创建短信发送记录
// @Description 创建短信发送记录
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/record/create [post]
func (c *SmsController) CreateSmsSendRecord(w http.ResponseWriter, r *http.Request) {
	// 获取登录信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败:", err)
		response.BadRequest(w, "未登录或登录已过期")
		return
	}

	// 参数绑定和校验
	var req omnibus_vo.SmsSendRecordCreateRequest
	req, err = utils.Bind[omnibus_vo.SmsSendRecordCreateRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}

	// 手动校验必填字段
	if err := c.validate.Struct(req); err != nil {
		log.Error("参数校验失败:", err)
		response.BadRequest(w, "参数校验失败:"+err.Error())
		return
	}

	req.StoreId = jwtInfo.TenantId

	// 调用服务层创建发送记录
	if err := c.service.CreateSmsSendRecord(context.Background(), req); err != nil {
		log.Error("创建短信发送记录失败:", err)
		response.InternalError(w, "创建短信发送记录失败")
		return
	}

	resp := viewmodel.BaseHttpResponse{
		Code:    200,
		Message: "success",
	}

	response.SuccessWithData(w, resp)
}

// GetSmsOrderDetail 获取短信订单详情
// @Summary 获取短信订单详情
// @Description 获取短信订单详情
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param SmsOrderGetRequest body omnibus_vo.SmsOrderGetRequest true "创建短信订单参数"
// @Success 200 {object} omnibus_vo.SmsOrderDetailResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/order/detail [Post]
func (c *SmsController) GetSmsOrderDetail(w http.ResponseWriter, r *http.Request) {
	// 获取订单ID
	// 参数绑定和校验
	var req omnibus_vo.SmsOrderGetRequest
	req, err := utils.Bind[omnibus_vo.SmsOrderGetRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}

	// 调用服务层获取详情
	resp, err := c.service.GetSmsOrderDetail(context.Background(), cast.ToInt64(req.Id))
	if err != nil {
		log.Error("获取短信订单详情失败:", err)
		response.InternalError(w, "获取短信订单详情失败")
		return
	}

	response.SuccessWithData(w, resp.Data)
}

// SmsReport 处理阿里云短信回执
// @Summary 处理阿里云短信回执
// @Description 处理阿里云短信发送状态回执
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/report [post]
func (c *SmsController) SmsReport(w http.ResponseWriter, r *http.Request) {
	// 读取请求体
	var reports []omnibus_vo.SmsReportRequest
	if err := json.NewDecoder(r.Body).Decode(&reports); err != nil {
		log.Error("解析短信回执失败:", err)
		response.JSON(w, http.StatusBadRequest, "解析回执失败", map[string]interface{}{
			"code": 0,
			"msg":  "解析回执失败",
		})
		return
	}

	// 调用服务层处理回执
	if err := c.service.HandleSmsReport(context.Background(), reports); err != nil {
		log.Error("处理短信回执失败:", err)
		response.JSON(w, http.StatusInternalServerError, "处理回执失败", map[string]interface{}{
			"code": 0,
			"msg":  "处理回执失败",
		})
		return
	}

	// 按阿里云要求格式响应
	response.JSON(w, http.StatusOK, "接收成功", map[string]interface{}{
		"code": 0,
		"msg":  "接收成功",
	})
}

// GetRefundSmsDetail 获取退款记录详情
// @Summary 退款记录详情
// @Description 获取退款记录详情
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param id query string true "退款记录ID"
// @Success 200 {object} omnibus_vo.SmsRefundDetailResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/refund/detail [get]
func (c *SmsController) GetRefundSmsDetail(w http.ResponseWriter, r *http.Request) {
	id := r.URL.Query().Get("id")
	if id == "" {
		response.BadRequest(w, "退款id不能为空")
		return
	}
	// 调用服务层获取详情
	refundData, err := c.service.GetSmsRefundDetail(context.Background(), cast.ToInt64(id))
	if err != nil {
		log.Error("获取退款记录详情失败:", err)
		response.InternalError(w, "获取退款记录详情失败")
		return
	}

	if refundData == nil {
		response.BadRequest(w, "退款记录不存在")
		return
	}

	response.SuccessWithData(w, refundData)
}

// AuditRefundSms 审核退款
// @Summary 审核退款
// @Description 审核退款（通过/拒绝）
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param SmsRefundAuditRequest body omnibus_vo.SmsRefundAuditRequest true "退款审核请求"
// @Success 200 {object} omnibus_vo.SmsRefundAuditResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/refund/audit [post]
func (c *SmsController) AuditRefundSms(w http.ResponseWriter, r *http.Request) {
	// 获取登录信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败:", err)
		response.BadRequest(w, "未登录或登录已过期")
		return
	}

	// 参数绑定和校验
	var req omnibus_vo.SmsRefundAuditRequest
	req, err = utils.Bind[omnibus_vo.SmsRefundAuditRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}

	// 手动校验必填字段
	if err := c.validate.Struct(req); err != nil {
		log.Error("参数校验失败:", err)
		response.BadRequest(w, "参数校验失败:"+err.Error())
		return
	}

	// 设置操作人
	req.Operator = jwtInfo.UserName

	// 调用服务层审核
	if err := c.service.AuditSmsRefund(req, nil); err != nil {
		log.Error("审核退款失败:", err)
		response.InternalError(w, "审核退款失败:"+err.Error())
		return
	}

	response.Success(w)
}

// SendVerifyCode 发送短信验证码
// @Summary 发送短信验证码
// @Description 发送短信验证码(登录验证/支付验证)
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param SendVerifyCodeRequest body omnibus_vo.SendVerifyCodeRequest true "发送验证码请求"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/verify/send [post]
func (c *SmsController) SendVerifyCode(w http.ResponseWriter, r *http.Request) {
	// 参数绑定和校验
	var req omnibus_vo.SendVerifyCodeRequest
	req, err := utils.Bind[omnibus_vo.SendVerifyCodeRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}

	// 手动校验必填字段
	if err := c.validate.Struct(req); err != nil {
		log.Error("参数校验失败:", err)
		response.BadRequest(w, "参数校验失败:"+err.Error())
		return
	}

	// 调用服务层发送验证码
	if err := c.service.SendVerifyCode(context.Background(), req); err != nil {
		log.Error("发送验证码失败:", err)
		response.InternalError(w, "发送验证码失败:"+err.Error())
		return
	}

	response.Success(w)
}

// VerifyCode 验证短信验证码
// @Summary 验证短信验证码
// @Description 验证短信验证码(登录验证/支付验证)
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param VerifyCodeRequest body omnibus_vo.VerifyCodeRequest true "验证码验证请求"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/verify/check [post]
func (c *SmsController) VerifyCode(w http.ResponseWriter, r *http.Request) {
	// 参数绑定和校验
	var req omnibus_vo.VerifyCodeRequest
	req, err := utils.Bind[omnibus_vo.VerifyCodeRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}

	// 手动校验必填字段
	if err := c.validate.Struct(req); err != nil {
		log.Error("参数校验失败:", err)
		response.BadRequest(w, "参数校验失败:"+err.Error())
		return
	}

	// 调用服务层验证验证码
	if err := c.service.VerifyCode(context.Background(), req); err != nil {
		log.Error("验证码验证失败:", err)
		response.InternalError(w, "验证码验证失败:"+err.Error())
		return
	}

	response.Success(w)
}

// ApplySmsRefund 申请短信订单退款
// @Summary 申请短信订单退款
// @Description 申请短信订单退款
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param SmsRefundApplyRequest body omnibus_vo.SmsRefundApplyRequest true "退款申请请求"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/refund/apply [post]
func (c *SmsController) ApplySmsRefund(w http.ResponseWriter, r *http.Request) {
	//// 获取登录信息
	//jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	//if err != nil {
	//	log.Error("获取登录信息失败:", err)
	//	response.BadRequest(w, "未登录或登录已过期")
	//	return
	//}

	// 参数绑定和校验
	var req omnibus_vo.SmsRefundApplyRequest
	req, err := utils.Bind[omnibus_vo.SmsRefundApplyRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}

	// 手动校验必填字段
	if err := c.validate.Struct(req); err != nil {
		log.Error("参数校验失败:", err)
		response.BadRequest(w, "参数校验失败:"+err.Error())
		return
	}

	// 设置操作人信息
	//req.ChainId = cast.ToInt64(jwtInfo.ChainId)
	//req.StoreId = jwtInfo.TenantId

	// 调用服务层处理退款申请
	if err := c.service.ApplySmsRefund(context.Background(), req); err != nil {
		log.Error("申请退款失败:", err)
		response.InternalError(w, "申请退款失败:"+err.Error())
		return
	}

	response.Success(w)
}

// SendMessage 发送短信
// @Summary 发送短信
// @Description 发送短信(消耗店铺短信条数)
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param SmsSendMessageRequest body omnibus_vo.SmsSendMessageRequest true "发送短信请求"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/send [post]
func (c *SmsController) SendMessage(w http.ResponseWriter, r *http.Request) {

	// 参数绑定和校验
	var req omnibus_vo.SmsSendMessageRequest
	req, err := utils.Bind[omnibus_vo.SmsSendMessageRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}

	// 手动校验必填字段
	if err := c.validate.Struct(req); err != nil {
		log.Error("参数校验失败:", err)
		response.BadRequest(w, "参数校验失败:"+err.Error())
		return
	}

	// 调用服务层发送短信
	if err := c.service.SendMessage(req); err != nil {
		log.Error("发送短信失败:", err)
		response.InternalError(w, "发送短信失败:"+err.Error())
		return
	}

	response.Success(w)
}

// ReadAllMessages 一键已读所有消息
// @Summary 一键已读所有消息
// @Description 将指定用户的所有未读消息标记为已读
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param memberMain query string true "用户ID"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/read-all [post]
func (c *SmsController) ReadAllMessages(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	// 获取登录信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败:", err)
		response.BadRequest(w, "未登录或登录已过期")
		return
	}

	// 调用服务层处理一键已读
	if err := c.service.ReadAllMessages(context.Background(), jwtInfo.UserId); err != nil {
		log.Error("一键已读消息失败:", err)
		response.InternalError(w, "一键已读消息失败:"+err.Error())
		return
	}

	response.Success(w)
}

// VerifyCode 验证短信验证码
// @Summary 验证短信验证码
// @Description 验证短信验证码(登录验证/支付验证)
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param TaskDoRequest body omnibus_vo.TaskDoRequest true "验证码验证请求"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Failure 500 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/sms/task-do [post]
func (c *SmsController) taskDo(w http.ResponseWriter, r *http.Request) {
	// 参数绑定和校验
	var req omnibus_vo.TaskDoRequest
	req, err := utils.Bind[omnibus_vo.TaskDoRequest](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}

	// 调用服务层验证验证码
	if err := c.service.TaskDo(context.Background(), req); err != nil {
		log.Error("失败:", err)
		response.InternalError(w, "失败:"+err.Error())
		return
	}

	response.Success(w)
}
