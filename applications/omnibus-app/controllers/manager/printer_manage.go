package manager

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/omnibus-service/services"
	viewmodel "eShop/view-model"
	vo "eShop/view-model/omnibus-vo"
	"encoding/json"
	"fmt"
	"net/http"
)

// 删除打印机
// @Summary 设置中心-打印设置-删除打印机
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param DelPrinterReq query vo.DelPrinterReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/printer/del [Post]
func DelPrinter(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	req, err := utils.Bind[vo.DelPrinterReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	req.StoreId = jwtInfo.TenantId

	server := services.PrinterService{}
	if err := server.DelPrinter(req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "删除成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// 添加、编辑打印机
// @Summary 设置中心-打印设置-添加、编辑打印机
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param AddPrinterReq query vo.AddPrinterReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/printer/add [Post]
func AddPrinter(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	req, err := utils.Bind[vo.AddPrinterReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	req.StoreId = jwtInfo.TenantId

	server := services.PrinterService{}
	if err := server.AddPrinter(req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "操作成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// 打印机详情
// @Summary 设置中心-打印设置-打印机详情
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param PrinterInfoReq query vo.PrinterInfoReq true " "
// @Success 200 {object} vo.PrinterInfoRes
// @Failure 400 {object} vo.PrinterInfoRes
// @Router /omnibus-app/manager/printer/info [Post]
func PrinterInfo(writer http.ResponseWriter, request *http.Request) {
	out := vo.PrinterInfoRes{}
	out.Code = 400
	req, err := utils.Bind[vo.PrinterInfoReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	req.StoreId = jwtInfo.TenantId

	server := services.PrinterService{}
	if out.Data, err = server.PrinterInfo(req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "查询成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// 打印收银台小票
// @Summary 打印收银台小票
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept  json
// @Produce  json
// @Param order_sn query string true "订单号"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/printer/print_cashier [Post]
func PrintCashierTicket(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{Code: 400}
	req, err := utils.Bind[vo.PrintTicketReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.PrinterService{}
	if err := server.PrintCashierTicket(req.OrderSn); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out.Message = "打印成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// 打印充值开卡小票
// @Summary 打印充值开卡小票
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept  json
// @Produce  json
// @Param order_sn query string true "订单号"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/printer/print_recharge [Post]
func PrintRechargeTicket(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{Code: 400}
	req, err := utils.Bind[vo.PrintTicketReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.PrinterService{}
	if err := server.PrintRechargeTicket(req.OrderSn); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out.Message = "打印成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// 打印预存押金小票
// @Summary 打印预存押金小票
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept  json
// @Produce  json
// @Param order_sn query string true "订单号"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/printer/print_deposit [Post]
func PrintDepositTicket(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{Code: 400}
	req, err := utils.Bind[vo.PrintTicketReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.PrinterService{}
	if err := server.PrintDepositTicket(req.OrderSn); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out.Message = "打印成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// 获取收银台小票信息
// @Summary 获取收银台小票信息
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept  json
// @Produce  json
// @Param order_sn query string true "订单号"
// @Success 200 {object} vo.CashierOrderInfoRes
// @Router /omnibus-app/manager/printer/cashier_ticket_info [Post]
func GetCashierTicketInfo(writer http.ResponseWriter, request *http.Request) {
	out := vo.CashierOrderInfoRes{Code: 400}
	req, err := utils.Bind[vo.PrintTicketReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.PrinterService{}

	data, err := server.GetCashierTicketInfo(req.OrderSn)
	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out.Message = "查询成功"
	out.Data = data
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// 获取充值开卡小票信息
// @Summary 获取充值开卡小票信息
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept  json
// @Produce  json
// @Param order_sn query string true "订单号"
// @Success 200 {object} vo.RechargeTicketInfoRes
// @Router /omnibus-app/manager/printer/recharge_ticket_info [Post]
func GetRechargeTicketInfo(writer http.ResponseWriter, request *http.Request) {
	out := vo.RechargeTicketInfoRes{Code: 400}
	req, err := utils.Bind[vo.PrintTicketReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.PrinterService{}
	data, err := server.GetRechargeTicketInfo(req.OrderSn)
	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out.Message = "查询成功"
	out.Data = data
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// 获取预存押金小票信息
// @Summary 获取预存押金小票信息
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept  json
// @Produce  json
// @Param order_sn query string true "订单号"
// @Success 200 {object} vo.DepositTicketInfoRes
// @Router /omnibus-app/manager/printer/deposit_ticket_info [Post]
func GetDepositTicketInfo(writer http.ResponseWriter, request *http.Request) {
	out := vo.DepositTicketInfoRes{Code: 400}
	req, err := utils.Bind[vo.PrintTicketReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.PrinterService{}
	data, err := server.GetDepositTicketInfo(req.OrderSn)
	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out.Message = "查询成功"
	out.Data = data
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
