package manager

import "github.com/go-chi/chi/v5"

func InitAllManager(r chi.Router) {
	InitUserRouter(r)
	// 门店后端路由
	InitStoreRouter(r)

	// 打印机路由
	InitPrinterRouter(r)

	//异步任务 端口
	InitAsyncRouter(r)

	// 统计相关路由
	InitStatisticsRouter(r)
}

func InitUserRouter(r chi.Router) {
	r.Route("/omnibus-app/user", func(r chi.Router) {
		r.Post("/token", GetToken)
	})
}

func InitStoreRouter(r chi.Router) {

	r.Route("/omnibus-app/manager", func(r chi.Router) {
		// 获取店铺信息
		r.Post("/store/info", StoreInfo)
		// 获取店铺信息
		r.Post("/store/edit", StoreEdit)
		// 获取店铺列表
		r.Post("/store/list", GetStoreList)
		// 已下发店铺列表
		r.Post("/store/distrilist", GetDistriStoreList)

		// 履约配置
		r.Post("/warehouse/edit", AddOrEditWarehouse)
		// 获取履约配置
		r.Post("/warehouse/config", GetWarehouseConfig)
		// 保存履约配置
		r.Post("/store/delivery/save", SaveDeliveryConfig)
		// 获取配送方式配置
		r.Post("/store/delivery/get", GetDeliveryConfig)

		r.Post("/store/print-type", GetPrintType)      // 获取打印类型
		r.Post("/store/update-print", UpdatePrintType) // 修改打印类型
	})
}

func InitAsyncRouter(r chi.Router) {

	r.Route("/omnibus-app/manager/async", func(r chi.Router) {
		// 获取店铺信息
		r.Post("/add", AsyncAdd)
		// 获取店铺信息
		r.Post("/list", GetAsyncTaskList)

		// 导入导出异步任务
		r.Post("/export_import/add", TaskImport)
	})
}

func InitPrinterRouter(r chi.Router) {
	r.Route("/omnibus-app/manager/printer", func(r chi.Router) {
		// 打印机相关
		r.Post("/info", PrinterInfo)
		r.Post("/add", AddPrinter)
		r.Post("/del", DelPrinter)

		// 打印相关
		r.Post("/print_cashier", PrintCashierTicket)
		r.Post("/print_recharge", PrintRechargeTicket)
		r.Post("/print_deposit", PrintDepositTicket)

		// 小票信息相关
		r.Post("/cashier_ticket_info", GetCashierTicketInfo)
		r.Post("/recharge_ticket_info", GetRechargeTicketInfo)
		r.Post("/deposit_ticket_info", GetDepositTicketInfo)
	})
}

func InitStatisticsRouter(r chi.Router) {
	r.Route("/omnibus-app/manager/statistics", func(r chi.Router) {
		r.Post("/overview", GetStatisticsOverview)
		r.Post("/trend", GetStatisticsTrend)
	})
}
