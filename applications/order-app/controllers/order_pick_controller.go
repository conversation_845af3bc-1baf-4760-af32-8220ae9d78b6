package controllers

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/order-service/services"
	vo "eShop/view-model/order-vo"
	"fmt"
	"net/http"
	"sync"

	"github.com/go-chi/chi/v5"
	"github.com/spf13/cast"
)

func NewOrderPickController(service services.OrderPickService) *OrderPickController {
	return &OrderPickController{
		service: service,
	}
}

type OrderPickController struct {
	service services.OrderPickService
}

func (w OrderPickController) RegisterRoutes(r chi.Router) {
	r.Route("/order-app/manager/order", func(r chi.Router) {
		r.Post("/pick/list", w.GetPickingTaskList)
		r.Post("/pick/receive", w.ReceivePickingTask)
		r.Post("/pick/complete", w.CompletePickingTask)
		r.Get("/pick/statistics", w.PickingTaskStatistics) //拣货任务统计
	})
}

// GetPickingTaskList 获取拣货任务列表
// @Summary 获取拣货任务列表
// @Description 获取拣货任务列表
// @Tags 宠物saas-B端小程序-拣货任务
// @Accept json
// @Produce json
// @Param command body order_vo.GetOrderListReq true "获取拣货任务列表"
// @Success 200 {object} []order_vo.GetOrderListRes "成功"
// @Failure 400 {object} []order_vo.GetOrderListRes "请求错误"
// @Router /order-app/manager/order/pick/list [post]
func (c *OrderPickController) GetPickingTaskList(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.GetOrderListReq](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())

		response.BadRequest(w, err.Error())
	}
	cmd.ShopId = jwtInfo.TenantId
	cmd.PickupUserId = cast.ToInt64(jwtInfo.UserId)
	logPrefix := fmt.Sprintf("获取拣货列表，入参:%s,用户信息%s", utils.JsonEncode(cmd), utils.JsonEncode(jwtInfo))
	log.Info(logPrefix)
	v, total, err := c.service.GetPickingTaskList(cmd)
	if err != nil {
		log.Error(logPrefix, "获取拣货任务列表失败err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, v, int(total))

}

// PickingTaskStatistics 拣货任务统计
// @Summary 拣货任务统计
// @Description 拣货任务统计
// @Tags 宠物saas-B端小程序-拣货任务
// @Accept json
// @Produce json
// @Param command body order_vo.PickingTaskStatReq true "拣货任务统计"
// @Success 200 {object} []order_vo.PickingTaskStatRes "成功"
// @Failure 400 {object} []order_vo.PickingTaskStatRes "请求错误"
// @Router /order-app/manager/order/pick/statistics [get]
func (c *OrderPickController) PickingTaskStatistics(w http.ResponseWriter, r *http.Request) {

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())

		response.BadRequest(w, err.Error())
	}

	logPrefix := fmt.Sprintf("拣货任务统计,用户信息%s", utils.JsonEncode(jwtInfo))
	log.Info(logPrefix)
	out := vo.PickingTaskStatRes{}
	// 获取统计
	var waitGroup sync.WaitGroup
	waitGroup.Add(3)
	req := vo.GetOrderListReq{
		ShopId:       jwtInfo.TenantId,
		PickupUserId: cast.ToInt64(jwtInfo.UserId),
	}
	go func(req vo.GetOrderListReq) {
		req.Type = 1 //待领取
		req.IsStats = true
		_, out.WaitPickupCount, err = c.service.GetPickingTaskList(req)
		waitGroup.Done()
	}(req)
	go func(req vo.GetOrderListReq) {
		req.Type = 2 //待拣货
		req.IsStats = true
		_, out.WaitPickingCount, err = c.service.GetPickingTaskList(req)
		waitGroup.Done()
	}(req)
	go func(req vo.GetOrderListReq) {
		req.Type = 3 //已完成
		req.IsStats = true
		_, out.CompletedCount, err = c.service.GetPickingTaskList(req)
		waitGroup.Done()
	}(req)
	waitGroup.Wait()
	response.SuccessWithData(w, out)

}

// ReceivePickingTask 领取拣货任务
// @Summary 领取拣货任务
// @Description 领取拣货任务
// @Tags 宠物saas-B端小程序-拣货任务
// @Accept json
// @Produce json
// @Param command body order_vo.ReceivePickingTaskReq true "领取拣货任务请求"
// @Success 200 {object} viewmodel.BaseHttpResponse "成功"
// @Failure 400 {object} viewmodel.BaseHttpResponse "请求错误"
// @Router /order-app/manager/order/pick/receive [post]
func (c *OrderPickController) ReceivePickingTask(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.ReceivePickingTaskReq](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		response.BadRequest(w, err.Error())
		return
	}

	cmd.ShopId = jwtInfo.TenantId
	cmd.PickupUserId = cast.ToInt64(jwtInfo.UserId)
	cmd.PickupUserName = jwtInfo.UserName
	logPrefix := fmt.Sprintf("领取拣货任务，入参:%s,用户信息%s", utils.JsonEncode(cmd), utils.JsonEncode(jwtInfo))
	log.Info(logPrefix)

	err = c.service.ReceivePickingTask(cmd)
	if err != nil {
		log.Error(logPrefix, "领取拣货任务失败err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// CompletePickingTask 完成拣货任务
// @Summary 完成拣货任务
// @Description 完成拣货任务
// @Tags 宠物saas-B端小程序-拣货任务
// @Accept json
// @Produce json
// @Param command body order_vo.CompletePickingTaskReq true "完成拣货任务请求"
// @Success 200 {object} viewmodel.BaseHttpResponse "成功"
// @Failure 400 {object} viewmodel.BaseHttpResponse "请求错误"
// @Router /order-app/manager/order/pick/complete [post]
func (c *OrderPickController) CompletePickingTask(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.CompletePickingTaskReq](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		response.BadRequest(w, err.Error())
		return
	}

	cmd.ShopId = jwtInfo.TenantId
	cmd.PickupUserId = cast.ToInt64(jwtInfo.UserId)
	logPrefix := fmt.Sprintf("完成拣货任务，入参:%s,用户信息%s", utils.JsonEncode(cmd), utils.JsonEncode(jwtInfo))
	log.Info(logPrefix)

	err = c.service.CompletePickingTask(cmd)
	if err != nil {
		log.Error(logPrefix, "完成拣货任务失败err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}
