CREATE TABLE eshop_saas.`t_tenant_retail_cfg`
(
    `id`                  BIGINT       NOT NULL COMMENT '主键ID',
    `chain_id`            BIGINT       NOT NULL COMMENT '店铺所属连锁ID',
    `tenant_id`           BIGINT       NOT NULL COMMENT '店铺ID',
    `operation_type`      TINYINT      NOT NULL DEFAULT '1' COMMENT '运营模式：1-独立运营，2-代运营模式',
    `purchase_type`       VARCHAR(10)  NOT NULL DEFAULT '1' COMMENT '采购模式：1-连锁集采，2-店铺自采',
    `authorized_chain_id` BIGINT       NOT NULL DEFAULT '0' COMMENT '授权连锁ID（店铺选择分仓运营时，关联的授权连锁ID）',
    `settlement_unit`     VARCHAR(100) NOT NULL DEFAULT '' COMMENT '结算单位',
    `settlement_type`     TINYINT               DEFAULT '0' COMMENT '结算类型：1-先货后款，2-先款后货，3-账期，4-其他',
    `settlement_remark`   VARCHAR(500) NOT NULL DEFAULT '' COMMENT '结算备注',
    `created_by`          BIGINT       NOT NULL DEFAULT '0' COMMENT '创建人',
    `created_time`        DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`          BIGINT       NOT NULL DEFAULT '0' COMMENT '更新人',
    `updated_time`        DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_id` (`tenant_id`),
    KEY `idx_chain_id` (`chain_id`),
    KEY `idx_authorized_chain_id` (`authorized_chain_id`)
) ENGINE = InnoDB COMMENT ='店铺新零售运营配置表';

INSERT INTO eshop_saas.t_tenant_retail_cfg (id, chain_id, tenant_id)
SELECT ROW_NUMBER() OVER (ORDER BY id),chain_id, id
FROM eshop_saas.t_tenant;

ALTER TABLE eshop_saas.t_tenant
ADD type TINYINT DEFAULT 1 NOT NULL COMMENT '店铺类型：1-直营点、2-加盟店、3-其他' AFTER name;

CREATE TABLE eshop.`inventory`
(
    `id`                    INT          NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `chain_id`              BIGINT       NOT NULL COMMENT '连锁id',
    `store_id`              VARCHAR(50)  NOT NULL DEFAULT '0' COMMENT '门店的主键',
    `warehouse_id`          INT          NOT NULL COMMENT '仓库id',
    `product_id`            INT          NOT NULL DEFAULT '0' COMMENT '商品id',
    `sku_id`                INT          NOT NULL DEFAULT '0' COMMENT 'sku id',
    `product_name`          VARCHAR(255) NOT NULL DEFAULT '' COMMENT '商品名称',
    `product_type`          INT                   DEFAULT '1' COMMENT '商品类型（1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体)',
    `bar_code`              VARCHAR(64)  NOT NULL DEFAULT '' COMMENT '条形码',
    `product_category_path` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '商品分类路径',
    `avg_cost_price`        INT          NOT NULL DEFAULT '0' COMMENT '平均成本价(分)',
    `total_amount`          INT          NOT NULL DEFAULT '0' COMMENT '总成本(分)',
    `total_num`             INT          NOT NULL DEFAULT '0' COMMENT '总库存',
    `freeze_num`            INT          NOT NULL DEFAULT '0' COMMENT '锁定库存',
    `available_num`         INT          NOT NULL DEFAULT '0' COMMENT '可用库存',
    `avg_daily_sales`       INT          NOT NULL DEFAULT '0' COMMENT '平均日销量(分)',
    `is_deleted`            TINYINT      NOT NULL DEFAULT '0' COMMENT '删除标识:0未删除,1已删除',
    `created_time`          DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`          DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_store_id_warehouse_id` (`store_id`, `warehouse_id`) COMMENT '门店id,仓库联合索引',
    KEY `idx_product` (`product_id`) COMMENT '商品索引',
    KEY `idx_sku` (`sku_id`) COMMENT 'skuId索引'
) ENGINE = InnoDB COMMENT ='库存表';

CREATE TABLE eshop.`inventory_flow`
(
    `id`                   INT          NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `chain_id`             BIGINT       NOT NULL COMMENT '连锁id',
    `store_id`             VARCHAR(50)  NOT NULL DEFAULT '0' COMMENT '门店的主键',
    `warehouse_id`         INT          NOT NULL COMMENT '仓库id',
    `product_id`           INT          NOT NULL DEFAULT '0' COMMENT '商品id',
    `sku_id`               INT          NOT NULL DEFAULT '0' COMMENT 'sku id',
    `bound_type`           TINYINT      NOT NULL DEFAULT '1' COMMENT '出库/入库类型：1-入库，2-出库',
    `item_type`            INT          NOT NULL DEFAULT '0' COMMENT '单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 12. 自用出库',
    `item_ref_id`          INT          NOT NULL DEFAULT '0' COMMENT '出入库关联的单据id',
    `item_ref_no`          VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '出入库关联的单据编号',
    `item_ref_type`        INT          NOT NULL DEFAULT '0' COMMENT '出入库关联的单据类型',
    `change_num`           INT          NOT NULL DEFAULT '0' COMMENT '变更数量',
    `current_cost_price`   INT          NOT NULL DEFAULT '0' COMMENT '当前变更时商品平均成本价(分)',
    `change_amount`        INT          NOT NULL DEFAULT '0' COMMENT '当前变更金额(分)',
    `total_num_before`     INT          NOT NULL DEFAULT '0' COMMENT '变更前总库存',
    `freeze_num_before`    INT          NOT NULL DEFAULT '0' COMMENT '变更前锁定库存',
    `available_num_before` INT          NOT NULL DEFAULT '0' COMMENT '变更前可用库存',
    `total_amount_before`  INT          NOT NULL DEFAULT '0' COMMENT '变更前总成本(分)',
    `total_num_after`      INT          NOT NULL DEFAULT '0' COMMENT '变更后总库存',
    `freeze_num_after`     INT          NOT NULL DEFAULT '0' COMMENT '变更后锁定库存',
    `available_num_after`  INT          NOT NULL DEFAULT '0' COMMENT '变更后可用库存',
    `total_amount_after`   INT          NOT NULL DEFAULT '0' COMMENT '变更后总成本(分)',
    `is_deleted`           TINYINT      NOT NULL DEFAULT '0' COMMENT '删除标识:0未删除,1已删除',
    `operator`             VARCHAR(255)          DEFAULT NULL COMMENT '操作人',
    `remark`               VARCHAR(255) NOT NULL DEFAULT '' COMMENT '备注',
    `created_time`         DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`         DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_store_id_warehouse_id` (`store_id`, `warehouse_id`) COMMENT '门店id,仓库联合索引',
    KEY `idx_product` (`product_id`) COMMENT '商品索引',
    KEY `idx_sku` (`sku_id`) COMMENT 'skuId索引'
) ENGINE = InnoDB COMMENT ='库存流水表';

CREATE TABLE eshop.`inventory_in_out_bound`
(
    `id`              INT          NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `chain_id`        BIGINT       NOT NULL COMMENT '连锁id',
    `store_id`        VARCHAR(50)  NOT NULL DEFAULT '0' COMMENT '门店的主键',
    `warehouse_id`    INT          NOT NULL COMMENT '仓库id',
    `bound_no`        VARCHAR(32)  NOT NULL DEFAULT '' COMMENT '出入库单号',
    `bound_type`      TINYINT      NOT NULL DEFAULT '1' COMMENT '出库/入库类型：1-入库，2-出库',
    `item_type`       INT          NOT NULL DEFAULT '0' COMMENT '单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 12. 自用出库',
    `item_ref_id`     INT          NOT NULL DEFAULT '0' COMMENT '出入库关联的单据id',
    `item_ref_no`     VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '出入库关联的单据编号',
    `item_ref_type`   INT          NOT NULL DEFAULT '0' COMMENT '出入库关联的单据类型',
    `total_num`       INT          NOT NULL DEFAULT '0' COMMENT '出入库总数量',
    `total_amount`    INT          NOT NULL DEFAULT '0' COMMENT '出入库总价(分)',
    `sell_amount`     INT          NOT NULL DEFAULT '0' COMMENT '出库售卖总价, 入库则为0(分)',
    `remark`          VARCHAR(255) NOT NULL DEFAULT '' COMMENT '备注',
    `is_deleted`      TINYINT      NOT NULL DEFAULT '0' COMMENT '删除标识:0未删除,1已删除',
    `operator`        VARCHAR(255) NOT NULL COMMENT '操作人',
    `occurrence_time` DATETIME     NOT NULL COMMENT '发生时间',
    `created_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_store_warehouse` (`store_id`, `warehouse_id`) COMMENT '门店仓库联合索引',
    KEY `idx_bound_no` (`bound_no`) COMMENT '出入库单号索引',
    KEY `idx_item_ref` (`item_ref_id`) COMMENT '关联单据ID索引'
) ENGINE = InnoDB COMMENT ='商品出入库';

CREATE TABLE eshop.`inventory_in_out_bound_detail`
(
    `id`                    INT          NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `chain_id`              BIGINT       NOT NULL COMMENT '连锁id',
    `store_id`              VARCHAR(50)  NOT NULL DEFAULT '0' COMMENT '门店的主键',
    `warehouse_id`          INT          NOT NULL COMMENT '仓库id',
    `bound_id`              INT          NOT NULL COMMENT '出入库id',
    `bound_no`              VARCHAR(32)  NOT NULL DEFAULT '' COMMENT '出入库单号',
    `bound_type`            VARCHAR(16)  NOT NULL DEFAULT '' COMMENT '出库/入库类型',
    `item_type`             INT          NOT NULL DEFAULT '0' COMMENT '单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 12. 自用出库',
    `item_detail_ref_id`    VARCHAR(50)  NOT NULL DEFAULT '0' COMMENT '出入库关联的单据详情id',
    `product_id`            INT          NOT NULL DEFAULT '0' COMMENT '商品id',
    `sku_id`                INT          NOT NULL DEFAULT '0' COMMENT 'sku id',
    `product_name`          VARCHAR(255) NOT NULL DEFAULT '' COMMENT '商品名称',
    `product_type`          VARCHAR(16)  NOT NULL DEFAULT '' COMMENT '商品类型',
    `bar_code`              VARCHAR(64)  NOT NULL DEFAULT '' COMMENT '条形码',
    `product_category_path` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '商品分类路径',
    `avg_cost_price`        INT          NOT NULL DEFAULT '0' COMMENT '出入库前平均成本价(分)',
    `io_price`              INT          NOT NULL DEFAULT '0' COMMENT '出入库单价(分)',
    `io_count`              INT          NOT NULL DEFAULT '0' COMMENT '出入库数量',
    `io_amount`             INT          NOT NULL DEFAULT '0' COMMENT '出入库小计(分)',
    `real_io_amount`        INT          NOT NULL DEFAULT '0' COMMENT '实际出入库小计(分)',
    `total_num_after`       INT          NOT NULL DEFAULT '0' COMMENT '变更后总库存',
    `freeze_num_after`      INT          NOT NULL DEFAULT '0' COMMENT '变更后锁定库存',
    `available_num_after`   INT          NOT NULL DEFAULT '0' COMMENT '变更后可用库存',
    `total_amount_after`    INT          NOT NULL DEFAULT '0' COMMENT '变更后总成本(分)',
    `operator`              VARCHAR(255) NOT NULL DEFAULT '' COMMENT '操作人',
    `is_deleted`            TINYINT      NOT NULL DEFAULT '0' COMMENT '删除标识:0未删除,1已删除',
    `created_time`          DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`          DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_voucher` (`bound_id`, `bound_no`) COMMENT '出入库单联合索引',
    KEY `idx_store_warehouse` (`store_id`, `warehouse_id`) COMMENT '门店仓库联合索引',
    KEY `idx_product_sku` (`product_id`, `sku_id`) COMMENT '商品SKU联合索引',
    KEY `idx_store_sku_type` (`store_id`, `sku_id`, `bound_type`) COMMENT '门店商品类型联合索引'
) ENGINE = InnoDB COMMENT ='商品出入库详情';

CREATE TABLE eshop.`inventory_location`
(
    `id`           INT          NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `chain_id`     BIGINT       NOT NULL COMMENT '连锁ID',
    `store_id`     VARCHAR(50)           DEFAULT '0' COMMENT '门店的主键',
    `warehouse_id` INT          NOT NULL COMMENT '仓库ID',
    `code`         VARCHAR(64)  NOT NULL COMMENT '库位编码',
    `name`         VARCHAR(128) NOT NULL COMMENT '库位名称',
    `product_id`   INT          NOT NULL DEFAULT '0' COMMENT '商品id',
    `sku_id`       INT          NOT NULL DEFAULT '0' COMMENT 'sku id',
    `created_time` DATETIME     NOT NULL COMMENT '创建时间',
    `updated_time` DATETIME     NOT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_store_warehouse` (`store_id`, `warehouse_id`),
    KEY `idx_sku` (`sku_id`),
    KEY `idx_code` (`code`)
) ENGINE = InnoDB COMMENT ='库位信息表';

create table eshop.inventory_suppliers
(
    id                int comment '供应商ID',
    chain_id          bigint                             not null comment '连锁ID',
    code              varchar(50)                        not null comment '供应商编码',
    name              varchar(100)                       not null comment '供应商名称',
    address           varchar(255)                       null comment '供应商地址',
    type              varchar(50)                        null comment '供应商类型',
    unified_social_id varchar(50)                        null comment '统一社会信用编码',
    purchase_period   int                                null comment '采购周期(天)',
    due_days          int                                null comment '到货天数',
    contact_person    varchar(50)                        null comment '联系人',
    contact_method    varchar(50)                        null comment '联系方式',
    bank_account      varchar(50)                        null comment '银行账号',
    bank_name         varchar(100)                       null comment '开户银行',
    account_name      varchar(100)                       null comment '开户名称',
    warehouse_id      int                                null comment '仓库ID',
    source            int                                null comment '1.店铺自建；2.连锁创建',
    status            tinyint                            not null comment '状态',
    created_operator  varchar(255)                       not null comment '创建人',
    created_time      datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_operator  varchar(255)                       not null comment '更新人',
    updated_time      datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    version           int      default 1                 not null comment '版本号'
)
    comment '供应商信息表' collate = utf8mb4_unicode_ci;

create unique index `PRIMARY`
    on eshop.inventory_suppliers (id);

create index idx_code
    on eshop.inventory_suppliers (code);

create index idx_name
    on eshop.inventory_suppliers (name);

create index idx_warehouse
    on eshop.inventory_suppliers (warehouse_id);

create unique index uk_code_chain
    on eshop.inventory_suppliers (code, chain_id);

alter table eshop.inventory_suppliers
    modify id int auto_increment comment '供应商ID';



CREATE TABLE `inventory_voucher` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `chain_id` bigint NOT NULL COMMENT '连锁id',
  `store_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '门店id',
  `warehouse_id` int NOT NULL COMMENT '仓库id',
  `supplier_id` int NOT NULL DEFAULT '0' COMMENT '供应商id',
  `name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '单据名称',
  `voucher_no` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '单据单号',
  `voucher_type` tinyint NOT NULL DEFAULT '0' COMMENT '单据类型: 1-采购单, 2-采购退货单, 3-盘点单,4-采购入库单，5-采购出库单',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '单据状态: 1-草稿, 2-待处理, 3-部分完成, 4-已完成, 5-已作废, 6-已取消',
  `source_type` tinyint NOT NULL DEFAULT '0' COMMENT '单据来源: 1-连锁采购, 2-店铺自采, 3-代运营采购',
  `profit_status` tinyint NOT NULL DEFAULT '0' COMMENT '盈亏状态: 0-无盈亏, 1-盈利, 2-亏损, 3-平账',
  `change_num` int NOT NULL DEFAULT '0' COMMENT '变更数量(盘盈/盘亏/入库/出库)',
  `change_amount` int NOT NULL DEFAULT '0' COMMENT '变更金额(分)',
  `remark` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `purchase_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '采购日期',
  `delivery_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '预计到货时间',
  `return_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退货时间',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识:0未删除,1已删除',
  `operator` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `voucher_time` datetime NOT NULL COMMENT '单据日期',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `pid` int DEFAULT NULL COMMENT '上级id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_store_voucher` (`store_id`,`voucher_no`) COMMENT '门店id,单号联合索引',
  KEY `idx_supplier` (`supplier_id`) COMMENT '供应商索引',
  KEY `idx_created_time` (`created_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='库存单据表(采购/退货/盘点)'


CREATE TABLE eshop.`inventory_voucher_detail`
(
    `id`               INT          NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `chain_id`         BIGINT       NOT NULL COMMENT '连锁id',
    `store_id`         VARCHAR(50)  NOT NULL DEFAULT '0' COMMENT '门店id',
    `warehouse_id`     INT          NOT NULL COMMENT '仓库id',
    `voucher_id`       INT          NOT NULL COMMENT '单据id',
    `product_id`       INT          NOT NULL COMMENT '商品id',
    `sku_id`           INT          NOT NULL COMMENT 'sku id',
    `price`            INT          NOT NULL DEFAULT '0' COMMENT '单价(分)',
    `quantity`         INT          NOT NULL DEFAULT '0' COMMENT '计划数量',
    `amount`           INT          NOT NULL DEFAULT '0' COMMENT '计划金额小计(分)',
    `actual_num`       INT          NOT NULL DEFAULT '0' COMMENT '实际数量',
    `actual_amount`    INT          NOT NULL DEFAULT '0' COMMENT '实际金额小计(分)',
    `total_num_before` INT          NOT NULL DEFAULT '0' COMMENT '操作前库存',
    `total_num_after`  INT          NOT NULL DEFAULT '0' COMMENT '操作后库存',
    `profit_status`    TINYINT      NOT NULL DEFAULT '0' COMMENT '盈亏状态: 0-无盈亏, 1-盈利, 2-亏损, 3-平账',
    `change_num`       INT          NOT NULL DEFAULT '0' COMMENT '变更数量',
    `change_amount`    INT          NOT NULL DEFAULT '0' COMMENT '变更金额(分)',
    `avg_cost_price`   INT          NOT NULL DEFAULT '0' COMMENT '当前平均成本价',
    `reason`           VARCHAR(255) NOT NULL DEFAULT '' COMMENT '原因(退货原因/盘点差异原因)',
    `remark`           VARCHAR(255) NOT NULL DEFAULT '' COMMENT '备注',
    `is_deleted`       TINYINT      NOT NULL DEFAULT '0' COMMENT '删除标识:0未删除,1已删除',
    `created_time`     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_voucher` (`voucher_id`) COMMENT '单据索引',
    KEY `idx_store_sku` (`store_id`, `sku_id`) COMMENT '门店商品联合索引'
) ENGINE = InnoDB COMMENT ='库存单据明细表';

