package controllers

import (
	"eShop/infra/response"
	"eShop/infra/utils"
	vo "eShop/view-model/inventory-vo/voucher"
	"net/http"
	"strconv"
)

// RequestPage godoc
// @Summary 获取凭证列表
// @Description 分页查询凭证列表
// @Tags 凭证管理
// @Accept json
// @Produce json
// @Param params query vo.VoucherQueryParams true "查询参数"
// @Success 200 {object} response.Response[[]vo.VoucherPageResponse] "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/voucher/purchase/request/page [get]
func (c VoucherController) RequestPage(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherQueryParams](r)
	cmd.VoucherType =1
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}
	page, total, err := c.service.Query(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, page, total)
}

// RequestDetail godoc
// @Summary 获取凭证详情
// @Description 根据ID获取凭证详细信息
// @Tags 凭证管理
// @Accept json
// @Produce json
// @Param id query int true "凭证ID"
// @Success 200 {object} response.Response[vo.VoucherWithDetailResponse] "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/voucher/purchase/request/detail [get]
func (c VoucherController) RequestDetail(w http.ResponseWriter, r *http.Request) {
	idStr := r.URL.Query().Get("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	data, err := c.service.RequestDetail(r.Context(), id)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, data)
}

// RequestSave godoc
// @Summary 保存凭证
// @Description 创建或更新凭证信息
// @Tags 凭证管理
// @Accept json
// @Produce json
// @Param request body vo.VoucherSaveCommand true "凭证信息"
// @Success 200 {object} response.Response[int] "成功，返回凭证ID"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/voucher/purchase/request/save [post]
func (c VoucherController) RequestSave(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherSaveCommand](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	if cmd.VoucherType != vo.VoucherTypePurchase {
		response.BadRequest(w, "单据类型错误")
		return
	}

	id, err := c.service.SaveRequest(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, id)
}


// RequestCancel godoc
// @Summary 取消单据
// @Description 取消单据
// @Tags 凭证管理
// @Accept json
// @Produce json
// @Param id query int true "单据ID"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/voucher/purchase/request/cancel [post]
func (c VoucherController) RequestCancel(w http.ResponseWriter, r *http.Request) {
	idStr := r.FormValue("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	err = c.service.Cancel(r.Context(), id)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}

// RequestManualComplete godoc
// @Summary 手动完成单据
// @Description 手动完成单据
// @Tags 凭证管理
// @Accept json
// @Produce json
// @Param id query int true "单据ID"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/voucher/purchase/request/manual-complete [post]
func (c VoucherController) RequestManualComplete(w http.ResponseWriter, r *http.Request) {
	idStr := r.FormValue("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	err = c.service.ManualComplete(r.Context(), id)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}

// RequestSaveLogisticsInfo godoc
// @Summary 保存物流信息
// @Description 保存物流信息
// @Tags 凭证管理
// @Accept json
// @Produce json
// @Param request body vo.VoucherRequestSaveLogisticsInfoCommand true "请求参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/voucher/purchase/request/save-logistics-info [post]
func (c VoucherController) RequestSaveLogisticsInfo(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherRequestSaveLogisticsInfoCommand](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	err = c.service.SaveLogisticsInfo(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}
