package controllers

import (
	"eShop/infra/response"
	"eShop/infra/utils"
	vo "eShop/view-model/inventory-vo/voucher"
	"net/http"
	"strconv"
)

// @Summary 采购退货单分页查询
// @Description 获取采购退货单列表，支持分页
// @Tags 采购退货
// @Accept json
// @Produce json
// @Param request query vo.VoucherQueryParams true "分页查询参数"
// @Success 200 {object} response.Response[[]vo.VoucherPageResponse] "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/voucher/purchase/return/page [get]
func (c VoucherController) ReturnPage(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherQueryParams](r)
	cmd.VoucherType =2
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}
	page, total, err := c.service.Query(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, page, total)
}

// @Summary 采购退货单详情
// @Description 根据ID获取采购退货单详细信息
// @Tags 采购退货
// @Accept json
// @Produce json
// @Param id query int true "退货单ID"
// @Success 200 {object} response.Response[vo.VoucherDetailResponse] "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/voucher/purchase/return/detail [get]
func (c VoucherController) ReturnDetail(w http.ResponseWriter, r *http.Request) {
	idStr := r.URL.Query().Get("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	data, err := c.service.ReturnDetail(r.Context(), id)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, data)
}

// @Summary 保存采购退货单
// @Description 创建或更新采购退货单
// @Tags 采购退货
// @Accept json
// @Produce json
// @Param request body vo.VoucherSaveCommand true "退货单信息"
// @Success 200 {object} response.Response[int] "成功，返回退货单ID"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/voucher/purchase/return/save [post]
func (c VoucherController) ReturnSave(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherSaveCommand](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	if cmd.VoucherType != vo.VoucherTypePurchaseReturn {
		response.BadRequest(w, "单据类型错误")
		return
	}

	id, err := c.service.SaveReturn(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, id)
}

// ReturnCancel godoc
// @Summary 取消采购退货单
// @Description 取消采购退货单
// @Tags 采购退货
// @Accept json
// @Produce json
// @Param id query int true "退货单ID"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/voucher/purchase/return/cancel [post]
func (c VoucherController) ReturnCancel(w http.ResponseWriter, r *http.Request) {
	idStr := r.FormValue("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	err = c.service.Cancel(r.Context(), id)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}

// ReturnSaveLogisticsInfo godoc
// @Summary 保存采购退货单物流信息
// @Description 保存采购退货单物流信息
// @Tags 采购退货
// @Accept json
// @Produce json
// @Param request body vo.VoucherRequestSaveLogisticsInfoCommand true "物流信息"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/voucher/purchase/return/save-logistics-info [post]
func (c VoucherController) ReturnSaveLogisticsInfo(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherRequestSaveLogisticsInfoCommand](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	err = c.service.SaveLogisticsInfo(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}
