package controllers

import (
	"eShop/infra/response"
	"eShop/infra/utils"
	vo "eShop/view-model/inventory-vo/voucher"
	"net/http"
	"strconv"
)

// OutboundPage godoc
// @Summary 出库凭证分页查询
// @Description 获取出库凭证列表，支持分页
// @Tags 出库凭证
// @Accept json
// @Produce json
// @Param request query vo.VoucherQueryParams true "分页查询参数"
// @Success 200 {object} response.Response[[]vo.VoucherPageResponse] "成功"
// @Failure 400 {object} response.BaseResp "请求参数错误"
// @Router /inventory-app/voucher/purchase/outbound/page [get]
func (c VoucherController) OutboundPage(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherQueryParams](r)
	cmd.VoucherType=5
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	page, total, err := c.service.Query(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, page, total)
}

// OutboundDetail godoc
// @Summary 出库凭证详情
// @Description 获取出库凭证详细信息
// @Tags 出库凭证
// @Accept json
// @Produce json
// @Param id query int true "凭证ID"
// @Success 200 {object} response.Response[vo.VoucherDetailResponse] "成功"
// @Failure 400 {object} response.BaseResp "请求参数错误"
// @Router /inventory-app/voucher/purchase/outbound/detail [get]
func (c VoucherController) OutboundDetail(w http.ResponseWriter, r *http.Request) {
	idStr := r.URL.Query().Get("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	data, err := c.service.OutboundDetail(r.Context(), id)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, data)
}

// OutboundSave godoc
// @Summary 保存出库凭证
// @Description 创建或更新出库凭证
// @Tags 出库凭证
// @Accept json
// @Produce json
// @Param request body vo.VoucherSaveCommand true "保存凭证参数"
// @Success 200 {object} response.Response[int] "成功，返回凭证ID"
// @Failure 400 {object} response.BaseResp "请求参数错误"
// @Router /inventory-app/voucher/purchase/outbound/save [post]
func (c VoucherController) OutboundSave(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherSaveCommand](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	if cmd.VoucherType!=vo.VoucherTypePurchaseReturnOut{
		response.BadRequest(w, "单据类型错误")
		return
	}

	id, err := c.service.SaveOutbound(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, id)
}
