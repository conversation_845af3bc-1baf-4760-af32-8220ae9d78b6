package controllers

import (
	"eShop/infra/response"
	"eShop/infra/utils"
	vo "eShop/view-model/inventory-vo/voucher"
	"net/http"
	"strconv"
)

// @Summary 入库单分页查询
// @Description 获取入库单列表，支持分页查询
// @Tags 入库单管理
// @Accept json
// @Produce json
// @Param request query vo.VoucherQueryParams true "分页查询参数"
// @Success 200 {object} response.Response[[]vo.VoucherPageResponse] "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/voucher/purchase/inbound/page [get]
func (c VoucherController) InboundPage(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherQueryParams](r)
	cmd.VoucherType=4
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	page, total, err := c.service.Query(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, page, total)
}

// @Summary 入库单详情
// @Description 获取指定入库单的详细信息
// @Tags 入库单管理
// @Accept json
// @Produce json
// @Param id query int true "入库单ID"
// @Success 200 {object} response.Response[vo.VoucherDetailResponse] "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/voucher/purchase/inbound/detail [get]
func (c VoucherController) InboundDetail(w http.ResponseWriter, r *http.Request) {
	idStr := r.URL.Query().Get("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	data, err := c.service.InboundDetail(r.Context(), id)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, data)
}

// @Summary 保存入库单
// @Description 创建或更新入库单信息
// @Tags 入库单管理
// @Accept json
// @Produce json
// @Param request body vo.VoucherSaveCommand true "入库单信息"
// @Success 200 {object} response.Response[int] "成功，返回入库单ID"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/voucher/purchase/inbound/save [post]
func (c VoucherController) InboundSave(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherSaveCommand](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	if cmd.VoucherType!=vo.VoucherTypePurchaseIn{
		response.BadRequest(w, "单据类型错误")
		return
	}

	id, err := c.service.SaveInbound(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, id)
}
