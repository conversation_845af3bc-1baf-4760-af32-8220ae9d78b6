package controllers

import (
	jwt "eShop/infra/jwtauth"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/inventory-service/warehouse"
	vo "eShop/view-model/inventory-vo/warehouse"
	"net/http"

	"github.com/go-chi/chi/v5"
)

type WarehouseController struct {
	service warehouse.WarehouseService
}

func NewWarehouseController(service warehouse.WarehouseService) *WarehouseController {
	return &WarehouseController{
		service: service,
	}
}

func (c WarehouseController) RegisterRoutes(r chi.Router) {
	r.Route("/inventory-app/warehouse", func(r chi.Router) {
		r.Get("/query", c.GetByStoreId)
	})
}

// GetByStoreId 获取指定店铺下的所有仓库
// @Summary 获取指定店铺下的所有仓库
// @Description 获取指定店铺下的所有仓库
// @Tags 仓库管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response[[]vo.WarehouseVO] "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/warehouse/query [get]
func (c WarehouseController) GetByStoreId(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.QueryRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	storeId := cmd.StoreId
	if len(storeId) == 0 {
		storeId = jwt.CtxGet[string](r.Context(), "TenantId")
	}

	var list []vo.WarehouseVO
	list, err = c.service.GetByStoreId(r.Context(), storeId)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	response.SuccessWithData(w, list)
}
