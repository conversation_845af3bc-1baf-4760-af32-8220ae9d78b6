package main

import (
	"context"
	"eShop/applications/petai-app/controllers"

	"eShop/infra/cache"
	"eShop/infra/config"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	emiddware "eShop/infra/middleware"
	"eShop/infra/security"
	"eShop/infra/tracing"
	"eShop/infra/utils"
	"eShop/services/petai-service/services"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	cache_source "eShop/services/distribution-service/enum/cache-source"

	"github.com/BurntSushi/toml"
	"github.com/go-chi/chi/v5"
	httpSwagger "github.com/swaggo/http-swagger"
)

// service 这是小闻养宠助手
func service() http.Handler {
	//接下来才是正题
	r := chi.NewRouter()

	r.Use(emiddware.WithLogger)  //用中间件去处理日志问题。未完待续
	r.Use(emiddware.WithTracing) //基于jaeger的open tracing

	// jwtAuth
	jwtauth.JwtInit()
	r.Use(jwtauth.Verify(jwtauth.TokenFromHeader))

	//coze模块
	cozesvc := services.NewCozeService()
	CozeController := controllers.NewCozeController(*cozesvc)
	CozeController.RegisterRoutes(r)

	// 授权阿闻信息模块
	authsvc := services.NewAuthService()
	AuthController := controllers.NewAuthController(*authsvc)
	AuthController.RegisterRoutes(r)

	// 宠物模块
	petsvc := services.NewPetService()
	PetController := controllers.NewPetController(*petsvc)
	PetController.RegisterRoutes(r)

	// 宠物健康管理模块
	petVaccinatesvc := services.NewPetVaccinateService()
	PetVaccinateController := controllers.NewPetVaccinateController(*petVaccinatesvc)
	PetVaccinateController.RegisterRoutes(r)

	// 数据采集模块
	dataAcquisitionsvc := services.NewDataAcquisitionService()
	dataAcquisitionController := controllers.NewDataAcquisitionController(*dataAcquisitionsvc)
	dataAcquisitionController.RegisterRoutes(r)

	r.Mount("/swagger", httpSwagger.WrapHandler)
	return r
}

func main() {
	toml.DecodeFile("appsetting.toml", &config.LocalSetting)
	config.LocalSetting.LocalIP = config.GetCurrentIP() //获取本机地址
	security.InitPEM("rsa_1024_priv.pem", "rsa_1024_pub.pem", "rsa_1024_pub.pem")

	sh, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = sh

	//临时注释掉
	//config.InitConfig(fmt.Sprintf("http://%s", config.LocalSetting.AppService.Address), config.LocalSetting.AppService.Appid, env.GetEnv())

	cache.CacheSources[cache_source.EShop] = cache.Address(config.Get("redis.PassAddr"))

	log.Init()

	fmt.Println("petai-app 启动成功! 端口 8156")

	_, closer, err := tracing.InitJaeger()
	if err != nil {
		panic(err)
	}

	//下面的代码主要是平滑关闭，
	//也就是 监听类似于 ctrl + c 等中断信号再关闭程序的处理。

	// The HTTP Server
	server := &http.Server{Addr: "0.0.0.0:8156", Handler: service()}

	// Server run context
	serverCtx, serverStopCtx := context.WithCancel(context.Background())

	// Listen for syscall signals for process to interrupt/quit
	sig := make(chan os.Signal, 1)
	signal.Notify(sig, syscall.SIGHUP, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
	go func() {
		<-sig

		// Shutdown signal with grace period of 30 seconds
		shutdownCtx, _ := context.WithTimeout(serverCtx, 30*time.Second)

		go func() {
			<-shutdownCtx.Done()
			closer.Close()

			if errors.Is(shutdownCtx.Err(), context.DeadlineExceeded) {
				log.Fatal("graceful shutdown timed out.. forcing exit.")
			}
		}()

		// Trigger graceful shutdown
		err := server.Shutdown(shutdownCtx)
		if err != nil {
			log.Fatal(err)
		}
		serverStopCtx()
	}()

	//初始化
	utils.InitClient()

	// Run the server
	err = server.ListenAndServe()
	if err != nil && !errors.Is(err, http.ErrServerClosed) {
		log.Fatal(err)
	}

	// Wait for server context to be stopped
	<-serverCtx.Done()
}
