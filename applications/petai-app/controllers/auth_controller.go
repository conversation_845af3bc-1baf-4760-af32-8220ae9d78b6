package controllers

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/petai-service/services"
	petai_vo "eShop/view-model/petai-vo"
	"net/http"

	"github.com/go-chi/chi/v5"
)

func NewAuthController(service services.AuthService) *AuthController {
	return &AuthController{
		service: service,
	}
}

// 用户授权阿闻数据 到 小闻养宠助手
type AuthController struct {
	service services.AuthService
}

func (w *AuthController) RegisterRoutes(r chi.Router) {

	//小程序
	r.Route("/petai-app/api/auth", func(r chi.Router) {
		r.Post("/agree", w.UserAgreeAuth)         // 用户授权阿闻数据 到 小闻养宠助手
		r.Post("/sync-data", w.syncScrmData)      // 同步scrm新增数据到 小闻养宠助手
		r.Post("/user-info", w.UserInfo)          // 获取用户信息
		r.Post("/edit-user-info", w.EditUserInfo) // 编辑用户信息

	})

}

// UserAgreeAuth 用户同意或拒绝授权阿闻数据 到 小闻养宠助手
// @Summary 用户同意授权阿闻数据 到 小闻养宠助手 @petai-v1.0.0
// @Description 用户同意授权阿闻数据 到 小闻养宠助手
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.UserAgreeAuthReq true "请求对话参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/auth/agree [post]
func (c *AuthController) UserAgreeAuth(w http.ResponseWriter, r *http.Request) {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====用户同意或拒绝授权====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====用户同意或拒绝授权====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	userInfoId := jwtInfo.Id

	cmd, err := utils.Bind[petai_vo.UserAgreeAuthReq](r)
	if err != nil {
		log.Error("====用户同意或拒绝授权====获取请求参数失败 err=", err.Error())
		response.BadRequest(w, "获取请求参数失败")
		return
	}
	cmd.UserInfoId = userInfoId
	err = c.service.UserAgreeAuth(cmd)
	if err != nil {
		log.Error("====用户同意或拒绝授权====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}

// syncScrmData 同步scrm新增数据到 小闻养宠助手
// @Summary 同步scrm新增数据到 小闻养宠助手 @petai-v1.0.0
// @Description 同步scrm新增数据到 小闻养宠助手
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/auth/sync-data [post]
func (c *AuthController) syncScrmData(w http.ResponseWriter, r *http.Request) {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====同步scrm新增数据====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====同步scrm新增数据====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	eshopUserId := jwtInfo.Id

	err = c.service.SyncScrmData(eshopUserId)
	if err != nil {
		log.Error("====同步scrm新增数据====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}

// UserInfo 小闻养宠助手用户信息
// @Summary 小闻养宠助手用户信息 @petai-v1.0.0
// @Description 小闻养宠助手用户信息
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/auth/user-info [post]
func (c *AuthController) UserInfo(w http.ResponseWriter, r *http.Request) {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====小闻养宠助手用户信息====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====小闻养宠助手用户信息====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	eshopUserId := jwtInfo.Id
	log.Info("UserInfo,eshopUserId=", eshopUserId)
	data, err := c.service.UserInfo(eshopUserId)
	if err != nil {
		log.Error("====小闻养宠助手用户信息====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.SuccessWithData(w, data)
}

// EditUserInfo 编辑小闻养宠助手用户信息
// @Summary 编辑小闻养宠助手用户信息 @petai-v1.0.0
// @Description 编辑小闻养宠助手用户信息
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.UserInfo true "请求对话参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/auth/edit-user-info [post]
func (c *AuthController) EditUserInfo(w http.ResponseWriter, r *http.Request) {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====编辑小闻养宠助手用户信息====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====编辑小闻养宠助手用户信息====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	eshopUserId := jwtInfo.Id

	cmd, err := utils.Bind[petai_vo.UserInfo](r)
	if err != nil {
		log.Error("====编辑小闻养宠助手====获取请求参数失败 err=", err.Error())
		response.BadRequest(w, "获取请求参数失败")
		return
	}
	cmd.UserInfoId = eshopUserId

	err = c.service.EditUserInfo(cmd)
	if err != nil {
		log.Error("====编辑小闻养宠助手用户信息====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}
