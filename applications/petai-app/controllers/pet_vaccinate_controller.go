package controllers

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/petai-service/services"
	petai_vo "eShop/view-model/petai-vo"
	"net/http"

	"github.com/go-chi/chi/v5"
)

func NewPetVaccinateController(service services.PetVaccinateService) *PetVaccinateController {
	return &PetVaccinateController{
		service: service,
	}
}

// 宠物健康档案之 驱虫、免疫
type PetVaccinateController struct {
	service services.PetVaccinateService
}

func (w *PetVaccinateController) RegisterRoutes(r chi.Router) {

	//小程序
	r.Route("/petai-app/api/pet-vaccinate", func(r chi.Router) {
		r.Post("/add", w.PetVaccinateAdd)        // 添加宠物健康档案记录
		r.Post("/update", w.PetVaccinateUpdate)  // 更新宠物健康档案记录
		r.Post("/delete", w.PetVaccinateDelete)  // 删除宠物健康档案记录
		r.Post("/list", w.PetVaccinateList)      // 宠物健康档案记录列表
		r.Post("/get-new-date", w.GetNewPetDate) // 查询宠物健康记录的时间

	})

}

// PetVaccinateAdd 新增宠物健康档案记录
// @Summary 新增宠物健康档案记录 @petai-v1.1.0
// @Description
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.PetVaccinate true "请求对话参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/pet-vaccinate/add [post]
func (c *PetVaccinateController) PetVaccinateAdd(w http.ResponseWriter, r *http.Request) {

	cmd, err := utils.Bind[petai_vo.PetVaccinate](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====新增宠物健康档案记录====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====新增宠物健康档案记录====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}

	err = c.service.PetVaccinateAddOrUpdate(cmd)
	if err != nil {
		log.Error("====新增宠物健康档案记录====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}

// PetVaccinateUpdate 更新宠物健康档案记录记录
// @Summary 更新宠物健康档案记录记录 @petai-v1.1.0 @petai-v2.2.0
// @Description
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.PetInfo true "请求对话参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/pet-vaccinate/update [post]
func (c *PetVaccinateController) PetVaccinateUpdate(w http.ResponseWriter, r *http.Request) {

	cmd, err := utils.Bind[petai_vo.PetVaccinate](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====更新宠物健康档案记录记录====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====更新宠物健康档案记录记录====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}

	err = c.service.PetVaccinateAddOrUpdate(cmd)
	if err != nil {
		log.Error("====更新宠物健康档案记录记录====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}

// PetVaccinateList 获取用户宠物健康档案记录记录列表
// @Summary 获取用户宠物健康档案记录记录列表 @petai-v1.0.0 @petai-v2.2.0
// @Description
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.PetVaccinateListReq true "请求对话参数"
// @Success 200 {object} petai_vo.PetVaccinate "成功"
// @Failure 400 {object} petai_vo.PetVaccinate "请求错误"
// @Router /petai-app/api/pet-vaccinate/list [post]
func (c *PetVaccinateController) PetVaccinateList(w http.ResponseWriter, r *http.Request) {

	cmd, err := utils.Bind[petai_vo.PetVaccinateListReq](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====获取用户宠物健康档案记录记录列表====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====获取用户宠物健康档案记录记录列表====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	if len(cmd.PetInfoId) <= 0 {
		response.BadRequest(w, "无效的宠物id")
		return
	}

	data, total, err := c.service.PetVaccinateList(cmd)
	if err != nil {
		log.Error("====获取用户宠物健康档案记录记录列表====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.SuccessWithPage(w, data, int(total))
}

// PetVaccinateDelete 删除宠物健康档案记录
// @Summary 删除宠物健康档案记录 @petai-v1.0.0
// @Description
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.PetVaccinateDeleteReq true "请求对话参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/pet-vaccinate/delete [post]
func (c *PetVaccinateController) PetVaccinateDelete(w http.ResponseWriter, r *http.Request) {

	cmd, err := utils.Bind[petai_vo.PetVaccinateDeleteReq](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====删除宠物健康档案记录====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====删除宠物健康档案记录====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}

	err = c.service.PetVaccinateDelete(cmd)
	if err != nil {
		log.Error("====删除宠物健康档案记录====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}

// GetNewPetDate 查询宠物健康记录的时间
// @Summary 查询宠物健康记录的时间 @petai-v1.0.0
// @Description
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.GetNewPetDateReq true "请求对话参数"
// @Success 200 {object} petai_vo.NewPetRecordDateRes "成功"
// @Failure 400 {object} petai_vo.NewPetRecordDateRes "请求错误"
// @Router /petai-app/api/pet-vaccinate/get-new-date [post]
func (c *PetVaccinateController) GetNewPetDate(w http.ResponseWriter, r *http.Request) {

	cmd, err := utils.Bind[petai_vo.GetNewPetDateReq](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====查询宠物健康记录的时间====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====查询宠物健康记录的时间====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}

	data, err := c.service.GetNewPetDate(cmd)
	if err != nil {
		log.Error("====查询宠物健康记录的时间====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.SuccessWithData(w, data)
}
