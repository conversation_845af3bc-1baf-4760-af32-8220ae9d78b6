package controllers

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/petai-service/services"
	petai_vo "eShop/view-model/petai-vo"
	"net/http"

	"github.com/go-chi/chi/v5"
)

func NewPetController(service services.PetService) *PetController {
	return &PetController{
		service: service,
	}
}

// 用户授权阿闻数据 到 小闻养宠助手
type PetController struct {
	service services.PetService
}

func (w *PetController) RegisterRoutes(r chi.Router) {

	//小程序
	r.Route("/petai-app/api/pet", func(r chi.Router) {
		r.Post("/add", w.PetInfoAdd)                // 添加宠物信息
		r.Post("/update", w.PetInfoUpdate)          // 更新宠物信息
		r.Post("/edit-status", w.PetInfoEditStatus) // 编辑宠物状态
		r.Post("/list", w.PetInfoList)              // 宠物列表

	})

}

// PetInfoAdd 新增宠物信息
// @Summary 新增宠物信息 @petai-v1.0.0
// @Description
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.PetInfo true "请求对话参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/pet/add [post]
func (c *PetController) PetInfoAdd(w http.ResponseWriter, r *http.Request) {

	cmd, err := utils.Bind[petai_vo.PetInfo](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====新增宠物====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====新增宠物====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	eshopUserId := jwtInfo.Id
	cmd.UserInfoId = eshopUserId

	petInfoId, err := c.service.PetInfoAddOrUpdate(cmd)
	if err != nil {
		log.Error("====新增宠物====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.SuccessWithData(w, petInfoId)
}

// PetInfoUpdate 更新宠物信息
// @Summary 更新宠物信息 @petai-v1.0.0 @petai-v2.2.0
// @Description
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.PetInfo true "请求对话参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/pet/update [post]
func (c *PetController) PetInfoUpdate(w http.ResponseWriter, r *http.Request) {

	cmd, err := utils.Bind[petai_vo.PetInfo](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====更新宠物信息====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====更新宠物信息====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	eshopUserId := jwtInfo.Id
	cmd.UserInfoId = eshopUserId

	_, err = c.service.PetInfoAddOrUpdate(cmd)
	if err != nil {
		log.Error("====更新宠物信息====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}

// PetInfoList 获取用户宠物列表
// @Summary 获取用户宠物列表 @petai-v1.0.0
// @Description
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.PetInfoListReq true "请求对话参数"
// @Success 200 {object} petai_vo.PetInfoListResp "成功"
// @Failure 400 {object} petai_vo.PetInfoListResp "请求错误"
// @Router /petai-app/api/pet/list [post]
func (c *PetController) PetInfoList(w http.ResponseWriter, r *http.Request) {

	cmd, err := utils.Bind[petai_vo.PetInfoListReq](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====获取用户宠物列表====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====获取用户宠物列表====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	eshopUserId := jwtInfo.Id
	cmd.UserInfoId = eshopUserId

	data, total, err := c.service.PetInfoList(cmd)
	if err != nil {
		log.Error("====获取用户宠物列表====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.SuccessWithPage(w, data, int(total))
}

// PetInfoEditStatus 编辑宠物状态
// @Summary 编辑宠物状态 @petai-v1.0.0
// @Description
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.PetInfoEditStatusReq true "请求对话参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/pet/edit-status [post]
func (c *PetController) PetInfoEditStatus(w http.ResponseWriter, r *http.Request) {

	cmd, err := utils.Bind[petai_vo.PetInfoEditStatusReq](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====编辑宠物状态====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====编辑宠物状态====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	if len(cmd.PetInfoId) == 0 {
		log.Error("====编辑宠物状态====宠物ID不能为空")
		response.BadRequest(w, "宠物ID不能为空")
		return
	}
	err = c.service.PetInfoEditStatus(cmd)
	if err != nil {
		log.Error("====编辑宠物状态====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}
