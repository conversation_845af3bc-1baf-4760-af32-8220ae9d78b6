package manager

import (
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	omnibus_vo "eShop/view-model/omnibus-vo"
	product_vo "eShop/view-model/product-vo"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"

	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/product-service/services"

	"github.com/spf13/cast"
)

// GetChainProductInfo
// @Summary 商品服务-门店商品-门店商品详情
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param GetStoreProductReq body product_vo.GetStoreProductReq true " "
// @Success 200 {object} product_vo.GetStoreProductRes
// @Router /product-app/manager/info [POST]
func GetStoreProductInfo(writer http.ResponseWriter, request *http.Request) {
	out := product_vo.GetStoreProductRes{}
	out.Code = 400
	req, err := utils.Bind[product_vo.GetStoreProductReq](request)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
	} else if jwtInfo.TenantId == "" {
		out.Message = "门店ID不能为空"
	}
	req.StoreId = jwtInfo.TenantId
	server := services.StoreProductService{}
	out.Data, err = server.GetProductInfo(req)
	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 商品服务-门店商品-门店商品编辑
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param EditStoreProduct body product_vo.EditStoreProductReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/edit [POST]
func EditStoreProduct(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{Code: 400}
	req, err := utils.Bind[product_vo.EditStoreProductReq](request)
	if err != nil {
		out.Message = "解析参数错误"
	} else {
		jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
		if err != nil {
			out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		} else if jwtInfo.TenantId == "" {
			out.Message = "门店ID不能为空"
		} else {
			req.StoreId = jwtInfo.TenantId
			server := services.StoreProductService{}
			if err = server.EditStoreProduct(req); err != nil {
				out.Message = err.Error()
			}
			out.Code = 200
		}
	}
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 商品服务-门店商品-门店商品操作
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param BatchStoreProductReq body product_vo.BatchStoreProductReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/batch_product [POST]
func BatchStoreProduct(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{Code: 400}
	req, err := utils.Bind[product_vo.BatchStoreProductReq](request)
	if err != nil {
		out.Message = "解析参数错误"
	} else {
		jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
		if err != nil {
			out.Message = fmt.Sprintf("获取登录信息失败OfflineTokenFromHeader：%s", err.Error())
		} else if jwtInfo.TenantId == "" {
			out.Message = "门店ID不能为空"
		} else if jwtInfo.RoleType == 1 && req.Type != 4 && (strings.Contains(req.ChannelIds, "2") || strings.Contains(req.ChannelIds, "3")) {
			out.Message = "门店商品操作权限不足"
		} else {
			req.TenantId = jwtInfo.TenantId
			req.RoleType = jwtInfo.RoleType
			req.SyncType = 1

			// 从请求头获取token
			jwtInfo.Token = request.Header.Get("Token")
			server := services.StoreProductService{JwtInfo: jwtInfo}
			if _, err = server.BatchStoreProduct(req); err != nil {
				out.Message = err.Error()
			} else {
				if req.ProductIds != "" {
					out.Message = "操作成功，点击任务查看，或稍后刷新页面查看结果"
				}
				out.Code = 200
			}
		}
	}
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// FindStoreProductList
// @Summary 商品服务-门店商品-门店商品列表
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param FindStoreProductListReq body product_vo.FindStoreProductListReq true " "
// @Success 200 {object} product_vo.FindStoreProductListRes
// @Router /product-app/manager/list [POST]
func FindStoreProductList(writer http.ResponseWriter, request *http.Request) {
	out := product_vo.FindStoreProductListRes{}
	out.Code = 400
	out.Data.List = make([]product_vo.FindStoreProductList, 0)
	req, err := utils.Bind[product_vo.FindStoreProductListReq](request)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.StoreProductService{JwtInfo: jwtInfo}
	req.ChainId = jwtInfo.ChainId
	if len(jwtInfo.TenantId) > 0 {
		req.StoreId = jwtInfo.TenantId
	}

	var total int64
	out.Data.List, total, err = server.FindStoreProductList(req)
	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	if req.IsStats {
		// 获取统计
		var waitGroup sync.WaitGroup
		waitGroup.Add(6)
		go func(req product_vo.FindStoreProductListReq) {
			req.IsOnlyCount = true
			req.Type = 0
			_, out.Data.Stats.TotalCnt, err = server.FindStoreProductList(req) // 全部tab页的总条数

			waitGroup.Done()
		}(req)

		go func(req product_vo.FindStoreProductListReq) {
			req.IsOnlyCount = true
			req.Type = 1
			_, out.Data.Stats.UpCnt, err = server.FindStoreProductList(req) //已上架条数

			waitGroup.Done()
		}(req)

		go func(req product_vo.FindStoreProductListReq) {
			req.IsOnlyCount = true
			req.Type = 2
			_, out.Data.Stats.DownCnt, err = server.FindStoreProductList(req) // 已下架条数

			waitGroup.Done()
		}(req)

		go func(req product_vo.FindStoreProductListReq) {
			req.IsOnlyCount = true
			req.Type = 3
			_, out.Data.Stats.UnLaunchCnt, err = server.FindStoreProductList(req) // 未铺品条数

			waitGroup.Done()
		}(req)
		go func(req product_vo.FindStoreProductListReq) {
			req.IsOnlyCount = true
			req.Type = 4
			_, out.Data.Stats.StockCnt, err = server.FindStoreProductList(req) // 有库存

			waitGroup.Done()
		}(req)

		go func(req product_vo.FindStoreProductListReq) {
			req.IsOnlyCount = true
			req.Type = 5
			_, out.Data.Stats.StockAndDownCnt, err = server.FindStoreProductList(req) // 有库存未上架

			waitGroup.Done()
		}(req)
		waitGroup.Wait()
	}

	out.Code = 200
	out.Total = int(total)
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// FindStoreProductListBySkuId
// @Summary 商品服务-门店商品-门店商品、服务和活体列表
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param FindStoreSkuListReq body product_vo.FindStoreSkuListReq true " "
// @Success 200 {object} product_vo.FindStoreSkuListRes
// @Router /product-app/manager/list-sku [POST]
func FindStoreSkuList(writer http.ResponseWriter, request *http.Request) {
	out := product_vo.FindStoreSkuListRes{}
	out.Code = 400
	out.Data = make([]product_vo.FindStoreProductList, 0)
	req, err := utils.Bind[product_vo.FindStoreSkuListReq](request)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.StoreProductService{JwtInfo: jwtInfo}
	var total int64
	out.Data, total, err = server.FindStoreSkuList(req)
	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Total = int(total)
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// FindStoreInOutList
// @Summary 库存中心-入库商品列表
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param FindStoreInOutListReq body product_vo.FindStoreInOutListReq true " "
// @Success 200 {object} product_vo.FindStoreInOutListRes
// @Router /product-app/manager/list-in-out [POST]
func FindStoreInOutList(writer http.ResponseWriter, request *http.Request) {
	out := product_vo.FindStoreInOutListRes{}
	out.Code = 400
	out.Data = make([]product_vo.FindStoreInOutList, 0)
	req, err := utils.Bind[product_vo.FindStoreInOutListReq](request)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.StoreProductService{JwtInfo: jwtInfo}
	req.StoreId = jwtInfo.TenantId

	// 店铺被运营了， 代运营账号 只能看自己得商品， 自己店铺得员工可用看所有得商品
	if cast.ToInt64(jwtInfo.SourceChainId) > 0 && cast.ToInt(jwtInfo.RoleType) == 2 {
		req.ChainId = cast.ToInt64(jwtInfo.SourceChainId)
	}

	var total int64
	out.Data, total, err = server.FindStoreInOutList(req)
	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Total = int(total)
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// TaskImport
// @Summary 商品批量任务接口
// @Description
// @Tags 宠物连锁SAAS-管理后台
// @Accept plain
// @Produce plain
// @Accept plain
// @Produce plain
// @Param task_content formData int true "任务内容:1:同步创建 2:批量更新 3:批量删除 4:同步分类任务 5-批量上架 6-批量下架务 7-批量调价"
// @Param operation_file_url formData string true "操作文件路径"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/task-import [POST]
func TaskImport(writer http.ResponseWriter, request *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[omnibus_vo.TaskListAsync](request)
	//req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	if err != nil {
		log.Error("创建任务，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分页查询组织操作，参数解析失败：%s", err.Error())
	} else {
		err = new(common.AsyncCommService).CreatSyncTask(request, omnibus_vo.TaskListAsync{
			TaskContent:      req.TaskContent,
			TaskStatus:       0,
			OperationFileUrl: req.OperationFileUrl,
			RequestHeader:    "",
			//OrgId:            req.OrgId,
		})
		if err != nil {
			resp.Message = fmt.Sprintf("创建任务失败：%s", err.Error())
		} else {
			resp.Code = 200
		}
	}
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// StatsSkuExport
// @Summary 门店商品导出
// @Tags 宠物连锁SAAS-管理后台
// @Accept  plain
// @Produce  json
// @Param FindStoreProductListReq body product_vo.FindStoreProductListReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/export [POST]
func Export(w http.ResponseWriter, r *http.Request) {
	out := viewmodel.BaseHttpResponse{
		Code: 400,
	}
	req, err := utils.Bind[product_vo.FindStoreProductListReq](r)
	if err != nil {
		out.Message = fmt.Sprintf("导出请求参数解析失败：%s", err.Error())
	} else {
		jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
		if err != nil {
			out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		} else if jwtInfo.TenantId == "" {
			out.Message = "门店ID不能为空"
		}
		req.StoreId = jwtInfo.TenantId
		req.ChainId = jwtInfo.ChainId
		s := common.TaskListService{}
		var task distribution_vo.TaskList
		par, _ := json.Marshal(req)
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(r.Header.Get("org_id"))
		task.TaskContent = 17
		task.CreateId = jwtInfo.UserId
		task.CreateName = jwtInfo.UserName
		err = s.CreatTask(r, task)
		if err != nil {
			out.Message = fmt.Sprintf("导出商品数据：%s", err.Error())
		} else {
			out.Code = 200
		}
	}
	bytes, _ := json.Marshal(out)
	w.Write(bytes)
}

// GetTaskList
// @Summary 获取任务列表
// @Description
// @Tags 宠物连锁SAAS-管理后台
// @Param GetTaskListRequest query distribution_vo.GetTaskListRequest true "获取任务列表请求"
// @Success 200 {object} distribution_vo.TaskListRes
// @Failure 400 {object} distribution_vo.TaskListRes
// @Router /product-app/manager/task-list [get]
func GetTaskList(writer http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.TaskListRes{}
	resp.Code = 400

	req, err := utils.Bind[distribution_vo.GetTaskListRequest](r)
	if err != nil {
		log.Error("获取任务出错，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分页查询组织操作，参数解析失败：%s", err.Error())
	} else {
		if req.OrgId == 0 {
			req.OrgId = cast.ToInt(r.Header.Get("org_id"))
		}
		jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
		if err != nil {
			log.Error("获取登录信息失败: err", err.Error())
			resp.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		}
		req.CreateId = jwtInfo.UserId
		s := common.TaskListService{}
		ret, count, err := s.GetTaskList(req)
		if err != nil {
			log.Error("获取任务出错：err=" + err.Error())
			resp.Message = fmt.Sprintf("获取任务出错：%s", err.Error())
		} else {
			resp.Code = 200
			resp.Total = count
			resp.Data = ret
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// GetStoreProductUp
// @Summary 获指定渠道已上架商品
// @Description
// @Tags 宠物连锁SAAS-管理后台
// @Param page_index query int false "当前页，默认第1页"
// @Param page_size query int false "每页显示数据条数，默认显示10条"
// @Param sort query string false "排序类型:createTimeDesc：按创建时间顺序倒序；"
// @Param task_status query int false "任务状态:1:未开始;2:进行中;3:已完成；"
// @Param content_str query string true "任务内容:17:门店商品导出"
// @Param promoter query int false "0:自己;1:全部;2:其他;"
// @Success 200 {object} distribution_vo.TaskListRes
// @Failure 400 {object} distribution_vo.TaskListRes
// @Router /product-app/manager/store-product-up [get]
func GetStoreProductUp(writer http.ResponseWriter, request *http.Request) {
	out := product_vo.StoreProductUpDistinctRes{}
	out.Code = 400
	out.Data = make([]product_vo.StoreUpProduct, 0)
	req, err := utils.Bind[product_vo.StoreProductUpDistinctReq](request)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	_, err = jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.StoreProductService{}
	data, err := server.StoreProductUpDistinct(req)
	if err != nil {
		out.Message = err.Error()
	} else {
		out.Code = 200
		out.Data = data.Data
		out.Total = data.Total
	}

	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// GetStoresCountByPrice
// @Summary 获取指定渠道已上架商品
// @Description
// @Tags 宠物连锁SAAS-管理后台
// @Param page_index query int false "当前页，默认第1页"
// @Param page_size query int false "每页显示数据条数，默认显示10条"
// @Param sort query string false "排序类型:createTimeDesc：按创建时间顺序倒序；"
// @Param task_status query int false "任务状态:1:未开始;2:进行中;3:已完成；"
// @Param content_str query string true "任务内容:17:门店商品导出"
// @Param promoter query int false "0:自己;1:全部;2:其他;"
// @Success 200 {object} distribution_vo.TaskListRes
// @Failure 400 {object} distribution_vo.TaskListRes
// @Router /product-app/manager/count-by-price [get]
func GetStoresCountByPrice(writer http.ResponseWriter, request *http.Request) {
	out := product_vo.ProductCountByPriceRes{}
	out.Code = 400
	out.Data = make([]product_vo.ProductCountByPrice, 0)
	req, err := utils.Bind[product_vo.ProductCountByPriceReq](request)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	_, err = jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.StoreProductService{}
	data, err := server.CountByPrice(req)
	if err != nil {
		out.Message = err.Error()
	} else {
		out.Code = 200
		out.Data = data.Data
	}

	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// UpdateProductLocation 更新商品库位信息
// @Summary 更新商品库位信息
// @Description 更新商品库位信息
// @Tags 宠物连锁SAAS-管理后台
// @Accept json
// @Produce json
// @Param UpdateLocationRequest body product_vo.UpdateLocationRequest true "更新库位请求"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/store-product/update-location [POST]
func UpdateProductLocation(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[product_vo.UpdateLocationRequest](r)
	if err != nil {
		log.Error("更新商品库位信息，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("更新商品库位信息，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		resp.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(resp)
		w.Write(out2)
		return
	}
	req.StoreId = jwtInfo.TenantId
	req.ChainId = cast.ToInt64(jwtInfo.ChainId)
	server := services.StoreProductService{}
	err = server.UpdateProductLocation(req)
	if err != nil {
		resp.Message = err.Error()
	} else {
		resp.Code = 200
		resp.Message = "更新成功"
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// UpdateLocationSku 库位更换绑定商品
// @Summary 库位更换绑定商品
// @Description 库位更换绑定商品
// @Tags 宠物连锁SAAS-管理后台
// @Accept json
// @Produce json
// @Param UpdateLocationSkuRequest body product_vo.UpdateLocationSkuRequest true "库位更换绑定商品"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/store-product/update-location-sku [POST]
func UpdateLocationSku(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[product_vo.UpdateLocationSkuRequest](r)
	if err != nil {
		log.Error("库位更换绑定商品，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("库位更换绑定商品，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		resp.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(resp)
		w.Write(out2)
		return
	}
	req.StoreId = jwtInfo.TenantId
	req.ChainId = cast.ToInt64(jwtInfo.ChainId)
	server := services.StoreProductService{}
	err = server.UpdateLocationSku(req)
	if err != nil {
		resp.Message = err.Error()
	} else {
		resp.Code = 200
		resp.Message = "更新成功"
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// GetServiceList 获取服务列表
// @Summary 获取服务列表
// @Tags 商品管理
// @Accept json
// @Produce json
// @Param ServiceListReq body product_vo.ServiceListReq true "获取服务列表请求"
// @Success 200 {object} product_vo.ServiceListRes
// @Router /product-app/manager/service/list [post]
func GetServiceList(w http.ResponseWriter, r *http.Request) {
	resp := product_vo.ServiceListRes{}
	resp.Code = 400

	req, err := utils.Bind[product_vo.ServiceListReq](r)
	if err != nil {
		resp.Message = fmt.Sprintf("参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 获取店铺ID
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		resp.Message = "获取登录信息失败"
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	req.StoreId = jwtInfo.TenantId

	server := services.StoreProductService{}
	list, total, err := server.GetServiceList(req)
	if err != nil {
		resp.Message = err.Error()
	} else {
		resp.Code = 200
		resp.Data = list
		resp.Total = total
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// SaveService 保存服务
// @Summary 保存服务(新增/编辑)
// @Tags 商品管理
// @Accept json
// @Produce json
// @Param SaveServiceReq body product_vo.SaveServiceReq true "保存服务请求"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/service/save [post]
func SaveService(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[product_vo.SaveServiceReq](r)
	if err != nil {
		resp.Message = fmt.Sprintf("参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 获取店铺ID
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		resp.Message = "获取登录信息失败"
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	req.StoreId = jwtInfo.TenantId
	req.ChainId = cast.ToInt64(jwtInfo.ChainId)
	server := services.StoreProductService{}
	err = server.SaveService(req)
	if err != nil {
		resp.Message = err.Error()
	} else {
		resp.Code = 200
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// DeleteService 删除服务
// @Summary 删除服务
// @Tags 商品管理
// @Accept json
// @Produce json
// @Param SaveServiceReq body product_vo.SaveServiceReq true "保存服务请求"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/service/delete [post]
func DeleteService(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400
	mode, err := utils.Bind[product_vo.SaveServiceReq](r)
	if err != nil {
		resp.Message = fmt.Sprintf("参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	if mode.Id == 0 {
		resp.Message = "ID不能为空"
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 获取店铺ID
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		resp.Message = "获取登录信息失败"
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	server := services.StoreProductService{}
	err = server.DeleteService(mode.Id, jwtInfo.TenantId) // 传入 store_id
	if err != nil {
		resp.Message = err.Error()
	} else {
		resp.Code = 200
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// GetService 获取服务详情
// @Summary 获取服务详情
// @Tags 商品管理
// @Accept json
// @Produce json
// @Param GetServiceReq body product_vo.GetServiceReq true "获取服务详情请求"
// @Success 200 {object} product_vo.GetServiceRes
// @Router /product-app/manager/service/detail [post]
func GetService(w http.ResponseWriter, r *http.Request) {
	resp := product_vo.GetServiceRes{}
	resp.Code = 400

	req, err := utils.Bind[product_vo.GetServiceReq](r)
	if err != nil {
		resp.Message = fmt.Sprintf("参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 获取店铺ID
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		resp.Message = "获取登录信息失败"
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	req.StoreId = jwtInfo.TenantId

	server := services.StoreProductService{}
	data, err := server.GetService(req)
	if err != nil {
		resp.Message = err.Error()
	} else {
		resp.Code = 200
		resp.Data = data
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}
