package api

import (
	"eShop/infra/jwtauth"
	"eShop/infra/utils"
	"eShop/services/product-service/services"
	vo "eShop/view-model/product-vo"
	"encoding/json"
	"fmt"
	"net/http"
)

// CategoryList
// @Summary 商品服务-商品分类-分类列表
// @Tags 宠物连锁SAAS-小程序
// @Accept  json
// @Produce  json
// @Param CategoryListForApiReq body vo.CategoryListForApiReq true " "
// @Success 200 {object} vo.CategoryListForApiRes
// @Router /product-app/api/category/list [POST]
func CategoryListForApi(writer http.ResponseWriter, request *http.Request) {
	out := vo.CategoryListForApiRes{}
	out.Code = 400
	req, err := utils.Bind[vo.CategoryListForApiReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else if jwtInfo.TenantId == "" {
		out.Message = "门店ID不能为空"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	req.StoreId = jwtInfo.TenantId
	req.ChainId = jwtInfo.ChainId

	server := services.CategoryService{Request: request}
	if out.Data, err = server.CategoryListForApi(req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "查询成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
