package api

import (
	"eShop/infra/jwtauth"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/product-service/services"
	vo "eShop/view-model/product-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// StoreProductDetail
// @Summary 商品服务-门店商品-门店商品sku详情
// @Tags 宠物连锁SAAS-小程序
// @Accept  json
// @Produce  json
// @Param StoreSkuDetailReq body vo.StoreSkuDetailReq true " "
// @Success 200 {object} vo.StoreSkuDetailRes
// @Router /product-app/api/sku/detail [POST]
func StoreSkuDetail(writer http.ResponseWriter, request *http.Request) {
	out := vo.StoreSkuDetailRes{}
	out.Code = 400

	req, err := utils.Bind[vo.StoreSkuDetailReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else if jwtInfo.TenantId == "" {
		out.Message = fmt.Sprintf("门店ID不能为空")
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	req.StoreId = jwtInfo.TenantId
	req.ChainId = cast.ToInt64(jwtInfo.ChainId)
	server := services.StoreProductService{}
	out.Data, err = server.StoreSkuDetail(req)
	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// FindStoreProduct
// @Summary 商品服务-门店商品-门店商品列表
// @Tags 宠物连锁SAAS-小程序
// @Accept  json
// @Produce  json
// @Param StoreProductListApiReq body vo.StoreProductListApiReq true " "
// @Success 200 {object} vo.StoreProductListApiRes
// @Router /product-app/api/product/list [POST]
func StoreProductList(writer http.ResponseWriter, request *http.Request) {
	out := vo.StoreProductListApiRes{}
	out.Code = 400

	req, err := utils.Bind[vo.StoreProductListApiReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else if jwtInfo.TenantId == "" {
		out.Message = "门店ID不能为空"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	req.ChannelId = cast.ToInt(request.Header.Get("channel_id"))

	req.StoreId = jwtInfo.TenantId
	req.ChainId = cast.ToInt64(jwtInfo.ChainId)
	server := services.StoreProductService{}
	var total int64
	out.List, total, err = server.StoreProductListApi(req)

	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Total = int(total)
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// StoreProductStock
// @Summary 商品服务-门店商品-商品库存
// @Tags 宠物连锁SAAS-小程序
// @Accept  json
// @Produce  json
// @Param StoreProductStockReq body vo.StoreProductStockReq true " "
// @Success 200 {object} vo.StoreProductStockRes
// @Router /product-app/api/product/stock [POST]
func StoreProductStock(writer http.ResponseWriter, request *http.Request) {
	out := vo.StoreProductStockRes{}
	out.Code = 400

	req, err := utils.Bind[vo.StoreProductStockReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	if len(req.FinanceCode) == 0 {
		out.Message = "店铺ID必填"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	if len(req.SkuIds) == 0 {
		out.Message = "skuid信息失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	channelId := cast.ToInt64(request.Header.Get("channel_id"))
	if channelId == 0 {
		channelId = common.ChannelIdWeChatApp
	}

	req.ChannelId = channelId
	server := services.StoreProductService{}
	out.Data, err = server.StoreProductStock(req)

	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// StoreProductUpdownstatus
// @Summary 商品服务-门店商品-商品上下架
// @Tags 宠物连锁SAAS-小程序
// @Accept  json
// @Produce  json
// @Param StoreProductUpdownstatusReq body vo.StoreProductUpdownstatusReq true " "
// @Success 200 {object} vo.StoreProductUpdownstatusRes
// @Router /product-app/api/product/updownstatus [POST]
func StoreProductUpdownstatus(writer http.ResponseWriter, request *http.Request) {
	out := vo.StoreProductUpdownstatusRes{}
	out.Code = 400

	req, err := utils.Bind[vo.StoreProductUpdownstatusReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else if jwtInfo.TenantId == "" {
		out.Message = "门店ID不能为空"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	req.FinanceCode = jwtInfo.TenantId
	req.ChainId = cast.ToInt64(jwtInfo.ChainId)

	if len(req.SkuIds) == 0 {
		out.Message = "skuid不能为空"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	channelId := cast.ToInt(request.Header.Get("channel_id"))
	if channelId == 0 {
		channelId = common.ChannelIdWeChatApp
	}

	req.ChannelId = channelId
	server := services.StoreProductService{}
	out.Data, err = server.StoreProductUpdownstatus(req)

	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
