package manager

import "github.com/go-chi/chi/v5"

func InitAllManager(r chi.Router) {

	// 门店后端路由
	InitStoreRouter(r)

	// 饿了么后端路由
	InitElmRouter(r)
}

func InitStoreRouter(r chi.Router) {
	r.Post("/external-app/manager/store/authorization", StoreAuthorization)
	r.Post("/external-app/manager/store/check-clean", CheckStoreClean)
}

func InitElmRouter(r chi.Router) {
	r.Route("/external-app/manager/elm", func(r chi.Router) {
		r.Get("/property/list", GetElmCategoryPropertyList)
	})
}
