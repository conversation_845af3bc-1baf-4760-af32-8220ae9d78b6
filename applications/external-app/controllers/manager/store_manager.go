package manager

import (
	"eShop/infra/jwtauth"
	viewmodel "eShop/view-model"
	dto2 "eShop/view-model/external-vo/dto"
	"encoding/json"
	"fmt"
	"net/http"

	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/external-service/services"
)

// @Summary 清理商品和分类
// @Description 清理商品和分类
// @Tags 宠物连锁SAAS-店铺授权
// @Accept json
// @Produce json
// @Param StoreAuthorization body dto.StoreAuthorization true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /external-app/manager/store/authorization [POST]
func StoreAuthorization(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[dto2.StoreAuthorization](r)
	if err != nil {
		log.Error("店铺清理数据，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("店铺清理数据，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		resp.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
	} else if jwtInfo.TenantId == "" {
		resp.Message = "门店ID不能为空"
	}
	req.ShopId = jwtInfo.TenantId
	req.ChainId = jwtInfo.ChainId

	server := services.StoreService{}
	err = server.StoreAuthorization(req)
	if err != nil {
		resp.Message = err.Error()
	} else {
		resp.Code = 200
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// @Summary 检查店铺数据清除状态
// @Description 检查店铺数据清除状态
// @Tags 宠物连锁SAAS-第三方回调
// @Accept json
// @Produce json
// @Param StoreAuthorization body dto.StoreAuthorization true " "
// @Success 200 {object} dto.StoreCleanResponse
// @Failure 400 {object} dto.StoreCleanResponse
// @Router /external-app/manager/store/check-clean [POST]
func CheckStoreClean(w http.ResponseWriter, r *http.Request) {
	resp := dto2.StoreCleanResponse{}
	resp.Code = 400

	req, err := utils.Bind[dto2.StoreAuthorization](r)
	if err != nil {
		log.Error("检查店铺数据清除状态，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("检查店铺数据清除状态，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		resp.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
	} else if jwtInfo.TenantId == "" {
		resp.Message = "门店ID不能为空"
	}
	req.ShopId = jwtInfo.TenantId
	req.ChainId = jwtInfo.ChainId

	server := services.StoreService{}
	details, isClean, err := server.CheckStoreClean(req)
	if err != nil {
		resp.Message = err.Error()
	} else {
		resp.Code = 200
		resp.Data.Details = details
		resp.Data.IsClean = isClean
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}
