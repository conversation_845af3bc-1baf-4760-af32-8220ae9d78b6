package api

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	sjan_service "eShop/services/external-service/services"
	viewmodel "eShop/view-model"
	external_vo "eShop/view-model/external-vo"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
)

// 世纪安诺接口说明文档
// 签名验证说明
//    - 签名算法: MD5(key={signKey}&timestamp={timestamp}&url={url})
//    - 签名参数:
//      * signKey: 系统配置的签名密钥
//      * timestamp: 请求时间戳，精确到秒
//      * url: 请求的完整URL
//    - 验证流程:
//      1. 从请求头获取sign、timestamp和Referer
//      2. 使用相同算法生成期望的签名
//      3. 比对请求中的签名与期望的签名是否一致

func VerifySignFromRequest(r *http.Request) error {
	sign := r.Header.Get("sign")
	timestamp := r.Header.Get("timestamp")
	// 从Referer头获取URL
	url := r.Header.Get("Referer")

	if sign == "" || timestamp == "" {
		log.Errorf("签名验证失败: 缺少必要参数 sign=%s, timestamp=%s", sign, timestamp)
		return errors.New("签名验证失败: 缺少必要参数")
	}

	if url == "" {
		log.Errorf("签名验证失败: 请求头中缺少Referer信息")
		return errors.New("签名验证失败: 请求头中缺少Referer信息")
	}

	// log.Infof("SJAN_REQUEST_LOG: 验证签名参数: sign=%s, timestamp=%s, url=%s", sign, timestamp, url)
	return jwtauth.VerifySign(sign, timestamp, url)
}

// AddProducts 添加产品信息接口
// @Summary 世纪安诺-添加产品信息
// @Description 世纪安诺-添加产品信息
// @Tags 世纪安诺-第三方接口
// @Accept json
// @Produce json
// @Param sign header string true "签名，使用MD5(key={signKey}&timestamp={timestamp}&url={url})生成"
// @Param timestamp header string true "时间戳，精确到秒"
// @Param Referer header string true "请求来源URL，用于签名验证"
// @Param products body []external_vo.ProductInfo true "产品信息数组"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /external-app/api/sjan/product/add [POST]
func AddProducts(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	// 使用新函数验证签名
	err := VerifySignFromRequest(r)
	if err != nil {
		log.Errorf("添加产品信息，签名验证失败: %v", err)
		resp.Message = fmt.Sprintf("添加产品信息，签名验证失败: %s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 直接从请求体解析产品数组
	var products []external_vo.ProductInfo
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&products); err != nil {
		log.Errorf("添加产品信息，参数解析失败: %v", err)
		resp.Message = fmt.Sprintf("添加产品信息，参数解析失败: %s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	defer r.Body.Close()

	// 记录请求参数
	requestData, _ := json.Marshal(products)
	log.Infof("SJAN_REQUEST_LOG: 添加产品信息接口请求参数: %s", string(requestData))

	// 参数校验
	if len(products) == 0 {
		resp.Message = "请提供产品信息"
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 调用服务层处理业务逻辑
	service := sjan_service.SjanService{}
	err = service.AddProducts(products)
	if err != nil {
		log.Errorf("添加产品信息失败: %v", err)
		resp.Message = fmt.Sprintf("添加产品信息失败: %v", err)
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 构造响应
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// AddAgents 添加代理商信息接口
// @Summary 世纪安诺-添加代理商信息
// @Description 世纪安诺-添加代理商信息
// @Tags 世纪安诺-第三方接口
// @Accept json
// @Produce json
// @Param sign header string true "签名，使用MD5(key={signKey}&timestamp={timestamp}&url={url})生成"
// @Param timestamp header string true "时间戳，精确到秒"
// @Param Referer header string true "请求来源URL，用于签名验证"
// @Param agents body []external_vo.AgentInfo true "代理商信息数组"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /external-app/api/sjan/agent/add [POST]
func AddAgents(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	// 使用新函数验证签名
	err := VerifySignFromRequest(r)
	if err != nil {
		log.Errorf("添加代理商信息，签名验证失败: %v", err)
		resp.Message = fmt.Sprintf("添加代理商信息，签名验证失败: %s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 直接从请求体解析产品数组
	var agents []external_vo.AgentInfo
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&agents); err != nil {
		log.Errorf("添加代理商信息，参数解析失败: %v", err)
		resp.Message = fmt.Sprintf("添加产品信息，参数解析失败: %s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	defer r.Body.Close()

	// 记录请求参数
	requestData, _ := json.Marshal(agents)
	log.Infof("SJAN_REQUEST_LOG: 添加代理商信息接口请求参数: %s", string(requestData))

	// 参数校验
	if len(agents) == 0 {
		resp.Message = "请提供代理商信息"
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 调用服务层处理业务逻辑
	service := sjan_service.SjanService{}
	err = service.AddAgents(agents)
	if err != nil {
		log.Errorf("添加代理商信息失败: %v", err)
		resp.Message = fmt.Sprintf("添加代理商信息失败: %v", err)
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 构造响应
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// AddPackagingTask 添加包装任务及明细
// @Summary 世纪安诺-添加包装任务及明细
// @Description 世纪安诺-添加包装任务及明细
// @Tags 世纪安诺-第三方接口
// @Accept json
// @Produce json
// @Param sign header string true "签名，使用MD5(key={signKey}&timestamp={timestamp}&url={url})生成"
// @Param timestamp header string true "时间戳，精确到秒"
// @Param Referer header string true "请求来源URL，用于签名验证"
// @Param task body external_vo.PackagingTaskRequest true "包装任务信息"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /external-app/api/sjan/packaging/add [POST]
func AddPackagingTask(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	// 使用新函数验证签名
	err := VerifySignFromRequest(r)
	if err != nil {
		log.Errorf("添加包装任务，签名验证失败: %v", err)
		resp.Message = fmt.Sprintf("添加包装任务，签名验证失败: %s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 直接从请求体解析包装任务数据
	var task external_vo.PackagingTaskRequest
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&task); err != nil {
		log.Errorf("添加包装任务，参数解析失败: %v", err)
		resp.Message = fmt.Sprintf("添加包装任务，参数解析失败: %s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	defer r.Body.Close()

	// 记录请求参数
	requestData, _ := json.Marshal(task)
	log.Infof("SJAN_REQUEST_LOG: 添加包装任务接口请求参数: %s", string(requestData))

	// 调用服务层处理业务逻辑
	service := sjan_service.SjanService{}
	err = service.AddPackagingTask(task)
	if err != nil {
		log.Errorf("添加包装任务失败: %v", err)
		resp.Message = fmt.Sprintf("添加包装任务失败: %v", err)
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 构造响应
	resp.Code = 200
	resp.Message = "包装任务添加成功"
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// AddOutboundRecord 添加出库记录及明细
// @Summary 世纪安诺-添加出库记录及明细
// @Description 世纪安诺-添加出库记录及明细
// @Tags 世纪安诺-第三方接口
// @Accept json
// @Produce json
// @Param sign header string true "签名，使用MD5(key={signKey}&timestamp={timestamp}&url={url})生成"
// @Param timestamp header string true "时间戳，精确到秒"
// @Param Referer header string true "请求来源URL，用于签名验证"
// @Param record body external_vo.OutboundRecordRequest true "出库记录信息"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /external-app/api/sjan/outbound/add [POST]
func AddOutboundRecord(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	// 使用新函数验证签名
	err := VerifySignFromRequest(r)
	if err != nil {
		log.Errorf("添加出库记录，签名验证失败: %v", err)
		resp.Message = fmt.Sprintf("添加出库记录，签名验证失败: %s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 直接从请求体解析出库记录数据
	var record external_vo.OutboundRecordRequest
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&record); err != nil {
		log.Errorf("添加出库记录，参数解析失败: %v", err)
		resp.Message = fmt.Sprintf("添加出库记录，参数解析失败: %s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	defer r.Body.Close()

	// 记录请求参数
	requestData, _ := json.Marshal(record)
	log.Infof("SJAN_REQUEST_LOG: 添加出库记录接口请求参数: %s", string(requestData))

	// 参数校验
	if record.BillNo == "" || record.CuNo == "" {
		log.Errorf("添加出库记录，缺少必要参数: billNo=%s, cuNo=%s", record.BillNo, record.CuNo)
		resp.Message = "出库单号和代理编号为必填项"
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 调用服务层处理业务逻辑
	service := sjan_service.SjanService{}
	err = service.AddOutboundRecord(record)
	if err != nil {
		log.Errorf("添加出库记录失败: %v", err)
		resp.Message = fmt.Sprintf("添加出库记录失败: %v", err)
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 构造响应
	resp.Code = 200
	resp.Message = "出库记录添加成功"
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// AddReturnRecords 添加退货记录
// @Summary 世纪安诺-添加退货记录
// @Description 世纪安诺-添加退货记录
// @Tags 世纪安诺-第三方接口
// @Accept json
// @Produce json
// @Param sign header string true "签名，使用MD5(key={signKey}&timestamp={timestamp}&url={url})生成"
// @Param timestamp header string true "时间戳，精确到秒"
// @Param Referer header string true "请求来源URL，用于签名验证"
// @Param records body []external_vo.ReturnRecordItem true "退货记录数组"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /external-app/api/sjan/return/add [POST]
func AddReturnRecords(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	// 使用新函数验证签名
	err := VerifySignFromRequest(r)
	if err != nil {
		log.Errorf("添加退货记录，签名验证失败: %v", err)
		resp.Message = fmt.Sprintf("添加退货记录，签名验证失败: %s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 直接从请求体解析退货记录数组
	var records []external_vo.ReturnRecordItem
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&records); err != nil {
		log.Errorf("添加退货记录，参数解析失败: %v", err)
		resp.Message = fmt.Sprintf("添加退货记录，参数解析失败: %s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	defer r.Body.Close()

	// 记录请求参数
	requestData, _ := json.Marshal(records)
	log.Infof("SJAN_REQUEST_LOG: 添加退货记录接口请求参数: %s", string(requestData))

	// 参数校验
	if len(records) == 0 {
		log.Errorf("添加退货记录，退货记录数据为空")
		resp.Message = "请提供退货记录数据"
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 构造请求对象
	request := external_vo.ReturnRecordRequest{
		Records: records,
	}

	// 调用服务层处理业务逻辑
	service := sjan_service.SjanService{}
	err = service.AddReturnRecords(request)
	if err != nil {
		log.Errorf("添加退货记录失败: %v", err)
		resp.Message = fmt.Sprintf("添加退货记录失败: %v", err)
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 构造响应
	resp.Code = 200
	resp.Message = "退货记录添加成功"
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}
