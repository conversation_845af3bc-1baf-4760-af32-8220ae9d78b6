package api

import (
	"github.com/go-chi/chi/v5"
)

func InitAllApi(r chi.Router) {
	//分销员
	InitDistributorApiRouter(r)

	//C端用户共用方法
	InitCommApiRouter(r)

	//我的佣金
	InitCommissionApiRouter(r)

	//业务员
	InitSalesManApiRouter(r)

	//分销客户
	InitDisFansApiRouter(r)

	//分销订单
	InitDisOrderApiRouter(r)

	//店铺信息
	InitShopApiRouter(r)

	//保险分销订单
	InitDisInsureSettleApiRouter(r)

	// 瑞鹏体制内的数据
	InitRPApiRouter(r)
	// 高德地图 瑞鹏体制外医院列表
	InitOutApiRouter(r)
	//数据统计
	InitStatsRouter(r)
	//内容数据
	InitCententApiRouter(r)
	//百林康源
	InitBlkyApiRouter(r)
}

// 分销员
func InitDistributorApiRouter(r chi.Router) {

	r.Route("/api/distributor", func(r chi.Router) {
		r.Get("/get", DistributorGet)
		r.Post("/insert", DistributorInsert)
		r.Post("/ocrp-prediction-social-code", OcrpPredictionSocialCode) //识别营业执照，返回社会信用代码和企业名称
		r.Post("/enterprise-by-social-code", EnterpriseBySocialCode)     //根据社会信用代码查询企业名称
		// 根据社会信用代码，和法人手机号， 判断该企业是否被其他分销员绑定
		r.Post("/check-enterprise-binded", CheckEnterpriseBinded)
		// 老板邀请注册店员二维码
		r.Post("/boss-invite-distributor", BossInviteDistributor)
		// 检查老板邀请码是否有效
		r.Post("/check-boss-invite-code", CheckBossInviteCode)
		r.Post("/edit", DistributorEdit)
		r.Post("/cls-edit", CLSDistributorEdit)
		r.Post("/share", SharePosters)
		r.Get("/shop-distributors", GetShopDistributors)
		// 将店员移出企业
		r.Post("/remove-distributor", RemoveDistributor)
		// 店员自己解绑所在企业
		r.Post("/self-remove-enterprise", SelfRemoveEnterprise)
		r.Get("/enterprises", GetDistributorEnterpriseList)
		r.Get("/enterprises-by-phone", GetEnterprisesByPhone) //根据手机号查询云订货企业列表
		r.Post("/enterprise/default", UpdateDefaultEnterprise)
	})
}

// 共用API
func InitCommApiRouter(r chi.Router) {

	r.Route("/api/comm", func(r chi.Router) {
		r.Post("/check-code", CheckCode)
		r.Post("/code-send", SendCode)
	})
}

// 我的佣金
func InitCommissionApiRouter(r chi.Router) {
	r.Route("/api/dis/commission", func(r chi.Router) {
		//r.Get("/info", GetDisCommissionInfo) 已废弃
		r.Get("/shop-info", GetShopCommInfo)
		r.Post("/apply", DisCommissionApply)
		r.Post("/cls-apply", CLSCommissionApply)

	})
}

// 业务员
func InitSalesManApiRouter(r chi.Router) {
	r.Route("/api/salesman", func(r chi.Router) {
		r.Post("/get", SaleManGet)
		r.Post("/list", SaleManList)
		r.Post("/center-enterprise", SaleManCenterEnterprise)
		r.Post("/center-details", SaleManCenterDetails)
		r.Get("/shop-dis-page", SalesShopDisPage)
	})
}

// 分销客户
func InitDisFansApiRouter(r chi.Router) {

	r.Route("/api/dis/fans", func(r chi.Router) {
		r.Get("/list", GetDisFansList)
		r.Get("/info", GetBindDistributor)
	})
}

// 分销订单
func InitDisOrderApiRouter(r chi.Router) {
	r.Route("/api/dis/order", func(r chi.Router) {
		r.Get("/list", GetDisOrderListApi)
		r.Post("/sett", OrderSetted)
		// 宠利扫-获取可提现订单列表
		r.Get("/withdraw/list", GetWithdrawOrderList)
		// 宠利扫-获取退款单列表
		r.Get("/withdraw/refund-list", GetRefundOrderList)
	})
}

// 店铺信息
func InitShopApiRouter(r chi.Router) {
	r.Route("/api/shop", func(r chi.Router) {
		r.Post("/get", GetShopDetail)
		r.Get("/saas/get", GetSaasShopDetail)
		r.Post("/add", GetShopAdd)
		//SCRM-企业查询接口
		r.Get("/get", GetShop)
		r.Get("/list", GetShopList)

	})
}

// InitDisInsureSettleApiRouter 保险分销结算
func InitDisInsureSettleApiRouter(r chi.Router) {
	r.Route("/api/insure-settle", func(r chi.Router) {
		r.Get("/page-list", ListDisInsureSettle)
	})
}

func InitRPApiRouter(r chi.Router) {
	r.Route("/api/rp", func(r chi.Router) {
		r.Get("/hospital-list", RPHospitalList)
		r.Get("/dict-data", DictData)
		//体制内外的医院列表
		r.Get("/all-hospital-list", AllHospitalList)
	})
}
func InitStatsRouter(r chi.Router) {
	r.Route("/api/stats", func(r chi.Router) {
		// 数据统计-分销员中心
		r.Get("/dis-center/index", StatsDisCenterIndex)
		// 数据统计-分销员中心-数据概览
		r.Get("/dis-center/overview", StatsDisCenterOverview)
		// 数据统计-分销员中心-数据概览-曲线图
		r.Get("/dis-center/graph", StatsDisCenterGraph)
		// 数据统计-分销员中心-数据概览-累计数据
		r.Get("/dis-center/total", StatsDisCenterTotal)
		// 数据统计-分销员中心-分销员数据
		r.Get("/dis-center/distributor", StatsDisCenterDistributor)
		// 数据统计-分销员中心-商品分析
		r.Get("/dis-center/goods", StatsDisCenterGoods)

		// 业务员中心-数据概览-累计数据展示
		r.Get("/salesperson/summary", StatsSalespersonSummary)
		// 业务员中心-数据概览-分销数据趋势（曲线图）
		r.Get("/salesperson/graph", StatsDisGraph)
		// 业务员中心-数据概览-分销数据趋势（数据展示）
		r.Get("/salesperson/trend", StatsDisTrend)
		// 业务员中心-商品分析
		r.Get("/salesperson/goods", StatsDisGoods)
		//百林康源-医生销售看板-医生使用百林康源小程序分布
		r.Get("/blky/doctors-real-id", StatsDoctorRealID)
	})
}

func InitOutApiRouter(r chi.Router) {
	r.Route("/api/out", func(r chi.Router) {
		r.Get("/hospital-list", OutHospitalList)

	})
}

func InitCententApiRouter(r chi.Router) {
	r.Route("/api/centent", func(r chi.Router) {
		r.Post("/tag-list", TagList)
		r.Post("/article-list", ArticleList)
		r.Post("/search_article-list", SearchArticleList)
	})
}

func InitBlkyApiRouter(r chi.Router) {
	r.Route("/api/blky", func(r chi.Router) {
		r.Get("/query-code", BlkyQueryCode)
		r.Post("/sync-code-date", SyncCodeDate)
		// 防伪码验证
		r.Post("/verify-security-code", VerifySecurityCode)
		// 更新用户信息
		r.Post("/update-user-info", UpdateUserInfo)
	})
}
