package api

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"net/http"
)

//分销客户

// @Summary 获取分销客户列表
// @Description
// @Tags 小程序接口-分销客户
// @Accept plain
// @Produce json
// @Param GetDisFansListReq query vo.GetDisFansListReq true " "
// @Success 200 {object} vo.GetDisFansListRes
// @Failure 400 {object} vo.GetDisFansListRes
// @Router /api/dis/fans/list [GET]
func GetDisFansList(writer http.ResponseWriter, request *http.Request) {

	var err error
	out := vo.GetDisFansListRes{}
	out.Code = 400

	param, err := utils.Bind[vo.GetDisFansListReq](request)
	if err != nil {
		log.Errorf("获取分销客户列表-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 从jwt里获取 分销员电话号码
	if jwtInfo, err := jwtauth.GetJwtInfo(request); err != nil {
		log.Errorf("获取分销客户列表-鉴权失败-错误为%s", err.Error())
		out.Message = "鉴权失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else {
		param.MemberId = jwtInfo.Memberid
	}

	server := services.DisFansService{}
	if out.Data, out.Total, err = server.GetDisFansList(param); err != nil {
		log.Errorf("获取分销客户列表-错误为%s", err.Error())
		out.Message = "获取分销客户列表失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// GetBindDistributor 查询用户绑定的分销员
// @Summary 查询用户绑定的分销员
// @Tags 小程序接口-分销客户
// @Accept  json
// @Produce  json
// @Success 200 {object} vo.GetDisFansListRes
// @Failure 400 {object} vo.GetDisFansListRes
// @Router /api/dis/fans/info [GET]
func GetBindDistributor(writer http.ResponseWriter, request *http.Request) {

	var err error
	out := vo.GetDisFansInfoRes{}
	out.Code = 400
	MemberId := 0
	// 从jwt里获取 分销员电话号码
	if jwtInfo, err := jwtauth.GetJwtInfo(request); err != nil {
		log.Errorf("查询用户绑定的分销员-鉴权失败-错误为%s", err.Error())
		out.Message = "鉴权失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else {
		MemberId = jwtInfo.Memberid
	}

	server := services.DisFansService{}
	if out.Data, err = server.GetBindDistributor(MemberId); err != nil {
		log.Errorf("查询用户绑定的分销员-错误为%s", err.Error())
		out.Message = "查询用户绑定的分销员失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
