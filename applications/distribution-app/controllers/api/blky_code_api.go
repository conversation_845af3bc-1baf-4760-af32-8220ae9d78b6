package api

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// StatsDoctorRealID
// @Summary 百林康源物流码/箱码查询
// @Tags 小程序接口-数据统计-分销员中心
// @Accept plain
// @Produce json
// @Success 200 {object} vo.StatsDoctorResponse
// @Failure 400 {object} vo.StatsDoctorResponse
// @Router /api/blky/query-code [get]
func BlkyQueryCode(writer http.ResponseWriter, request *http.Request) {
	// @Param StatsDisCenterGoodsReq query vo.StatsDisCenterGoodsReq true " "
	resp := vo.BlkyQueryCodeResponse{}
	resp.Code = 400

	if request.URL.Query().Get("code") == "" {
		resp.Message = "请输入箱码或物流码，参数不可为空"
		return
	}

	code := request.URL.Query()["code"][0]

	service := services.BlkyCodeService{}
	resp.Data = service.QueryCode(code)
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// 同步贵族商品码数据
// @Summary 深圳利都-同步贵族商品码数据
// @Tags 对外接口-深圳利都
// @Accept plain
// @Produce json
// @Param SyncCodeDateReq body vo.SyncCodeDateReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /api/blky/sync-code-date [post]
func SyncCodeDate(writer http.ResponseWriter, r *http.Request) {
	out := viewmodel.BaseHttpResponse{Code: 400}
	if req, err := utils.Bind[vo.SyncCodeDateReq](r); err != nil {
		out.Message = fmt.Sprintf("参数解析失败：%s", err.Error())
	} else {
		// 获取上传的文件
		file, _, err := r.FormFile("file")
		if err != nil {
			out.Message = err.Error()
			out2, _ := json.Marshal(out)
			writer.Write(out2)
			return
		}
		defer file.Close()

		uploadFile := utils.UploadFile{
			Name:   utils.GenerateNo("") + ".txt",
			Reader: file,
		}
		url, err := uploadFile.ToQiNiu()
		if err != nil {
			out.Message = err.Error()
			out2, _ := json.Marshal(out)
			writer.Write(out2)
			return
		}

		req.FileUrl = url
		err = new(services.BlkyCodeService).SyncCodeDate(req, r)
		if err != nil {
			out.Message = err.Error()
		} else {
			out.Message = "成功"
			out.Code = 200
		}
	}

	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// VerifySecurityCode 防伪码验证
// @Summary 深圳利都-防伪码验证
// @Tags 小程序接口-防伪查询
// @Accept json
// @Produce json
// @Param SecurityCodeVerifyReq body vo.SecurityCodeVerifyReq true "防伪码验证请求"
// @Success 200 {object} vo.SecurityCodeVerifyResponse
// @Failure 400 {object} vo.SecurityCodeVerifyResponse
// @Router /api/blky/verify-security-code [post]
func VerifySecurityCode(writer http.ResponseWriter, r *http.Request) {
	resp := vo.SecurityCodeVerifyResponse{
		Code: 400,
	}

	// 解析请求
	req, err := utils.Bind[vo.SecurityCodeVerifyReq](r)
	if err != nil {
		log.Errorf("解析防伪码验证请求失败: %v", err)
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	// 从请求中获取IP地址和地理位置
	clientIP := utils.GetClientIP(r)
	location := utils.GetLocationByIP(clientIP)
	req.ClientIP = clientIP
	req.Location = location

	// 尝试检查用户是否已登录（通过检查token）
	requestInfo := &jwtauth.RequestInfo{} // 创建默认空结构体
	hasUserInfo := false

	// 尝试从请求头获取token
	tokenStr := jwtauth.TokenFromHeader(r)
	if tokenStr != "" {
		// 有token，尝试直接验证token
		jwtauth.JwtInit()
		claims, err := jwtauth.VerifyToken(tokenStr, jwtauth.PublicKey)
		if err == nil && claims != nil {
			// token有效，手动构建RequestInfo
			requestInfo.OrgId = cast.ToInt(r.Header.Get("org_id"))

			if memberId, ok := claims["member_id"]; ok {
				requestInfo.MemberId = int(cast.ToFloat64(memberId))
				hasUserInfo = true
			}

			if userId, ok := claims["scrmid"]; ok {
				requestInfo.UserId = cast.ToString(userId)
			}

			if openId, ok := claims["openid"]; ok {
				requestInfo.OpenId = cast.ToString(openId)
			}

			if mobile, ok := claims["mobile"]; ok {
				requestInfo.Mobile = cast.ToString(mobile)
			}

			log.Infof("用户已登录，获取到用户信息: MemberId=%d, Mobile=%s",
				requestInfo.MemberId, utils.AddStar(requestInfo.Mobile))
		}
	}

	// 填充用户信息到请求
	if hasUserInfo {
		req.MemberId = requestInfo.MemberId
		req.OrgId = requestInfo.OrgId
		req.UserId = requestInfo.UserId
		req.OpenId = requestInfo.OpenId
		req.Mobile = requestInfo.Mobile
	} else {
		log.Info("用户未登录，将作为匿名用户处理")
	}

	// 调用服务
	service := &services.SecurityCodeService{}
	result, err := service.VerifySecurityCode(req)
	if err != nil {
		log.Errorf("验证防伪码失败: %v", err)
		resp.Message = fmt.Sprintf("%v", err)
		resp.Data = result
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	// 构建响应
	resp.Code = 200
	resp.Data = result

	// 防伪码不存在
	if !result.IsValid {
		resp.Message = "查询错误"
		resp.Code = 400
	} else {
		resp.Message = "验证成功"
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// 增加个接口，用于更新用户信息
// @Summary 深圳利都-更新用户信息
// @Tags 小程序接口-防伪查询
// @Accept json
// @Produce json
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /api/blky/update-user-info [post]
func UpdateUserInfo(writer http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Errorf("获取JWT信息失败: %v", err)
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	if jwtInfo.Openid == "" {
		resp.Message = "请先登录"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	req := vo.UpdateUserInfoReq{
		OpenId: jwtInfo.Openid,
		UserId: jwtInfo.Scrmid,
		Mobile: jwtInfo.Mobile,
	}
	// 调用服务
	service := &services.SecurityCodeService{}
	err = service.UpdateUserInfo(req)
	if err != nil {
		log.Errorf("更新用户信息失败: %v", err)
		resp.Message = err.Error()
	}

	// 构建响应
	resp.Code = 200
	resp.Message = "更新用户信息成功"
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}
