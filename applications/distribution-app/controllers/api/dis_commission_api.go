package api

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

/**
小程序端- 分销佣金管理
*/

// @Summary 获取店铺佣金 @fuma-v1.3 @blky-v1.0 @cls-v1.0.0
// @Tags 小程序接口-佣金管理
// @Accept  plain
// @Produce  json
// @Param GetDisCommissionInfoReq query vo.GetDisCommissionInfoReq true " "
// @Success 200 {object} vo.GetShopCommInfoResp
// @Failure 400 {object} vo.GetShopCommInfoResp
// @Router /api/dis/commission/shop-info [GET]
func GetShopCommInfo(writer http.ResponseWriter, request *http.Request) {

	var err error
	out := vo.GetShopCommInfoResp{}

	out.Code = 400
	param, err := utils.Bind[vo.GetDisCommissionInfoReq](request)
	logPrefix := fmt.Sprintf("店铺佣金-入参%s", utils.InterfaceToJSON(param))
	log.Info(logPrefix)
	if err != nil {
		log.Errorf("%s-解析参数失败-错误为%s", logPrefix, err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	if param.PageIndex < 1 {
		param.PageIndex = 1
	}
	if param.PageSize < 1 {
		param.PageSize = 10
	}

	param.OrgId = cast.ToInt(request.Header.Get("org_id"))
	// 从jwt里获取 分销员电话号码
	if jwtInfo, err := jwtauth.GetJwtInfo(request); err != nil {
		log.Errorf("%s-鉴权失败-错误为%s", logPrefix, err.Error())
		out.Message = "鉴权失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else {
		param.MemberId = jwtInfo.Memberid
	}

	server := services.DiscommissionService{}
	if out.Data, err = server.GetShopCommInfo(param); err != nil {
		log.Errorf("%s-获取店铺佣金失败-错误为%s", logPrefix, err.Error())
		out.Message = "获取店铺佣金失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)

}

// @Summary 佣金提现申请 @fuma-v1.3 @blky-v1.0 @cls-v1.0.0
// @Tags 小程序接口-佣金管理
// @Accept  json
// @Produce  json
// @Param DisCommissionApplyReq body vo.DisCommissionApplyReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /api/dis/commission/apply [POST]
func DisCommissionApply(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	param, err := utils.Bind[vo.DisCommissionApplyReq](request)
	if err != nil {
		log.Errorf("佣金提现申请-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	vErr := validate.Validate(param, "")
	if vErr != nil {
		log.Errorf("佣金提现申请-参数校验失败：err=%s", utils.InterfaceToJSON(vErr))
		out.Message = "佣金提现申请-参数校验失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 从jwt里获取 分销员电话号码
	if jwtInfo, err := jwtauth.GetJwtInfo(request); err != nil {
		log.Errorf("获取分销客户列表-鉴权失败-错误为%s", err.Error())
		out.Message = "鉴权失败" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else {
		param.MemberId = jwtInfo.Memberid
	}

	param.OrgId = cast.ToInt(request.Header.Get("org_id"))

	server := services.DiscommissionService{}
	if param.Amount <= 0 {
		out.Message = "提现金额不能小于0"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	if err = server.DisCommissionApply(param); err != nil {
		log.Errorf("佣金提现申请失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)

}

// @Summary 宠利扫老板佣金提现申请 @cls-v1.2
// @Tags 小程序接口-佣金管理
// @Accept  json
// @Produce  json
// @Param DisCommissionApplyReq body vo.DisCommissionApplyReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /api/dis/commission/cls-apply [POST]
func CLSCommissionApply(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := vo.DisCommissionApplyRes{}
	out.Code = 400
	param, err := utils.Bind[vo.DisCommissionApplyReq](request)
	if err != nil {
		log.Errorf("宠利扫老板佣金提现申请-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	vErr := validate.Validate(param, "")
	if vErr != nil {
		log.Errorf("宠利扫老板佣金提现申请-参数校验失败：err=%s", utils.InterfaceToJSON(vErr))
		out.Message = "宠利扫老板佣金提现申请-参数校验失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 从jwt里获取 分销员电话号码
	if jwtInfo, err := jwtauth.GetJwtInfo(request); err != nil {
		log.Errorf("宠利扫老板佣金提现申请-鉴权失败-错误为%s", err.Error())
		out.Message = "鉴权失败" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else {
		param.MemberId = jwtInfo.Memberid
	}

	param.OrgId = cast.ToInt(request.Header.Get("org_id"))

	server := services.DiscommissionService{}

	if out.OperateType, err = server.CLSCommissionApply(param); err != nil {
		log.Errorf("宠利扫老板佣金提现申请失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)

}
