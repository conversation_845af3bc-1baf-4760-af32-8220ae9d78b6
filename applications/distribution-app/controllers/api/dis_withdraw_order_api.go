package api

import (
	"eShop/infra/cache"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/distribution-service/enum"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"

	"github.com/spf13/cast"
)

// GetWithdrawOrderList 获取可提现订单列表
// @Summary 获取可提现订单列表 @blky-v1.0 @cls_v1.2
// @Tags 小程序接口-提现订单
// @Accept plain
// @Produce json
// @Param GetWithdrawOrderListReq query vo.GetWithdrawOrderListReq true " "
// @Success 200 {object} vo.GetWithdrawOrderListRes
// @Failure 400 {object} vo.GetWithdrawOrderListRes
// @Router /api/dis/order/withdraw/list [GET]
func GetWithdrawOrderList(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := vo.GetWithdrawOrderListRes{}
	out.Code = 400

	orgId := cast.ToInt(request.Header.Get("org_id"))
	if orgId == 0 {
		orgId = enum.OrgId
	}

	param, err := utils.Bind[vo.GetWithdrawOrderListReq](request)
	if err != nil {
		log.Errorf("获取可提现订单列表-解析参数失败: %s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 从jwt里获取
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Errorf("获取可提现订单列表-鉴权失败-错误为%s", err.Error())
		out.Message = "鉴权失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	param.OrgId = orgId
	param.MemberId = jwtInfo.Memberid
	param.ScrmUserId = jwtInfo.Scrmid
	server := services.DisWithdrawOrderService{}
	allWithdrawOrderItem := make([]vo.WithdrawOrderItem, 0)
	shopId := 0
	disid := 0
	orderSnSlice := make([]string, 0)
	if out.Data, allWithdrawOrderItem, shopId, disid, out.Total, out.Amount, out.MaxOrderId, err = server.GetWithdrawOrderList(param); err != nil {
		log.Errorf("获取可提现订单列表失败: %s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	for _, v := range allWithdrawOrderItem {
		orderSnSlice = append(orderSnSlice, v.OrderSn)
	}

	cacheK := fmt.Sprintf(cachekey.CLSShopWithdrawOrder, param.OrgId, shopId, disid)
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	mCache.Save(string(cache_source.EShop), cacheK, strings.Join(orderSnSlice, ","), time.Hour*1)

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// GetRefundOrderList 获取退款单列表
// @Summary 获取退款单列表 @blky-v1.0 @cls_v1.2
// @Tags 小程序接口-提现订单
// @Accept plain
// @Produce json
// @Param GetRefundOrderListReq query vo.GetRefundOrderListReq true " "
// @Success 200 {object} vo.GetRefundOrderListRes
// @Failure 400 {object} vo.GetRefundOrderListRes
// @Router /api/dis/order/withdraw/refund-list [GET]
func GetRefundOrderList(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := vo.GetRefundOrderListRes{}
	out.Code = 400

	orgId := cast.ToInt(request.Header.Get("org_id"))
	if orgId == 0 {
		orgId = enum.OrgId
	}

	param, err := utils.Bind[vo.GetRefundOrderListReq](request)
	if err != nil {
		log.Errorf("获取退款单列表-解析参数失败: %s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 从jwt里获取
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Errorf("获取退款单列表-鉴权失败-错误为%s", err.Error())
		out.Message = "鉴权失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	param.OrgId = orgId
	param.MemberId = jwtInfo.Memberid
	param.ScrmUserId = jwtInfo.Scrmid
	server := services.DisWithdrawOrderService{}
	shopId := 0
	disId := 0
	if out.Data, shopId, disId, err = server.GetRefundOrderList(param); err != nil {
		log.Errorf("获取退款单列表失败: %s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 总金额
	amount := 0
	orderSnSlice := make([]string, 0)
	for _, v := range out.Data {
		amount += v.Amount
		orderSnSlice = append(orderSnSlice, v.OrderSn)
	}

	cacheK := fmt.Sprintf(cachekey.CLSShopWithdrawRefundOrder, orgId, shopId, disId)
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	mCache.Save(string(cache_source.EShop), cacheK, strings.Join(orderSnSlice, ","), time.Hour*1)

	out.Amount = amount

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
