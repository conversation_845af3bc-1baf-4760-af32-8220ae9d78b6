package api

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	"eShop/services/distribution-service/services"
	"eShop/view-model"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// @Summary 验证短信验证码
// @Description
// @Tags C端共用API
// @Accept json
// @Produce json
// @Param BaseUserRequest body viewmodel.BaseUserRequest true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /eshop/api/comm/check-code [Post]
func CheckCode(writer http.ResponseWriter, request *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[viewmodel.BaseUserRequest](request)
	if err != nil {
		log.Error("验证短信，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("发送短信，参数解析失败：" + err.Error())
		return
	} else {
		if req.OrgId == 0 {
			req.OrgId = cast.ToInt(request.Header.Get("org_id"))
		}
		vErr := validate.Validate(req, "check")
		if vErr != nil {
			resp.Message = fmt.Sprintf("验证短信参数校验异常：%s", vErr)
			bytes, _ := json.Marshal(resp)
			writer.Write(bytes)
			return
		}
		err := utils.CheckCode(req.Mobile, req.Code, cast.ToString(req.OrgId))
		if err != nil {
			log.Error("验证短信，失败：err=" + err.Error())
			resp.Message = fmt.Sprintf(err.Error())
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

func CheckMobile(req viewmodel.BaseUserRequest, request *http.Request) string {

	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		return "获取登录信息失败"
	}
	server := services.DistributorManageService{}
	//用用户ID去判断手机号的重复和是否存在
	req.Id = jwtInfo.Memberid
	return server.DistributorMobileCheck(req)

	return ""
}

// @Summary 获取验证码
// @Description
// @Tags C端共用API
// @Accept json
// @Produce json
// @Param BaseUserRequest body viewmodel.BaseUserRequest true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /eshop/api/comm/code-send [Post]
func SendCode(writer http.ResponseWriter, request *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[viewmodel.BaseUserRequest](request)
	if req.OrgId == 0 {
		req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	}
	if err != nil {
		log.Error("参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("参数解析失败：" + err.Error())
	} else {

		errmes := CheckMobile(req, request)
		if errmes != "" {
			resp.Message = errmes
			bytes, _ := json.Marshal(resp)
			writer.Write(bytes)
			return
		}

		err := utils.SendCode(req)
		if err != nil {
			log.Error("发送短信，失败：err=" + err.Error())
			resp.Message = err.Error()
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}
