package api

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	"eShop/services/distribution-service/enum"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"

	"github.com/spf13/cast"
)

/**
小程序端 - 分销订单管理
*/

// @Summary 获取分销订单列表接口@fuma-v1.3 @blky-v1.0
// @Tags 小程序接口-分销订单
// @Accept  plain
// @Produce  json
// @Param GetDisOrderListReq query vo.GetDisOrderListApiReq true " "
// @Success 200 {object} vo.GetDisOrderListApiRes
// @Failure 400 {object} vo.GetDisOrderListApiRes
// @Router /api/dis/order/list [GET]
func GetDisOrderListApi(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := vo.GetDisOrderListApiRes{}
	out.Code = 400
	orgId := cast.ToInt(request.Header.Get("org_id"))
	if orgId == 0 {
		orgId = enum.OrgId
	}
	param, err := utils.Bind[vo.GetDisOrderListApiReq](request)
	if err != nil {
		log.Errorf("获取分销订单列表-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	if param.PageIndex < 1 {
		param.PageIndex = 1
	}
	if param.PageSize < 1 {
		param.PageSize = 10
	}
	param.OrgId = orgId
	param.SaasShopId = request.Header.Get("financialcode")
	// 从jwt里获取
	if jwtInfo, err := jwtauth.GetJwtInfo(request); err != nil {
		log.Errorf("获取分销订单列表-鉴权失败-错误为%s", err.Error())
		out.Message = "鉴权失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else {
		param.MemberId = jwtInfo.Memberid
	}

	server := services.DisOrderService{}

	if out.Data, out.Total, _, _, err = server.GetDisOrderListApi(param); err != nil {
		log.Errorf("获取分销订单列表失败-错误为%s", err.Error())
		out.Message = "获取分销订单列表失败" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	var wg sync.WaitGroup
	wg.Add(2)

	go func(in vo.GetDisOrderListApiReq) {
		defer wg.Done()

		in.StatsType = 1
		if _, _, out.TotalSale, _, err = server.GetDisOrderListApi(in); err != nil {
			log.Errorf("获取分销订单列表失败-错误为%s", err.Error())
		}

	}(param)
	go func(in vo.GetDisOrderListApiReq) {
		defer wg.Done()
		if in.OrgId != 4 {
			return
		}
		in.StatsType = 2
		if _, _, _, out.RealCommission, err = server.GetDisOrderListApi(in); err != nil {
			log.Errorf("获取分销订单列表失败-错误为%s", err.Error())
		}
	}(param)
	wg.Wait()
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)

}

// @Summary 分销订单进行结算（目前仅百林康源）@blky-v1.0
// @Tags 小程序接口-分销订单
// @Accept  json
// @Produce  json
// @Param OrderSetted body vo.OrderSettedReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /api/dis/order/sett [POST]
func OrderSetted(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	param, err := utils.Bind[vo.OrderSettedReq](request)
	if err != nil {
		log.Errorf("分销订单进行结算-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	logPrefix := fmt.Sprintf("分销订单进行结算,电商会员id=%d|order_no=%s", param.Data[0].MemberId, param.Data[0].OrderNo)
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(param))

	if vErr := validate.Validate(param, ""); vErr != nil {
		out.Message = fmt.Sprintf("%s,分销订单结算校验异常：%s", logPrefix, vErr)
		bytes, _ := json.Marshal(out)
		writer.Write(bytes)
		return
	}

	// 从jwt里获取
	if jwtInfo, err := jwtauth.GetJwtInfo(request); err != nil {
		log.Errorf("%s 分销订单进行结算-鉴权失败-错误为%s", logPrefix, err.Error())
		out.Message = "鉴权失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else {
		for _, v := range param.Data {
			if v.MemberId != jwtInfo.Memberid {
				log.Errorf("%s ,分销订单进行结算-非法操作-只能结算自己的", logPrefix)
				out.Message = "非法操作"
				out2, _ := json.Marshal(out)
				writer.Write(out2)
				return
			}
		}

	}

	server := services.DisOrderService{}

	if err = server.OrderSetted(param); err != nil {
		log.Errorf("%s 分销订单进行结算-错误为%s", logPrefix, err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
