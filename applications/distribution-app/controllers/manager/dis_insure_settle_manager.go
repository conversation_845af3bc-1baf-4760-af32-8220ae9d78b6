package manager

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// InsureSettlePage 分页查询
// @Summary 分页查询保险结算列表
// @Description 分页查询保险结算列表
// @Tags 后台接口-保险结算管理
// @Accept json
// @Produce json
// @Param req query distribution_vo.InsureSettlePageReq true "查询参数"
// @Success 200 {object} distribution_vo.InsureSettlePageResp "成功"
// @Failure 400 {object} distribution_vo.InsureSettlePageResp "失败"
// @Router /manager/insure-settle/page [get]
func InsureSettlePage(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.InsureSettlePageResp{
		BasePageHttpResponse: viewmodel.BasePageHttpResponse{
			Code: 400,
		},
	}

	req, err := utils.Bind[distribution_vo.InsureSettlePageReq](r)
	orgId := r.Header.Get("org_id")
	req.OrgId = cast.ToInt(orgId)
	if err != nil {
		log.Error("分页查询保险分销结算操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分页查询保险分销结算操作，参数解析失败：%s", err.Error())
	} else {
		service := services.DisInsureSettleService{}
		list, total, err := service.InsureSettlePage(req)
		if err != nil {
			log.Error("分页查询保险分销结算操作失败：err=" + err.Error())
			resp.Message = fmt.Sprintf("分页查询保险分销结算操作异常：%s", err.Error())
		} else {
			resp.BasePageHttpResponse.Code = 200
			resp.BasePageHttpResponse.Total = total
			resp.Data = list
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// InsureSettleExport 导出保险分销结算
// @Summary 导出保险分销结算
// @Description 导出保险分销结算接口
// @Tags 后台接口-保险分销结算
// @Accept  json
// @Produce  json
// @Param InsSettlePageReq body distribution_vo.InsureSettlePageReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/insure-settle/export [POST]
func InsureSettleExport(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.InsureSettlePageReq](r)
	orgId := r.Header.Get("org_id")
	req.OrgId = cast.ToInt(orgId)
	if err != nil {
		log.Error("导出保险分销结算，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("导出保险分销结算，参数解析失败：%s", err.Error())
	} else {
		s := common.TaskListService{}
		var task distribution_vo.TaskList
		par, _ := json.Marshal(req)
		task.OperationFileUrl = string(par)
		task.OrgId = req.OrgId
		task.TaskContent = 10
		err := s.CreatTask(r, task)
		if err != nil {
			log.Error("导出保险分销结算：err=" + err.Error())
			resp.Message = fmt.Sprintf("导出保险分销结算：%s", err.Error())
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}
