package manager

import (
	tasklist "eShop/infra/enum"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/spf13/cast"
)

/**
管理后台 - 分销员提现管理
*/

// @Summary 获取提现列表接口
// @Tags 后台接口-提现管理
// @Accept  plain
// @Produce  json
// @Param GetDisWithdrawListReq query distribution_vo.GetDisWithdrawListReq true " "
// @Success 200 {object} distribution_vo.GetDisWithdrawListRes
// @Failure 400 {object} distribution_vo.GetDisWithdrawListRes
// @Router /manager/dis/withdraw/list [GET]
func GetDisWithdrawList(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := distribution_vo.GetDisWithdrawListRes{}
	out.Code = 400
	orgId := cast.ToInt(request.Header.Get("org_id"))
	param, err := utils.Bind[distribution_vo.GetDisWithdrawListReq](request)
	param.OrgId = orgId
	if err != nil {
		log.Errorf("获取提现列表-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.DisWithdrawService{}
	if out.Data, out.Total, err = server.GetDisWithdrawList(param); err != nil {
		log.Errorf("获取提现列表失败-错误为%s", err.Error())
		out.Message = "获取提现列表失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 获取提现详情接口
// @Tags 后台接口-提现管理
// @Accept  plain
// @Produce  json
// @Param GetDisWithdrawDetailReq query distribution_vo.GetDisWithdrawDetailReq true " "
// @Success 200 {object} distribution_vo.GetDisWithdrawDetailRes
// @Failure 400 {object} distribution_vo.GetDisWithdrawDetailRes
// @Router /manager/dis/withdraw/detail [GET]
func GetDisWithdrawDetail(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := distribution_vo.GetDisWithdrawDetailRes{}
	out.Code = 400
	orgId := cast.ToInt(request.Header.Get("org_id"))
	param, err := utils.Bind[distribution_vo.GetDisWithdrawDetailReq](request)
	param.OrgId = orgId
	if err != nil {
		log.Errorf("获取提现详情-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.DisWithdrawService{}
	if out.Data, err = server.GetDisWithdrawDetail(param); err != nil {
		log.Errorf("获取提现详情失败-错误为%s", err.Error())
		out.Message = "获取提现详情失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 提现打款/拒绝  @blky-v1.0 @cls-v1.2
// @Tags 后台接口-提现管理
// @Accept  json
// @Produce  json
// @Param DisWithdrawCheckReq body distribution_vo.DisWithdrawCheckReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/dis/withdraw/check [POST]
func DisWithdrawCheck(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	param, err := utils.Bind[distribution_vo.DisWithdrawCheckReq](request)
	if err != nil {
		log.Errorf("提现打款/拒绝-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	vErr := validate.Validate(param, "")
	if vErr != nil {
		log.Errorf("提现打款/拒绝-参数校验失败：err=%s", utils.InterfaceToJSON(vErr))
		out.Message = "参数校验失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	orgId := request.Header.Get("org_id")
	param.OrgId = cast.ToInt(orgId)

	if param.CheckType != 1 && param.CheckType != 2 {
		out.Message = "无效的操作类型"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	if param.CheckType == 2 && strings.Trim(param.RejectReason, " ") == "" {
		out.Message = "拒绝原因不能为空"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	if param.CheckType == 1 {
		param.RejectReason = ""
	}

	server := services.DisWithdrawService{}
	if err = server.DisWithdrawCheck(param); err != nil {
		log.Errorf("提现打款/拒绝失败-错误为%s", err.Error())
		out.Message = "提现打款/拒绝失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 导出提现列表
// @Tags 后台接口-提现管理
// @Accept  json
// @Produce  json
// @Param GetDisWithdrawListReq body distribution_vo.GetDisWithdrawListReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/dis/withdraw/export [POST]
func DisWithdrawExport(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400

	param, err := utils.Bind[distribution_vo.GetDisWithdrawListReq](request)
	param.OrgId = cast.ToInt(request.Header.Get("org_id"))
	if err != nil {
		log.Errorf("导出提现列表参数解析失败-err=%s", err.Error())
		out.Message = fmt.Sprintf("导出提现列表，参数解析失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	s := common.TaskListService{}
	var task distribution_vo.TaskList
	par, _ := json.Marshal(param)
	task.OperationFileUrl = string(par)
	task.OrgId = cast.ToInt(request.Header.Get("org_id"))
	if param.ExportType == 0 {
		task.TaskContent = tasklist.TaskContentDisWithdrawExport
	} else {
		task.TaskContent = tasklist.TaskContentDisWithdrawOrderExport
	}
	logPrefix := fmt.Sprintf("导出提现列表-orgId=%d,taskContent=%d,入参=%s", task.OrgId, task.TaskContent, string(par))
	log.Infof("%s", logPrefix)
	err = s.CreatTask(request, task)
	if err != nil {
		log.Errorf("%s-导出提现列表失败：err=%s", logPrefix, err.Error())
		out.Message = fmt.Sprintf("导出提现列表失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200

	bytes, _ := json.Marshal(out)
	writer.Write(bytes)
}
