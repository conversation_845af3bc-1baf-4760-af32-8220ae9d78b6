package manager

import (
	enum2 "eShop/infra/enum"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	base_service "eShop/services/base-service"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

/**
管理后台 - 线下企业管理
*/

// @Summary 线下企业管理列表接口
// @Tags 后台接口-线下企业管理
// @Accept  plain
// @Produce  json
// @Param DisShopListReq query distribution_vo.DisShopListReq true " "
// @Success 200 {object} distribution_vo.DisShopListRes
// @Failure 400 {object} distribution_vo.DisShopListRes
// @Router /manager/dis/shop/list [GET]
func GetShopList(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := distribution_vo.DisShopListRes{}
	out.Code = 400
	orgId := cast.ToInt(request.Header.Get("org_id"))
	param, err := utils.Bind[distribution_vo.DisShopListReq](request)
	param.OrgId = orgId
	if err != nil {
		log.Errorf("获取线下企业管理列表-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.DisShopService{}
	if out.Data, out.Total, err = server.DisShopList(param); err != nil {
		log.Errorf("获取线下企业管理列表失败-错误为%s", err.Error())
		out.Message = "获取线下企业管理列表失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// 导出线下企业管理列表
// @Summary 导出线下企业管理列表
// @Tags 后台接口-线下企业管理
// @Accept  json
// @Produce  json
// @Param DisShopListReq query distribution_vo.DisShopListReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/dis/shop/export [Post]
func ExportShopList(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}
	req, err := utils.Bind[distribution_vo.DisShopListReq](r)
	req.OrgId = cast.ToInt(r.Header.Get("org_id"))
	if err != nil {
		log.Error("导出线下企业管理列表，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("导出线下企业管理列表，参数解析失败：%s", err.Error())
	} else {
		s := common.TaskListService{}
		var task distribution_vo.TaskList
		par, _ := json.Marshal(req)
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(r.Header.Get("org_id"))
		task.TaskContent = enum2.TaskContentShopExport
		err := s.CreatTask(r, task)
		if err != nil {
			log.Error("导出分销员：err=" + err.Error())
			resp.Message = fmt.Sprintf("导出分销员：%s", err.Error())
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// @Summary 企业查询接口
// @Tags 后台接口-线下企业管理
// @Accept  json
// @Produce  json
// @Param DisShopReq body distribution_vo.EnterpriseReq true " "
// @Success 200 {object} distribution_vo.EnterpriseRes
// @Failure 400 {object} distribution_vo.EnterpriseRes
// @Router /manager/dis/shop/get [GET]
func GetShop(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := distribution_vo.EnterpriseRes{}
	out.Code = 400
	param, err := utils.Bind[distribution_vo.EnterpriseReq](request)
	orgId := cast.ToInt(request.Header.Get("org_id"))
	if err != nil {
		log.Errorf("企业查询失败-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.DisShopService{}
	if orgId == enum.BLKYOrgId {
		out.Data, out.Total, err = server.GetBLKYDisShop(param)
	} else {
		out.Data, out.Total, err = server.GetDisShop(param)
	}
	if err != nil {
		log.Errorf("企业查询失败-错误为%s", err.Error())
		out.Message = "企业查询失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// 分销员角色更换
// @Tags 后台接口-线下企业管理
// @Accept  json
// @Produce  json
// @Param DisRoleChangeReq body distribution_vo.DisRoleChangeReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/dis/shop/role-change [POST]
func DistributorRoleChange(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.DisRoleChangeReq](r)
	req.OrgId = cast.ToInt(r.Header.Get("org_id"))
	if err != nil {
		log.Error("分销员角色更换，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分销员角色更换，参数解析失败：%s", err.Error())
	} else {
		vErr := validate.Validate(req, "")
		if vErr != nil {
			resp.Message = fmt.Sprintf("分销员角色更换参数校验异常：%s", vErr)
		} else {
			service := services.DistributorManageService{}
			description := ""
			if req.OrgId == enum.BLKYOrgId {
				description, err = service.BLKYDistributorRoleChange(req)
			} else {
				description, err = service.DistributorRoleChange(req)
			}
			if err != nil {
				log.Error("分销员角色更换失败：err=" + err.Error())
				resp.Message = fmt.Sprintf("分销员角色更换异常：%s", err.Error())
			} else {
				resp.Code = 200
				//添加操作日志
				go func(r *http.Request, enterpriseId string) {
					operateLogService := base_service.OperateLogService{}
					operateLogService.Add(r, distribution_vo.OperateLogReq{
						ModuleType:  base_service.ModuleShop,
						Type:        base_service.ShopRoleChange,
						FromId:      enterpriseId,
						Description: description,
					})
				}(r, req.EnterpriseId)
			}
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// SyncEsShopGoods
// @Summary 同步店铺es数据
// @Tags 后台接口-线下企业管理
// @Accept  json
// @Produce  json
// @Param DisRoleChangeReq body distribution_vo.DisShopReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/dis/shop/sync_goods [POST]
func SyncEsShopGoods(writer http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.BlkyCodeListRes{}
	resp.Code = 400
	req, err := utils.Bind[distribution_vo.DisShopReq](r)
	if err != nil {
		resp.Message = fmt.Sprintf("参数解析失败：%s", err.Error())
	}

	service := services.DisShopService{}
	if err := service.SyncEsShopGoods(req.ShopId); err != nil {
		log.Error("同步店铺es数据失败：err=" + err.Error())
		resp.Message = fmt.Sprintf("同步店铺es数据失败：%s", err.Error())
	} else {
		resp.Code = 200
	}
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}
