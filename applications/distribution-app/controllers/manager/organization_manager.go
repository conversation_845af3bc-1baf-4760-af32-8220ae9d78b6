package manager

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	"eShop/services/base-service"
	"eShop/view-model"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"net/http"
)

// OrgPage 分页查询
// @Summary 分页查询
// @Description 组织列表查询接口
// @Tags 后台接口-组织管理
// @Accept  json
// @Produce  json
// @Param OrgPageReq body vo.OrgPageReq true " "
// @Success 200 {object} vo.OrgPageResp
// @Failure 400 {object} vo.OrgPageResp
// @Router /manager/organization/page-list [GET]
func OrgPage(w http.ResponseWriter, r *http.Request) {
	resp := vo.OrgPageResp{
		BasePageHttpResponse: viewmodel.BasePageHttpResponse{
			Code: 400,
		},
	}

	req, err := utils.Bind[vo.OrgPageReq](r)
	if err != nil {
		log.Error("分页查询组织操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分页查询组织操作，参数解析失败：%s", err.Error())
	} else {
		service := base_service.OrgService{}
		list, total, err := service.OrgPage(req)
		if err != nil {
			log.Error("分页查询组织操作失败：err=" + err.Error())
			resp.Message = fmt.Sprintf("分页查询组织操作异常：%s", err.Error())
		} else {
			resp.BasePageHttpResponse.Code = 200
			resp.BasePageHttpResponse.Total = total
			resp.Data = list
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// OrgAdd 新增组织
// @Summary 新增组织
// @Description 新增组织接口
// @Tags 后台接口-组织管理
// @Accept  json
// @Produce  json
// @Param OrgPageReq body vo.OrgAddReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/organization/add [POST]
func OrgAdd(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[vo.OrgAddReq](r)
	if err != nil {
		log.Error("新增组织操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("新增组织操作，参数解析失败：%s", err.Error())
	} else {
		vErr := validate.Validate(req, "add")
		if vErr != nil {
			resp.Message = fmt.Sprintf("新增组织操作参数校验异常：%s", vErr)
		} else {
			service := base_service.OrgService{}
			_, err = service.OrgAdd(req)
			if err != nil {
				log.Error("新增组织失败：err=", err.Error())
				resp.Message = fmt.Sprintf("新增组织操作异常：%s", err.Error())
			} else {
				resp.Code = 200
			}
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// OrgEdit 编辑组织
// @Summary 编辑组织
// @Description 编辑组织接口
// @Tags 后台接口-组织管理
// @Accept  json
// @Produce  json
// @Param OrgEditReq body vo.OrgEditReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/organization/edit [POST]
func OrgEdit(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[vo.OrgEditReq](r)
	if err != nil {
		log.Error("编辑组织操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("编辑组织操作，参数解析失败：%s", err.Error())
	} else {
		vErr := validate.Validate(req, "upd")
		if vErr != nil {
			resp.Message = fmt.Sprintf("编辑组织操作参数校验异常：%s", vErr)
		} else {
			service := base_service.OrgService{}
			err = service.OrgEdit(req)
			if err != nil {
				log.Error("编辑组织失败：err=" + err.Error())
				resp.Message = fmt.Sprintf("编辑组织操作异常：%s", err.Error())
			} else {
				resp.Code = 200
			}
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// OrgIsUse 启用、停用组织
// @Summary 启用、停用组织
// @Description 停用组织接口
// @Tags 后台接口-组织管理
// @Accept  json
// @Produce  json
// @Param UnbindReq body vo.OrgIsUseReq true " "
// @Param id query int true "组织id"
// @Param is_use query int true "组织id:0：未启用 1：启用(默认-1)"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/organization/is-use [POST]
func OrgIsUse(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[vo.OrgIsUseReq](r)
	if err != nil {
		log.Error("启用、停用组织操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("启用、停用组织操作，参数解析失败：%s", err.Error())
	} else {
		vErr := validate.Validate(req, "")
		if vErr != nil {
			resp.Message = fmt.Sprintf("启用、停用组织操作参数校验异常：%s", vErr)
		} else {
			service := base_service.OrgService{}
			err = service.OrgIsUse(req)
			if err != nil {
				log.Error("启用、停用组织失败：err=" + err.Error())
				resp.Message = fmt.Sprintf("启用、停用组织操作异常：%s", err.Error())
			} else {
				resp.Code = 200
			}
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// OrgDetail 组织详情
// @Summary 组织详情
// @Description 组织详情查询接口
// @Tags 后台接口-组织管理
// @Accept  json
// @Produce  json
// @Param id query int true "组织id"
// @Success 200 {object} vo.OrgDetailResp
// @Failure 400 {object} vo.OrgDetailResp
// @Router /manager/organization/detail [GET]
func OrgDetail(w http.ResponseWriter, r *http.Request) {
	resp := vo.OrgDetailResp{
		BaseHttpResponse: viewmodel.BaseHttpResponse{
			Code: 400,
		},
	}

	idStr := r.URL.Query().Get("id")
	if len(idStr) == 0 {
		log.Error("组织详情查询操作，参数解析失败：id为空")
		resp.Message = "组织详情查询操作，参数解析失败：id为空"
	} else {
		service := base_service.OrgService{}
		data, err := service.OrgDetail(cast.ToInt(idStr))
		if err != nil {
			log.Error("组织详情查询操作失败：err=" + err.Error())
			resp.Message = fmt.Sprintf("组织详情查询操作异常：%s", err.Error())
		} else {
			resp.BaseHttpResponse.Code = 200
			resp.Data = data
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}
