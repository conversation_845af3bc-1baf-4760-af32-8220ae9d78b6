package manager

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	orderService "eShop/services/order-service/services"
	viewmodel "eShop/view-model"
	vo "eShop/view-model/distribution-vo"
	order_vo "eShop/view-model/order-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// ClsVerifyPage 防伪码查询
// @Summary 防伪码查询
// @Description 防伪码查询
// @Tags 后台接口-极宠家
// @Accept json
// @Produce json
// @Param ClsVerifyPageReq body vo.ClsVerifyPageReq true "防伪码查询请求"
// @Success 200 {object} vo.ClsVerifyPageResp
// @Failure 400 {object} vo.ClsVerifyPageResp
// @Router /manager/cls/verify/list [GET]
func ClsVerifyPage(w http.ResponseWriter, r *http.Request) {
	resp := vo.ClsVerifyPageResp{
		Code: 400,
	}

	req, err := utils.Bind[vo.ClsVerifyPageReq](r)
	if err != nil {
		log.Error("防伪码查询，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("防伪码查询，参数解析失败：%s", err.Error())
	} else {
		vErr := validate.Validate(req, "")
		if vErr != nil {
			resp.Message = fmt.Sprintf("防伪码查询，参数校验异常：%s", vErr)
		} else {
			// 使用新的服务实例化方式
			service := services.ClsVerifyService{}
			list, total, err := service.GetClsVerifyPage(req)
			if err != nil {
				log.Error("防伪码查询失败：err=" + err.Error())
				resp.Message = fmt.Sprintf("防伪码查询异常：%s", err.Error())
			} else {
				resp.Code = 200
				resp.Total = int(total)
				resp.Data = list
			}
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// 防伪码导出
// @Summary 防伪码导出
// @Tags 后台接口-极宠家
// @Accept  plain
// @Produce  json
// @Param ClsVerifyPageReq query distribution_vo.ClsVerifyPageReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/cls/verify/export [GET]
func ClsVerifyExport(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}
	req, err := utils.Bind[vo.ClsVerifyPageReq](r)
	if err != nil {
		resp.Message = fmt.Sprintf("导出防伪码数据，参数解析失败：%s", err.Error())
	} else {
		s := common.TaskListService{}
		var task vo.TaskList
		par, _ := json.Marshal(req)
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(r.Header.Get("org_id"))
		task.TaskContent = 102
		err = s.CreatTask(r, task)
		if err != nil {
			resp.Message = fmt.Sprintf("导出防伪码数据：%s", err.Error())
		} else {
			resp.Code = 200
		}
	}
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// GetUserList 极宠家用户列表
// @Summary 极宠家用户列表
// @Tags 后台接口-极宠家
// @Description 获取极宠家(org_id=2)的用户列表
// @Accept json
// @Produce json
// @Param UserPageReq query distribution_vo.UserPageReq true "分页查询参数"
// @Success 200 {object} distribution_vo.UserPageResp
// @Failure 400 {object} distribution_vo.UserPageResp
// @Router /manager/cls/user/list [GET]
func GetUserList(w http.ResponseWriter, r *http.Request) {
	resp := vo.UserPageResp{
		Code: 400,
		Data: make([]vo.VoUserMemberView, 0),
	}

	// 解析请求参数
	req, err := utils.Bind[vo.UserPageReq](r)
	if err != nil {
		log.Error("极宠家用户列表查询，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("参数解析失败：%s", err.Error())
	} else {
		// 使用服务实例
		service := services.NewUpetUserService()
		req.OrgId = cast.ToInt(r.Header.Get("org_id"))
		list, total, err := service.GetUserList(req)
		if err != nil {
			log.Error("极宠家用户列表查询失败：err=" + err.Error())
			resp.Message = fmt.Sprintf("查询异常：%s", err.Error())
		} else {
			resp.Code = 200
			resp.Total = total
			resp.Data = list
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// ExportUserList 极宠家用户导出
// @Summary 极宠家用户导出
// @Tags 后台接口-极宠家
// @Accept plain
// @Produce json
// @Param UserPageReq query distribution_vo.UserPageReq true "导出参数"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/cls/user/export [GET]
func ExportUserList(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[vo.UserPageReq](r)
	if err != nil {
		resp.Message = fmt.Sprintf("导出客户数据，参数解析失败：%s", err.Error())
	} else {
		// 创建任务
		s := common.TaskListService{}
		var task vo.TaskList
		req.OrgId = cast.ToInt(r.Header.Get("org_id"))
		par, _ := json.Marshal(req)
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(r.Header.Get("org_id"))
		task.TaskContent = 103
		err = s.CreatTask(r, task)
		if err != nil {
			resp.Message = fmt.Sprintf("导出防伪码数据：%s", err.Error())
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// VoucherList 优惠券列表
// @Summary 优惠券列表
// @Description 获取优惠券列表
// @Tags 后台接口-极宠家
// @Accept json
// @Produce json
// @Param VoucherPageReq query vo.VoucherPageReq true "分页查询参数"
// @Success 200 {object} vo.VoucherPageResp
// @Failure 400 {object} vo.VoucherPageResp
// @Router /manager/cls/voucher/list [GET]
func VoucherList(w http.ResponseWriter, r *http.Request) {
	resp := vo.VoucherPageResp{
		Code: 400,
		Data: make([]vo.VoucherItem, 0),
	}

	// 解析请求参数
	req, err := utils.Bind[vo.VoucherPageReq](r)
	if err != nil {
		log.Error("优惠券列表查询，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("参数解析失败：%s", err.Error())
	} else {
		req.OrgId = cast.ToInt(r.Header.Get("org_id"))
		// 使用服务实例
		service := services.NewVoucherService()
		list, total, err := service.GetVoucherList(req)
		if err != nil {
			log.Error("优惠券列表查询失败：err=" + err.Error())
			resp.Message = fmt.Sprintf("查询异常：%s", err.Error())
		} else {
			resp.Code = 200
			resp.Total = total
			resp.Data = list
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// OrderList 订单列表
// @Summary 订单列表
// @Description 获取订单列表
// @Tags 后台接口-极宠家
// @Accept json
// @Produce json
// @Param OrderListReq query order_vo.OrderListReq true "订单列表请求"
// @Success 200 {object} order_vo.OrderListResp
// @Failure 400 {object} order_vo.OrderListResp
// @Router /manager/cls/order/list [GET]
func OrderList(w http.ResponseWriter, r *http.Request) {
	resp := order_vo.OrderListResp{
		Code: 400,
	}

	// 解析请求参数
	req, err := utils.Bind[order_vo.OrderListReq](r)
	if err != nil {
		log.Error("订单列表查询，参数解析失败：err=", err.Error())
		resp.Message = "参数解析失败"
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 参数默认值处理
	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}

	req.OrgId = cast.ToInt(r.Header.Get("org_id"))

	// 使用服务实例
	service := orderService.NewOrderService()
	list, total, err := service.GetOrderList(req)
	if err != nil {
		log.Error("订单列表查询失败：err=", err.Error())
		resp.Message = "查询失败"
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	resp.Code = 200
	resp.Total = total
	resp.Data = list
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// GetUserDetail 用户详情
// @Summary 用户详情
// @Description 获取用户详细信息
// @Tags 后台接口-极宠家
// @Accept json
// @Produce json
// @Param scrm_user_id query string true "用户ID"
// @Success 200 {object} vo.UserDetailResp
// @Failure 400 {object} vo.UserDetailResp
// @Router /manager/cls/user/detail [GET]
func GetUserDetail(w http.ResponseWriter, r *http.Request) {
	resp := vo.UserDetailResp{
		Code: 400,
		Data: &vo.UserDetail{},
	}

	// 解析请求参数
	req, err := utils.Bind[vo.UserDetailReq](r)
	if err != nil {
		log.Error("用户详情查询，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	req.OrgId = cast.ToInt(r.Header.Get("org_id"))

	// 使用服务实例
	service := services.NewUpetUserService()
	detail, err := service.GetUserDetail(req)
	if err != nil {
		log.Error("用户详情查询失败：err=" + err.Error())
		resp.Message = fmt.Sprintf("用户详情查询失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	resp.Code = 200
	resp.Data = detail
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// GetUserPhone 获取用户手机号
// @Summary 获取用户手机号
// @Description 根据会员ID获取用户手机号
// @Tags 后台接口-极宠家
// @Accept json
// @Produce json
// @Param member_id query string true "会员ID"
// @Success 200 {object} vo.UserPhoneResp
// @Failure 400 {object} vo.UserPhoneResp
// @Router /manager/cls/user/phone [GET]
func GetUserPhone(w http.ResponseWriter, r *http.Request) {
	resp := vo.UserPhoneResp{
		Code: 400,
		Data: vo.UserPhoneData{},
	}

	// 解析请求参数
	req, err := utils.Bind[vo.UserPhoneReq](r)
	if err != nil {
		log.Error("获取用户手机号，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	// 使用服务实例
	service := services.NewUpetUserService()
	phone, err := service.GetUserPhone(req)
	if err != nil {
		log.Error("获取用户手机号失败：err=" + err.Error())
		resp.Message =  err.Error()
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	resp.Code = 200
	resp.Data = phone
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}
