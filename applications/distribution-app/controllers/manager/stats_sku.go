package manager

import (
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"

	"eShop/infra/log"
	"eShop/infra/utils"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// StatsSkuList
// @Summary 商品数据列表查询
// @Tags 后台接口-数据统计
// @Accept  plain
// @Produce  json
// @Param StatsSkuCityDailyPageReq query distribution_vo.StatsSkuCityDailyPageReq true " "
// @Success 200 {object} distribution_vo.StatsSkuCityDailyPageRes
// @Failure 400 {object} distribution_vo.StatsSkuCityDailyPageRes
// @Router /manager/stats/sku/list [GET]
func StatsSkuList(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := distribution_vo.StatsSkuCityDailyPageRes{}
	out.Code = 400
	param, err := utils.Bind[distribution_vo.StatsSkuCityDailyPageReq](request)
	if err != nil {
		log.Errorf("获取商品数据列表-报表解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	service := services.StatsSkuCityDailyService{}
	if out.Data, out.Total, err = service.GetStatsSkuCityDailyByDate(param); err != nil {
		log.Errorf("获取商品数据列表-报表失败-错误为%s", err.Error())
		out.Message = "获取商品数据列表-报表概览失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	bytes, _ := json.Marshal(out)
	writer.Write(bytes)
}

// StatsSkuExport
// @Summary 商品数据导出
// @Tags 后台接口-数据统计
// @Accept  plain
// @Produce  json
// @Param StatsSkuCityDailyPageReq query distribution_vo.StatsSkuCityDailyPageReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/stats/sku/export [GET]
func StatsSkuExport(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}
	req, err := utils.Bind[distribution_vo.StatsSkuCityDailyPageReq](r)
	if err != nil {
		resp.Message = fmt.Sprintf("导出商品数据，参数解析失败：%s", err.Error())
	} else if req.Type != 15 && req.Type != 16 {
		resp.Message = "导出商品数据，参数类型错误"
	} else {
		s := common.TaskListService{}
		var task distribution_vo.TaskList
		par, _ := json.Marshal(req)
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(r.Header.Get("org_id"))
		task.TaskContent = req.Type
		err = s.CreatTask(r, task)
		if err != nil {
			resp.Message = fmt.Sprintf("导出商品数据：%s", err.Error())
		} else {
			resp.Code = 200
		}
	}
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// StatsSkuCityList
// @Summary 商品区域列表查询
// @Tags 后台接口-数据统计
// @Accept  plain
// @Produce  json
// @Param StatsSkuCityDailyPageReq query distribution_vo.StatsSkuCityDailyPageReq true " "
// @Success 200 {object} distribution_vo.StatsSkuCityDailyPageRes
// @Failure 400 {object} distribution_vo.StatsSkuCityDailyPageRes
// @Router /manager/stats/sku/city-list [GET]
func StatsSkuCityList(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := distribution_vo.StatsSkuCityDailyPageRes{}
	out.Code = 400
	param, err := utils.Bind[distribution_vo.StatsSkuCityDailyPageReq](request)
	if err != nil {
		log.Errorf("获取商品区域列表-报表解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)

		return
	}
	service := services.StatsSkuCityDailyService{}
	if out.Data, out.Total, err = service.GetStatsSkuCityDailyBySkuid(param); err != nil {
		log.Errorf("获取商品区域列表-报表失败-错误为%s", err.Error())
		out.Message = "获取商品区域列表-报表概览失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	bytes, _ := json.Marshal(out)
	writer.Write(bytes)
}
