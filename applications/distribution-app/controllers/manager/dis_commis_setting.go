package manager

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	base_service "eShop/services/base-service"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// GetDisCommisSettingPageList 佣金设置列表 @cls-v1.0.0
// @Summary 佣金设置列表
// @Description 佣金设置列表接口
// @Tags 后台接口-佣金设置
// @Accept  json
// @Produce  json
// @Param GetDisCommisSettingPageListReq body distribution_vo.GetDisCommisSettingPageListReq true " "
// @Success 200 {object} distribution_vo.GetDisCommisSettingPageListResp
// @Failure 400 {object} distribution_vo.GetDisCommisSettingPageListResp
// @Router /manager/dis/commis-setting/page-list [GET]
func GetDisCommisSettingPageList(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.GetDisCommisSettingPageListResp{
		BasePageHttpResponse: viewmodel.BasePageHttpResponse{
			Code: 400,
		},
	}

	req, err := utils.Bind[distribution_vo.GetDisCommisSettingPageListReq](r)
	orgId := r.Header.Get("org_id")
	req.OrgId = cast.ToInt(orgId)
	if err != nil {
		log.Error("佣金设置列表查询，参数解析失败：err=", err.Error())
		resp.BasePageHttpResponse.Message = err.Error()
	} else {
		service := services.DisCommisSettingService{}
		list, total, err := service.GetDisCommisSettingPageList(req)
		if err != nil {
			log.Error("佣金设置列表查询失败：err=", err.Error())
			resp.BasePageHttpResponse.Message = err.Error()
		} else {
			resp.BasePageHttpResponse.Code = 200
			resp.BasePageHttpResponse.Total = cast.ToInt(total)
			resp.Data = list
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// AddDisCommisSetting 新增客户渠道佣金设置 @cls-v1.0.0
// @Summary 新增客户渠道佣金设置
// @Description 新增客户渠道佣金设置接口
// @Tags 后台接口-佣金设置
// @Accept  json
// @Produce  json
// @Param AddDisCommisSettingReq body distribution_vo.AddDisCommisSettingReq true " "
// @Success 200 {object} distribution_vo.AddDisCommisSettingResp
// @Failure 400 {object} distribution_vo.AddDisCommisSettingResp
// @Router /manager/dis/commis-setting/add [POST]
func AddDisCommisSetting(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.AddDisCommisSettingResp{
		BaseHttpResponse: viewmodel.BaseHttpResponse{
			Code: 400,
		},
	}

	req, err := utils.Bind[distribution_vo.AddDisCommisSettingReq](r)
	orgId := r.Header.Get("org_id")
	req.OrgId = cast.ToInt(orgId)

	if err != nil {
		log.Error("新增客户渠道佣金设置，参数解析失败：err=", err.Error())
		resp.Message = err.Error()
	} else {
		if req.IsDefault != 1 && (req.DisCommisRate < 0 || req.DisCommisRate > 100) {
			log.Error("新增客户渠道佣金设置，佣金比例非法")
			resp.Message = "佣金比例校验失败，请输入0~100的数字"
			bytes, _ := json.Marshal(resp)
			w.Write(bytes)
			return
		}
		service := services.DisCommisSettingService{}
		data, err := service.AddDisCommisSetting(req)
		if err != nil {
			log.Error("新增客户渠道佣金设置失败：err=", err.Error())
			resp.Message = err.Error()
		} else {
			resp.Code = 200
			resp.Data = data
			go func(r *http.Request, data distribution_vo.AddDisCommisSettingData) {
				desc := fmt.Sprintf("%s佣金比例设置为%f%%", data.ChannelPath, data.DisCommisRate)
				if data.IsDefault == 1 {
					desc = fmt.Sprintf("%s佣金比例设置为默认佣金比例", data.ChannelPath)
				}
				new(base_service.OperateLogService).Add(r, distribution_vo.OperateLogReq{
					ModuleType:  base_service.ModuleDisCommisSetting,
					Type:        base_service.DisCommisSettingCreate,
					FromId:      cast.ToString(data.DisCommisSettingId),
					Description: desc,
				})
			}(r, resp.Data)
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// EditDisCommisSetting 编辑佣金设置 @cls-v1.0.0
// @Summary 编辑佣金设置
// @Description 编辑佣金设置接口
// @Tags 后台接口-佣金设置
// @Accept  json
// @Produce  json
// @Param EditDisCommisSettingReq body distribution_vo.EditDisCommisSettingReq true " "
// @Success 200 {object} distribution_vo.AddDisCommisSettingResp
// @Failure 400 {object} distribution_vo.AddDisCommisSettingResp
// @Router /manager/dis/commis-setting/edit [POST]
func EditDisCommisSetting(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.EditDisCommisSettingResp{
		BaseHttpResponse: viewmodel.BaseHttpResponse{
			Code: 400,
		},
	}

	req, err := utils.Bind[distribution_vo.EditDisCommisSettingReq](r)
	orgId := r.Header.Get("org_id")
	req.OrgId = cast.ToInt(orgId)
	if err != nil {
		log.Error("编辑佣金设置，参数解析失败：err=", err.Error())
		resp.Message = err.Error()
	} else {
		if req.IsDefault != 1 && (req.DisCommisRate < 0 || req.DisCommisRate > 100) {
			log.Error("编辑佣金设置，佣金比例非法")
			resp.Message = "佣金比例校验失败，请输入0~100的数字"
			bytes, _ := json.Marshal(resp)
			w.Write(bytes)
			return
		}
		service := services.DisCommisSettingService{}
		data, err := service.EditDisCommisSetting(req)
		if err != nil {
			log.Error("编辑佣金设置失败：err=", err.Error())
			resp.Message = err.Error()
		} else {
			resp.Code = 200
			resp.Data = data
			go func(r *http.Request, data distribution_vo.EditDisCommisSettingData) {
				desc, old, newRate := "", "", ""
				if data.OldIsDefault == 1 {
					old = "默认佣金比例"
				} else {
					old = fmt.Sprintf("%.2f%%", data.OldDisCommisRate)
				}
				if data.IsDefault == 1 {
					newRate = "默认佣金比例"
				} else {
					newRate = fmt.Sprintf("%.2f%%", data.DisCommisRate)
				}
				desc = fmt.Sprintf("%s佣金比例从%s变更为%s", data.ChannelPath, old, newRate)
				new(base_service.OperateLogService).Add(r, distribution_vo.OperateLogReq{
					ModuleType:  base_service.ModuleDisCommisSetting,
					Type:        base_service.DisCommisSettingEdit,
					FromId:      cast.ToString(data.DisCommisSettingId),
					Description: desc,
				})
			}(r, resp.Data)
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// DeleteDisCommisSetting 删除佣金设置 @cls-v1.0.0
// @Summary 删除佣金设置
// @Description 删除佣金设置接口
// @Tags 后台接口-佣金设置
// @Accept  json
// @Produce  json
// @Param DeleteDisCommisSettingReq body distribution_vo.DeleteDisCommisSettingReq true " "
// @Success 200 {object} distribution_vo.DeleteDisCommisSettingResp
// @Failure 400 {object} distribution_vo.DeleteDisCommisSettingResp
// @Router /manager/dis/commis-setting/delete [POST]
func DeleteDisCommisSetting(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.DeleteDisCommisSettingResp{
		BaseHttpResponse: viewmodel.BaseHttpResponse{
			Code: 400,
		},
	}

	req, err := utils.Bind[distribution_vo.DeleteDisCommisSettingReq](r)
	orgId := r.Header.Get("org_id")
	req.OrgId = cast.ToInt(orgId)
	if err != nil {
		log.Error("删除佣金设置，参数解析失败：err=", err.Error())
		resp.Message = err.Error()
	} else {
		service := services.DisCommisSettingService{}
		channelPath, err := service.DeleteDisCommisSetting(req)
		if err != nil {
			log.Error("删除佣金设置失败：err=", err.Error())
			resp.Message = err.Error()
		} else {
			resp.Code = 200

			go func(r *http.Request, channelPath string) {
				new(base_service.OperateLogService).Add(r, distribution_vo.OperateLogReq{
					ModuleType:  base_service.ModuleDisCommisSetting,
					Type:        base_service.DisCommisSettingDelete,
					FromId:      cast.ToString(req.DisCommisSettingId),
					Description: fmt.Sprintf("删除渠道佣金设置:%s", channelPath),
				})
			}(r, channelPath)
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// CustomerChannelGlobalSetting 客户渠道佣金设置 @cls-v1.0.0
// @Summary 客户渠道佣金设置
// @Description 客户渠道佣金设置接口
// @Tags 后台接口-佣金设置
// @Accept  json
// @Produce  json
// @Param CustomerChannelGlobalSettingReq body distribution_vo.CustomerChannelGlobalSettingReq true " "
// @Success 200 {object} distribution_vo.CustomerChannelGlobalSettingResp
// @Failure 400 {object} distribution_vo.CustomerChannelGlobalSettingResp
// @Router /manager/dis/commis-setting/global-setting [POST]
func CustomerChannelGlobalSetting(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.CustomerChannelGlobalSettingResp{
		BaseHttpResponse: viewmodel.BaseHttpResponse{
			Code: 400,
		},
	}

	req, err := utils.Bind[distribution_vo.CustomerChannelGlobalSettingReq](r)
	req.OrgId = cast.ToInt(r.Header.Get("org_id"))
	if err != nil {
		log.Error("渠道佣金设置，参数解析失败：err=", err.Error())
		resp.Message = err.Error()
	} else {
		if req.DisCommisRate < 0 || req.DisCommisRate > 100 {
			log.Error("渠道佣金设置，佣金比例非法")
			resp.Message = "佣金比例校验失败，请输入0~100的数字"
			bytes, _ := json.Marshal(resp)
			w.Write(bytes)
			return
		}
		server := services.DisCommisSettingService{}
		oldRate, newRate, err := server.CustomerChannelGlobalSetting(req.OrgId, req.DisCommisRate)
		if err != nil {
			log.Error("渠道佣金设置失败：err=", err.Error())
			resp.Message = err.Error()
		} else {
			resp.Code = 200
			go func(r *http.Request, oldRate, newRate float64) {
				new(base_service.OperateLogService).Add(r, distribution_vo.OperateLogReq{
					ModuleType:  base_service.ModuleDisCommisSetting,
					Type:        base_service.DisCommisSettingEdit,
					FromId:      "0",
					Description: fmt.Sprintf("全局佣金比例设置由%f%%变更为%f%%", oldRate, newRate),
				})
			}(r, oldRate, newRate)
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// GetCustomerChannelGlobalSetting 获取全局佣金设置 @cls-v1.0.0
// @Summary 获取全局佣金设置
// @Description 获取全局佣金设置接口
// @Tags 后台接口-佣金设置
// @Accept  json
// @Produce  json
// @Success 200 {object} distribution_vo.GetCustomerChannelGlobalSettingResp
// @Failure 400 {object} distribution_vo.GetCustomerChannelGlobalSettingResp
// @Router /manager/dis/commis-setting/global-setting [GET]
func GetCustomerChannelGlobalSetting(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.GetCustomerChannelGlobalSettingResp{
		BaseHttpResponse: viewmodel.BaseHttpResponse{
			Code: 200,
		},
	}

	server := services.DisCommisSettingService{}
	resp.Data = server.GetCustomerChannelGlobalSetting(cast.ToInt(r.Header.Get("org_id")))

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// GetCustomerChannelList 获取客户渠道列表 @cls-v1.0.0
// @Summary 获取客户渠道列表
// @Description 获取客户渠道列表接口
// @Tags 后台接口-佣金设置
// @Accept  json
// @Produce  json
// @Success 200 {object} distribution_vo.GetCustomerChannelListResp
// @Failure 400 {object} distribution_vo.GetCustomerChannelListResp
// @Router /manager/dis/commis-setting/customer-channel-list [GET]
func GetCustomerChannelList(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.GetCustomerChannelListResp{
		BaseHttpResponse: viewmodel.BaseHttpResponse{
			Code: 400,
		},
	}
	req, err := utils.Bind[distribution_vo.GetCustomerChannelListReq](r)
	orgId := r.Header.Get("org_id")
	req.OrgId = cast.ToInt(orgId)
	if err != nil {
		log.Error("获取客户渠道列表，参数解析失败：err=", err.Error())
		resp.Message = err.Error()
	} else {
		service := services.DisCommisSettingService{}
		data, err := service.GetCustomerChannelList(req)
		if err != nil {
			log.Error("获取客户渠道列表失败：err=", err.Error())
			resp.Message = err.Error()
		} else {
			resp.Code = 200
			resp.Data = data
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)

}
