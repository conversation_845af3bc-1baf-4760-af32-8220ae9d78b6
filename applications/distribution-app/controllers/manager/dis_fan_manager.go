package manager

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	"eShop/services/distribution-service/services"
	"eShop/view-model"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"net/http"

	"github.com/spf13/cast"
)

// @Summary 获取粉丝关系保护期
// @Tags 后台接口-粉丝关系
// @Accept  plain
// @Produce  json
// @Success 200 {object} vo.GetDisFansProtectSettingRes
// @Failure 400 {object} vo.GetDisFansProtectSettingRes
// @Router /manager/dis/fans/protect/get [GET]
func GetDisFansProtectSetting(writer http.ResponseWriter, request *http.Request) {

	orgId := cast.ToInt(request.Header.Get("org_id"))
	log.Infof("分销系统-获取粉丝关系保护期入参：主体id=%d", orgId)
	out := vo.GetDisFansProtectSettingRes{}
	out.Code = 400
	if orgId <= 0 {
		out.Message = "主体id无效"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	var err error
	server := services.DisFansService{}
	out.Data, err = server.GetDisFansProtectSetting(orgId)
	if err != nil {
		out.Message = "获取粉丝保护期失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)

}

// @Summary 编辑粉丝关系保护期
// @Tags 后台接口-粉丝关系
// @Accept  json
// @Produce  json
// @Param EditDisFansProtectSettingReq body vo.DistributorFansProtect true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/dis/fans/protect/edit [POST]
func EditDisFansProtectSetting(writer http.ResponseWriter, request *http.Request) {

	orgId := cast.ToInt(request.Header.Get("org_id"))
	param := vo.DistributorFansProtect{}
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	if orgId <= 0 {
		out.Message = "主体id无效"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	param, err := utils.Bind[vo.DistributorFansProtect](request)
	if err != nil {
		log.Errorf("编辑粉丝关系保护期，参数解析失败：err=%s", err.Error())
		out.Message = "解析入参失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	vErr := validate.Validate(param, "")
	if vErr != nil {
		log.Errorf("编辑粉丝关系保护期，参数校验失败：err=%s", utils.InterfaceToJSON(vErr))
		out.Message = "参数校验失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	if param.HasProtect <= 0 {
		out.Message = "请传入正确的有无保护期参数"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	if param.HasProtect == 1 && param.ProtectDay <= 0 {
		out.Message = "请设置有效的粉丝关系保护期天数"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	// 无保护期，将day参数置为0
	if param.HasProtect != 1 && param.ProtectDay > 0 {
		param.ProtectDay = 0
	}
	param.OrgId = orgId

	server := services.DisFansService{}
	if err := server.EditDisFansProtectSetting(param); err != nil {
		out.Message = "设置有效的粉丝关系保护期天数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
