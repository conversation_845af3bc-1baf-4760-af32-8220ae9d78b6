package manager

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"

	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// @Summary 数据统计-数据总览-看板
// @Tags 后台接口-数据统计-看板
// @Accept  plain
// @Produce  json
// @Param GetKanbanOverviewReq query distribution_vo.GetKanbanOverviewReq true " "
// @Success 200 {object} distribution_vo.GetKanbanOverviewResp
// @Failure 400 {object} distribution_vo.GetKanbanOverviewResp
// @Router /manager/stats/kanban/overview [GET]
func GetKanbanOverview(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := distribution_vo.GetKanbanOverviewResp{}
	out.Code = 400
	orgId := cast.ToInt64(request.Header.Get("org_id"))
	param, err := utils.Bind[distribution_vo.GetKanbanOverviewReq](request)
	param.OrgId = orgId
	if err != nil {
		log.Errorf("数据总览-看板-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	// 获取订单数据概览和分销订单数据概览数据
	ser := services.StatsShopService{}
	out.Data, err = ser.GetKanbanOverview(param)
	if err != nil {
		log.Errorf("数据总览-看板-获取数据失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	// 获取业务员数据和分销企业数据
	server := services.StatsEnterpriseService{}
	if out.Data.SalesmanAndEnt, err = server.GetStatsEntView(param); err != nil {
		log.Errorf("获取数据总览-分销企业数据失败-错误为%s", err.Error())
		out.Message = "获取数据总览-分销企业数据失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 数据统计-商品订单-看板
// @Tags 后台接口-数据统计-看板
// @Accept  plain
// @Produce  json
// @Param GetKanbanOverviewReq query distribution_vo.GetKanbanOverviewReq true " "
// @Success 200 {object} distribution_vo.GetKanbanOrderResp
// @Failure 400 {object} distribution_vo.GetKanbanOrderResp
// @Router /manager/stats/kanban/order [GET]
func GetKanbanOrder(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := distribution_vo.GetKanbanOrderResp{}
	out.Code = 400
	orgId := cast.ToInt64(request.Header.Get("org_id"))
	param, err := utils.Bind[distribution_vo.GetKanbanOverviewReq](request)
	param.OrgId = orgId
	if err != nil {
		log.Errorf("商品订单-看板-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	// 获取订单数据概览和分销订单数据概览数据
	ser := services.StatsShopService{}
	out.Data, err = ser.GetKanbanOrder(param)
	if err != nil {
		log.Errorf("商品订单-看板-获取数据失败-错误为%s", err.Error())
		out.Message = "获取数据失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 数据统计-保险订单-看板
// @Tags 后台接口-数据统计-看板
// @Accept  plain
// @Produce  json
// @Param GetKanbanOverviewReq query distribution_vo.GetKanbanOverviewReq true " "
// @Success 200 {object} distribution_vo.GetKanbanInsOrderResp
// @Failure 400 {object} distribution_vo.GetKanbanInsOrderResp
// @Router /manager/stats/kanban/insorder [GET]
func GetKanbanInsOrder(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := distribution_vo.GetKanbanInsOrderResp{}
	out.Code = 400
	orgId := cast.ToInt64(request.Header.Get("org_id"))
	param, err := utils.Bind[distribution_vo.GetKanbanOverviewReq](request)
	param.OrgId = orgId
	if err != nil {
		log.Errorf("数据总览-看板-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	// 获取订单数据概览和分销订单数据概览数据
	ser := services.StatsShopService{}
	out.Data, err = ser.GetKanbanInsOrder(param)
	if err != nil {
		log.Errorf("数据总览-看板-获取数据失败-错误为%s", err.Error())
		out.Message = "获取数据失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// StatsShopPageList
// @Summary 店铺数据-报表
// @Tags 后台接口-店铺数据
// @Accept  plain
// @Produce  json
// @Param StatsShopPageReq query distribution_vo.StatsShopPageReq true " "
// @Success 200 {object} distribution_vo.StatsShopPageResp
// @Failure 400 {object} distribution_vo.StatsShopPageResp
// @Router /manager/stats/shop/page-list [GET]
func StatsShopPageList(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := distribution_vo.StatsShopPageResp{}
	out.Code = 400
	param, err := utils.Bind[distribution_vo.StatsShopPageReq](request)
	if err != nil {
		log.Errorf("获取店铺统计数据-报表解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	service := services.StatsShopService{}
	if out.Data, out.Total, err = service.StatsShopPageList(param); err != nil {
		log.Errorf("获取店铺统计数据-报表失败-错误为%s", err.Error())
		out.Message = "获取店铺统计数据-报表概览失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	bytes, _ := json.Marshal(out)
	writer.Write(bytes)
}

// StatsShopExport
// @Summary 店铺数据-导出
// @Tags 后台接口-店铺数据
// @Accept  plain
// @Produce  json
// @Param StatsShopPageReq query distribution_vo.StatsShopPageReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/stats/shop/export [GET]
func StatsShopExport(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.StatsShopPageReq](r)
	if err != nil {
		log.Error("导出店铺统计数据，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("导出店铺统计数据，参数解析失败：%s", err.Error())
	} else {
		s := common.TaskListService{}
		var task distribution_vo.TaskList
		par, _ := json.Marshal(req)
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(r.Header.Get("org_id"))
		task.TaskContent = 13
		err := s.CreatTask(r, task)
		if err != nil {
			log.Error("导出店铺统计数据：err=" + err.Error())
			resp.Message = fmt.Sprintf("导出店铺统计数据：%s", err.Error())
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}
