package manager

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"
)

// RPHospitalList 获取医院列表
// @Summary 获取医院列表@blky-v1.0
// @Description 获取医院列表
// @Tags 后台接口-瑞鹏体制内的数据
// @Accept json
// @Produce json
// @Param RPHospitalListReq query vo.RPHospitalListReq true " "
// @Success 200 {object} vo.RPHospitalListRes
// @Failure 400 {object} vo.RPHospitalListRes
// @Router /manager/rp/hospital-list [GET]
func RPHospitalList(w http.ResponseWriter, r *http.Request) {
	resp := vo.RPHospitalListRes{}
	resp.Code = 400

	req, err := utils.Bind[vo.AllHospitalListReq](r)
	if err != nil {
		log.Error("获取瑞鹏医院，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取瑞鹏医院，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	server := services.RPService{}
	data, _, err := server.AllHospitalList(req)
	if err != nil {
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	resp.Data = data
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}
