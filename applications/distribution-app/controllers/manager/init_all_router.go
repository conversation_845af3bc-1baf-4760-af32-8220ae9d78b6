package manager

/**
给管理后台用的api
*/
import (
	"github.com/go-chi/chi/v5"
)

func InitAllManager(r chi.Router) {

	// 分销员后端路由
	InitDistributorRouter(r)
	//业务员的后端路由
	InitDisSalesmanRouter(r)
	//分销员与客户关系
	InitDistributorFansRouter(r)
	//分销商品
	InitDistributorGoods(r)
	//组织管理
	InitOrgRouter(r)
	//通用的方法
	InitCommRouter(r)
	//分销提现管理
	InitDisWithdrawRouter(r)
	//结算管理
	InitDisSettlementRouter(r)
	//分销订单管理
	InitDisOrderRouter(r)
	//保险分销结算管理
	InitDisInsureSettle(r)
	//工具接口 管理（主要用于开发人员手动调接口）
	InitDisToolRouter(r)
	//线下企业管理
	InitDisShopRouter(r)
	//瑞鹏体制内的数据
	InitRPRouter(r)
	// 百林康源
	InitBlkyRouter(r)
	//数据统计
	InitStatsRouter(r)
	//宠利扫 贵族渠道佣金设置
	InitCommisSettingRouter(r)

}
func InitDistributorRouter(r chi.Router) {

	r.Route("/manager/distributor", func(r chi.Router) {
		// 分页查询
		r.Get("/page-list", DistributorPage)

		// 导出
		r.Post("/export", DistributorExport)

		// 清退操作
		r.Post("/unbind", DistributorUnbind)

		// 更换业务员
		r.Post("/change", DistributorChange)

		// 启用分销员
		r.Post("/able", DistributorAble)

		// 分销员详情
		r.Post("/detail", DistributorAbleDetail)

		// 分销员（医生）更换绑定的医院
		r.Post("/change-hospital", ChangeHospital)

		// 分销员（医生）审核
		r.Post("/approve", Approve)

		// 变更注册业务员
		r.Post("/change-salesperson", ChangeSalesperson)

	})
}
func InitDisSalesmanRouter(r chi.Router) {
	r.Route("/manager/salesman", func(r chi.Router) {
		r.Post("/list", SaleManList)
		r.Post("/add", SaleManAdd)
		r.Post("/edit", SaleManEdit)
		r.Post("/stop", SaleManStopOrStar)
		r.Post("/export", SaleManExport)
		r.Post("/get", SaleManGet)

	})
}

func InitDistributorFansRouter(r chi.Router) {
	r.Route("/manager/dis/fans", func(r chi.Router) {
		//获取分销粉丝关系保护期设置
		r.Get("/protect/get", GetDisFansProtectSetting)
		//编辑分销粉丝关系保护期设置
		r.Post("/protect/edit", EditDisFansProtectSetting)
	})
}

func InitDistributorGoods(r chi.Router) {
	r.Route("/manager/goods", func(r chi.Router) {
		// 分销商品-列表查询
		r.Get("/page-list", GoodsPage)
		// 分销商品-批量导出
		r.Post("/export", GoodsExport)
		// 分销商品-全局设置
		r.Post("/global-setting", GoodsGlobalSetting)
		// 分销商品-查询全局佣金设置
		r.Get("/global-setting-get", GetGoodsGlobalSetting)
		// 分销商品-佣金设置
		r.Post("/commission-setting", CommissionSetting)
		// 分销商品-推广素材
		r.Post("/dis-write", GoodsDisWrite)
	})
}

func InitDisInsureSettle(r chi.Router) {
	r.Route("/manager/insure-settle", func(r chi.Router) {
		// 保险分销结算-列表查询
		r.Get("/page-list", InsureSettlePage)
		// 保险分销结算-批量导出
		r.Post("/export", InsureSettleExport)
	})
}

func InitOrgRouter(r chi.Router) {
	r.Route("/manager/organization", func(r chi.Router) {
		//组织分页查询
		r.Get("/page-list", OrgPage)
		//新增组织
		r.Post("/add", OrgAdd)
		//编辑组织
		r.Post("/edit", OrgEdit)
		//停用组织
		r.Post("/is-use", OrgIsUse)
		//组织详情
		r.Get("/detail", OrgDetail)
	})
}

func InitCommRouter(r chi.Router) {
	r.Route("/manager/comm", func(r chi.Router) {
		r.Get("/task-list", GetTaskList)
		// 分页查询操作日志列表
		r.Get("/operate-log-page", OperateLogPage)
		r.Get("/region-list", BaseRegionList)
		r.Post("/task-import", TaskImport)

	})
}

func InitDisWithdrawRouter(r chi.Router) {
	r.Route("/manager/dis/withdraw", func(r chi.Router) {
		//获取提现列表接口
		r.Get("/list", GetDisWithdrawList)
		//获取提现详情接口
		r.Get("/detail", GetDisWithdrawDetail)
		//提现打款/拒绝
		r.Post("/check", DisWithdrawCheck)

		r.Post("/export", DisWithdrawExport)
		//备注：批量导入打款记录 统一用导入接口
		//备注：批量任务查看 接口统一用一个。这里不用实现

	})
}

func InitDisSettlementRouter(r chi.Router) {
	r.Route("/manager/dis/settlement", func(r chi.Router) {
		//获取结算列表接口
		r.Get("/list", GetDisSettlementList)

		//结算列表导出
		r.Post("/export", DisSettlementExport)

		//备注：批量任务查看 接口统一用一个。这里不用实现

	})
}
func InitDisOrderRouter(r chi.Router) {
	r.Route("/manager/dis/order", func(r chi.Router) {
		//获取分销订单列表接口
		r.Get("/list", GetDisOrderList)

		//分销订单列表导出
		r.Post("/export", DisOrderExport)

		//分销订单列表导出订单数据（含商品明细）
		r.Post("/export-detail", DisOrderDetailExport)

		//备注：批量任务查看 接口统一用一个。这里不用实现

	})
}

func InitDisToolRouter(r chi.Router) {
	r.Route("/manager/dis/tool", func(r chi.Router) {
		r.Post("/task-do", TaskDo)
	})
}

func InitDisShopRouter(r chi.Router) {
	r.Route("/manager/dis/shop", func(r chi.Router) {
		//线下企业管理列表接口
		r.Get("/list", GetShopList)
		//线下企业管理列表导出
		r.Post("/export", ExportShopList)
		//企业查询接口
		r.Get("/get", GetShop)
		//分销员更换角色
		r.Post("/role-change", DistributorRoleChange)
		//商品同步es
		r.Post("/sync_goods", SyncEsShopGoods)
	})
}

func InitRPRouter(r chi.Router) {
	r.Route("/manager/rp", func(r chi.Router) {
		r.Get("/hospital-list", RPHospitalList)
	})
}

func InitBlkyRouter(r chi.Router) {
	r.Route("/manager/blky", func(r chi.Router) {
		//百林康源商品信息列表
		r.Get("/product/page-list", BlkyProductPage)
		//百林康源商品导出
		r.Post("/product/export", BlkyProductExport)
		//百林康源物流码查询
		r.Get("/query-code", BlkyQueryCode)
		//扣除佣金
		r.Post("/deduct_commission", DeductCommission)
		//物流码恢复
		r.Post("/restore_code", RestoreCode)
		//隐藏测试数据
		r.Post("/del_data", DelTestData)
	})
}

func InitStatsRouter(r chi.Router) {
	r.Route("/manager/stats", func(r chi.Router) {
		// 数据总览-看板
		r.Get("/kanban/overview", GetKanbanOverview)
		// 商品订单-看板
		r.Get("/kanban/order", GetKanbanOrder)
		// 保险订单-看板
		r.Get("/kanban/insorder", GetKanbanInsOrder)
		// 商品数据列表查询
		r.Get("/sku/list", StatsSkuList)
		// 商品区域列表查询
		r.Get("/sku/city-list", StatsSkuCityList)
		// 商品数据导出
		r.Get("/sku/export", StatsSkuExport)
		// 店铺数据-报表
		r.Get("/shop/page-list", StatsShopPageList)
		// 店铺数据-导出
		r.Get("/shop/export", StatsShopExport)
		// 业务员数据列表查询
		r.Get("/salesperson/page-list", StatsSalespersonPage)
		// 业务员数据导出
		r.Get("/salesperson/export", StatsSalespersonExport)
	})
	// 防伪码查询
	r.Route("/manager/cls", func(r chi.Router) {
		r.Get("/verify/list", ClsVerifyPage)
		r.Get("/verify/export", ClsVerifyExport)
		// 极宠家用户列表
		r.Get("/user/list", GetUserList)
		// 极宠家用户导出
		r.Get("/user/export", ExportUserList)
		// 极宠家订单列表
		r.Get("/order/list", OrderList)
		// 极宠家优惠券列表
		r.Get("/voucher/list", VoucherList)
		// 极宠家用户详情
		r.Get("/user/detail", GetUserDetail)
		// 获取用户手机号
		r.Get("/user/phone", GetUserPhone)
	})

}
func InitCommisSettingRouter(r chi.Router) {
	// 宠利扫 贵族渠道  佣金设	置
	r.Route("/manager/dis/commis-setting", func(r chi.Router) {
		// 佣金设置列表
		r.Get("/page-list", GetDisCommisSettingPageList)
		// 新增佣金设置
		r.Post("/add", AddDisCommisSetting)
		// 编辑佣金设置
		r.Post("/edit", EditDisCommisSetting)
		// 删除佣金设置
		r.Post("/delete", DeleteDisCommisSetting)
		// 全局佣金设置
		r.Post("/global-setting", CustomerChannelGlobalSetting)
		// 获取全局佣金设置
		r.Get("/global-setting", GetCustomerChannelGlobalSetting)
		// 获取客户渠道列表
		r.Get("/customer-channel-list", GetCustomerChannelList)
	})
}
