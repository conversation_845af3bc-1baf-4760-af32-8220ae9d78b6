package main

import (
	"context"
	"eShop/applications/marketing-app/controllers"
	"eShop/infra/cache"
	"eShop/infra/config"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	emiddware "eShop/infra/middleware"
	"eShop/infra/security"
	"eShop/infra/tracing"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"eShop/services/marketing-service/services"

	"github.com/BurntSushi/toml"
	"github.com/go-chi/chi/v5"
	httpSwagger "github.com/swaggo/http-swagger"
)

func service() http.Handler {
	r := chi.NewRouter()

	r.Use(emiddware.WithLogger)
	r.Use(emiddware.WithTracing)

	jwtauth.JwtInit()
	r.Use(jwtauth.UrlBasedVerify(jwtauth.TokenFromHeader))
	r.Use(emiddware.SetOrgId)

	// 注册优惠券服务
	couponSvc := services.CouponService{}
	couponHandle := controllers.NewCouponController(couponSvc)
	couponHandle.RegisterRoutes(r)

	// 注册赠品服务
	giftSvc := services.GiftService{}
	giftHandle := controllers.NewGiftController(giftSvc)
	giftHandle.RegisterRoutes(r)

	// 注册营销活动服务
	activitySvc := services.ActivityService{}
	activityHandle := controllers.NewActivityController(activitySvc)
	activityHandle.RegisterRoutes(r)

	// 宠物贵族裂变活动
	petActivitySvc := services.PetActivityService{}
	petActivityHandle := controllers.NewPetActivityController(petActivitySvc)
	petActivityHandle.RegisterRoutes(r)

	// 宠物贵族裂变作品
	PetArtworkSvc := services.NewPetArtworkService()
	petArtworkHandle := controllers.NewPetArtworkController(PetArtworkSvc)
	petArtworkHandle.RegisterRoutes(r)

	// 宠物贵族裂变邀请
	petInviteSvc := services.NewPetInviteService()
	petInviteHandle := controllers.NewPetInviteController(petInviteSvc)
	petInviteHandle.RegisterRoutes(r)

	// 宠物贵族裂变奖品
	petPrizeSvc := services.NewPetPrizeService()
	petPrizeHandle := controllers.NewPetPrizeController(petPrizeSvc)
	petPrizeHandle.RegisterRoutes(r)

	petStatSvc := services.PetStatService{}
	petStatHandle := controllers.NewPetStatController(petStatSvc)
	petStatHandle.RegisterRoutes(r)

	r.Mount("/swagger", httpSwagger.WrapHandler)
	return r
}

func main() {
	toml.DecodeFile("appsetting.toml", &config.LocalSetting)
	config.LocalSetting.LocalIP = config.GetCurrentIP()
	security.InitPEM("rsa_1024_priv.pem", "rsa_1024_pub.pem", "rsa_1024_pub.pem")

	cache.CacheSources[cache_source.EShop] = cache.Address(config.Get("redis.PassAddr"))
	log.Init()

	fmt.Println("marketing-app 启动成功! 端口 8153")

	_, closer, err := tracing.InitJaeger()
	if err != nil {
		panic(err)
	}

	server := &http.Server{Addr: "0.0.0.0:8153", Handler: service()}
	serverCtx, serverStopCtx := context.WithCancel(context.Background())

	sig := make(chan os.Signal, 1)
	signal.Notify(sig, syscall.SIGHUP, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
	go func() {
		<-sig
		shutdownCtx, _ := context.WithTimeout(serverCtx, 30*time.Second)
		go func() {
			<-shutdownCtx.Done()
			closer.Close()
			if shutdownCtx.Err() == context.DeadlineExceeded {
				log.Error("强制退出")
			}
			serverStopCtx()
		}()
		server.Shutdown(shutdownCtx)
	}()
	err = server.ListenAndServe()
	if err != nil && err != http.ErrServerClosed {
		log.Error(err)
	}
	<-serverCtx.Done()
}
