package controllers

import (
	taskEnum "eShop/infra/enum"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/marketing-service/services"
	distribution_vo "eShop/view-model/distribution-vo"
	marketing_vo "eShop/view-model/marketing-vo"
	"encoding/json"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/spf13/cast"
)

type PetActivityController struct {
	service services.PetActivityService
}

func NewPetActivityController(service services.PetActivityService) *PetActivityController {
	return &PetActivityController{service: service}
}

// RegisterRoutes 注册路由
func (c *PetActivityController) RegisterRoutes(r chi.Router) {
	r.Route("/marketing-app/awen/manager", func(r chi.Router) {
		r.Get("/pet_contestant/list", c.GetPetContestantList)
		r.Get("/pet_stat/list", c.GetPetStatList)
		r.Post("/qr_code", c.<PERSON>r<PERSON>)
	})

	r.Route("/marketing-app/awen/api", func(r chi.Router) {
		r.Get("/my_rank", c.GetMyRank)
		r.Get("/my_vote_detail", c.GetMyVoteDetail)
		r.Get("/activity-info", c.GetActivityInfo) // 获取活动信息
	})
}

// @Summary 我的全国排名
// @Tags 贵族裂变活动-我的全国排名
// @Accept plain
// @Produce json
// @Param user_id query string true "用户ID"
// @Success 200 {object} marketing_vo.MyRankRes
// @Failure 400 {object} marketing_vo.MyRankRes
// @Router /marketing-app/awen/manager/my_rank [get]
func (c *PetActivityController) GetMyRank(writer http.ResponseWriter, request *http.Request) {
	userId := request.URL.Query().Get("user_id")
	param := marketing_vo.MyRankReq{UserId: userId}
	res, err := c.service.GetMyRank(&param)
	out := marketing_vo.MyRankRes{}
	out.Code = 400
	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Data = res
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 我的助力明细
// @Tags 贵族裂变活动-助力明细
// @Accept plain
// @Produce json
// @Param user_id query string true "用户ID"
// @Param page_index query int false "页码"
// @Param page_size query int false "每页数量"
// @Success 200 {object} marketing_vo.MyVoteDetailRes
// @Failure 400 {object} marketing_vo.MyVoteDetailRes
// @Router /marketing-app/awen/api/my_vote_detail [get]
func (c *PetActivityController) GetMyVoteDetail(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := marketing_vo.MyVoteDetailRes{}
	out.Code = 400

	param, err := utils.Bind[marketing_vo.MyVoteDetailReq](request)
	if err != nil {
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	res, total, err := c.service.GetMyVoteDetail(&param)
	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Data = res
	out.Total = total
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 获取二维码
// @Tags 管理后台-贵族裂变活动
// @Accept plain
// @Produce json
// @Param path query string true "路径"
// @Param sid query string true "会话ID"
// @Param org_id query int true "组织ID"
// @Param type_url query int true "URL类型"
// @Success 200 {object} marketing_vo.GetQrCodeRes
// @Failure 400 {object} marketing_vo.GetQrCodeRes
// @Router /marketing-app/awen/manager/qr_code [POST]
func (c *PetActivityController) GetQrCode(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := marketing_vo.GetQrCodeRes{}
	out.Code = 400

	param, err := utils.Bind[marketing_vo.GetQrCodeReq](request)
	if err != nil {
		log.Errorf("获取二维码-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	data, err := c.service.GetQrCode(&param)
	if err != nil {
		log.Errorf("获取二维码失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Data = data
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 参赛用户管理列表接口
// @Tags 管理后台-贵族裂变活动
// @Accept plain
// @Produce json
// @Param PetContestantListReq query marketing_vo.PetContestantListReq true " "
// @Success 200 {object} marketing_vo.PetContestantListRes
// @Failure 400 {object} marketing_vo.PetContestantListRes
// @Router /marketing-app/awen/manager/pet_contestant/list [GET]
func (c *PetActivityController) GetPetContestantList(w http.ResponseWriter, r *http.Request) {
	var err error
	out := marketing_vo.PetContestantListRes{}
	out.Code = 400

	param, err := utils.Bind[marketing_vo.PetContestantListReq](r)
	if err != nil {
		log.Errorf("获取参赛用户列表-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	if out.Data, out.Total, err = c.service.ListPetContestants(&param); err != nil {
		log.Errorf("获取参赛用户列表失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}
	if out.Data == nil {
		out.Data = make([]marketing_vo.PetContestantListItem, 0)
	}

	if param.IsExport {
		taskServ := common.TaskListService{}
		var task distribution_vo.TaskList
		par, _ := json.Marshal(param)
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(r.Header.Get("org_id"))
		task.TaskContent = taskEnum.TaskContentPetContestantExport
		err = taskServ.CreatTask(r, task)
		if err != nil {
			log.Errorf("导出参赛用户列表失败-错误为%s", err.Error())
			response.BadRequest(w, err.Error())
			return
		}
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	w.Write(out2)
}

// @Summary 获取活动信息
// @Tags 管理后台-贵族裂变活动
// @Accept plain
// @Produce json
// @Success 200 {object} response.Response[marketing_vo.PetActivityInfo] "成功获取奖品列表数据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /marketing-app/awen/api/activity-info [GET]
func (c *PetActivityController) GetActivityInfo(w http.ResponseWriter, r *http.Request) {
	petActivityInfo, err := c.service.GetActivityInfo(nil)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, petActivityInfo)
}

// @Summary 数据监测统计列表
// @Tags 管理后台-贵族裂变活动
// @Accept plain
// @Produce json
// @Param PetStatListReq query marketing_vo.PetStatListReq true " "
// @Success 200 {object} marketing_vo.PetStatListRes
// @Failure 400 {object} marketing_vo.PetStatListRes
// @Router /marketing-app/awen/manager/pet_stat/list [GET]
func (c *PetActivityController) GetPetStatList(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := marketing_vo.PetStatListRes{}
	out.Code = 400

	param, err := utils.Bind[marketing_vo.PetStatListReq](request)
	if err != nil {
		log.Errorf("获取数据监测统计列表-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Data, out.Total, err = c.service.ListPetStats(&param)
	if err != nil {
		log.Errorf("获取数据监测统计列表失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	if out.Data == nil {
		out.Data = make([]marketing_vo.PetStatListItem, 0)
	}

	// 处理导出请求
	if param.IsExport {
		taskServ := common.TaskListService{}
		var task distribution_vo.TaskList
		par, _ := json.Marshal(param)
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(request.Header.Get("org_id"))
		task.TaskContent = taskEnum.TaskContentPetStatExport
		err = taskServ.CreatTask(request, task)
		if err != nil {
			log.Errorf("导出数据监测统计列表失败-错误为%s", err.Error())
			response.BadRequest(writer, err.Error())
			return
		}
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
