package controllers

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/marketing-service/services"
	viewmodel "eShop/view-model"
	marketing_vo "eShop/view-model/marketing-vo"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/spf13/cast"

	jwt "eShop/infra/jwtauth"

	"github.com/go-chi/chi/v5"
)

type CouponController struct {
	service services.CouponService
}

func NewCouponController(service services.CouponService) *CouponController {
	return &CouponController{
		service: service,
	}
}

// RegisterRoutes 注册路由
func (c *CouponController) RegisterRoutes(r chi.Router) {
	// 管理后台接口
	r.Route("/marketing-app/manager/coupon", func(r chi.Router) {
		// 优惠券基础管理
		r.Get("/list", c.GetManagerCouponList)
		r.Get("/detail/{id}", c.GetCouponDetail)
		r.Post("/save", c.SaveCoupon)
		r.Delete("/{id}", c.DeleteCoupon) // 删除优惠券
		r.Put("/{id}", c.EndCoupon)       // 结束优惠券活动

		// 优惠券领取管理
		r.Get("/receiver/list", c.GetCouponReceiverList) // 领取列表
		r.Post("/receiver/stop", c.StopCoupon)           // 停止用券

		// 优惠券发放
		r.Post("/issue", c.IssueCoupon) // 发放优惠券
	})

	r.Route("/marketing-app/api/coupon", func(r chi.Router) {
		r.Get("/list", c.GetUserCouponList) //优惠券列表领取状
	})
}

// @Summary 优惠券管理列表接口
// @Tags 后台接口-优惠券管理
// @Accept plain
// @Produce json
// @Param CouponListReq query marketing_vo.CouponListReq true " "
// @Success 200 {object} marketing_vo.CouponListRes
// @Failure 400 {object} marketing_vo.CouponListRes
// @Router /marketing-app/manager/coupon/list [GET]
func (c *CouponController) GetManagerCouponList(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := marketing_vo.CouponListRes{}
	out.Code = 400

	// 从 JWT 获取 store_id
	jwtInfo, err := jwt.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	param, err := utils.Bind[marketing_vo.CouponListReq](request)
	if err != nil {
		log.Errorf("获取优惠券管理列表-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	param.StoreId = jwtInfo.TenantId // 设置 store_id

	var total int64
	if out.Data, total, err = c.service.GetCouponList(&param); err != nil {
		log.Errorf("获取优惠券管理列表失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 如果 out.Data 为 nil，设置为空数组
	if out.Data == nil {
		out.Data = make([]marketing_vo.MarketingCouponListRes, 0)
	}
	out.Code = 200
	out.Total = int(total)
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 保存优惠券
// @Tags 后台接口-优惠券管理
// @Accept json
// @Produce json
// @Param SaveCouponReq body marketing_vo.SaveCouponReq true "保存优惠券请求"
// @Success 200 {object} marketing_vo.SaveCouponRes
// @Failure 400 {object} marketing_vo.SaveCouponRes
// @Router /marketing-app/manager/coupon/save [POST]
func (c *CouponController) SaveCoupon(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := marketing_vo.SaveCouponRes{}
	out.Code = 400

	// 从 JWT 获取 store_id
	jwtInfo, err := jwt.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	param := &marketing_vo.SaveCouponReq{}
	if err = json.NewDecoder(request.Body).Decode(param); err != nil {
		log.Errorf("保存优惠券-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	param.ChainId = cast.ToInt64(jwtInfo.ChainId)
	param.StoreId = jwtInfo.TenantId // 设置 store_id

	if err = c.service.SaveCoupon(param); err != nil {
		log.Errorf("保存优惠券失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 删除优惠券
// @Tags 后台接口-优惠券管理
// @Accept plain
// @Produce json
// @Param id path int true "优惠券ID"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /marketing-app/manager/coupon/{id} [DELETE]
func (c *CouponController) DeleteCoupon(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{Code: 400}

	// 从 JWT 获取 store_id
	jwtInfo, err := jwt.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	idStr := chi.URLParam(request, "id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		log.Errorf("删除优惠券-解析ID参数失败-错误为%s", err.Error())
		out.Message = "无效的优惠券ID"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 调用 service 时传入 store_id
	if err = c.service.DeleteCoupon(id, jwtInfo.TenantId); err != nil {
		log.Errorf("删除优惠券失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "删除成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 结束优惠券活动
// @Tags 后台接口-优惠券管理
// @Accept plain
// @Produce json
// @Param id path int true "优惠券ID"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /marketing-app/manager/coupon/{id} [PUT]
func (c *CouponController) EndCoupon(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{Code: 400}

	// 从 JWT 获取 store_id
	jwtInfo, err := jwt.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	idStr := chi.URLParam(request, "id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		log.Errorf("结束优惠券活动-解析ID参数失败-错误为%s", err.Error())
		out.Message = "无效的优惠券ID"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 调用 service 时传入 store_id
	if err = c.service.EndCoupon(id, jwtInfo.TenantId); err != nil {
		log.Errorf("结束优惠券活动失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "操作成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 获取优惠券详情
// @Tags 后台接口-优惠券管理
// @Accept plain
// @Produce json
// @Param id path int true "优惠券ID"
// @Success 200 {object} marketing_vo.CouponDetailResp
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /marketing-app/manager/coupon/detail/{id} [GET]
func (c *CouponController) GetCouponDetail(writer http.ResponseWriter, request *http.Request) {
	// 获取并验证ID参数
	idStr := chi.URLParam(request, "id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		log.Errorf("获取优惠券详情-解析ID参数失败-错误为%s", err.Error())
		response := viewmodel.BaseHttpResponse{
			Code:    400,
			Message: "无效的优惠券ID",
		}
		out, _ := json.Marshal(response)
		writer.Write(out)
		return
	}

	// 获取store_id
	storeId := request.Header.Get("Financialcode")
	if storeId == "" {
		log.Error("获取优惠券详情-未获取到店铺ID")
		response := viewmodel.BaseHttpResponse{
			Code:    400,
			Message: "未获取到店铺ID",
		}
		out, _ := json.Marshal(response)
		writer.Write(out)
		return
	}

	// 调用service获取详情
	detail, err := c.service.GetCouponDetail(&marketing_vo.CouponDetailReq{
		Id:      id,
		StoreId: storeId,
	})
	if err != nil {
		log.Errorf("获取优惠券详情失败-错误为%s", err.Error())
		response := viewmodel.BaseHttpResponse{
			Code:    400,
			Message: err.Error(),
		}
		out, _ := json.Marshal(response)
		writer.Write(out)
		return
	}

	// 返回成功响应
	resp := struct {
		Code    int                            `json:"code"`
		Message string                         `json:"message"`
		Data    *marketing_vo.CouponDetailResp `json:"data"`
	}{
		Code: 200,
		Data: detail,
	}
	out, _ := json.Marshal(resp)
	writer.Write(out)
}

// @Summary 优惠券领取列表
// @Tags 后台接口-优惠券管理
// @Accept plain
// @Produce json
// @Param CouponReceiverListReq query marketing_vo.CouponReceiverListReq true " "
// @Success 200 {object} marketing_vo.CouponReceiverListRes
// @Failure 400 {object} marketing_vo.CouponReceiverListRes
// @Router /marketing-app/manager/coupon/receiver/list [GET]
func (c *CouponController) GetCouponReceiverList(w http.ResponseWriter, r *http.Request) {
	var err error
	out := marketing_vo.CouponReceiverListRes{}
	out.Code = 400

	// 1. 参数验证
	param, err := utils.Bind[marketing_vo.CouponReceiverListReq](r)
	if err != nil {
		log.Errorf("获取优惠券领取列表-解析参数失败: %s", err.Error())
		out.Message = "参数解析失败"
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	// 2. 获取store_id
	jwtInfo, err := jwt.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	param.StoreId = jwtInfo.TenantId
	if param.CustomerId == "" {
		param.CustomerId = jwtInfo.CustomerId
	}
	// 3. 调用service
	out.Data, out.Total, err = c.service.GetCouponReceiverList(&param)
	if err != nil {
		log.Errorf("获取优惠券领取列表失败: %s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}
	// 如果 out.Data 为 nil，设置为空数组
	if out.Data == nil {
		out.Data = make([]marketing_vo.CouponReceiverInfo, 0)
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	w.Write(out2)
}

// @Summary 停止用券
// @Tags 后台接口-优惠券管理
// @Accept json
// @Produce json
// @Param StopCouponReq body marketing_vo.StopCouponReq true "停止用券请求"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /marketing-app/manager/coupon/receiver/stop [POST]
func (c *CouponController) StopCoupon(w http.ResponseWriter, r *http.Request) {
	out := viewmodel.BaseHttpResponse{Code: 400}

	// 1. 参数验证
	param, err := utils.Bind[marketing_vo.StopCouponReq](r)
	if err != nil {
		log.Errorf("获取优惠券领取列表-解析参数失败: %s", err.Error())
		out.Message = "参数解析失败"
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	// 2. 获取store_id
	jwtInfo, err := jwt.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}
	param.StoreId = jwtInfo.TenantId
	// 3. 调用service
	if err := c.service.StopCoupon(&param); err != nil {
		log.Errorf("停止用券失败: %s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "操作成功"
	out2, _ := json.Marshal(out)
	w.Write(out2)
}

// @Summary 发放优惠券
// @Tags 后台接口-优惠券管理
// @Accept json
// @Produce json
// @Param IssueCouponReq body marketing_vo.IssueCouponReq true "发放优惠券请求"
// @Success 200 {object} marketing_vo.IssueCouponRes
// @Failure 400 {object} marketing_vo.IssueCouponRes
// @Router /marketing-app/manager/coupon/issue [POST]
func (c *CouponController) IssueCoupon(w http.ResponseWriter, r *http.Request) {
	out := marketing_vo.IssueCouponRes{}
	out.Code = 400
	// 1. 参数验证
	param, err := utils.Bind[marketing_vo.IssueCouponReq](r)
	if err != nil {
		log.Errorf("获取优惠券领取列表-解析参数失败: %s", err.Error())
		out.Message = "参数解析失败"
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	// 2. 获取store_id
	jwtInfo, err := jwt.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}
	param.StoreId = jwtInfo.TenantId
	// 3. 调用service
	err = c.service.IssueCoupon(&param)
	if err != nil {
		log.Errorf("发放优惠券失败: %s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "发放成功"
	out2, _ := json.Marshal(out)
	w.Write(out2)
}

// @Summary 优惠券列表接口
// @Tags 小程序接口-优惠券
// @Accept plain
// @Produce json
// @Param CouponReceiverListReq query marketing_vo.CouponReceiverListReq true " "
// @Success 200 {object} marketing_vo.CouponUserListRes
// @Failure 400 {object} marketing_vo.CouponUserListRes
// @Router /marketing-app/api/coupon/list [GET]
func (c *CouponController) GetUserCouponList(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := marketing_vo.CouponUserListRes{}
	out.Code = 400

	// 从 JWT 获取用户信息
	jwtInfo, err := jwt.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 解析请求参数
	param, err := utils.Bind[marketing_vo.CouponUserListReq](request)
	if err != nil {
		log.Error("解析请求参数失败: err", err.Error())
		out.Message = fmt.Sprintf("解析请求参数失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 设置必要参数
	param.StoreId = jwtInfo.TenantId
	param.CustomerId = jwtInfo.CustomerId

	// 调用 service 获取数据
	list, total, err := c.service.GetUserCouponList(&param)
	if err != nil {
		log.Error("获取优惠券列表失败: err", err.Error())
		out.Message = fmt.Sprintf("获取优惠券列表失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 组装返回数据
	out.Code = 200
	out.Message = "success"
	out.Data = list
	out.Total = int(total)
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
