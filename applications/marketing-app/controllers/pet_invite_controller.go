package controllers

import (
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/marketing-service/services"
	vo "eShop/view-model/marketing-vo"
	"net/http"

	taskEnum "eShop/infra/enum"
	"eShop/infra/log"
	"eShop/services/common"
	distribution_vo "eShop/view-model/distribution-vo"
	"encoding/json"

	"github.com/go-chi/chi/v5"
	"github.com/spf13/cast"
)

// PetInviteController 邀请控制器
type PetInviteController struct {
	services services.PetInviteService
}

// NewPetInviteController 创建邀请控制器
func NewPetInviteController(services services.PetInviteService) *PetInviteController {
	return &PetInviteController{
		services: services,
	}
}

// SharePetArtwork 分享作品
// @Summary 分享作品
// @Description 点击分享作品接口
// @Tags 邀请
// @Accept json
// @Produce json
// @Param command body vo.SharePetArtworkReq true "分享作品请求"
// @Success 200 {object} vo.SharePetArtworkRes "成功"
// @Failure 400 {object} vo.SharePetArtworkRes "请求错误"
// @Router /marketing-app/awen/api/pet-invite/share [post]
func (c *PetInviteController) SharePetArtwork(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.SharePetArtworkReq](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	data, err := c.services.SharePetArtwork(&cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, data)
}

// VotePetArtwork 投票
// @Summary 投票
// @Description 投票接口
// @Tags 邀请
// @Accept json
// @Produce json
// @Param command body vo.VotePetArtworkReq true "投票请求"
// @Success 200 {object} vo.VotePetArtworkRes "成功"
// @Failure 400 {object} vo.VotePetArtworkRes "请求错误"
// @Router /marketing-app/awen/api/pet-invite/vote [post]
func (c *PetInviteController) VotePetArtwork(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VotePetArtworkReq](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	data, err := c.services.VotePetArtwork(&cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, data)
}

// RegisterRoutes 注册路由
func (c *PetInviteController) RegisterRoutes(r chi.Router) {
	r.Route("/marketing-app/awen/pet-invite", func(r chi.Router) {
		r.Post("/page", c.Page)
		r.Post("/export", c.Export)
	})
	r.Route("/marketing-app/awen/api/pet-invite", func(r chi.Router) {
		r.Post("/share", c.SharePetArtwork)
		r.Post("/vote", c.VotePetArtwork)
	})
}

// Page 邀请分页
// @Summary 邀请分页
// @Description 获取邀请分页数据
// @Tags 贵族裂变活动-邀请
// @Accept json
// @Produce json
// @Param page body vo.PetInvitePageReq true "分页请求"
// @Success 200 {object} response.Response[[]vo.PetInviteResp] "成功获取邀请分页数据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /marketing-app/awen/pet-invite/page [post]
func (c PetInviteController) Page(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.PetInvitePageReq](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	page, total, err := c.services.Page(nil, cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, page, int(total))
}

// Export 导出邀请
// @Summary 导出邀请
// @Description 导出邀请数据
// @Tags 贵族裂变活动-邀请
// @Accept json
// @Produce json
// @Param page body vo.PetInvitePageReq true "导出请求"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /marketing-app/awen/pet-invite/export [post]
func (c PetInviteController) Export(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.PetInvitePageReq](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	taskServ := common.TaskListService{}
	var task distribution_vo.TaskList
	par, _ := json.Marshal(cmd)
	task.OperationFileUrl = string(par)
	task.OrgId = cast.ToInt(r.Header.Get("org_id"))
	task.TaskContent = taskEnum.TaskContentPetInviteExport // 110表示导出邀请
	err = taskServ.CreatTask(r, task)
	if err != nil {
		log.Error("导出邀请助力明细：err=" + err.Error())
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}
