package controllers

import (
	jwt "eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/marketing-service/services"
	viewmodel "eShop/view-model"
	marketing_vo "eShop/view-model/marketing-vo"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/spf13/cast"

	"github.com/go-chi/chi/v5"
)

type GiftController struct {
	service services.GiftService
}

func NewGiftController(service services.GiftService) *GiftController {
	return &GiftController{
		service: service,
	}
}

// RegisterRoutes 注册路由
func (c *GiftController) RegisterRoutes(r chi.Router) {
	r.Route("/marketing-app/manager/gift", func(r chi.Router) {
		r.Get("/list", c.GetManagerGiftList)
		r.Get("/detail/{id}", c.GetGiftDetail)
		r.Post("/save", c.SaveGift)
		r.Delete("/{id}", c.DeleteGift)
		r.Put("/{id}", c.EndGift)
	})
}

// @Summary 赠品管理列表接口
// @Tags 后台接口-赠品管理
// @Accept plain
// @Produce json
// @Param GiftListReq query marketing_vo.GiftListReq true " "
// @Success 200 {object} marketing_vo.GiftListRes
// @Failure 400 {object} marketing_vo.GiftListRes
// @Router /marketing-app/manager/gift/list [GET]
func (c *GiftController) GetManagerGiftList(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := marketing_vo.GiftListRes{}
	out.Code = 400

	param, err := utils.Bind[marketing_vo.GiftListReq](request)
	if err != nil {
		log.Errorf("获取赠品列表-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 从 JWT 获取 store_id
	jwtInfo, err := jwt.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	param.StoreId = jwtInfo.TenantId
	if out.Data, out.Total, err = c.service.GetGiftList(&param); err != nil {
		log.Errorf("获取赠品列表失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	if out.Data == nil {
		out.Data = make([]marketing_vo.GiftListItem, 0)
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 保存赠品
// @Tags 后台接口-赠品管理
// @Accept json
// @Produce json
// @Param SaveGiftReq body marketing_vo.SaveGiftReq true "保存赠品请求"
// @Success 200 {object} marketing_vo.SaveGiftRes
// @Failure 400 {object} marketing_vo.SaveGiftRes
// @Router /marketing-app/manager/gift/save [POST]
func (c *GiftController) SaveGift(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := marketing_vo.SaveGiftRes{}
	out.Code = 400
	jwtInfo, err := jwt.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	param, err := utils.Bind[marketing_vo.SaveGiftReq](request)
	if err != nil {
		log.Errorf("保存赠品-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	param.ChainId = cast.ToInt64(jwtInfo.ChainId)
	param.StoreId = jwtInfo.TenantId
	if err = c.service.SaveGift(&param); err != nil {
		log.Errorf("保存赠品失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 删除赠品
// @Tags 后台接口-赠品管理
// @Accept plain
// @Produce json
// @Param id path int true "赠品ID"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /marketing-app/manager/gift/{id} [DELETE]
func (c *GiftController) DeleteGift(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{Code: 400}

	idStr := chi.URLParam(request, "id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		log.Errorf("删除赠品-解析ID参数失败-错误为%s", err.Error())
		out.Message = "无效的赠品ID"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	jwtInfo, err := jwt.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	if err = c.service.DeleteGift(id, jwtInfo.TenantId); err != nil {
		log.Errorf("删除赠品失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "删除成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 结束赠品活动
// @Tags 后台接口-赠品管理
// @Accept plain
// @Produce json
// @Param id path int true "赠品ID"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /marketing-app/manager/gift/{id} [PUT]
func (c *GiftController) EndGift(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{Code: 400}

	idStr := chi.URLParam(request, "id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		log.Errorf("结束赠品活动-解析ID参数失败-错误为%s", err.Error())
		out.Message = "无效的赠品ID"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	jwtInfo, err := jwt.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	if err = c.service.EndGift(id, jwtInfo.TenantId); err != nil {
		log.Errorf("结束赠品活动失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "操作成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 获取赠品详情
// @Tags 后台接口-赠品管理
// @Accept plain
// @Produce json
// @Param id path int true "赠品ID"
// @Success 200 {object} marketing_vo.GiftDetailResp
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /marketing-app/manager/gift/detail/{id} [GET]
func (c *GiftController) GetGiftDetail(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := viewmodel.BaseHttpResponse{Code: 400}

	// 获取并验证ID参数
	idStr := chi.URLParam(request, "id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		log.Errorf("获取赠品详情-解析ID参数失败-错误为%s", err.Error())
		out.Message = "无效的赠品ID"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 从 JWT 获取 store_id
	jwtInfo, err := jwt.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 调用service获取详情
	param := &marketing_vo.GiftDetailReq{
		Id:      id,
		StoreId: jwtInfo.TenantId,
	}

	detail, err := c.service.GetGiftDetail(param)
	if err != nil {
		log.Errorf("获取赠品详情失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 返回成功响应
	resp := struct {
		Code    int                          `json:"code"`
		Message string                       `json:"message"`
		Data    *marketing_vo.GiftDetailResp `json:"data"`
	}{
		Code: 200,
		Data: detail,
	}

	out2, _ := json.Marshal(resp)
	writer.Write(out2)
}
