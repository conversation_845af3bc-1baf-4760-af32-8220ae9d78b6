package controllers

import (
	po "eShop/domain/points-po"
	"eShop/infra/response"
	"eShop/infra/utils"
	service "eShop/services/points-service"
	vo "eShop/view-model/points-vo"
	"net/http"

	"github.com/go-chi/chi"
)

// ClsPointsOrderController 提供了积分订单相关的API接口
type ClsPointsOrderController struct {
	SuperController[int, po.ClsPointsOrder, vo.ClsPointsOrderSaveVO, vo.ClsPointsOrderUpdateVO, vo.ClsPointsOrderQueryVO, vo.ClsPointsOrderResultVO]
	ControllerHooks[int, po.ClsPointsOrder, vo.ClsPointsOrderSaveVO, vo.ClsPointsOrderUpdateVO, vo.ClsPointsOrderQueryVO, vo.ClsPointsOrderResultVO]
	service service.ClsPointsOrderService
}

// NewClsPointsOrderController 创建一个新的 ClsPointsOrderController 实例
func NewClsPointsOrderController() ClsPointsOrderController {
	return ClsPointsOrderController{
		NewSuperController(
			service.NewClsPointsOrderService(),
			&ClsPointsOrderController{},
		),
		NewControllerHooks[int, po.ClsPointsOrder, vo.ClsPointsOrderSaveVO, vo.ClsPointsOrderUpdateVO, vo.ClsPointsOrderQueryVO, vo.ClsPointsOrderResultVO](),
		service.NewClsPointsOrderService(),
	}
}

func (c ClsPointsOrderController) Routes(r chi.Router) {
	c.SuperController.Routes(r)
	r.Post("/deliver", c.Deliver)      // 发货
	r.Post("/express", c.QueryExpress) // 查询物流信息
	r.Post("/finish", c.FinishOrder)   // 自动完成积分兑换订单
	r.Post("/point-out", c.PointOut)   // 兑换订单积分扣减
}

// Deliver 订单发货
// @Summary 订单发货
// @Description 更新订单状态为已发货，并记录快递信息
// @Tags 积分订单
// @Accept json
// @Produce json
// @Param data body vo.ClsPointsOrderUpdateVO true "发货信息"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求参数错误"
// @Router /points-app/cls-points-order/deliver [post]
func (c ClsPointsOrderController) Deliver(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	updateVO, err := utils.Bind[vo.ClsPointsOrderUpdateVO](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	// 执行发货操作
	err = c.service.Deliver(nil, updateVO)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.Success(w)
}

// QueryExpress 查询物流信息
// @Summary 查询物流信息
// @Description 根据订单ID查询物流轨迹信息
// @Tags 积分订单
// @Accept json
// @Produce json
// @Param data body vo.IdVO[int] true "订单ID"
// @Success 200 {object} response.Response[utils.ExpressInfo] "成功"
// @Failure 400 {object} response.BaseResp "请求参数错误"
// @Router /points-app/cls-points-order/express [post]
func (c ClsPointsOrderController) QueryExpress(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	idVO, err := utils.Bind[vo.IdVO[int]](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	// 查询物流信息
	result, err := c.service.QueryExpress(nil, idVO.Id)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.SuccessWithData(w, result)
}

func (c ClsPointsOrderController) FinishOrder(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	queryVO, err := utils.Bind[vo.ClsPointsOrderQueryVO](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	err = c.service.FinishOrder(nil, queryVO.ExpressTime)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.Success(w)
}

func (c ClsPointsOrderController) PointOut(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	idVO, err := utils.Bind[vo.IdVO[int]](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	err = c.service.PointOut(nil, idVO.Id)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.Success(w)
}
