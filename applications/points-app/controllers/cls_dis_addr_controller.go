package controllers

import (
	po "eShop/domain/points-po"
	service "eShop/services/points-service"
	vo "eShop/view-model/points-vo"

	"github.com/go-chi/chi"
)

type ClsDisAddrController struct {
	SuperController[int, po.ClsDisAddr, vo.ClsDisAddrSaveVO, vo.ClsDisAddrUpdateVO, vo.ClsDisAddrQueryVO, vo.ClsDisAddrResultVO]
	ControllerHooks[int, po.ClsDisAddr, vo.ClsDisAddrSaveVO, vo.ClsDisAddrUpdateVO, vo.ClsDisAddrQueryVO, vo.ClsDisAddrResultVO]
	service service.ClsDisAddrService
}

func NewClsDisAdrrController() ClsDisAddrController {
	return ClsDisAddrController{
		NewSuperController(
			service.NewClsDisAddrService(),
			&ClsDisAddrController{},
		),
		NewControllerHooks[int, po.ClsDisAddr, vo.ClsDisAddrSaveVO, vo.ClsDisAddrUpdateVO, vo.ClsDisAddrQueryVO, vo.ClsDisAddrResultVO](),
		service.NewClsDisAddrService(),
	}
}

func (c ClsDisAddrController) Routes(r chi.Router) {
	c.SuperController.Routes(r)
}
