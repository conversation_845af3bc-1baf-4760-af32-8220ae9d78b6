package controllers

import (
	"github.com/go-chi/chi"
)

func InitAllApi(r chi.Router) {
	r.Route("/points-app", func(r chi.Router) {
		clsDisAdrrCtrl := NewClsDisAdrrController()
		r.Route("/cls-dis-addr", func(r chi.Router) {
			clsDisAdrrCtrl.Routes(r)
		})

		clsPointsGoodsCtrl := NewClsPointsGoodsController()
		r.Route("/cls-points-goods", func(r chi.Router) {
			clsPointsGoodsCtrl.Routes(r)
		})

		clsPointsRuleCtrl := NewClsPointsRuleController()
		r.Route("/cls-points-rule", func(r chi.Router) {
			clsPointsRuleCtrl.Routes(r)
		})

		clsPointsRuleLogCtrl := NewClsPointsRuleLogController()
		r.Route("/cls-points-rule-log", func(r chi.Router) {
			clsPointsRuleLogCtrl.Routes(r)
		})

		clsPointsOrderCtrl := NewClsPointsOrderController()
		r.Route("/cls-points-order", func(r chi.Router) {
			clsPointsOrderCtrl.Routes(r)
		})

		clsPointsOrderLogCtrl := NewClsPointsOrderLogController()
		r.Route("/cls-points-order-log", func(r chi.Router) {
			clsPointsOrderLogCtrl.Routes(r)
		})

		clsPointsAccountCtrl := NewClsPointsAccountController()
		r.Route("/cls-points-account", func(r chi.Router) {
			clsPointsAccountCtrl.Routes(r)
		})

		clsPointsFlowCtrl := NewClsPointsFlowController()
		r.Route("/cls-points-flow", func(r chi.Router) {
			clsPointsFlowCtrl.Routes(r)
		})

		clsPointsDailyStatsCtrl := NewClsPointsDailyStatsController()
		r.Route("/cls-points-stats", func(r chi.Router) {
			clsPointsDailyStatsCtrl.Routes(r)
		})
	})
}
