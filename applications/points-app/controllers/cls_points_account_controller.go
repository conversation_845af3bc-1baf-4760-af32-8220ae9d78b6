package controllers

import (
	po "eShop/domain/points-po"
	"eShop/infra/response"
	"eShop/infra/utils"
	service "eShop/services/points-service"
	vo "eShop/view-model/points-vo"
	"net/http"

	"github.com/go-chi/chi"
)

// ClsPointsAccountController 提供了积分账户相关的API接口
type ClsPointsAccountController struct {
	SuperController[int, po.ClsPointsAccount, vo.ClsPointsAccountSaveVO, vo.ClsPointsAccountUpdateVO, vo.ClsPointsAccountQueryVO, vo.ClsPointsAccountResultVO]
	ControllerHooks[int, po.ClsPointsAccount, vo.ClsPointsAccountSaveVO, vo.ClsPointsAccountUpdateVO, vo.ClsPointsAccountQueryVO, vo.ClsPointsAccountResultVO]
	service service.ClsPointsAccountService
}

// NewClsPointsAccountController 创建一个新的 ClsPointsAccountController 实例
func NewClsPointsAccountController() ClsPointsAccountController {
	return ClsPointsAccountController{
		NewSuperController(
			service.NewClsPointsAccountService(),
			&ClsPointsAccountController{},
		),
		NewControllerHooks[int, po.ClsPointsAccount, vo.ClsPointsAccountSaveVO, vo.ClsPointsAccountUpdateVO, vo.ClsPointsAccountQueryVO, vo.ClsPointsAccountResultVO](),
		service.NewClsPointsAccountService(),
	}
}

// Routes 定义并注册与积分账户相关的路由
func (c ClsPointsAccountController) Routes(r chi.Router) {
	c.SuperController.Routes(r)
	// 查询积分账户
	r.Get("/list-accounts", c.ListAccounts)
	// 赠送积分
	r.Post("/give-points", c.GivePoints)

	// 以下的接口是工具接口
	r.Post("/expire-alert", c.ExpireAlert)
	r.Post("/batch-update", c.BatchUpdate)
	r.Post("/register-worker", c.RegisterWorker)
	// r.Post("/encrypt-mobile", c.EncryptMobile)
}

// ListAccounts 获取积分账户列表
// @Summary 获取积分账户列表
// @Description 获取积分账户列表
// @Tags 积分账户
// @Accept json
// @Produce json
// @Param queryVO body vo.ClsPointsAccountQueryVO true "查询参数"
// @Success 200 {object} response.Response[[]vo.ClsPointsAccountResultVO] "成功"
// @Failure 400 {object} response.BaseResp "参数错误"
// @Router /points-app/cls-points-account/list-accounts [get]
func (c ClsPointsAccountController) ListAccounts(w http.ResponseWriter, r *http.Request) {
	req, err := utils.Bind[vo.ClsPointsAccountQueryVO](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	results, total, err := c.service.ListAccounts(nil, req)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, results, int(total))
}

// GivePoints 赠送积分
// @Summary 赠送积分
// @Description 赠送积分
// @Tags 积分账户
// @Accept json
// @Produce json
// @Param saveVO body vo.ClsPointsGiveVO true "赠送积分参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "参数错误"
// @Router /points-app/cls-points-account/give-points [post]
func (c ClsPointsAccountController) GivePoints(w http.ResponseWriter, r *http.Request) {
	giveVO, err := utils.Bind[vo.ClsPointsGiveVO](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	err = c.service.GivePoints(nil, giveVO)
	if err != nil {
		response.BadRequest(w, err.Error())
	}

	response.Success(w)
}

func (c ClsPointsAccountController) ExpireAlert(w http.ResponseWriter, r *http.Request) {
	idVO, err := utils.Bind[vo.IdVO[int]](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	operateType := idVO.Id
	if operateType == 1 {
		err = c.service.PointsExpireAlert(nil)
	} else if operateType == 2 {
		err = c.service.GivenPointsExpireAlert(nil)
	}
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}

func (c ClsPointsAccountController) BatchUpdate(w http.ResponseWriter, r *http.Request) {
	req, err := utils.Bind[vo.ClsPointsAccountBatchUpdateVO](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	updAccount := make(map[int]int)
	for _, item := range req.SaveVO {
		updAccount[item.DisId] = item.EnterpriseId
	}
	err = c.service.BatchUpdateByDisIds(nil, updAccount, "", 0)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}

func (c ClsPointsAccountController) RegisterWorker(w http.ResponseWriter, r *http.Request) {
	vo, err := utils.Bind[vo.RegisterClsWorkerVO](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	err = c.service.RegisterClsWorker(nil, vo.StaffNo)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}

// func (c ClsPointsAccountController) EncryptMobile(w http.ResponseWriter, r *http.Request) {
// 	err := c.service.EncryptMobile(nil)
// 	if err != nil {
// 		response.BadRequest(w, err.Error())
// 		return
// 	}
// 	response.Success(w)
// }
