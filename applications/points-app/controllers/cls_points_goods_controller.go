package controllers

import (
	po "eShop/domain/points-po"
	service "eShop/services/points-service"
	vo "eShop/view-model/points-vo"

	"github.com/go-chi/chi"
)

// ClsPointsGoodsController 封装了积分商品相关的HTTP处理逻辑
type ClsPointsGoodsController struct {
	SuperController[int, po.ClsPointsGoods, vo.ClsPointsGoodsSaveVO, vo.ClsPointsGoodsUpdateVO, vo.ClsPointsGoodsQueryVO, vo.ClsPointsGoodsResultVO]
	// 可以嵌入 ControllerHooks 以便重写特定的Controller级别钩子，如果需要的话
	ControllerHooks[int, po.ClsPointsGoods, vo.ClsPointsGoodsSaveVO, vo.ClsPointsGoodsUpdateVO, vo.ClsPointsGoodsQueryVO, vo.ClsPointsGoodsResultVO]
	service service.ClsPointsGoodsService
}

// NewClsPointsGoodsController 创建一个新的 ClsPointsGoodsController 实例
func NewClsPointsGoodsController() ClsPointsGoodsController {
	return ClsPointsGoodsController{
		NewSuperController(
			service.NewClsPointsGoodsService(),
			&ClsPointsGoodsController{},
		),
		// 如果ClsPointsGoodsController要实现ControllerHooks，则在这里初始化
		NewControllerHooks[int, po.ClsPointsGoods, vo.ClsPointsGoodsSaveVO, vo.ClsPointsGoodsUpdateVO, vo.ClsPointsGoodsQueryVO, vo.ClsPointsGoodsResultVO](),
		service.NewClsPointsGoodsService(),
	}
}

// Routes 为积分商品相关的API定义路由
func (c ClsPointsGoodsController) Routes(r chi.Router) {
	c.SuperController.Routes(r) // 使用SuperController中定义的标准CRUD路由
	// 如果需要添加特定于积分商品的非CRUD路由，可以在这里添加
	// 例: r.Post("/points-goods/{id}/publish", c.PublishPointsGood)
}
