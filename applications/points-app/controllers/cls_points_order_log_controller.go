package controllers

import (
	po "eShop/domain/points-po"
	service "eShop/services/points-service"
	vo "eShop/view-model/points-vo"

	"github.com/go-chi/chi"
)

// ClsPointsOrderLogController 提供了订单日志相关的API接口
type ClsPointsOrderLogController struct {
	SuperController[int, po.ClsPointsOrderLog, vo.ClsPointsOrderLogSaveVO, vo.ClsPointsOrderLogUpdateVO, vo.ClsPointsOrderLogQueryVO, vo.ClsPointsOrderLogResultVO]
	ControllerHooks[int, po.ClsPointsOrderLog, vo.ClsPointsOrderLogSaveVO, vo.ClsPointsOrderLogUpdateVO, vo.ClsPointsOrderLogQueryVO, vo.ClsPointsOrderLogResultVO]
	service service.ClsPointsOrderLogService
}

// NewClsPointsOrderLogController 创建一个新的 ClsPointsOrderLogController 实例
func NewClsPointsOrderLogController() ClsPointsOrderLogController {
	return ClsPointsOrderLogController{
		NewSuperController(
			service.NewClsPointsOrderLogService(),
			&ClsPointsOrderLogController{},
		),
		NewControllerHooks[int, po.ClsPointsOrderLog, vo.ClsPointsOrderLogSaveVO, vo.ClsPointsOrderLogUpdateVO, vo.ClsPointsOrderLogQueryVO, vo.ClsPointsOrderLogResultVO](),
		service.NewClsPointsOrderLogService(),
	}
}

func (c ClsPointsOrderLogController) Routes(r chi.Router) {
	c.SuperController.Routes(r)
}
