package controllers

import (
	"eShop/infra/utils"
	"eShop/services/demo-service/account"
	"eShop/view-model"
	"encoding/json"
	"net/http"

	"github.com/go-chi/chi/v5"
)

type AccountController struct {
	service account.AccountService
}

// NewAccountController 创建账户处理器
func NewAccountController(service account.AccountService) *AccountController {
	return &AccountController{
		service: service,
	}
}

// RegisterRoutes 注册路由
func (h *AccountController) RegisterRoutes(r chi.Router) {
	r.Route("/demo-app/api", func(r chi.Router) {
		r.Post("/create", h.Create)
		r.Post("/withdraw", h.Withdraw)
		r.Post("/deposit", h.Deposit)
		r.Post("/transfer", h.Transfer)
		r.Get("/test/hi", func(w http.ResponseWriter, r *http.Request) {
			w.Write([]byte("11111"))
		})
	})

	r.Route("/demo-app/manager", func(r chi.Router) {
		r.Get("/query", h.Query)
	})
}

func (h *AccountController) Create(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	var cmd account.CreateCommand
	cmd, err := utils.Bind[account.CreateCommand](request)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	if err := h.service.Create(request.Context(), cmd); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else {
		out.Code = 200
		out.Message = "创建成功"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
}

func (h *AccountController) Withdraw(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	var cmd account.WithdrawCommand
	cmd, err := utils.Bind[account.WithdrawCommand](request)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	if err := h.service.Withdraw(request.Context(), cmd); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else {
		out.Code = 200
		out.Message = "提现成功"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
}

func (h *AccountController) Deposit(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	var cmd account.DepositCommand
	cmd, err := utils.Bind[account.DepositCommand](request)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	if err := h.service.Deposit(request.Context(), cmd); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else {
		out.Code = 200
		out.Message = "充值成功"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
}

func (h *AccountController) Transfer(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	var cmd account.TransferCommand
	cmd, err := utils.Bind[account.TransferCommand](request)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	err = h.service.Transfer(request.Context(), cmd)
	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	} else {
		out.Code = 200
		out.Message = "转账成功"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
}

func (h *AccountController) Query(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BasePageHttpResponse{}
	out.Code = 400
	utils.Bind[account.QueryParams](request)
}
