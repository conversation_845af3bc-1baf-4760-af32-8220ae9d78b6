package main

import (
	"context"
	"eShop/applications/marketing-app/controllers"
	"eShop/infra/config"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/services/marketing-service/services"
	marketing_vo "eShop/view-model/marketing-vo"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/BurntSushi/toml"
	"github.com/go-chi/chi/v5"
	httpSwagger "github.com/swaggo/http-swagger"
)

func service() http.Handler {
	r := chi.NewRouter()

	// 添加CORS中间件
	r.Use(func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			w.<PERSON><PERSON>().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
			
			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}
			
			next.ServeHTTP(w, r)
		})
	})

	// 简化的监测数据API
	r.Route("/marketing-app/awen/manager", func(r chi.Router) {
		r.Get("/pet_stat/list", func(w http.ResponseWriter, r *http.Request) {
			// 模拟监测数据
			mockData := []marketing_vo.PetStatListItem{
				{
					DaliyDate: "2024-08-15",
					Metric1:   120, // 阿闻首页弹窗点击uv
					Metric2:   85,  // 阿闻首页banner点击uv
					Metric3:   95,  // 阿闻首页浮标点击uv
					Metric4:   78,  // 小闻首页广告点击uv
					Metric5:   156, // 贵族首页弹窗点击uv
					Metric6:   134, // 贵族首页banner点击uv
					Metric7:   2340, // 贵族活动主页访问的pv
					Metric8:   1890, // 贵族活动主页访问uv
					Metric9:   1567, // 作品助力页面访问pv
					Metric10:  1234, // 作品助力页面访问uv
					Metric11:  89,   // 生成贵族创作图的用户数
					Metric12:  67,   // 分享的用户数
					Metric13:  234,  // 贵族宠物图分享总次数
					Metric14:  456,  // 好友助力总次数
					Metric15:  23,   // 达到5票的用户数
					Metric16:  8,    // 达到25票的用户数
				},
				{
					DaliyDate: "2024-08-14",
					Metric1:   110,
					Metric2:   92,
					Metric3:   88,
					Metric4:   71,
					Metric5:   145,
					Metric6:   128,
					Metric7:   2180,
					Metric8:   1756,
					Metric9:   1423,
					Metric10:  1156,
					Metric11:  82,
					Metric12:  61,
					Metric13:  218,
					Metric14:  423,
					Metric15:  19,
					Metric16:  6,
				},
				{
					DaliyDate: "2024-08-13",
					Metric1:   98,
					Metric2:   76,
					Metric3:   82,
					Metric4:   65,
					Metric5:   132,
					Metric6:   115,
					Metric7:   1980,
					Metric8:   1623,
					Metric9:   1298,
					Metric10:  1045,
					Metric11:  75,
					Metric12:  58,
					Metric13:  195,
					Metric14:  387,
					Metric15:  17,
					Metric16:  5,
				},
			}

			result := marketing_vo.PetStatListRes{
				Code:    200,
				Message: "success",
				Data:    mockData,
				Total:   len(mockData),
			}

			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(result)
		})
	})

	// 如果有数据库连接，使用真实的服务
	if config.Get("mysql.eshop") != "" {
		// 注册真实的监测数据服务
		petActivitySvc := services.PetActivityService{}
		petActivityHandle := controllers.NewPetActivityController(petActivitySvc)
		petActivityHandle.RegisterRoutes(r)
	}

	// 健康检查接口
	r.Get("/health", func(w http.ResponseWriter, r *http.Request) {
		response.SuccessWithData(w, map[string]interface{}{
			"status":    "ok",
			"timestamp": time.Now().Unix(),
			"service":   "marketing-app",
		})
	})

	// API文档
	r.Mount("/swagger", httpSwagger.WrapHandler)
	
	return r
}

func main() {
	// 加载配置
	toml.DecodeFile("appsetting.toml", &config.LocalSetting)
	
	// 初始化日志
	log.Init()

	fmt.Println("=== 监测数据服务启动 ===")
	fmt.Println("marketing-app 启动成功! 端口 8153")
	fmt.Println("监测数据API: http://localhost:8153/marketing-app/awen/manager/pet_stat/list")
	fmt.Println("健康检查API: http://localhost:8153/health")
	fmt.Println("API文档: http://localhost:8153/swagger/")

	server := &http.Server{Addr: "0.0.0.0:8153", Handler: service()}
	serverCtx, serverStopCtx := context.WithCancel(context.Background())

	// 优雅关闭
	sig := make(chan os.Signal, 1)
	signal.Notify(sig, syscall.SIGHUP, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
	go func() {
		<-sig
		fmt.Println("\n正在关闭服务...")
		shutdownCtx, _ := context.WithTimeout(serverCtx, 30*time.Second)
		go func() {
			<-shutdownCtx.Done()
			if shutdownCtx.Err() == context.DeadlineExceeded {
				log.Error("强制退出")
			}
			serverStopCtx()
		}()
		server.Shutdown(shutdownCtx)
	}()

	err := server.ListenAndServe()
	if err != nil && err != http.ErrServerClosed {
		log.Error(err)
	}
	<-serverCtx.Done()
}
