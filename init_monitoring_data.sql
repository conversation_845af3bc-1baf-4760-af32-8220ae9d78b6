-- 创建数据库和表结构
CREATE DATABASE IF NOT EXISTS eshop DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE eshop;

-- 创建监测数据统计表
CREATE TABLE IF NOT EXISTS pet_stat (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    daliy_date VARCHAR(10) NOT NULL COMMENT '日期',
    metric_type INT NOT NULL COMMENT '指标编码',
    num INT NOT NULL DEFAULT 0 COMMENT '事件数值',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_date_metric (daliy_date, metric_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据监测统计表';

-- 创建监测数据事件流水表
CREATE TABLE IF NOT EXISTS pet_stat_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    metric_type INT NOT NULL COMMENT '指标类型',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    work_code VARCHAR(50) DEFAULT '' COMMENT '作品编号',
    user_id VARCHAR(50) DEFAULT '' COMMENT '用户ID',
    unique_id VARCHAR(100) DEFAULT '' COMMENT '唯一ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_metric_type (metric_type),
    INDEX idx_user_id (user_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据监测事件流水表';

-- 插入测试数据
INSERT INTO pet_stat (daliy_date, metric_type, num) VALUES
-- 2024-08-15 的数据
('2024-08-15', 1, 120),  -- 阿闻首页弹窗点击uv
('2024-08-15', 2, 85),   -- 阿闻首页banner点击uv
('2024-08-15', 3, 95),   -- 阿闻首页浮标点击uv
('2024-08-15', 4, 78),   -- 小闻首页广告点击uv
('2024-08-15', 5, 156),  -- 贵族首页弹窗点击uv
('2024-08-15', 6, 134),  -- 贵族首页banner点击uv
('2024-08-15', 7, 2340), -- 贵族活动主页访问的pv
('2024-08-15', 8, 1890), -- 贵族活动主页访问uv
('2024-08-15', 9, 1567), -- 作品助力页面访问pv
('2024-08-15', 10, 1234), -- 作品助力页面访问uv
('2024-08-15', 11, 89),  -- 生成贵族创作图的用户数
('2024-08-15', 12, 67),  -- 分享的用户数
('2024-08-15', 13, 234), -- 贵族宠物图分享总次数
('2024-08-15', 14, 456), -- 好友助力总次数
('2024-08-15', 15, 23),  -- 达到5票的用户数
('2024-08-15', 16, 8),   -- 达到25票的用户数

-- 2024-08-14 的数据
('2024-08-14', 1, 110),
('2024-08-14', 2, 92),
('2024-08-14', 3, 88),
('2024-08-14', 4, 71),
('2024-08-14', 5, 145),
('2024-08-14', 6, 128),
('2024-08-14', 7, 2180),
('2024-08-14', 8, 1756),
('2024-08-14', 9, 1423),
('2024-08-14', 10, 1156),
('2024-08-14', 11, 82),
('2024-08-14', 12, 61),
('2024-08-14', 13, 218),
('2024-08-14', 14, 423),
('2024-08-14', 15, 19),
('2024-08-14', 16, 6),

-- 2024-08-13 的数据
('2024-08-13', 1, 98),
('2024-08-13', 2, 76),
('2024-08-13', 3, 82),
('2024-08-13', 4, 65),
('2024-08-13', 5, 132),
('2024-08-13', 6, 115),
('2024-08-13', 7, 1980),
('2024-08-13', 8, 1623),
('2024-08-13', 9, 1298),
('2024-08-13', 10, 1045),
('2024-08-13', 11, 75),
('2024-08-13', 12, 58),
('2024-08-13', 13, 195),
('2024-08-13', 14, 387),
('2024-08-13', 15, 17),
('2024-08-13', 16, 5),

-- 2024-08-12 的数据
('2024-08-12', 1, 105),
('2024-08-12', 2, 89),
('2024-08-12', 3, 91),
('2024-08-12', 4, 73),
('2024-08-12', 5, 149),
('2024-08-12', 6, 131),
('2024-08-12', 7, 2156),
('2024-08-12', 8, 1734),
('2024-08-12', 9, 1445),
('2024-08-12', 10, 1178),
('2024-08-12', 11, 86),
('2024-08-12', 12, 64),
('2024-08-12', 13, 225),
('2024-08-12', 14, 441),
('2024-08-12', 15, 21),
('2024-08-12', 16, 7);

-- 插入一些流水记录示例
INSERT INTO pet_stat_log (metric_type, metric_name, work_code, user_id, unique_id) VALUES
(1, '阿闻首页弹窗点击uv', 'WORK001', 'USER001', 'UNIQUE001'),
(2, '阿闻首页banner点击uv', 'WORK002', 'USER002', 'UNIQUE002'),
(3, '阿闻首页浮标点击uv', 'WORK003', 'USER003', 'UNIQUE003'),
(11, '生成贵族创作图的用户数', 'WORK004', 'USER004', 'UNIQUE004'),
(12, '分享的用户数', 'WORK005', 'USER005', 'UNIQUE005');

-- 查询验证数据
SELECT 
    daliy_date,
    COUNT(*) as metric_count,
    SUM(num) as total_events
FROM pet_stat 
GROUP BY daliy_date 
ORDER BY daliy_date DESC;

-- 查询最新的监测数据
SELECT 
    daliy_date,
    MAX(CASE WHEN metric_type = 1 THEN num ELSE 0 END) AS metric1,
    MAX(CASE WHEN metric_type = 2 THEN num ELSE 0 END) AS metric2,
    MAX(CASE WHEN metric_type = 3 THEN num ELSE 0 END) AS metric3,
    MAX(CASE WHEN metric_type = 4 THEN num ELSE 0 END) AS metric4,
    MAX(CASE WHEN metric_type = 5 THEN num ELSE 0 END) AS metric5,
    MAX(CASE WHEN metric_type = 6 THEN num ELSE 0 END) AS metric6,
    MAX(CASE WHEN metric_type = 7 THEN num ELSE 0 END) AS metric7,
    MAX(CASE WHEN metric_type = 8 THEN num ELSE 0 END) AS metric8,
    MAX(CASE WHEN metric_type = 9 THEN num ELSE 0 END) AS metric9,
    MAX(CASE WHEN metric_type = 10 THEN num ELSE 0 END) AS metric10,
    MAX(CASE WHEN metric_type = 11 THEN num ELSE 0 END) AS metric11,
    MAX(CASE WHEN metric_type = 12 THEN num ELSE 0 END) AS metric12,
    MAX(CASE WHEN metric_type = 13 THEN num ELSE 0 END) AS metric13,
    MAX(CASE WHEN metric_type = 14 THEN num ELSE 0 END) AS metric14,
    MAX(CASE WHEN metric_type = 15 THEN num ELSE 0 END) AS metric15,
    MAX(CASE WHEN metric_type = 16 THEN num ELSE 0 END) AS metric16
FROM pet_stat
GROUP BY daliy_date
ORDER BY daliy_date DESC
LIMIT 5;
