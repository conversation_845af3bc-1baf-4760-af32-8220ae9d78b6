package blky_po

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"fmt"
	"time"

	"xorm.io/xorm"
)

// VerifyRecord 防伪码验证记录领域模型
type VerifyRecord struct {
	Id int64 `xorm:"pk autoincr bigint 'id'" json:"id"`
	// 公司名称
	CompanyName string `xorm:"varchar(100) 'company_name'" json:"company_name"`
	// 防伪码
	Code string `xorm:"varchar(50) 'code'" json:"code"`
	// 验证时间
	VerifyTime time.Time `xorm:"datetime 'verify_time'" json:"verify_time"`
	// 用户Openid
	UserOpenid string `xorm:"varchar(50) 'user_openid'" json:"user_openid"`
	// 用户ID
	UserId string `xorm:"varchar(50) 'user_id'" json:"user_id"`
	// 用户手机号
	UserPhone string `xorm:"varchar(20) 'user_phone'" json:"user_phone"`
	// 加密用户手机号
	EncryptUserPhone string `xorm:"varchar(20) 'encrypt_user_phone'" json:"encrypt_user_phone"`
	// 验证方式 1: 手动输入 2: 扫码查询
	VerifyType int `xorm:"int 'verify_type'" json:"verify_type"`
	// 验证IP
	VerifyIp string `xorm:"varchar(255) 'verify_ip'" json:"verify_ip"`
	// 验证描述 1-首页-扫一扫,2-微信扫一扫,3-个人中心查验真伪
	Source int `xorm:"tinyint 'source'" json:"source"`
	// 验证所在地
	VerifyLocation string `xorm:"varchar(255) 'verify_location'" json:"verify_location"`
	// 验证入口
	VerifyEntry string `xorm:"varchar(50) 'verify_entry'" json:"verify_entry"`
	// 创建时间
	CreatedAt time.Time `xorm:"datetime created 'created_at'" json:"created_at"`
	// 更新时间
	UpdatedAt time.Time `xorm:"datetime updated 'updated_at'" json:"updated_at"`
}

// TableName 表名
func (v *VerifyRecord) TableName() string {
	return "eshop.verify_record"
}

// 拓展字段
type VerifyRecordExt struct {
	VerifyRecord `xorm:"extends"`
	// 验证次数
	VerifyCount int `xorm:"verify_count"`
	// 验证描述
	SourceName string `xorm:"source_name -"`
}

// GetVerifyStats 获取验证统计数据
func (v *VerifyRecord) GetVerifyStats(engine *xorm.Engine, companyName, code string, pageIndex, pageSize int) ([]VerifyRecordExt, int64, error) {
	whereSql := v.buildWhereSql(companyName, code)

	// 统计视图SQL
	sql := `
        SELECT 
            MIN(id) as id,
            company_name,
            code,
            COUNT(*) as verify_count,
            MAX(verify_time) as verify_time
        FROM eshop.verify_record 
        WHERE %s 
        GROUP BY code
    `

	var total int64
	_, err := engine.SQL(fmt.Sprintf("SELECT COUNT(1) FROM (%s) t", fmt.Sprintf(sql, whereSql))).Get(&total)
	if err != nil {
		log.Error("查询防伪码统计总数失败:", err.Error())
		return nil, 0, err
	}

	var records []VerifyRecordExt
	start := (pageIndex - 1) * pageSize
	err = engine.SQL(fmt.Sprintf(sql+" LIMIT %d, %d", whereSql, start, pageSize)).Find(&records)

	if err != nil {
		log.Error("查询防伪码统计列表失败:", err.Error())
		return nil, 0, err
	}

	return records, total, nil
}

// GetVerifyDetails 获取验证明细数据
func (v *VerifyRecord) GetVerifyDetails(engine *xorm.Engine, companyName, code string, pageIndex, pageSize int) ([]VerifyRecordExt, int64, error) {
	whereSql := v.buildWhereSql(companyName, code)

	var records []VerifyRecordExt
	start := (pageIndex - 1) * pageSize

	// 使用Table方法替代原生SQL
	total, err := engine.Table("eshop.verify_record").
		Select("*, case source when 1 then '首页-扫一扫' when 2 then '微信扫一扫' when 3 then '个人中心查验真伪' end as source_name,1 as verify_count").
		Where(whereSql).
		OrderBy("verify_time DESC").
		Limit(pageSize, start).
		FindAndCount(&records)

	if err != nil {
		log.Error("查询防伪码明细列表失败:", err.Error())
		return nil, 0, err
	}

	return records, total, nil
}

// buildWhereSql 构建查询条件
func (v *VerifyRecord) buildWhereSql(companyName, code string) string {
	whereSql := "1=1"
	if len(companyName) > 0 {
		whereSql += fmt.Sprintf(" AND company_name = '%s'", companyName)
	}
	if len(code) > 0 {
		whereSql += fmt.Sprintf(" AND code = '%s'", code)
	}
	return whereSql
}

// UpdateUserInfoByOpenId 通过OpenId批量更新用户信息
func (v *VerifyRecord) UpdateUserInfoByOpenId(engine *xorm.Engine, openId, userId, mobile string) (updatedCount int64, err error) {
	if openId == "" || userId == "" {
		return 0, fmt.Errorf("OpenId和UserId不能为空")
	}

	// 查询所有该open_id但user_id为空的记录
	var emptyUserIdRecords []VerifyRecord
	err = engine.Table(v.TableName()).
		Where("user_openid = ? AND user_id = ''", openId).
		Find(&emptyUserIdRecords)

	if err != nil {
		log.Errorf("查询open_id=%s的空user_id记录失败: %v", openId, err)
		return 0, err
	}

	// 如果没有需要更新的记录，直接返回
	if len(emptyUserIdRecords) == 0 {
		return 0, nil
	}

	// 批量更新这些记录的user_id和手机号相关信息
	updatedCount, err = engine.Table(v.TableName()).
		Where("user_openid = ? AND user_id = ''", openId).
		Update(map[string]interface{}{
			"user_id":            userId,
			"user_phone":         utils.AddStar(mobile),
			"encrypt_user_phone": utils.MobileEncrypt(mobile),
		})

	if err != nil {
		log.Errorf("批量更新user_id失败: %v", err)
		return 0, err
	}

	log.Infof("成功更新%d条记录的user_id为%s", updatedCount, userId)
	return updatedCount, nil
}
