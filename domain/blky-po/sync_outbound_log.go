package blky_po

import (
	"time"

	"xorm.io/xorm"
)

// SyncOutboundLog 北京世纪安诺物流码明细表领域模型
type SyncOutboundLog struct {
	// 自增主键
	Id int `xorm:"pk autoincr not null 'id'" json:"id"`
	// 出库单号
	BillNo string `xorm:"not null VARCHAR(50) 'bill_no'" json:"bill_no"`
	// 原物流码/箱码
	SourceBarcode string `xorm:"not null VARCHAR(100) 'source_barcode'" json:"source_barcode"`
	// 物流码
	Barcode string `xorm:"not null VARCHAR(100) 'barcode'" json:"barcode"`
	// 产品编号
	PNo string `xorm:"not null VARCHAR(50) 'p_no'" json:"p_no"`
	// 扫码时间
	InDate time.Time `xorm:"not null datetime 'in_date'" json:"in_date"`
	// 是否可用 0-可用 1-不可用
	Status int `xorm:"not null default 0 'status'" json:"status"`
	// 创建时间
	CreateTime time.Time `xorm:"datetime created 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime time.Time `xorm:"datetime updated 'update_time'" json:"update_time"`
}

// TableName 表名
func (s *SyncOutboundLog) TableName() string {
	return "blky.sync_outbound_log"
}

// UpdateLatestRecord 更新最新的一条记录状态
func (s *SyncOutboundLog) UpdateLatestRecord(session *xorm.Session, barcode string, beforeTime time.Time) (bool, error) {
	sql := `UPDATE blky.sync_outbound_log 
			SET status = 1
			WHERE (source_barcode = ?  or barcode = ?)
			AND status = 0 
			AND in_date < ?`

	result, err := session.Exec(sql, barcode, barcode, beforeTime)
	if err != nil {
		return false, err
	}

	affected, err := result.RowsAffected()
	if err != nil {
		return false, err
	}

	return affected > 0, nil
}

// UpdateStatusBySourceBarcode 更新出库记录状态
func (s *SyncOutboundLog) UpdateStatusBySourceBarcode(session *xorm.Session, sourceBarcode string, inDate time.Time) (bool, error) {
	result, err := session.Exec(
		"UPDATE blky.sjan_outbound_log SET status = 1, update_time = NOW() "+
			"WHERE source_barcode = ? AND status = 0 AND in_date < ? ORDER BY in_date DESC LIMIT 1",
		sourceBarcode, inDate)
	if err != nil {
		return false, err
	}
	affected, err := result.RowsAffected()
	return affected > 0, err
}
