package blky_po

import (
	"time"

	"xorm.io/xorm"
)

// Xkucun 商品对应物流码信息
type Xkucun struct {
	Iid        int       `json:"iid" xorm:"pk autoincr not null 'iid'"`          // 自增主键
	Swlm       string    `json:"swlm" xorm:"not null unique VARCHAR(50) 'swlm'"` // 商品物流码
	Sspmc      string    `json:"sspmc" xorm:"not null VARCHAR(200) 'sspmc'"`     // 商品名称
	Spici      string    `json:"spici" xorm:"VARCHAR(50) 'spici'"`               // 批次
	Sdxtm      string    `json:"sdxtm" xorm:"VARCHAR(50) 'sdxtm'"`               // 箱码
	Dregtime   time.Time `json:"dregtime" xorm:"datetime not null 'dregtime'"`   // 注册时间
	Drktime    time.Time `json:"drktime" xorm:"datetime 'drktime'"`              // 入库时间
	Dcktime    time.Time `json:"dcktime" xorm:"datetime 'dcktime'"`              // 出库时间
	Izt        int       `json:"izt" xorm:"not null default 0 'izt'"`            // 状态
	Saddman    string    `json:"saddman" xorm:"VARCHAR(50) 'saddman'"`           // 添加人
	Sfwm       string    `json:"sfwm" xorm:"unique VARCHAR(50) 'sfwm'"`          // 防伪码
	Sddbh      string    `json:"sddbh" xorm:"VARCHAR(50) 'sddbh'"`               // 订单编号
	Dbdatetime time.Time `json:"dbdatetime" xorm:"datetime 'dbdatetime'"`        // 更新时间
}

// TableName 表名
func (x *Xkucun) TableName() string {
	return "blky.xkucun"
}

// GetByWlm 根据商品物流码获取记录
func (x *Xkucun) GetByWlm(session *xorm.Session, wlm string) (*Xkucun, error) {
	var item Xkucun
	has, err := session.Where("swlm = ?", wlm).Get(&item)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &item, nil
}

// GetByFwm 根据防伪码获取记录
func (x *Xkucun) GetByFwm(session *xorm.Session, fwm string) (*Xkucun, error) {
	var item Xkucun
	has, err := session.Where("sfwm = ?", fwm).Get(&item)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &item, nil
}

