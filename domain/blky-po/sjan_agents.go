package blky_po

import (
	"time"

	"xorm.io/xorm"
)

// SjanAgent 北京世纪安诺代理商信息表领域模型
type SjanAgent struct {
	Id         int       `xorm:"pk autoincr not null 'id'" json:"id"`               // 自增主键
	CuNo       string    `xorm:"not null unique VARCHAR(50) 'cu_no'" json:"cu_no"`  // 代理编号
	CuName     string    `xorm:"not null VARCHAR(100) 'cu_name'" json:"cu_name"`    // 代理名称
	Ctype      string    `xorm:"not null VARCHAR(50) 'ctype'" json:"ctype"`         // 代理分类
	Phone      string    `xorm:"VARCHAR(20) 'phone'" json:"phone"`                  // 电话
	Province   string    `xorm:"VARCHAR(50) 'province'" json:"province"`            // 省份
	City       string    `xorm:"VARCHAR(50) 'city'" json:"city"`                    // 城市
	Address    string    `xorm:"VARCHAR(255) 'address'" json:"address"`             // 地址
	CreateTime time.Time `xorm:"datetime created 'create_time'" json:"create_time"` // 创建时间
	UpdateTime time.Time `xorm:"datetime updated 'update_time'" json:"update_time"` // 更新时间
}

// TableName 表名
func (s *SjanAgent) TableName() string {
	return "blky.sjan_agents"
}

// GetByCuNo 根据代理编号获取代理商信息
func (s *SjanAgent) GetByCuNo(session *xorm.Session, cuNo string) (*SjanAgent, error) {
	var agent SjanAgent
	has, err := session.Where("cu_no = ?", cuNo).Get(&agent)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &agent, nil
}
