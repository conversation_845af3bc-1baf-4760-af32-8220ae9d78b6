package blky_po

import (
	"time"

	"xorm.io/xorm"
)

// SjanPackagingDetail 北京世纪安诺包装明细表领域模型
type SjanPackagingDetail struct {
	// 自增主键
	Id int `xorm:"pk autoincr not null 'id'" json:"id"`
	// 任务单号
	BillNo string `xorm:"not null VARCHAR(50) 'bill_no'" json:"bill_no"`
	// 小标-物流码
	Barcode string `xorm:"not null VARCHAR(100) 'barcode'" json:"barcode"`
	// 大标-箱码
	Ubarcode string `xorm:"not null VARCHAR(100) 'ubarcode'" json:"ubarcode"`
	// 采集时间
	InDate time.Time `xorm:"not null datetime 'in_date'" json:"in_date"`
	// 创建时间
	CreateTime time.Time `xorm:"datetime created 'create_time'" json:"create_time"`
}

// TableName 表名
func (s *SjanPackagingDetail) TableName() string {
	return "blky.sjan_packaging_details"
}

// GetByBillNo 根据任务单号获取包装明细列表
func (s *SjanPackagingDetail) GetByBillNo(session *xorm.Session, billNo string) ([]SjanPackagingDetail, error) {
	var details []SjanPackagingDetail
	err := session.Where("bill_no = ?", billNo).Find(&details)
	if err != nil {
		return nil, err
	}
	return details, nil
}

// GetByBarcode 根据小标获取包装明细
func (s *SjanPackagingDetail) GetByBarcode(session *xorm.Session, barcode string) (*SjanPackagingDetail, error) {
	var detail SjanPackagingDetail
	has, err := session.Where("barcode = ?", barcode).Get(&detail)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &detail, nil
}

// GetByUbarcode 根据大标获取包装明细列表
func (s *SjanPackagingDetail) GetByUbarcode(session *xorm.Session, ubarcode string) ([]SjanPackagingDetail, error) {
	var details []SjanPackagingDetail
	err := session.Where("ubarcode = ?", ubarcode).Find(&details)
	if err != nil {
		return nil, err
	}
	return details, nil
}

// GetPackagingDetail 查询大标小标关系
func (s *SjanPackagingDetail) GetPackagingDetail(session *xorm.Session, barcode string) (bool, error) {
	return session.Where("barcode = ? or ubarcode = ?", barcode, barcode).Get(s)
}
