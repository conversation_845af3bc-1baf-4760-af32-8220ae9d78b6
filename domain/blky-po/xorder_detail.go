package blky_po

import (
	"time"
)

// XOrderDetail 订单明细表
type XOrderDetail struct {
	IID      int       `json:"iid" xorm:"pk autoincr not null 'iID'"`                 // 自增主键
	Sddbh    string    `json:"sddbh" xorm:"not null VARCHAR(50) 'sddbh'"`             // 订单编号
	Scpbh    string    `json:"scpbh" xorm:"VARCHAR(50) 'scpbh'"`                      // 产品编号
	Sspmc    string    `json:"sspmc" xorm:"VARCHAR(200) 'sspmc'"`                     // 商品名称
	Inum     int       `json:"inum" xorm:"not null default 0 'inum'"`                 // 商品数量
	Icpdj    float64   `json:"icpdj" xorm:"not null decimal(18,2) default 0 'icpdj'"` // 产品单价
	Izsnum   int       `json:"izsnum" xorm:"not null default 0 'izsnum'"`             // 发货数量
	Dregtime time.Time `json:"dregtime" xorm:"datetime not null 'dregtime'"`          // 创建时间
	Dcktime  time.Time `json:"dcktime" xorm:"datetime 'dcktime'"`                     // 出库时间
}

// TableName 表名
func (x *XOrderDetail) TableName() string {
	return "blky.xorder_detail"
}
