package points_po

import "time"

type ZlDoctorSaleGoods struct {
	SimpleEntity[int] `xorm:"extends"`
	Area              string    `json:"area" xorm:"'area'"`                           // 区域
	OperateUserId     string    `json:"operate_user_id" xorm:"'operate_user_id'"`     // 员工id
	OperateUserName   string    `json:"operate_user_name" xorm:"'operate_user_name'"` // 医生姓名
	R1Code            string    `json:"r1_code" xorm:"'r1_code'"`                     // R1商品编码
	ZlCode            string    `json:"zl_code" xorm:"'zl_code'"`                     // 子龙商品编码
	SkuName           string    `json:"sku_name" xorm:"'sku_name'"`                   // 商品名称
	OrderNumber       string    `json:"order_number" xorm:"'order_number'"`           // 开单订单号
	OccurTime         time.Time `json:"occur_time" xorm:"'occur_time'"`               // 发生时间
	ItemNum           float64   `json:"item_num" xorm:"'item_num'"`                   // 开单数量
	Dt                time.Time `json:"dt" xorm:"'dt'"`                               // 大数据按天更新数据用
}

func (e ZlDoctorSaleGoods) TableName() string {
	return "big_data.zl_doctor_sale_goods"
}

func (e ZlDoctorSaleGoods) AsPointer() any {
	return &e
}
