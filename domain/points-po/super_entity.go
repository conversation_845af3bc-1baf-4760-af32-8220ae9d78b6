package points_po

import (
	"time"
)

type Id interface {
	int | int64
}

type Entity interface {
	TableName() string
	AsPointer() any
	GetId() any
}

type SimpleEntity[T Id] struct {
	Id T `json:"id" xorm:"pk autoincr 'id'" validate:"create:min=0,max=0,update:required"`
}

func (e SimpleEntity[T]) GetId() any {
	return e.Id
}

type SuperEntity[T Id] struct {
	Id        T         `json:"id" xorm:"pk autoincr 'id'" validate:"create:min=0,max=0,update:required"`
	CreatedAt time.Time `json:"created_at" xorm:"created 'created_at'"`
	UpdatedAt time.Time `json:"updated_at" xorm:"updated 'updated_at'"`
}

func (e SuperEntity[T]) GetId() any {
	return e.Id
}
