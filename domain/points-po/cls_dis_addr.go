package points_po

type ClsDisAddr struct {
	SuperEntity[int] `xorm:"extends"`
	DisId            int    `json:"dis_id" xorm:"'dis_id'"`
	Province         string `json:"province"`
	City             string `json:"city" xorm:"'city'"`
	District         string `json:"district" xorm:"'district'"`
	Ress             string `json:"ress" xorm:"'ress'"`
	Name             string `json:"name" xorm:"'name'"`
	Phone            string `json:"phone" xorm:"'phone'"`
	EncryptPhone     string `json:"encrypt_phone" xorm:"'encrypt_phone'"` // 加密后的手机号
	IsDefault        int    `json:"is_default" xorm:"'is_default'"`
}

func (e ClsDisAddr) TableName() string {
	return "cls_dis_address"
}

func (e ClsDisAddr) AsPointer() any {
	return &e
}
