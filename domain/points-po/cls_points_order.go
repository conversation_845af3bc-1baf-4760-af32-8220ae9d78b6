package points_po

import "time"

// ClsPointsOrder 对应 cls_points_order 表的持久化对象
type ClsPointsOrder struct {
	SuperEntity[int]   `xorm:"extends"` // 主键ID、CreatedAt、UpdatedAt 通常由 SuperEntity 管理
	OrderNo            string           `xorm:"'order_no' varchar(50) notnull unique" json:"order_no"` // 订单号，唯一
	DisId              int              `xorm:"'dis_id' notnull index(idx_dis_id)" json:"dis_id"`
	GoodsId            int              `xorm:"'goods_id' notnull unique(idx_goods_id)" json:"goods_id"`
	GoodsName          string           `xorm:"'goods_name' varchar(100) notnull" json:"goods_name"`
	GoodsType          int              `xorm:"'goods_type' notnull" json:"goods_type"` // 商品类型: 1-实物商品，2-虚拟商品
	PointsCost         int              `xorm:"'points_cost' notnull" json:"points_cost"`
	Quantity           int              `xorm:"'quantity' notnull default 1" json:"quantity"`
	Status             int              `xorm:"'status' notnull index(idx_status)" json:"status"` // 订单状态：1-待发货，2-已发货，3-已完成
	AddressId          int              `xorm:"'address_id' null" json:"address_id,omitempty"`
	Remark             string           `xorm:"'remark' varchar(255) null" json:"remark,omitempty"`
	ExpressCompanyCode string           `xorm:"'express_company_code' varchar(50) notnull default ''" json:"express_company_code,omitempty"`
	ExpressCompany     string           `xorm:"'express_company' varchar(50) notnull default ''" json:"express_company,omitempty"`
	ExpressNo          string           `xorm:"'express_no' varchar(50) null" json:"express_no,omitempty"`
	ExpressTime        time.Time        `xorm:"'express_time' Datetime null" json:"express_time,omitempty"`
	PaymentMethod      int              `xorm:"'payment_method' notnull default 1" json:"payment_method"`     // 支付方式:1-积分
	OrderTime          time.Time        `xorm:"'order_time' Datetime notnull" json:"order_time"`              // 下单时间
	CompleteTime       time.Time        `xorm:"'complete_time' Datetime null" json:"complete_time,omitempty"` // 完成时间
	OrderSource        int              `xorm:"'order_source' notnull" json:"order_source"`                   // 订单来源: 1-宠利扫小程序
	OrderType          int              `xorm:"'order_type' notnull default 1" json:"order_type"`             // 订单类型:1-积分兑换
}

// TableName 指定数据库中的表名
func (e ClsPointsOrder) TableName() string {
	return "cls_points_order"
}

// AsPointer 返回实体对象的指针，用于 SuperService 泛型约束
func (e ClsPointsOrder) AsPointer() any {
	return &e
}
