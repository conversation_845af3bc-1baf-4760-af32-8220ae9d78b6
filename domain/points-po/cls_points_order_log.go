package points_po

import "time"

// time 可能被 SuperEntity 间接需要

// ClsPointsOrderLog 对应 cls_points_order_log 表的持久化对象
type ClsPointsOrderLog struct {
	SimpleEntity[int] `xorm:"extends"` // 假设 SuperEntity 提供 Id, CreatedAt
	OrderId           int              `xorm:"'order_id' notnull index(idx_order_id)" json:"orderId"`
	Status            int              `xorm:"'status' notnull" json:"status"`                        // 订单状态: 1-待发货，2-已发货，3-已完成
	Operator          string           `xorm:"'operator' varchar(50) null" json:"operator,omitempty"` // 操作人，可为空
	CreatedAt         time.Time        `xorm:"'created_at' notnull" json:"createdAt"`
}

// TableName 指定数据库中的表名
func (e ClsPointsOrderLog) TableName() string {
	return "cls_points_order_log"
}

// AsPointer 返回实体对象的指针，用于 SuperService 泛型约束
func (e ClsPointsOrderLog) AsPointer() any {
	return &e
}
