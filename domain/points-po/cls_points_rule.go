package points_po

import (
	"time"
)

type ClsPointsRule struct {
	SuperEntity[int] `xorm:"extends"`
	GoodsCode        string    `json:"goods_code" xorm:"'goods_code'"`
	Type             int       `json:"type" xorm:"'type'"` // 规则类型：1-体系内，2-体系外
	StartTime        time.Time `json:"start_time" xorm:"'start_time'"`
	EndTime          time.Time `json:"end_time" xorm:"'end_time'"`
	Region           string    `json:"region" xorm:"'region'"`
	Points           int       `json:"points" xorm:"'points'"`
	Status           int       `json:"status" xorm:"'status'"`
}

func (e ClsPointsRule) TableName() string {
	return "cls_points_rule"
}

func (e ClsPointsRule) AsPointer() any {
	return &e
}
