// Package marketing_po 邀请助力明细表领域模型
package marketing_po

import (
	"time"
)

// PetInvite 邀请助力明细表
// 对应表：eshop.pet_invite
// 记录用户邀请、助力、投票等明细
// 包含邀请人、被邀请人、注册、投票、作品等信息
type PetInvite struct {
	// 主键
	Id int `xorm:"pk autoincr 'id'"`
	// 邀请人用户id
	InviterId string `xorm:"inviter_id"`
	// 邀请人昵称
	InviterNickname string `xorm:"inviter_nickname"`
	// 邀请人手机号带星
	InviterMobile string `xorm:"inviter_mobile"`
	// 邀请人加密手机号
	InviterEnMobile string `xorm:"inviter_en_mobile"`
	// 邀请人客户类型 0新客 1老客
	InviterType int `xorm:"inviter_type"`
	// 被邀请用户id
	InviteeId string `xorm:"invitee_id"`
	// 被邀请人昵称
	InviteeOpenId string `xorm:"invitee_open_id"`
	// 被邀请人昵称
	InviteeNickname string `xorm:"invitee_nickname"`
	// 被邀请人手机号带星
	InviteeMobile string `xorm:"invitee_mobile"`
	// 被邀请人加密手机号
	InviteeEnMobile string `xorm:"invitee_en_mobile"`
	// 被邀请用户注册状态 0未注册 1已注册
	InviteeRegisterStatus int `xorm:"invitee_register_status"`
	// 被邀请用户注册时间
	InviteeRegisterTime time.Time `json:"invitee_register_time" xorm:"default 'null' comment('投票时间') DATETIME 'invitee_register_time'"`
	// 被邀请客户类型 0新客 1老客
	InviteeType int `xorm:"invitee_type"`
	// 被邀请客户投票状态 0未投票 1已投票 2投票失败
	VoteStatus int `xorm:"vote_status"`
	// 关联的作品编号
	WorkCode string `xorm:"work_code"`
	// 投票时间
	VoteTime time.Time `json:"vote_time" xorm:"default 'null' comment('投票时间') DATETIME 'vote_time'"`
	// 投票数
	VoteCount int `xorm:"vote_count"`
	// 创建时间
	CreateTime time.Time `xorm:"create_time" "created"`
	// 更新时间
	UpdateTime time.Time `xorm:"update_time" "updated"`
}

// TableName 返回表名
func (PetInvite) TableName() string {
	return "eshop.pet_invite"
}
