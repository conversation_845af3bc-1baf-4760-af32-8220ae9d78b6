package marketing_po

import (
	"errors"
	"fmt"
	"math"
	"time"

	"github.com/shopspring/decimal"

	"xorm.io/xorm"
)

// MCardDiscount 储值卡折扣范围
type MCardDiscount struct {
	Id              int64     `json:"id" xorm:"pk not null comment('主键') BIGINT 'id'"`
	ChainId         int64     `json:"chain_id" xorm:"not null default 0 comment('连锁id') BIGINT 'chain_id'"`
	TenantId        int64     `json:"tenant_id" xorm:"not null default 0 comment('店铺id') BIGINT 'tenant_id'"`
	CardType        string    `json:"card_type" xorm:"not null default '' comment('卡类型 STORE_CARD 储值卡 TIME_CARD次卡') VARCHAR(16) 'card_type'"`
	CardId          int64     `json:"card_id" xorm:"not null default 0 comment('储值卡id') BIGINT 'card_id'"`
	ProductType     string    `json:"product_type" xorm:"not null default '' comment('商品类型，GOODS实物，SER服务，ALIVE活体') VARCHAR(16) 'product_type'"`
	RefId           int64     `json:"ref_id" xorm:"not null default 0 comment('关联id,商品skuid') BIGINT 'ref_id'"`
	ProductDiscount float64   `json:"product_discount" xorm:"not null default '100.0000' comment('商品折扣,为100则表示不打折') DECIMAL(18) 'product_discount'"`
	ServiceDiscount float64   `json:"service_discount" xorm:"not null default '100.0000' comment('服务折扣,为100则表示不打折') DECIMAL(18) 'service_discount'"`
	CreatedBy       int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime     time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time'"`
	UpdatedBy       int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime     time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time'"`
	IsDeleted       bool      `json:"is_deleted" xorm:"not null default 0 comment('删除标识:0未删除,1已删除') BIT(1) 'is_deleted'"`
}

// MStoreCard 储值卡信息表
type MStoreCard struct {
	Id             int64     `json:"id" xorm:"pk not null comment('唯一数据id') BIGINT 'id'"`
	ChainId        int64     `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	TenantId       int64     `json:"tenant_id" xorm:"not null default 0 comment('店铺ID') BIGINT 'tenant_id'"`
	CardId         int64     `json:"card_id" xorm:"not null default 0 comment('卡基础表id') BIGINT 'card_id'"`
	Denomination   float64   `json:"denomination" xorm:"not null default '0.0000' comment('面额') DECIMAL(18) 'denomination'"`
	GiftAmount     float64   `json:"gift_amount" xorm:"not null default '0.0000' comment('赠送金') DECIMAL(18) 'gift_amount'"`
	DiscountType   string    `json:"discount_type" xorm:"not null default 'UN_LIMIT' comment('使用限制:UN_LIMIT 无限制,MONEY_LIMIT每次使用可抵扣多少元,DISCOUNT_LIMIT每次使用打几折') VARCHAR(16) 'discount_type'"`
	Discount       float64   `json:"discount" xorm:"not null default '0.0000' comment('折扣') DECIMAL(18) 'discount'"`
	DiscountRange  string    `json:"discount_range" xorm:"not null default 'UN_LIMIT' comment('折扣范围(UN_LIMIT-无限制，PRODUCT-商品，PRODUCT_CATEGORY-商品分类)') VARCHAR(16) 'discount_range'"`
	FosterDiscount float64   `json:"foster_discount" xorm:"not null default '100.0000' comment('寄养折扣,为100则表示不打折') DECIMAL(18) 'foster_discount'"`
	Status         string    `json:"status" xorm:"not null default 'ENABLE' comment('数据状态: ENABLE启用, DISABLE停用') VARCHAR(16) 'status'"`
	IsDeleted      bool      `json:"is_deleted" xorm:"not null default 0 comment('删除标识') BIT(1) 'is_deleted'"`
	CreatedBy      int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime    time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedBy      int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime    time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
}

// MStoreCardOrder 储值卡订单
type MStoreCardOrder struct {
	Id                   int64     `json:"id" xorm:"pk autoincr not null comment('主键') BIGINT 'id'"`
	TenantId             int64     `json:"tenant_id" xorm:"not null default 0 comment('租户号') BIGINT 'tenant_id'"`
	CardInfoId           int64     `json:"card_info_id" xorm:"not null default 0 comment('基础卡id') BIGINT 'card_info_id'"`
	CardId               int64     `json:"card_id" xorm:"not null default 0 comment('卡id') BIGINT 'card_id'"`
	CardName             string    `json:"card_name" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'card_name'"`
	CardType             string    `json:"card_type" xorm:"not null default '' comment('卡类型') VARCHAR(16) 'card_type'"`
	RecordId             int64     `json:"record_id" xorm:"not null default 0 comment('卡记录id;卡id') BIGINT 'record_id'"`
	BizType              string    `json:"biz_type" xorm:"not null default '' comment('业务类型') VARCHAR(255) 'biz_type'"`
	OrderAmount          float64   `json:"order_amount" xorm:"not null default '0.0000' comment('订单金额') DECIMAL(18) 'order_amount'"`
	OrderId              int64     `json:"order_id" xorm:"not null default 0 comment('订单id') BIGINT 'order_id'"`
	OrderNo              string    `json:"order_no" xorm:"not null default '' comment('订单号') VARCHAR(255) 'order_no'"`
	Status               string    `json:"status" xorm:"not null default '进行中' comment('状态') VARCHAR(255) 'status'"`
	Amount               float64   `json:"amount" xorm:"not null default '0.0000' comment('购买储值卡额度') DECIMAL(18) 'amount'"`
	AmountGift           float64   `json:"amount_gift" xorm:"not null default '0.0000' comment('赠送额度') DECIMAL(18) 'amount_gift'"`
	CustomerId           int64     `json:"customer_id" xorm:"not null default 0 comment('客户id') BIGINT 'customer_id'"`
	CustomerMobile       string    `json:"customer_mobile" xorm:"not null default '' comment('客户手机号') VARCHAR(255) 'customer_mobile'"`
	CustomerName         string    `json:"customer_name" xorm:"not null default '' comment('客户名称') VARCHAR(255) 'customer_name'"`
	SellerId             int64     `json:"seller_id" xorm:"not null default 0 comment('收银员id') BIGINT 'seller_id'"`
	SellerName           string    `json:"seller_name" xorm:"not null default '' comment('收银员名') VARCHAR(64) 'seller_name'"`
	Remark               string    `json:"remark" xorm:"not null default '' comment('备注') VARCHAR(255) 'remark'"`
	RefundFlag           bool      `json:"refund_flag" xorm:"not null default 0 comment('是否退单') BIT(1) 'refund_flag'"`
	RefundAmount         float64   `json:"refund_amount" xorm:"not null default '0.0000' comment('退单金额') DECIMAL(18) 'refund_amount'"`
	RefundOrderId        int64     `json:"refund_order_id" xorm:"not null default 0 comment('关联退单id') BIGINT 'refund_order_id'"`
	RefundCardAmount     float64   `json:"refund_card_amount" xorm:"not null default '0.0000' comment('卡退的额度') DECIMAL(18) 'refund_card_amount'"`
	RefundCardAmountGift float64   `json:"refund_card_amount_gift" xorm:"not null default '0.0000' comment('卡退的额度-赠送金') DECIMAL(18) 'refund_card_amount_gift'"`
	RefundSellerId       int64     `json:"refund_seller_id" xorm:"not null default 0 comment('退单收银员id') BIGINT 'refund_seller_id'"`
	IsDeleted            bool      `json:"is_deleted" xorm:"not null default 0 comment('是否删除') BIT(1) 'is_deleted'"`
	CreatedBy            int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime          time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedBy            int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime          time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
}

// MStoreCardRecord 储值卡记录
type MStoreCardRecord struct {
	Id             int64     `json:"id" xorm:"pk autoincr not null comment('唯一数据id') BIGINT 'id'"`
	ChainId        int64     `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	TenantId       int64     `json:"tenant_id" xorm:"not null default 0 comment('店铺ID') BIGINT 'tenant_id'"`
	CardId         int64     `json:"card_id" xorm:"not null default 0 comment('卡id') BIGINT 'card_id'"`
	CardNo         string    `json:"card_no" xorm:"not null default '' comment('卡号') VARCHAR(64) 'card_no'"`
	CardName       string    `json:"card_name" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'card_name'"`
	ValidType      string    `json:"valid_type" xorm:"not null default '' comment('有效期类型: PERMANENT永久有效 VALID_DAYS办卡起多少天有效，FIXED_TIME指定时间内有效') VARCHAR(16) 'valid_type'"`
	EnableTime     time.Time `json:"enable_time" xorm:"not null default '0000-01-01 00:00:00' comment('启用时间') DATETIME 'enable_time'"`
	ExpirationTime time.Time `json:"expiration_time" xorm:"not null default '0000-01-01 00:00:00' comment('到期时间') DATETIME 'expiration_time'"`
	RefundTime     time.Time `json:"refund_time" xorm:"not null default '0000-01-01 00:00:00' comment('退款时间') DATETIME 'refund_time'"`
	TotalAmount    float64   `json:"total_amount" xorm:"not null default '0.0000' comment('总金额') DECIMAL(18) 'total_amount'"`
	Principal      float64   `json:"principal" xorm:"not null default '0.0000' comment('本金') DECIMAL(18) 'principal'"`
	GiftAmount     float64   `json:"gift_amount" xorm:"not null default '0.0000' comment('赠送金') DECIMAL(18) 'gift_amount'"`
	Price          float64   `json:"price" xorm:"not null default '0.0000' comment('售价') DECIMAL(18) 'price'"`
	RefundAmount   float64   `json:"refund_amount" xorm:"not null default '0.0000' comment('退款金额') DECIMAL(18) 'refund_amount'"`
	RefundType     string    `json:"refund_type" xorm:"not null default '' comment('退款类型: RETURN_CARD.仅退卡,REFUND_CARD 退卡退款') VARCHAR(16) 'refund_type'"`
	RefundReason   string    `json:"refund_reason" xorm:"not null default '' comment('退款原因') VARCHAR(100) 'refund_reason'"`
	GiftPackage    string    `json:"gift_package" xorm:"not null default '' comment('礼包') VARCHAR(200) 'gift_package'"`
	CustomerId     int64     `json:"customer_id" xorm:"not null default 0 comment('客户id') BIGINT 'customer_id'"`
	CustomerName   string    `json:"customer_name" xorm:"not null default '' comment('客户名称') VARCHAR(50) 'customer_name'"`
	CustomerMobile string    `json:"customer_mobile" xorm:"not null default '' comment('客户手机号') VARCHAR(11) 'customer_mobile'"`
	EmployeeId     int64     `json:"employee_id" xorm:"not null default 0 comment('员工id') BIGINT 'employee_id'"`
	Status         string    `json:"status" xorm:"not null default '' comment('数据状态:PIN_CARD销卡，USE使用中,FREEZE冻结,NOT_USE不可用,INEFFECTIVE.未生效,IN_THE_CARD退卡中,RETIRED_CARD已退卡') VARCHAR(16) 'status'"`
	Version        int       `json:"version" xorm:"not null default 0 comment('版本号') INT 'version'"`
	IsDeleted      bool      `json:"is_deleted" xorm:"not null default 0 comment('删除标识') BIT(1) 'is_deleted'"`
	CreatedBy      int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime    time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedBy      int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime    time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
}

// MStoreCardRecordExt 储值卡记录扩展
type MStoreCardRecordExt struct {
	Id              int64   `json:"id" xorm:"pk autoincr not null comment('主键') BIGINT 'id'"`
	ChainId         int64   `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	TenantId        int64   `json:"tenant_id" xorm:"not null default 0 comment('店铺ID') BIGINT 'tenant_id'"`
	CardId          int64   `json:"card_id" xorm:"not null default 0 comment('卡id') BIGINT 'card_id'"`
	CardNo          string  `json:"card_no" xorm:"not null default '' comment('卡号') VARCHAR(64) 'card_no'"`
	CardName        string  `json:"card_name" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'card_name'"`
	CustomerId      int64   `json:"customer_id" xorm:"not null default 0 comment('客户id') BIGINT 'customer_id'"`
	EmployeeId      int64   `json:"employee_id" xorm:"not null default 0 comment('员工id') BIGINT 'employee_id'"`
	Status          string  `json:"status" xorm:"not null default '' comment('数据状态:PIN_CARD销卡，IN_USE使用中,FREEZE冻结,NOT_USE不可用,INEFFECTIVE.未生效,IN_THE_CARD退卡中,RETIRED_CARD已退卡') VARCHAR(16) 'status'"`
	BalanceChange   float64 `json:"balance_change" xorm:"not null default '0.0000' comment('余额变动') DECIMAL(18) 'balance_change'"`
	PrincipalChange float64 `json:"principal_change" xorm:"not null default '0.0000' comment('本金变动') DECIMAL(18) 'principal_change'"`
	GiftChange      float64 `json:"gift_change" xorm:"not null default '0.0000' comment('赠送金变动') DECIMAL(18) 'gift_change'"`
	Balance         float64 `json:"balance" xorm:"not null default '0.0000' comment('本金余额') DECIMAL(18) 'balance'"`
	TotalAmount     float64 `json:"total_amount" xorm:"not null default '0.0000' comment('总金额') DECIMAL(18) 'total_amount'"`
	Principal       float64 `json:"principal" xorm:"not null default '0.0000' comment('本金') DECIMAL(18) 'principal'"`
	GiftAmount      float64 `json:"gift_amount" xorm:"not null default '0.0000' comment('赠送金') DECIMAL(18) 'gift_amount'"`
	RefundAmount    float64 `json:"refund_amount" xorm:"not null default '0.0000' comment('退款金额') DECIMAL(18) 'refund_amount'"`
	OperateType     string  `json:"operate_type" xorm:"not null default '' comment('操作类型') VARCHAR(16) 'operate_type'"`
	Price           float64 `json:"price" xorm:"not null default '0.0000' comment('售价') DECIMAL(18) 'price'"`
}

// MStoreCardUseRecord 储值卡使用记录
type MStoreCardUseRecord struct {
	Id                  int64     `json:"id" xorm:"pk autoincr not null comment('唯一数据id') BIGINT 'id'"`
	ChainId             int64     `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	TenantId            int64     `json:"tenant_id" xorm:"not null default 0 comment('店铺ID') BIGINT 'tenant_id'"`
	RecordId            int64     `json:"record_id" xorm:"not null default 0 comment('卡记录id') BIGINT 'record_id'"`
	CardId              int64     `json:"card_id" xorm:"not null default 0 comment('卡id') BIGINT 'card_id'"`
	CardNo              string    `json:"card_no" xorm:"not null default '' comment('卡号') VARCHAR(64) 'card_no'"`
	CardName            string    `json:"card_name" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'card_name'"`
	OperateType         string    `json:"operate_type" xorm:"not null default '' comment('操作类型:CONSUMPTION消费,TOP_UP充值,REFUND退款,RECONCILIATION调账,RETURN_CARD退卡') VARCHAR(16) 'operate_type'"`
	BalanceChange       float64   `json:"balance_change" xorm:"not null default '0.0000' comment('余额变动') DECIMAL(18) 'balance_change'"`
	PrincipalChange     float64   `json:"principal_change" xorm:"not null default '0.0000' comment('本金变动') DECIMAL(18) 'principal_change'"`
	GiftChange          float64   `json:"gift_change" xorm:"not null default '0.0000' comment('赠送金变动') DECIMAL(18) 'gift_change'"`
	Balance             float64   `json:"balance" xorm:"not null default '0.0000' comment('本金余额') DECIMAL(18) 'balance'"`
	GiftBalance         float64   `json:"gift_balance" xorm:"not null default '0.0000' comment('赠送金余额') DECIMAL(18) 'gift_balance'"`
	ReturnableGift      float64   `json:"returnable_gift" xorm:"not null default '0.0000' comment('可退赠金') DECIMAL(18) 'returnable_gift'"`
	ReturnablePrincipal float64   `json:"returnable_principal" xorm:"not null default '0.0000' comment('可退本金') DECIMAL(18) 'returnable_principal'"`
	OrderId             int64     `json:"order_id" xorm:"not null default 0 comment('订单id') BIGINT 'order_id'"`
	OrderNo             string    `json:"order_no" xorm:"not null default '' comment('订单编号') VARCHAR(32) 'order_no'"`
	CustomerId          int64     `json:"customer_id" xorm:"not null default 0 comment('客户id') BIGINT 'customer_id'"`
	EmployeeId          int64     `json:"employee_id" xorm:"not null default 0 comment('员工id') BIGINT 'employee_id'"`
	GiftPackage         string    `json:"gift_package" xorm:"not null default '' comment('礼包') VARCHAR(200) 'gift_package'"`
	Remark              string    `json:"remark" xorm:"not null default '' comment('备注') VARCHAR(200) 'remark'"`
	Status              string    `json:"status" xorm:"not null default '' comment('数据状态') VARCHAR(16) 'status'"`
	IsDeleted           bool      `json:"is_deleted" xorm:"not null default 0 comment('删除标识') BIT(1) 'is_deleted'"`
	CreatedBy           int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime         time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedBy           int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime         time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
}

// StoreCardQuery 储值卡查询参数
type StoreCardQuery struct {
	StoreId     string `json:"store_id"`     // 店铺ID
	ChainId     int64  `json:"chain_id"`     // 连锁ID
	CustomerId  int64  `json:"customer_id"`  // 客户ID
	CardNo      string `json:"card_no"`      // 卡号
	SkuIds      []int  `json:"sku_ids"`      // 商品SKU IDs
	CategoryIds []int  `json:"category_ids"` // 商品分类IDs
	ProductType string `json:"product_type"` // 商品类型
}

// StoreCardInfo 储值卡信息出参
type StoreCardInfo struct {
	StoreCard   MStoreCard      `json:"store_card"`   // 储值卡基础信息
	Balance     float64         `json:"balance"`      // 余额
	GiftBalance float64         `json:"gift_balance"` // 赠送金余额
	CardNo      string          `json:"card_no"`      // 卡号
	CardName    string          `json:"card_name"`    // 卡名称
	Discounts   []MCardDiscount `json:"discounts"`    // 折扣信息
	RecordID    int64           `json:"record_id"`    // 卡名称
}

// OperateInfo 操作信息结构体
type OperateInfo struct {
	OrderId      int    `json:"order_id"`      // 订单ID
	OrderNo      string `json:"order_no"`      // 订单编号
	OperatorId   int64  `json:"operator_id"`   // 操作人ID
	OperatorName string `json:"operator_name"` // 操作人名称
}

// NewOperateInfo 创建操作信息
func NewOperateInfo(orderId int, orderNo string, operatorId int64, operatorName string) *OperateInfo {
	return &OperateInfo{
		OrderId:      orderId,
		OrderNo:      orderNo,
		OperatorId:   operatorId,
		OperatorName: operatorName,
	}
}

// MCardInfo 储值卡基础信息表

// GetStoreCardInfo 获取储值卡信息
func (m *MStoreCard) GetStoreCardInfo(session *xorm.Session, query StoreCardQuery) (*StoreCardInfo, error) {
	if query.ChainId == 0 {
		return nil, errors.New("chain_id is required")
	}
	if query.StoreId == "" {
		return nil, errors.New("store_id is required")
	}
	if query.CardNo == "" {
		return nil, errors.New("card_no is required")
	}

	// 先查询储值卡记录
	var record MStoreCardRecord
	exists, err := session.Where("card_no = ?", query.CardNo).
		Where("customer_id = ?", query.CustomerId).
		Where("status = 'IN_USE'"). // 使用中的卡
		Where("is_deleted = 0").
		Get(&record)

	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.New("储值卡不存在或未启用")
	}

	// 检查有效期
	now := time.Now()
	switch record.ValidType {
	case "PERMANENT": // 永久有效
		// 无需检查
	case "VALID_DAYS", "FIXED_TIME": // 固定天数或指定时间
		if now.After(record.ExpirationTime) {
			return nil, errors.New("储值卡已过期")
		}
	}

	// 查询储值卡基础信息和卡名称
	var storeCard struct {
		MStoreCard `xorm:"extends"`
		CardName   string `xorm:"'name'"`
	}
	exists, err = session.Table("eshop_saas.m_store_card").
		Join("LEFT", "eshop_saas.m_card_info", "m_store_card.card_id = m_card_info.id").
		Where("m_store_card.card_id = ?", record.CardId).
		Where("m_store_card.chain_id = ?", query.ChainId).
		Where("m_store_card.tenant_id = ?", query.StoreId).
		Where("m_store_card.status = 'ON_SELL'").
		Where("m_store_card.is_deleted = 0").
		Get(&storeCard)

	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.New("储值卡基础信息不存在")
	}

	// 查询储值卡折扣信息
	var cardDiscounts []MCardDiscount
	err = session.Where("chain_id = ?", query.ChainId).
		Where("tenant_id = ?", query.StoreId).
		Where("card_id = ?", record.CardId).
		Where("card_type = 'STORE_CARD'").
		Where("is_deleted = 0").
		Find(&cardDiscounts)
	if err != nil {
		return nil, fmt.Errorf("查询储值卡折扣信息失败: %v", err)
	}

	cardInfo := &StoreCardInfo{
		StoreCard:   storeCard.MStoreCard,
		Balance:     record.Principal,
		GiftBalance: record.GiftAmount,
		CardNo:      record.CardNo,
		CardName:    storeCard.CardName,
		Discounts:   cardDiscounts,
		RecordID:    record.Id,
	}

	return cardInfo, nil
}

// CreateStoreCardOrder 创建储值卡订单
func (m *MStoreCard) CreateStoreCardOrder(session *xorm.Session, order *MStoreCardOrder) error {
	if order == nil {
		return errors.New("储值卡订单信息不能为空")
	}

	_, err := session.InsertOne(order)
	if err != nil {
		return fmt.Errorf("创建储值卡订单失败: %v", err)
	}

	return nil
}

// CreateOrUpdateStoreCardRecord 创建或更新储值卡记录
func (m *MStoreCard) CreateOrUpdateStoreCardRecord(session *xorm.Session, record *MStoreCardRecord, isRenewal bool) error {
	if record == nil {
		return errors.New("储值卡记录信息不能为空")
	}

	if isRenewal {
		// 续卡：更新现有记录
		sql := `UPDATE eshop_saas.m_store_card_record 
			SET principal=principal+?,
			    gift_amount = gift_amount + ?,
			    total_amount = total_amount + ?,
				version = version + 1,
				updated_time = NOW()
			WHERE card_no = ? AND customer_id = ? AND status = 'IN_USE' AND version = ?`

		result, err := session.Exec(sql,
			record.Principal,
			record.GiftAmount,
			record.TotalAmount,
			record.CardNo,
			record.CustomerId,
			record.Version)

		if err != nil {
			return fmt.Errorf("更新储值卡记录失败: %v", err)
		}

		affected, err := result.RowsAffected()
		if err != nil {
			return err
		}

		if affected == 0 {
			return errors.New("储值卡记录已被修改,请重试")
		}
	} else {
		// 新购买：创建新记录
		_, err := session.InsertOne(record)
		if err != nil {
			return fmt.Errorf("创建储值卡记录失败: %v", err)
		}
	}

	return nil
}

// CreateStoreCardUseRecord 创建储值卡使用记录
func (m *MStoreCard) CreateStoreCardUseRecord(session *xorm.Session, record *MStoreCardUseRecord) error {
	if record == nil {
		return errors.New("储值卡使用记录不能为空")
	}

	_, err := session.InsertOne(record)
	if err != nil {
		return fmt.Errorf("创建储值卡使用记录失败: %v", err)
	}

	return nil
}

// CheckExistingCard 检查用户是否已有该类型的储值卡
func (m *MStoreCard) CheckExistingCard(session *xorm.Session, cardId int, customerId int64) (bool, *MStoreCardRecord, error) {
	var record MStoreCardRecord
	exists, err := session.Where("card_id = ?", cardId).
		Where("customer_id = ?", customerId).
		Where("status = 'IN_USE'").
		Where("is_deleted = 0").
		Get(&record)

	if err != nil {
		return false, nil, err
	}

	return exists, &record, nil
}

// UpdateStoreCardRecordStatus 更新储值卡相关金额
//func (m *MStoreCard) UpdateStoreCardRecordStatus(session *xorm.Session, recordId int64, status string) error {
//	_, err := session.Where("id = ?", recordId).Update(&MStoreCardRecord{
//		RefundTime: time.Now(),
//		RefundType: OperateTypeReturnCard,
//		Status:     RETIRED_CARD,
//	})
//	return err
//}

func (t MCardDiscount) TableName() string {
	return "eshop_saas.m_card_discount"
}

func (t MStoreCard) TableName() string {
	return "eshop_saas.m_store_card"
}

func (t MStoreCardOrder) TableName() string {
	return "eshop_saas.m_store_card_order"
}

func (t MStoreCardRecord) TableName() string {
	return "eshop_saas.m_store_card_record"
}

func (t MStoreCardUseRecord) TableName() string {
	return "eshop_saas.m_store_card_use_record"
}

// GetCardInfo 获取储值卡基础信息
func (m *MStoreCard) GetCardInfo(session *xorm.Session, chainId int64, tenantId int64, cardId int64) (*MCardInfo, error) {
	var cardInfo MCardInfo
	exists, err := session.Where("id = ?", cardId).
		Where("chain_id = ?", chainId).
		Where("tenant_id = ?", tenantId).
		Where("card_type = 'STORE_CARD'").
		Where("status = 'ON_SELL'").
		Where("is_deleted = 0").
		Get(&cardInfo)

	if err != nil {
		return nil, fmt.Errorf("查询储值卡基础信息失败: %v", err)
	}

	if !exists {
		return nil, errors.New("储值卡基础信息不存在或已下架")
	}

	return &cardInfo, nil
}

// GetStoreCardConfig 获取储值卡配置信息
func (m *MStoreCard) GetStoreCardConfig(session *xorm.Session, chainId int64, tenantId int64, cardId int64) (*MStoreCard, error) {
	var storeCard MStoreCard
	exists, err := session.Where("card_id = ?", cardId).
		Where("chain_id = ?", chainId).
		Where("tenant_id = ?", tenantId).
		Where("status = 'ENABLE'").
		Where("is_deleted = 0").
		Get(&storeCard)

	if err != nil {
		return nil, fmt.Errorf("查询储值卡配置信息失败: %v", err)
	}

	if !exists {
		return nil, errors.New("储值卡配置信息不存在或已停用")
	}

	return &storeCard, nil
}

// GetStoreCardRecord 获取储值卡记录
func (m *MStoreCard) GetStoreCardRecord(session *xorm.Session, cardNo string, customerId int64) (*MStoreCardRecord, error) {
	var record MStoreCardRecord
	exists, err := session.Where("card_no = ?", cardNo).
		Where("customer_id = ?", customerId).
		Where("status = 'IN_USE'").
		Where("is_deleted = 0").
		Get(&record)

	if err != nil {
		return nil, fmt.Errorf("查询储值卡记录失败: %v", err)
	}

	if !exists {
		return nil, errors.New("储值卡不存在或已失效")
	}

	return &record, nil
}

// RefundStoreCard 创建储值卡退款记录
func (m *MStoreCardUseRecord) RefundStoreCard(session *xorm.Session, record *MStoreCardUseRecord, operateInfo *OperateInfo) error {
	//涉及余额要取绝对值
	// balance := decimal.NewFromFloat(record.Balance).Add(decimal.NewFromFloat(math.Abs(record.PrincipalChange))).InexactFloat64()
	// giftBalance := decimal.NewFromFloat(record.GiftBalance).Add(decimal.NewFromFloat(math.Abs(record.GiftChange))).InexactFloat64()
	useRecord := &MStoreCardUseRecord{
		TenantId:        record.TenantId,
		ChainId:         record.ChainId,
		RecordId:        record.RecordId,
		CardId:          record.CardId,
		CardNo:          record.CardNo,
		CardName:        record.CardName,
		OperateType:     record.OperateType,
		BalanceChange:   math.Abs(record.BalanceChange),
		PrincipalChange: math.Abs(record.PrincipalChange),
		GiftChange:      math.Abs(record.GiftChange),
		Balance:         record.Balance,
		GiftBalance:     record.GiftBalance,
		OrderId:         int64(operateInfo.OrderId),
		OrderNo:         operateInfo.OrderNo,
		CustomerId:      record.CustomerId,
		EmployeeId:      record.EmployeeId,
		Remark:          "订单退款",
		Status:          "SUCCESS",
		CreatedBy:       operateInfo.OperatorId,
		CreatedTime:     time.Now(),
		UpdatedBy:       operateInfo.OperatorId,
		UpdatedTime:     time.Now(),
	}

	if _, err := session.Insert(useRecord); err != nil {
		return fmt.Errorf("创建储值卡退款记录失败: %v", err)
	}

	return nil
}

// RestoreStoreCardAmount 增加储值卡金额
func (m *MStoreCard) RestoreStoreCardAmount(session *xorm.Session, record *MStoreCardUseRecord, operatorId int64) error {
	// 金额取绝对值
	balanceChange := decimal.NewFromFloat(math.Abs(record.BalanceChange))
	giftChange := decimal.NewFromFloat(math.Abs(record.GiftChange))
	// totalAmount := balanceChange.Add(giftChange)
	principalChange := decimal.NewFromFloat(math.Abs(record.PrincipalChange))
	sql := `UPDATE eshop_saas.m_store_card_record 
		SET total_amount = total_amount + ?,
			principal = principal + ?,
			gift_amount = gift_amount + ?,
			refund_amount = refund_amount + ?,
			updated_by = ?,
			updated_time = NOW()
		WHERE card_no = ? AND customer_id = ? AND status = 'IN_USE'`

	result, err := session.Exec(sql,
		balanceChange,
		principalChange,
		giftChange,
		balanceChange,
		operatorId,
		record.CardNo,
		record.CustomerId)

	if err != nil {
		return fmt.Errorf("扣减储值卡金额失败: %v", err)
	}

	affected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新影响行数失败: %v", err)
	}

	if affected == 0 {
		return errors.New("储值卡记录已被修改,请重试")
	}

	return nil
}

// UpdateStoreCardOrderRefund 更新储值卡订单退款信息
func (m *MStoreCardOrder) UpdateStoreCardOrderRefund(session *xorm.Session, orderNo string, refundAmount float64) error {
	storeCardOrder := &MStoreCardOrder{
		RefundFlag:   true,
		RefundAmount: refundAmount,
		UpdatedTime:  time.Now(),
	}

	_, err := session.Table(m.TableName()).
		Where("order_no = ?", orderNo).
		Cols("refund_flag", "refund_amount", "updated_time").
		Update(storeCardOrder)

	if err != nil {
		return fmt.Errorf("更新储值卡订单退款信息失败: %v", err)
	}

	return nil
}

// GetOrderSn 根据储值卡记录ID获取关联的订单号
func (m *MStoreCardUseRecord) GetOrderSn(session *xorm.Session, cardNo string) (string, error) {
	var orderSn string
	if has, err := session.Table("eshop_saas.m_store_card_use_record").Where("card_no = ? AND operate_type = 'OPEN_CARD'", cardNo).Cols("order_no").Get(&orderSn); err != nil {
		return "", fmt.Errorf("查询储值卡使用记录失败: %v", err)
	} else if !has {
		return "", fmt.Errorf("未找到储值卡记录: %s", cardNo)
	}
	return orderSn, nil
}

// GetTimeCardOrderSn 根据次卡记录ID获取关联的订单号
func (m *MTimeCardUseRecord) GetTimeCardOrderSn(session *xorm.Session, cardNo string) (string, error) {
	var orderSn string
	if has, err := session.Table("eshop_saas.m_time_card_use_record").Where("card_no = ? AND operate_type = 'OPEN_CARD'", cardNo).Cols("order_no").Get(&orderSn); err != nil {
		return "", fmt.Errorf("查询次卡使用记录失败: %v", err)
	} else if !has {
		return "", fmt.Errorf("未找到次卡记录: %s", cardNo)
	}
	return orderSn, nil
}
