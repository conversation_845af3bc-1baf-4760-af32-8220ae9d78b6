package marketing_po

import (
	"eShop/infra/utils"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"xorm.io/xorm"
)

// 活动类型：1-商品特价 2-店铺满减
const (
	ActivityTypeSpecialPrice  = 1 // 商品特价
	ActivityTypeFullReduction = 2 // 店铺满减
)

// 活动子类型：1-商品特价 2-商品折扣 3-店铺满减 4-店铺满折
const (
	ActivityTypeClassSpecialPrice  = 1 // 商品特价
	ActivityTypeClassDiscount      = 2 // 商品折扣
	ActivityTypeClassFullReduction = 3 // 店铺满减
	ActivityTypeClassFullDiscount  = 4 // 店铺满折
)

// 活动状态：1-待开始 2-进行中 3-已结束
const (
	ActivityStatusPending = 1 // 待开始
	ActivityStatusRunning = 2 // 进行中
	ActivityStatusEnded   = 3 // 已结束
)

// MarketingActivity 营销活动信息
type MarketingActivity struct {
	// 主键id
	Id int `json:"id" xorm:"pk autoincr not null comment('主键id') INT 'id'"`
	// 连锁id（前端无需传）
	ChainId int64 `json:"chain_id" xorm:"not null comment('连锁id') BIGINT 'chain_id'"`
	// 门店id（前端无需传）
	StoreId string `json:"store_id" xorm:"not null comment('门店id') VARCHAR(127) 'store_id'"`
	// 活动名称
	Name string `json:"name" xorm:"not null default '' comment('活动名称') VARCHAR(10) 'name'"`
	// 活动类型：1-商品特价 2-店铺满减
	Type int `json:"type" xorm:"not null default 1 comment('活动类型：1-商品特价 2-店铺满减') TINYINT 'type'"`
	// 活动子类型：1-商品特价 2-商品折扣 3-店铺满减 4-店铺满折
	TypeClass int `json:"type_class" xorm:"not null default 1 comment('活动子类型：1-商品特价 2-商品折扣 3-店铺满减 4-店铺满折') TINYINT 'type_class'"`
	// 开始时间
	StartTime string `json:"start_time" xorm:"not null default '0000-01-01 00:00:00' comment('开始时间') DATETIME 'start_time'"`
	// 结束时间
	EndTime string `json:"end_time" xorm:"not null default '0000-01-01 00:00:00' comment('结束时间') DATETIME 'end_time'"`
	// 活动星期（周一到周日：2，3...7,1）
	Periodicity string `json:"periodicity" xorm:"not null default '' comment('活动星期（周一到周日：2，3...7,1）') VARCHAR(20) 'periodicity'"`
	// 活动状态：1-待开始 2-进行中 3-已结束
	Status int `json:"status" xorm:"not null default 1 comment('活动状态：1-待开始 2-进行中 3-已结束') TINYINT 'status'"`
	// 规则（json）
	Rule string `json:"rule" xorm:"not null comment('规则（json）') TEXT 'rule'"`
	// 规则描述
	RuleDescribe string `json:"rule_describe" xorm:"not null comment('规则描述') VARCHAR(255) 'rule_describe'"`
	// 是否删除：0-否 1-是
	IsDeleted int `json:"is_deleted" xorm:"not null default 0 comment('是否删除：0-否 1-是') TINYINT 'is_deleted'"`
	// 创建时间
	CreatedTime string `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	// 更新时间
	UpdatedTime string `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
	//适用类型：1-全部商品 2-指定商品可用 3-指定商品分类可用
	ApplyType int `json:"apply_type" xorm:"-"`
}

func (MarketingActivity) TableName() string {
	return "eshop.marketing_activity"
}

type MarketingActivityInfo struct {
	MarketingActivity
	FullReductionRules []FullReductionRule `json:"rule_full_reduction_rules"`
	SpecialPriceRule   SpecialPriceRule    `json:"rule_special_price_rule"`
	MarketingProducts  []*MarketingProduct `json:"marketing_products"`
}

type FullReductionRule struct {
	ThresholdAmount string `json:"threshold_amount"` // 门槛金额（单位元）
	DiscountAmount  string `json:"discount_amount"`  // 减免金额/折扣(如果是金额，单位元，如果是折扣，单位百分比)
}
type FullReductionSetting struct {
	FullReductionSetting int   `json:"full_reduction_setting"` // 满减活动设置：1-全部商品 2-指定商品分类
	CategoryIds          []int `json:"category_ids"`           // 品类id

}

type SpecialPriceRule struct {
	LimitBuyType string `json:"limit_buy_type"` // 购买限制类型: limit_by_day(每人每天限购), limit_by_period(每人活动期间限购)
	LimitBuyNum  int    `json:"limit_buy_num"`  // 购买限制数量
}

// 商品特价信息
type SpecialPriceProduct struct {
	// 商品id
	ProductRefId int `json:"product_ref_id"`
	// skuId
	SkuId int `json:"sku_id"`
	// 折扣率
	DiscountRate float64 `json:"discount_rate"`
	// 活动价（单位分）
	ActivityPrice int `json:"activity_price"`
}

func NewMarketingActivity(info *MarketingActivityInfo) (out *MarketingActivity, err error) {
	var rule string
	if info.Type == ActivityTypeFullReduction {
		rule = utils.JsonEncode(info.FullReductionRules)
	} else if info.Type == ActivityTypeSpecialPrice {
		rule = utils.JsonEncode(info.SpecialPriceRule)
	} else {
		return nil, errors.New("invalid activity type")
	}
	out = &MarketingActivity{
		ChainId:      info.ChainId,
		StoreId:      info.StoreId,
		Name:         info.Name,
		Type:         info.Type,
		TypeClass:    info.TypeClass,
		StartTime:    info.StartTime,
		EndTime:      info.EndTime,
		Periodicity:  info.Periodicity,
		Status:       info.Status,
		Rule:         rule,
		RuleDescribe: info.RuleDescribe,
		IsDeleted:    info.IsDeleted,
	}
	return out, nil

}

// Create 创建营销活动
func (m *MarketingActivity) Create(session *xorm.Session) error {
	if session == nil {
		return errors.New("session is nil")
	}
	// 插入数据库
	_, err := session.Insert(m)
	if err != nil {
		return fmt.Errorf("insert marketing activity failed: %v", err)
	}

	return nil
}

// Update 更新营销活动
func (m *MarketingActivity) Update(session *xorm.Session) error {
	if session == nil {
		return errors.New("session is nil")
	}
	_, err := session.ID(m.Id).Update(m)
	if err != nil {
		return fmt.Errorf("update marketing activity failed: %v", err)
	}
	return nil
}

// ActivityListQuery 活动列表查询参数
type ActivityListQuery struct {
	Id        int    `json:"id"`         // 活动id
	Ids       []int  `json:"ids"`        // 活动id列表
	ChainId   int64  `json:"chain_id"`   // 连锁ID
	StoreId   string `json:"store_id"`   // 门店ID
	Status    int    `json:"status"`     // 活动状态：1-待开始 2-进行中 3-已结束，4-待开始和进行中
	Type      int    `json:"type"`       // 活动类型
	Name      string `json:"name"`       // 活动名称
	PageIndex int    `json:"page_index"` // 页码
	PageSize  int    `json:"page_size"`  // 每页条数
	OrderBy   string `json:"order_by"`   // 排序字段
}

// GetActivityList 获取活动列表
func (m *MarketingActivity) GetActivityList(session *xorm.Session, query ActivityListQuery) (data []MarketingActivity, total int64, err error) {
	if query.Id > 0 {
		session = session.Where("id=?", query.Id)
	}
	if len(query.Ids) > 0 {
		session = session.In("id", query.Ids)
	}
	// 构建查询条件
	if query.ChainId > 0 {
		session = session.Where("chain_id=?", query.ChainId)
	}
	if query.StoreId != "" {
		session = session.Where("store_id=?", query.StoreId)
	}
	if query.Status > 0 {
		if query.Status == 3 {
			session = session.Where("status=3 or end_time<?", time.Now().Format("2006-01-02 15:04:05"))
		} else if query.Status == 2 {
			session = session.Where("start_time<=?", time.Now().Format("2006-01-02 15:04:05")).Where("end_time>?", time.Now().Format("2006-01-02 15:04:05")).Where("status!=3")
		} else if query.Status == 1 {
			session = session.Where("start_time>?", time.Now().Format("2006-01-02 15:04:05")).Where("status!=3")
		} else if query.Status == 4 {
			session = session.Where("(start_time>? or end_time>?) and status!=3", time.Now().Format("2006-01-02 15:04:05"), time.Now().Format("2006-01-02 15:04:05"))
		}
	}

	if query.Type > 0 {
		session = session.Where("type=?", query.Type)
	}
	if query.Name != "" {
		session = session.Where("name like ?", "%"+query.Name+"%")
	}

	// 分页参数
	if query.PageIndex > 0 && query.PageSize > 0 {
		session = session.Limit(query.PageSize, (query.PageIndex-1)*query.PageSize)
	}

	// 排序
	if query.OrderBy != "" {
		session = session.OrderBy(query.OrderBy)
	} else {
		session = session.OrderBy("id desc")
	}

	total, err = session.Where("is_deleted=0").FindAndCount(&data)
	if err != nil {
		return nil, 0, fmt.Errorf("get marketing activity list failed: %v", err)
	}
	return data, total, nil
}

// GetActivityListMap 获取活动列表map
func (m *MarketingActivity) GetActivityListMap(session *xorm.Session, query ActivityListQuery) (out map[int]MarketingActivity, total int64, err error) {
	activities, total, err := m.GetActivityList(session, query)
	if err != nil {
		return nil, 0, err
	}
	out = make(map[int]MarketingActivity)
	for _, activity := range activities {
		out[activity.Id] = activity
	}
	return out, total, nil
}

func (m *MarketingActivity) GetRunningActivityThreshold(session *xorm.Session, query RunningActivityProductsQuery) (data []MarketingActivityInfo, total int64, err error) {
	if query.ChainId == 0 {
		return nil, 0, errors.New("chain_id is required")
	}
	if query.StoreId == "" {
		return nil, 0, errors.New("store_id is required")
	}

	data = make([]MarketingActivityInfo, 0)

	// 获取当前是星期几 （数据库里： 活动星期（周一到周日：2,3...7,1））
	weekday := int(time.Now().Weekday())
	if weekday == 0 { // 周日用1表示
		weekday = 1
	} else {
		weekday = weekday + 1
	}

	// 查询活动基本信息
	var activities []MarketingActivity
	err = session.Table("eshop.marketing_activity").
		Where("chain_id = ?", query.ChainId).
		Where("store_id = ?", query.StoreId).
		Where("type = ?", ActivityTypeFullReduction).
		Where("start_time <= ?", time.Now().Format("2006-01-02 15:04:05")).
		Where("end_time >= ?", time.Now().Format("2006-01-02 15:04:05")).
		Where("status != 3").
		Where("LOCATE(?, periodicity) > 0", weekday).
		Find(&activities)

	if err != nil {
		return nil, 0, err
	}

	// 查询活动关联的商品信息
	for _, activity := range activities {
		activityInfo := MarketingActivityInfo{
			MarketingActivity: activity,
		}

		// 解析rule字段为满减规则
		var rules []FullReductionRule
		err = json.Unmarshal([]byte(activity.Rule), &rules)
		if err != nil {
			return nil, 0, err
		}
		activityInfo.FullReductionRules = rules

		// 查询活动关联的商品
		var products []*MarketingProduct
		session = session.Table("eshop.marketing_product").
			Where("chain_id = ? ", query.ChainId).
			Where("store_id = ?", query.StoreId).
			Where("ref_id = ?", activity.Id)
		if query.Type > 0 {
			session.Where(" type=?", query.Type)
		}
		err = session.Find(&products)
		if err != nil {
			return nil, 0, err
		}
		activityInfo.MarketingProducts = products

		data = append(data, activityInfo)
	}

	return data, total, nil
}
