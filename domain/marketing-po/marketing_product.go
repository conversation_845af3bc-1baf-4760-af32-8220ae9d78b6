package marketing_po

import (
	"eShop/infra/utils"
	"errors"
	"fmt"
	"strings"
	"time"

	"xorm.io/xorm"
)

// 关联类型：1-优惠券 2-次卡 3-储值卡 4-满减 5-特价
const (
	TypeCoupon          = 1 // 优惠券
	TypeTimesCard       = 2 // 次卡
	TypeStoredValueCard = 3 // 储值卡
	TypeFullReduction   = 4 // 满减
	TypeSpecialPrice    = 5 // 特价
)

// 适用类型：1-商品 2-商品品类
const (
	ProductApplyTypeGoods    = 1 // 商品
	ProductApplyTypeCategory = 2 // 商品品类
	ExcludeApplyTypeGoods    = 3 // 排除商品
)

// MarketingProduct 营销适用商品信息
type MarketingProduct struct {
	Id            int       `xorm:"pk autoincr 'id'"`                           // 唯一数据ID
	ChainId       int64     `xorm:"chain_id"`                                   // 连锁ID
	StoreId       string    `xorm:"store_id"`                                   // 店铺ID
	RefId         int       `xorm:"ref_id"`                                     // 关联ID
	Type          int       `xorm:"type"`                                       // 关联类型：1-优惠券 2-次卡 3-储值卡 4-满减 5-特价
	ApplyType     int       `xorm:"apply_type"`                                 // 适用类型：1-商品 2-商品品类
	ProductType   int       `xorm:"product_type"`                               // 商品类型：1-实物 2-服务 3-活体
	ProductRefId  int       `xorm:"product_ref_id"`                             // 商品关联ID（商品id，或者品类id）
	SkuId         int       `xorm:"sku_id"`                                     // skuId
	DiscountRate  float64   `xorm:"discount_rate"`                              // 折扣率
	ActivityPrice int       `xorm:"activity_price"`                             // 活动价
	CreatedTime   time.Time `xorm:"created 'created_time'" json:"created_time"` // 创建时间
	UpdatedTime   time.Time `xorm:"updated 'updated_time'" json:"updated_time"` // 更新时间

}

// BatchCreate 批量创建营销商品
func (m *MarketingProduct) BatchCreate(session *xorm.Session, products []*MarketingProduct) (err error) {
	if session == nil {
		return errors.New("session is nil")
	}
	if len(products) == 0 {
		return nil
	}

	// 批量插入
	_, err = session.Insert(products)
	if err != nil {
		return err
	}

	return nil
}
func (m *MarketingProduct) Update(session *xorm.Session) (err error) {
	_, err = session.Table("eshop.marketing_product").ID(m.Id).Update(m)
	if err != nil {
		return err
	}
	return nil
}

func (m *MarketingProduct) Delete(session *xorm.Session, ids []int) (err error) {
	_, err = session.Table("eshop.marketing_product").In("id", ids).Delete()
	if err != nil {
		return err
	}
	return nil
}

// ActivityProductListQuery 活动商品列表查询参数
type ActivityProductListQuery struct {
	ActivityIds []int  // 活动id列表
	ActivityId  int    // 活动id
	ChainId     int64  // 连锁id
	StoreId     string // 门店id
	OutType     int    // 输出类型：0-列表 1-map[连锁id_门店id_活动id][] 2-map[连锁id_门店id_活动id_关联id]
	SkuIds      []int  // 商品SKUID
}

// 获取待开始 和 进行中的活动商品列表

// GetActivityProductList 获取活动商品列表
func (m *MarketingProduct) GetActivityProductList(session *xorm.Session, query ActivityProductListQuery) (data []MarketingProduct, out1 map[string][]MarketingProduct, out2 map[string]MarketingProduct, err error) {
	if session == nil {
		return nil, nil, nil, errors.New("session is nil")
	}

	data = make([]MarketingProduct, 0)
	if len(query.ActivityIds) > 0 {
		session.In("ref_id", query.ActivityIds)
	}
	if query.ActivityId > 0 {
		session.Where("ref_id = ?", query.ActivityId)
	}
	if query.ChainId == 0 {
		return nil, nil, nil, errors.New("chain_id is required")
	}
	if query.StoreId != "" {
		session = session.Where("store_id = ?", query.StoreId)
	}
	if len(query.SkuIds) > 0 {
		session.In("sku_id", query.SkuIds)
	}
	err = session.Where("chain_id = ?", query.ChainId).Find(&data)

	if err != nil {
		return nil, nil, nil, fmt.Errorf("find marketing products failed: %v", err)
	}

	out1 = make(map[string][]MarketingProduct, 0)
	out2 = make(map[string]MarketingProduct, 0)
	for _, v := range data {

		switch query.OutType {
		case 1:
			k := fmt.Sprintf("%s_%d", v.StoreId, v.RefId)
			if _, ok := out1[k]; !ok {
				out1[k] = make([]MarketingProduct, 0)
			}
			out1[k] = append(out1[k], v)
		case 2:
			out2[fmt.Sprintf("%s_%d_%d_%d_%d", v.StoreId, v.RefId, v.ApplyType, v.ProductRefId, v.SkuId)] = v

		}
	}

	return data, out1, out2, nil
}

// 检查指定商品是否参与其他特价活动中
// 检查指定分类是否参与其他满减活动中
func (m *MarketingProduct) CheckSkuIsParticipateOtherActivity(session *xorm.Session, query CheckSkuIsParticipateOtherActivity) (marketingProductMarketingActivity []MarketingProductMarketingActivity, err error) {
	marketingProductMarketingActivity = make([]MarketingProductMarketingActivity, 0)
	if query.ChainId == 0 {
		err = errors.New("chain_id is required")
		return
	}
	if query.StoreId == "" {
		err = errors.New("store_id is required")
		return
	}
	if query.Type != TypeSpecialPrice && query.Type != TypeFullReduction {
		err = errors.New("活动类型错误")
		return
	}
	// 特价活动，检查同个商品不能同时存在多个活动中
	if query.Type == TypeSpecialPrice && len(query.SkuIds) == 0 {
		return
	}
	// 满减活动， 检查同个品类不能同时存在多个活动中
	if query.Type == TypeFullReduction && len(query.CategoryIdOfflines) == 0 {
		err = errors.New("满减活动， 检查同个品类不能同时存在多个活动中,入参错误")
		return
	}

	// 注意： 满减活动 ， 如果设置全部商品可用即marketing_product.type ==4时，则apply_type==1 && product_ref_id==0
	session = session.Table("eshop.marketing_product").Alias("a").Select("a.*,b.*").
		Join("INNER", "eshop.marketing_activity b", "a.ref_id = b.id and a.chain_id = b.chain_id and a.store_id = b.store_id").
		Where("a.chain_id = ?", query.ChainId).
		Where("a.store_id = ?", query.StoreId).
		Where("(b.start_time <= ? and b.end_time >= ?) or (b.start_time > ?)", time.Now().Format("2006-01-02 15:04:05"), time.Now().Format("2006-01-02 15:04:05"), time.Now().Format("2006-01-02 15:04:05")).
		Where("b.status != 3").
		Where("a.apply_type != 3").
		Where("a.type=?", query.Type)
	if query.NotEqualActivityId > 0 {
		session = session.Where("b.id != ?", query.NotEqualActivityId)
	}

	// 查询指定sku是否参与活动。
	if query.Type == TypeSpecialPrice {
		SkuIds := utils.IntSliceToStringSlice(query.SkuIds)
		skuidStr := strings.Join(SkuIds, ",")
		// 注意： 情况1：特价活动适用指定商品； 情况2：满减活动适用所有商品的情况；情况3：满减活动适用指定分类
		session.Where("(a.sku_id>0 and a.sku_id in (?))", skuidStr)
	} else if query.Type == TypeFullReduction {
		CategoryIdOffline := utils.IntSliceToStringSlice(query.CategoryIdOfflines)
		CategoryIdOfflineStr := strings.Join(CategoryIdOffline, ",")
		// 注意： 情况1：特价活动适用指定商品； 情况2：满减活动适用所有商品的情况；情况3：满减活动适用指定分类
		session.Where("a.apply_type=2 and a.product_ref_id in (?)", CategoryIdOfflineStr)
	}

	err = session.Find(&marketingProductMarketingActivity)

	if err != nil {
		return
	}

	return
}

type CheckSkuIsParticipateOtherActivity struct {
	NotEqualActivityId int    // 活动id列表（排除指定活动）
	ChainId            int64  // 连锁id
	StoreId            string // 门店id
	SkuIds             []int  // 商品SKUID
	Type               int    //关联类型：1-优惠券 2-次卡 3-储值卡 4-满减 5-特价
	CategoryIdOfflines []int  // 指定商品分类
}

// GetRunningActivityProducts 获取正在参与特价活动的商品列表
type RunningActivityProductsQuery struct {
	ChainId            int64  // 连锁id
	StoreId            string // 门店id
	PageIndex          int    // 页码
	PageSize           int    // 每页条数
	SkuIds             []int  // 商品SKUID
	Type               int    //关联类型：1-优惠券 2-次卡 3-储值卡 4-满减 5-特价
	CategoryIdOfflines []int  // 指定商品分类
}

type RunningCouponProductsQuery struct {
	ChainId   int64  // 连锁id
	StoreId   string // 门店id
	PageIndex int    // 页码
	PageSize  int    // 每页条数
	SkuIds    []int  // 商品SKUID
}

type MarketingProductMarketingActivity struct {
	MarketingProduct  `xorm:"extends"`
	MarketingActivity `xorm:"extends"`
}

// GetRunningActivityProducts 获取正在参与特价活动或店铺满减活动的商品列表
func (m *MarketingProduct) GetRunningActivityProducts(session *xorm.Session, query RunningActivityProductsQuery) (marketingProductMarketingActivity []MarketingProductMarketingActivity, total int64, err error) {
	marketingProductMarketingActivity = make([]MarketingProductMarketingActivity, 0)
	if query.ChainId == 0 {
		err = errors.New("chain_id is required")
		return
	}
	if query.StoreId == "" {
		err = errors.New("store_id is required")
		return
	}
	// 获取当前是星期几 （数据库里： 活动星期（周一到周日：2,3...7,1））
	weekday := int(time.Now().Weekday())
	if weekday == 0 { // 周日用1表示
		weekday = 1
	} else {
		weekday = weekday + 1
	}

	// 注意： 满减活动 ， 如果设置全部商品可用即marketing_product.type ==4时，则apply_type==1 && product_ref_id==0
	session = session.Table("eshop.marketing_product").Alias("a").Select("a.*,b.*").
		Join("INNER", "eshop.marketing_activity b", "a.ref_id = b.id and a.chain_id = b.chain_id and a.store_id = b.store_id").
		Where("a.chain_id = ?", query.ChainId).
		Where("a.store_id = ?", query.StoreId).
		Where("b.start_time <= ?", time.Now().Format("2006-01-02 15:04:05")).
		Where("b.end_time >= ?", time.Now().Format("2006-01-02 15:04:05")).
		Where("b.status != 3").
		Where("a.apply_type != 3").
		Where("LOCATE(?, periodicity) > 0", weekday)
	// 查询指定sku是否参与活动。
	if len(query.SkuIds) > 0 {
		SkuIds := utils.IntSliceToStringSlice(query.SkuIds)
		skuidStr := strings.Join(SkuIds, ",")

		CategoryIdOffline := utils.IntSliceToStringSlice(query.CategoryIdOfflines)
		CategoryIdOfflineStr := strings.Join(CategoryIdOffline, ",")
		// 注意： 情况1：特价活动适用指定商品； 情况2：满减活动适用所有商品的情况；情况3：满减活动适用指定分类
		s := fmt.Sprintf("(a.type=5 and a.sku_id>0 and a.sku_id in (%s) ) or (a.type=4 and a.apply_type=1 and  a.product_ref_id=0)", skuidStr)
		if len(CategoryIdOfflineStr) > 0 {
			s = fmt.Sprintf("%s or (a.type=4 and a.apply_type=2 and a.product_ref_id in (%s))", s, CategoryIdOfflineStr)
		}

		session.Where(s)
	}

	if query.Type > 0 {
		session = session.Where("a.type = ?", query.Type)

	}

	// 分页参数
	if query.PageIndex > 0 && query.PageSize > 0 {
		total, err = session.Limit(query.PageSize, (query.PageIndex-1)*query.PageSize).FindAndCount(&marketingProductMarketingActivity)
	} else {
		err = session.Find(&marketingProductMarketingActivity)
	}
	if err != nil {
		return
	}

	return
}

type MarketingProductMarketingCoupon struct {
	MarketingProduct `xorm:"extends"`
	MarketingCoupon  `xorm:"extends"`
}

// GetRunningCouponProducts 获取正在参与优惠券活动的商品列表
// marketingCoupons 适用于全部商品的优惠券
func (m *MarketingProduct) GetRunningCouponProducts(session *xorm.Session, query RunningCouponProductsQuery) (data []MarketingProductMarketingCoupon, total int64, marketingCoupons []MarketingCoupon, err error) {
	data = make([]MarketingProductMarketingCoupon, 0)
	if query.ChainId == 0 {
		err = errors.New("chain_id is required")
		return
	}
	if query.StoreId == "" {
		err = errors.New("store_id is required")
		return
	}

	// 查询是否有有效的， 且适用所有商品的优惠券
	marketingCoupons = make([]MarketingCoupon, 0)
	err = session.Table("eshop.marketing_coupon").Alias("b").
		Where("b.store_id = ?", query.StoreId).
		Where("b.chain_id = ?", query.ChainId).
		Where("(b.use_start_type=3 and  b.start_time <= ? and b.end_time >= ?) or b.use_start_type in (1,2)", time.Now().Format("2006-01-02 15:04:05"), time.Now().Format("2006-01-02 15:04:05")).
		Where("b.status != 3").
		//Where("a.apply_type != 3").
		Where("b.apply_product=?", ApplyProductAll).
		Where("b.is_deleted=0").Find(&marketingCoupons)
	if err != nil {
		return
	}

	// 指定商品可用 和 指定商品不可用 商品列表
	session = session.Table("eshop.marketing_product").Alias("a").Select("a.*,b.*").
		Join("INNER", "eshop.marketing_coupon b", "a.ref_id = b.id and a.chain_id = b.chain_id and a.store_id = b.store_id").
		Where("a.chain_id = ?", query.ChainId).
		Where("a.store_id = ?", query.StoreId).
		Where("a.type = ?", TypeCoupon).
		Where("(b.use_start_type=3 and  b.start_time <= ? and b.end_time >= ?) or b.use_start_type in (1,2)", time.Now().Format("2006-01-02 15:04:05"), time.Now().Format("2006-01-02 15:04:05")).
		Where("b.status != 3").
		Where("a.apply_type != 3").
		Where("b.is_deleted=0")
	// 查询指定sku是否参与活动。
	if len(query.SkuIds) > 0 {
		SkuIds := utils.IntSliceToStringSlice(query.SkuIds)
		skuidStr := strings.Join(SkuIds, ",")
		// 分两种情况：2-指定商品可用， 3-指定商品不可用
		session.Where("(b.apply_product=? and a.sku_id in (?)) or (b.apply_product=? and a.sku_id not in (?))", ApplyProductYes, skuidStr, ApplyProductNo, skuidStr)
	}
	if query.PageIndex > 0 && query.PageSize > 0 {
		total, err = session.Limit(query.PageSize, (query.PageIndex-1)*query.PageSize).FindAndCount(&data)
	} else {
		err = session.Find(&data)
	}
	if err != nil {
		return
	}
	return
}
