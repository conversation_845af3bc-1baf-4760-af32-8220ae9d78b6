package marketing_po

import (
	"errors"
	"fmt"

	"xorm.io/xorm"

	"github.com/spf13/cast"
)

// Customer 客户领域模型
type Customer struct {
	Name   string  `xorm:"name"`
	Phone  string  `xorm:"phone"`
	IsStop []uint8 `xorm:"is_stop"`
}

// GetCustomerInfo 获取客户信息
func GetCustomerInfo(session *xorm.Session, customerId, storeId string) (*Customer, error) {
	var customer Customer
	exists, err := session.Table("eshop_saas.c_customer_info").
		Where("id = ? AND tenant_id = ? AND is_deleted = 0",
			cast.ToInt64(customerId), cast.ToInt64(storeId)).Get(&customer)
	if err != nil {
		return nil, fmt.Errorf("查询客户信息失败: %v", err)
	}
	if !exists {
		return nil, errors.New("客户不存在")
	}
	if len(customer.IsStop) > 0 && customer.IsStop[0] == 1 {
		return nil, errors.New("客户已停用")
	}
	return &customer, nil
}
