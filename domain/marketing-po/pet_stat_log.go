// Package marketing_po 数据监测事件流水表领域模型
package marketing_po

import "time"

type PetStatLog struct {
	Id         int64     `xorm:"pk autoincr" json:"id"`
	MetricType int       `xorm:"metric_type" json:"metric_type"`
	MetricName string    `xorm:"metric_name" json:"metric_name"`
	WorkCode   string    `xorm:"work_code" json:"work_code"`
	UserId     string    `xorm:"user_id" json:"user_id"`
	UniqueId   string    `xorm:"unique_id" json:"unique_id"`
	CreateTime time.Time `xorm:"created" json:"create_time"`
	UpdateTime time.Time `xorm:"updated" json:"update_time"`
}

func (PetStatLog) TableName() string {
	return "pet_stat_log"
}
