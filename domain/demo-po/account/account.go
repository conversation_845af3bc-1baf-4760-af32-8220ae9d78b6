package account

import (
	"context"
	"errors"
	"time"

	"xorm.io/xorm"
)

var (
	ErrInsufficientBalance = errors.New("insufficient balance")
	ErrInvalidAmount       = errors.New("invalid amount")
	ErrVersionConflict     = errors.New("version conflict")
	ErrInvalidStatus       = errors.New("invalid status")
)

// Account 账户
type Account struct {
	ID        string    `xorm:"pk" json:"id"`
	Balance   float64   `json:"balance"`
	Status    string    `json:"status"`
	Version   int       `xorm:"version" json:"version"`
	CreatedAt time.Time `xorm:"created" json:"created_at"`
	UpdatedAt time.Time `xorm:"updated" json:"updated_at"`
}

const (
	// StatusActive 激活状态
	StatusActive = "active"
	// StatusInactive 未激活状态
	StatusInactive = "inactive"
	// StatusFrozen 冻结状态
	StatusFrozen = "frozen"
	// StatusClosed 关闭状态
	StatusClosed = "closed"
)

// NewAccount 创建账户
func NewAccount(id string, balance float64) *Account {
	now := time.Now()
	return &Account{
		ID:        id,
		Balance:   balance,
		Status:    StatusActive,
		Version:   1,
		CreatedAt: now,
		UpdatedAt: now,
	}
}

// Transfer 转账
func (a *Account) Transfer(to *Account, amount float64) error {
	if err := a.Withdraw(amount); err != nil {
		return err
	}
	return to.Deposit(amount)
}

// Withdraw 提现
func (a *Account) Withdraw(amount float64) error {
	if amount <= 0 {
		return ErrInvalidAmount
	}
	if a.Status != StatusActive {
		return ErrInvalidStatus
	}
	if a.Balance < amount {
		return ErrInsufficientBalance
	}
	a.Balance -= amount
	a.Version++
	a.UpdatedAt = time.Now()
	return nil
}

// Deposit 存款
func (a *Account) Deposit(amount float64) error {
	if amount <= 0 {
		return ErrInvalidAmount
	}
	if a.Status != StatusActive {
		return ErrInvalidStatus
	}
	a.Balance += amount
	a.Version++
	a.UpdatedAt = time.Now()
	return nil
}

// Freeze 冻结账户
func (a *Account) Freeze() error {
	if a.Status != StatusActive {
		return ErrInvalidStatus
	}
	a.Status = StatusFrozen
	a.Version++
	a.UpdatedAt = time.Now()
	return nil
}

// Unfreeze 解冻账户
func (a *Account) Unfreeze() error {
	if a.Status != StatusFrozen {
		return ErrInvalidStatus
	}
	a.Status = StatusActive
	a.Version++
	a.UpdatedAt = time.Now()
	return nil
}

// Close 关闭账户
func (a *Account) Close() error {
	if a.Status == StatusClosed {
		return ErrInvalidStatus
	}
	a.Status = StatusClosed
	a.Version++
	a.UpdatedAt = time.Now()
	return nil
}

// Save 保存账户
func (a *Account) Save(ctx context.Context, session *xorm.Session) error {
	if session != nil {
		_, err := session.Insert(a)
		return err
	}
	_, err := session.Insert(a)
	return err
}

// Update 更新账户
func (a *Account) Update(ctx context.Context, session *xorm.Session) error {
	if session != nil {
		_, err := session.ID(a.ID).Update(a)
		return err
	}
	_, err := session.ID(a.ID).Update(a)
	return err
}

// FindByID 根据ID查找账户
func FindByID(ctx context.Context, id string, session *xorm.Session) (*Account, error) {
	var acc Account
	var has bool
	var err error

	if session != nil {
		has, err = session.ID(id).Get(&acc)
	} else {
		has, err = session.ID(id).Get(&acc)
	}

	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &acc, nil
}

// Delete 删除账户
func (a *Account) Delete(ctx context.Context, session *xorm.Session) error {
	if session != nil {
		_, err := session.ID(a.ID).Delete(a)
		return err
	}
	_, err := session.ID(a.ID).Delete(a)
	return err
}
