package account

import "eShop/infra/events"

const (
	EventTypeAccountCreated     = "account.created"
	EventTypeAccountCredited    = "account.credited"
	EventTypeAccountDebited     = "account.debited"
	EventTypeAccountTransferred = "account.transferred"
	EventTypeAccountWithdrawn   = "account.withdrawn"
	EventTypeAccountDeposited   = "account.deposited"
)

// AccountCreatedEvent 账户创建事件
type AccountCreatedEvent struct {
	events.BaseEvent
	Balance float64 `json:"balance"`
}

// AccountCreditedEvent 账户存款事件
type AccountCreditedEvent struct {
	events.BaseEvent
	Amount  float64 `json:"amount"`
	Balance float64 `json:"balance"`
}

// AccountDebitedEvent 账户取款事件
type AccountDebitedEvent struct {
	events.BaseEvent
	Amount  float64 `json:"amount"`
	Balance float64 `json:"balance"`
}

// AccountTransferredEvent 账户转账事件
type AccountTransferredEvent struct {
	events.BaseEvent
	FromID  string  `json:"from_id"`
	ToID    string  `json:"to_id"`
	Amount  float64 `json:"amount"`
	Balance float64 `json:"balance"`
}

// AccountWithdrawnEvent 账户提现事件
type AccountWithdrawnEvent struct {
	events.BaseEvent
	Amount  float64 `json:"amount"`
	Balance float64 `json:"balance"`
}

// AccountDepositedEvent 账户存款事件
type AccountDepositedEvent struct {
	events.BaseEvent
	Amount  float64 `json:"amount"`
	Balance float64 `json:"balance"`
}
