package omnibus_po

import (
	"time"

	"xorm.io/xorm"
)

// CommissionProduct 提成商品表 - 仅用于存储指定商品的数据
type CommissionProduct struct {
	// 主键ID
	Id int `xorm:"pk autoincr 'id'" json:"id"`
	// 关联提成明细表ID
	DetailId int `xorm:"'detail_id'" json:"detailId"`
	// 商品ID
	SkuId int64 `xorm:"'sku_id'" json:"skuId"`
	// 商品名称
	ProductName string `xorm:"'product_name'" json:"productName"`
	// 商品类型：与CommissionType对应，1-商品 2-服务 3-寄养 4-活体
	ProductType int `xorm:"'product_type'" json:"productType"`
	// 创建时间
	CreatedTime time.Time `xorm:"created 'created_time'" json:"createdTime"`
	// 更新时间
	UpdatedTime time.Time `xorm:"updated 'updated_time'" json:"updatedTime"`
}

// TableName 设置表名
func (c *CommissionProduct) TableName() string {
	return "eshop.commission_product"
}

// 创建提成商品
func (c *CommissionProduct) Create(session *xorm.Session) error {
	_, err := session.Insert(c)
	return err
}

// 批量创建提成商品
func (c *CommissionProduct) BatchCreate(session *xorm.Session, products []*CommissionProduct) error {
	_, err := session.Insert(products)
	return err
}

// 根据明细ID删除提成商品
func (c *CommissionProduct) DeleteByDetailId(session *xorm.Session, detailId int) error {
	_, err := session.Where("detail_id = ?", detailId).Delete(new(CommissionProduct))
	return err
}

// 根据明细ID获取提成商品列表
func (c *CommissionProduct) GetByDetailId(session *xorm.Session, detailId int) ([]*CommissionProduct, error) {
	var products []*CommissionProduct
	err := session.Where("detail_id = ?", detailId).Find(&products)
	return products, err
}

// 根据多个明细ID获取提成商品列表
func (c *CommissionProduct) GetByDetailIds(session *xorm.Session, detailIds []int) ([]*CommissionProduct, error) {
	var products []*CommissionProduct
	err := session.In("detail_id", detailIds).Find(&products)
	return products, err
}
