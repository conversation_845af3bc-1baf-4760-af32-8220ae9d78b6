// domain/omnibus-po/employee.go

package omnibus_po

import (
	"fmt"
	"time"

	"xorm.io/xorm"
)

// Employee 员工领域模型
type Employee struct {
	Id            int64     `xorm:"id pk" json:"id"`                        // ID
	TenantId      int64     `xorm:"tenant_id" json:"tenant_id"`             // 所属店铺
	UserId        int64     `xorm:"user_id" json:"user_id"`                 // 用户ID
	OrgId         int64     `xorm:"org_id" json:"org_id"`                   // 部门ID
	RoleId        int64     `xorm:"role_id" json:"role_id"`                 // 角色ID
	Account       string    `xorm:"account" json:"account"`                 // 账号
	RealName      string    `xorm:"real_name" json:"real_name"`             // 真实姓名
	EmployeeNo    string    `xorm:"employee_no" json:"employee_no"`         // 店员编号
	Mobile        string    `xorm:"mobile" json:"mobile"`                   // 手机
	State         bool      `xorm:"state" json:"state"`                     // 状态
	IsDefault     bool      `xorm:"is_default" json:"is_default"`           // 是否默认员工
	OperatorName  string    `xorm:"operator_name" json:"operator_name"`     // 操作人
	IsDeleted     bool      `xorm:"is_deleted" json:"is_deleted"`           // 删除状态
	SourceChainId int64     `xorm:"source_chain_id" json:"source_chain_id"` // 代运营来源连锁id
	CreatedBy     int64     `xorm:"created_by" json:"created_by"`           // 创建人
	CreatedTime   time.Time `xorm:"created_time" json:"created_time"`       // 创建时间
	UpdatedBy     int64     `xorm:"updated_by" json:"updated_by"`           // 最后更新人
	UpdatedTime   time.Time `xorm:"updated_time" json:"updated_time"`       // 最后更新时间
	DeleteRemark  string    `xorm:"delete_remark" json:"delete_remark"`     // 删除备注
}

// TableName 表名
func (e *Employee) TableName() string {
	return "eshop_saas.t_employee"
}

// NewEmployee 创建员工领域模型实例
func NewEmployee() *Employee {
	return &Employee{}
}

// GetStoreEmployees 获取店铺所有员工
func (e *Employee) GetStoreEmployees(session *xorm.Session, tenantId int64) ([]Employee, error) {
	var employees []Employee

	// 构建SQL查询
	sql := `SELECT e.* FROM t_employee e
			LEFT JOIN t_tenant t ON t.id = e.tenant_id
			WHERE e.tenant_id = ? 
			AND e.source_chain_id IN (0, t.chain_id)
			AND e.is_deleted = 0
			AND e.state = 1`

	err := session.SQL(sql, tenantId).Find(&employees)
	if err != nil {
		return nil, fmt.Errorf("查询店铺员工信息失败: %v", err)
	}

	return employees, nil
}

// GetEmployeeById 根据ID获取员工信息
func (e *Employee) GetEmployeeById(session *xorm.Session, id int64) (*Employee, error) {
	var employee Employee
	exists, err := session.Table(e.TableName()).
		Where("id = ?", id).
		Where("is_deleted = ?", false).
		Where("state = ?", true).
		Get(&employee)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, nil
	}
	return &employee, nil
}
