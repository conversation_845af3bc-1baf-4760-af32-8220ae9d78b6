package omnibus_po

import (
	"eShop/infra/log"
	"testing"
	"time"

	"xorm.io/xorm"
)

func TestCommissionPerformance_AssignOrderCommission(t *testing.T) {
	type fields struct {
		Id               int
		OrderId          int
		OrderNo          string
		EmployeeId       int64
		RealName         string
		StoreId          int64
		ProductType      int
		SkuId            int64
		ProductName      string
		SalesAmount      int64
		CommissionRate   float64
		CommissionAmount int64
		SetupId          int
		SetupName        string
		OrderTime        time.Time
		Operator         string
		OperatorId       int64
		CreatedTime      time.Time
		UpdatedTime      time.Time
	}
	type args struct {
		session    *xorm.Session
		orderItems []OrderItemCommission
		operator   string
		operatorId int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    int
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			fields: fields{
				Id:               1,
				OrderId:          1001,
				OrderNo:          "ORD20230615001",
				EmployeeId:       101,
				RealName:         "张三",
				StoreId:          2001,
				ProductType:      1,
				SkuId:            3001,
				ProductName:      "测试商品A",
				SalesAmount:      10000,
				CommissionRate:   500, // 5%
				CommissionAmount: 500, // 5元
				SetupId:          201,
				SetupName:        "标准提成方案",
				OrderTime:        time.Now(),
				Operator:         "admin",
				OperatorId:       999,
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommissionPerformance{
				Id:               tt.fields.Id,
				OrderId:          tt.fields.OrderId,
				OrderNo:          tt.fields.OrderNo,
				EmployeeId:       tt.fields.EmployeeId,
				RealName:         tt.fields.RealName,
				StoreId:          tt.fields.StoreId,
				ProductType:      tt.fields.ProductType,
				SkuId:            tt.fields.SkuId,
				ProductName:      tt.fields.ProductName,
				SalesAmount:      tt.fields.SalesAmount,
				CommissionRate:   tt.fields.CommissionRate,
				CommissionAmount: tt.fields.CommissionAmount,
				SetupId:          tt.fields.SetupId,
				SetupName:        tt.fields.SetupName,
				OrderTime:        tt.fields.OrderTime,
				Operator:         tt.fields.Operator,
				OperatorId:       tt.fields.OperatorId,
				CreatedTime:      tt.fields.CreatedTime,
				UpdatedTime:      tt.fields.UpdatedTime,
			}
			got, err := c.AssignOrderCommission(tt.args.session, tt.args.orderItems, tt.args.operator, tt.args.operatorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("AssignOrderCommission() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("AssignOrderCommission() got = %v, want %v", got, tt.want)
			}
		})
	}
}
