package omnibus_po

import (
	"eShop/infra/utils"
	"errors"

	"xorm.io/xorm"
)

type TScrmUserInfo struct {
	Id                int    `xorm:"not null pk autoincr INT(10)"`
	UserId            string `xorm:"not null comment('用户ID作为唯一标示') unique VARCHAR(32)"`
	UserImId          string `xorm:"not null comment('用户IM唯一标示') VARCHAR(32)"`
	UserImToken       string `xorm:"not null comment('im用户token密码') VARCHAR(128)"`
	UserName          string `xorm:"not null default '''' comment('用户姓名') VARCHAR(32)"`
	UserSex           int    `xorm:"not null default 0 comment('用户性别 0未知 1男 2女') TINYINT(3)"`
	UserMobile        string `xorm:"not null default '''' comment('用户手机号') index VARCHAR(16)"`
	UserSource        int    `xorm:"not null default 0 comment('用户来源 0阿闻小程序') TINYINT(3)"`
	UserStatus        int    `xorm:"not null default 0 comment('用户状态 0正常 1锁定 -1删除') TINYINT(3)"`
	UserAvatar        string `xorm:"not null default '''' comment('用户头像') VARCHAR(256)"`
	UserBirthday      string `xorm:"<-"`
	FirstRaisesPet    string `xorm:"<-"`
	Country           string `xorm:"not null default '''' comment('用户所在国家') VARCHAR(32)"`
	Province          string `xorm:"not null default '''' comment('用户所在省份') VARCHAR(32)"`
	City              string `xorm:"not null default '''' comment('用户所在城市') VARCHAR(32)"`
	Area              string `xorm:"not null default '''' comment('用户所在区域') VARCHAR(32)"`
	UserRemark        string `xorm:"not null default '''' comment('用户备注') VARCHAR(128)"`
	CreateTime        string `xorm:"<-"`
	UpdateTime        string `xorm:"<-"`
	BigdataUserId     string `xorm:"default 'NULL' comment('大数据唯一Id') index VARCHAR(32)"`
	RemoteTreatStatus int    `xorm:"not null default 0 comment('远程诊疗  0:否 1:是') TINYINT(2)"`
	LevelId           string `xorm:"not null default '''' comment('会员等级') VARCHAR(32)"`
	IsReal            int    `xorm:"default 0 comment('是否实名认证 0否1是') TINYINT(2)"`
	BackupName1       string `xorm:"not null default '''' comment('备用姓名1') VARCHAR(32)"`
	BackupMobile1     string `xorm:"not null default '''' comment('备用手机号1') VARCHAR(16)"`
	BackupName2       string `xorm:"not null default '''' comment('备用姓名2') VARCHAR(32)"`
	BackupMobile2     string `xorm:"not null default '''' comment('备用手机号2') VARCHAR(16)"`
	ChangeTimes       int    `xorm:"not null default 0 comment('修改手机号次数') INT(10)"`
	ModifyPhoneDate   string `xorm:"<-"`
	CountryCode       string `xorm:"not null default '''' comment('国家编码') VARCHAR(32)"`
	ProvinceCode      string `xorm:"not null default '''' comment('省份编码') VARCHAR(32)"`
	CityCode          string `xorm:"not null default '''' comment('城市编码') VARCHAR(32)"`
	AreaCode          string `xorm:"not null default '''' comment('区域编码') VARCHAR(32)"`
}

// 根据手机号码查询用户
func (t *TScrmUserInfo) GetUserByMobile(session *xorm.Session, mobile string) (user *TScrmUserInfo, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	if len(mobile) == 0 {
		err = errors.New("mobile is empty")
		return
	}
	user = new(TScrmUserInfo)
	if _, err = session.Table("scrm_organization_db.t_scrm_user_info").Where("user_mobile = ?", utils.MobileDecrypt(mobile)).Get(user); err != nil {
		return
	}

	return
}
