package omnibus_po

// 仓库对应的配送门店编码
type WarehouseDeliveryRelation struct {
	Id          int    `json:"id" xorm:"pk autoincr not null comment('主键') INT 'id'"`
	WarehouseId int    `json:"warehouse_id" xorm:"not null comment('仓库ID') INT 'warehouse_id'"`
	DeliveryId  int    `json:"delivery_id" xorm:"not null comment('配送配置ID') INT 'delivery_id'"`
	ShopNo      string `json:"shop_no" xorm:"default 'null' comment('配送门店编码') VARCHAR(100) 'shop_no'"`
}

func (t WarehouseDeliveryRelation) TableName() string {
	return "dc_dispatch.warehouse_delivery_relation"
}
