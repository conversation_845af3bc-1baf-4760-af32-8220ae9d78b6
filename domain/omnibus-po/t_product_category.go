package omnibus_po

import "time"

type TProductCategory struct {
	Id             int64     `json:"id" xorm:"pk not null comment('ID') BIGINT 'id'"`
	ChainId        int64     `json:"chain_id" xorm:"not null comment('连锁ID,连锁id=1000为初始化数据') BIGINT 'chain_id'"`
	CategoryCode   string    `json:"category_code" xorm:"not null default '' comment('目录编码') VARCHAR(64) 'category_code'"`
	CategoryName   string    `json:"category_name" xorm:"default '' comment('目录名称') VARCHAR(64) 'category_name'"`
	ParentId       int64     `json:"parent_id" xorm:"not null default 0 comment('上级目录ID') BIGINT 'parent_id'"`
	ProductType    string    `json:"product_type" xorm:"not null default '' comment('商品类型，GOODS实物，SER服务，ALIVE活体') CHAR(15) 'product_type'"`
	ProductTypeNum int       `json:"product_type_num" xorm:"not null default 0 comment('商品类型，1000: GOODS实物，2000: SER服务，3000: ALIVE活体') INT 'product_type_num'"`
	TreeGrade      int       `json:"tree_grade" xorm:"not null comment('树层级') INT 'tree_grade'"`
	IdPath         string    `json:"id_path" xorm:"not null default '' comment('目录path') VARCHAR(300) 'id_path'"`
	NamePath       string    `json:"name_path" xorm:"not null default '' comment('目录名称全路径') VARCHAR(300) 'name_path'"`
	Type           string    `json:"type" xorm:"not null default 'SELF' comment('所属类型: 自营: SELF,小程序: MINI==') VARCHAR(32) 'type'"`
	SortValue      int       `json:"sort_value" xorm:"not null default 0 comment('排序序号') INT 'sort_value'"`
	IsDeleted      bool      `json:"is_deleted" xorm:"not null default 0 comment('删除标识:0未删除,1已删除') Bit(1) 'is_deleted'"`
	CreatedBy      int       `json:"created_by" xorm:"not null default 0 comment('创建人ID') BIGINT 'created_by'"`
	CreatedTime    time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time'"`
	UpdatedBy      int       `json:"updated_by" xorm:"not null default 0 comment('更新人ID') BIGINT 'updated_by'"`
	UpdatedTime    time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time'"`
}
