package omnibus_po

import (
	"time"

	"xorm.io/xorm"
)

const (
	//任务状态:1:未开始;2:进行中;3:已完成 4:失败
	TaskStatusNotStart = 1
	TaskStatusDoing    = 2
	TaskStatusDone     = 3
	TaskStatusFail     = 4
)

type TaskListAsync struct {
	Id               int       `json:"id" xorm:"pk autoincr not null comment('任务id') INT 'id'"`
	TaskContent      int       `json:"task_content" xorm:"not null default 0 comment('任务内容:') INT 'task_content'"`
	TaskStatus       int8      `json:"task_status" xorm:"not null default 1 comment('任务状态:1:未开始;2:进行中;3:已完成 4:失败') TINYINT(1) 'task_status'"`
	TaskDetail       string    `json:"task_detail" xorm:"not null default '' comment('任务详情') VARCHAR(10000) 'task_detail'"`
	KeyStr           string    `json:"key_str" xorm:"not null default '' comment('任务KEY，相同KEY不允许同时进行') VARCHAR(500) 'key_str'"`
	OperationFileUrl string    `json:"operation_file_url" xorm:"not null comment('操作文件路径或者参数') TEXT 'operation_file_url'"`
	RequestHeader    string    `json:"request_header" xorm:"default 'null' comment('操作请求的token值，类似userinfo') VARCHAR(255) 'request_header'"`
	ResulteFileUrl   string    `json:"resulte_file_url" xorm:"not null default '' comment('操作结果文件路径') VARCHAR(255) 'resulte_file_url'"`
	CreateId         string    `json:"create_id" xorm:"not null default 0 comment('创建人id') VARCHAR(100) 'create_id'"`
	CreateName       string    `json:"create_name" xorm:"default '' comment('创建人姓名') VARCHAR(50) 'create_name'"`
	CreateMobile     string    `json:"create_mobile" xorm:"default '' comment('创建人手机号') VARCHAR(50) 'create_mobile'"`
	CreateIp         string    `json:"create_ip" xorm:"default '' comment('创建人ip') VARCHAR(100) 'create_ip'"`
	IpLocation       string    `json:"ip_location" xorm:"default '' comment('ip所属位置') VARCHAR(50) 'ip_location'"`
	SuccessNum       int       `json:"success_num" xorm:"default 0 comment('成功数量') INT 'success_num'"`
	FailNum          int       `json:"fail_num" xorm:"default 0 comment('失败数量') INT 'fail_num'"`
	ExtendedData     string    `json:"extended_data" xorm:"comment('任务名称扩展字段') TEXT 'extended_data'"`
	ContextData      string    `json:"context_data" xorm:"comment('上下文数据') LONGTEXT 'context_data'"`
	DoCount          int       `json:"do_count" xorm:"default 'null' comment('执行失败次数') INT 'do_count'"`
	ErrMes           string    `json:"err_mes" xorm:"default 'null' comment('上次执行失败原因') VARCHAR(500) 'err_mes'"`
	CreateTime       time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime       time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	ChainId          int64     `json:"chain_id" xorm:"default 0 comment('连锁id') INT 'chain_id'"`
	OrgId            int       `json:"org_id" xorm:"default 1 comment('主体id') INT 'org_id'"`
	IsPush           int       `json:"is_push" xorm:"default 0 comment('是否推送MQ了') INT 'is_push'"`
}

// 创建一条异步任务
func (m TaskListAsync) CreateAsyncTask(db *xorm.Engine) (err error) {
	_, err = db.Table("eshop.task_list_async").Insert(&m)
	return
}

// 创建一条异步任务
func (m TaskListAsync) CreateAsyncTask2(session *xorm.Session) (err error) {
	_, err = session.Table("eshop.task_list_async").Insert(&m)
	return
}

// 查询任务列表条件
type TaskListAsyncMapInfoReq struct {
	TaskContent int      // 任务内容
	TaskStatus  []int    // 任务状态:1:未开始;2:进行中;3:已完成 4:失败
	KeyStr      []string // 用于标识是否已存在这种任务，不运行同时进行的任务
	ContextData string   // 上下文数据
}

// 查询任务列表
func (m TaskListAsync) GetTaskListAsyncMapInfo(db *xorm.Engine, where TaskListAsyncMapInfoReq) ([]TaskListAsync, map[int]TaskListAsync, error) {
	session := db.NewSession()
	defer session.Close()

	if where.TaskContent > 0 {
		session = session.Where("task_content = ?", where.TaskContent)
	}

	if len(where.TaskStatus) > 0 {
		session = session.In("task_status", where.TaskStatus)
	}

	if len(where.KeyStr) > 0 {
		session = session.In("key_str", where.KeyStr)
	}

	if where.ContextData != "" {
		session = session.Where("context_data = ?", where.ContextData)
	}

	tasks := make([]TaskListAsync, 0)
	idMap := make(map[int]TaskListAsync)

	if err := session.Table("eshop.task_list_async").Find(&tasks); err != nil {
		return nil, nil, err
	}

	for _, task := range tasks {
		idMap[task.Id] = task
	}

	return tasks, idMap, nil
}
