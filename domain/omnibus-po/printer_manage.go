package omnibus_po

import "time"

type PrinterManage struct {
	Id          int       `json:"id" xorm:"pk autoincr not null INT 'id'"`
	PrinterSn   string    `json:"printer_sn" xorm:"not null comment('打印机编码') VARCHAR(64) 'printer_sn'"`
	StoreId     string    `json:"store_id" xorm:"default '' comment('门店财务编码') VARCHAR(64) 'store_id'"`
	CreateDate  time.Time `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('添加时间') DATETIME 'create_date'"`
	UpdateDate  time.Time `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'update_date'"`
	PrinterName string    `json:"printer_name" xorm:"not null comment('打印机名称') VARCHAR(64) 'printer_name'"`
	PrinterKey  string    `json:"printer_key" xorm:"not null comment('打印机密钥') VARCHAR(100) 'printer_key'"`
}

type FosterDeposit struct {
	Id             int64     `xorm:"pk autoincr" json:"id"`
	TenantId       int64     `json:"tenant_id"`
	OrderNo        string    `json:"order_no"`
	CustomerName   string    `json:"customer_name"`
	CustomerMobile string    `json:"customer_mobile"`
	SellerName     string    `json:"seller_name"`
	PayType        string    `json:"pay_type"`
	Amount         float64   `json:"amount"`
	CreatedTime    time.Time `json:"created_time"`
	DepositType    string    `json:"deposit_type"`
	// ... 其他字段省略 ...
}
