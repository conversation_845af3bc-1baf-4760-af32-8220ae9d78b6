package omnibus_po

import (
	"errors"

	"xorm.io/xorm"
)

type TScrmPetVaccinateRecord struct {
	Id            int    `xorm:"not null pk autoincr comment('主键') INT(11)"`
	PetId         string `xorm:"not null comment('宠物id') VARCHAR(32)"`
	OperationYear int    `xorm:"not null comment('接种日期/驱虫年份') INT(11)"`
	OperationDate string `xorm:"not null comment('接种日期/驱虫日期') DATE"`
	ShopName      string `xorm:"not null comment('接诊机构') VARCHAR(30)"`
	ProductName   string `xorm:"not null comment('产品名称') VARCHAR(30)"`
	Type          int    `xorm:"not null comment('1疫苗记录 2驱虫记录 3口腔 4体检 5洗护 6体况评分 7三围 8体重') TINYINT(4)"`
	CreateTime    string `xorm:"<-"`
	UpdateTime    string `xorm:"<-"`
	RecordPhoto   string `xorm:"not null default '' comment('记录拍照') VARCHAR(200)"`
}

func (s *TScrmPetVaccinateRecord) TableName() string {
	return "dc_customer.t_scrm_pet_vaccinate_record"

}

// 根据宠物id获取健康记录
func (s *TScrmPetVaccinateRecord) GetCustomerPetVaccinateRecord(session *xorm.Session, scrmPetIdSli []string) (list []TScrmPetVaccinateRecord, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	list = make([]TScrmPetVaccinateRecord, 0)
	if err = session.Table("dc_customer.t_scrm_pet_vaccinate_record").Where("type!=1 and type!=2").In("pet_id", scrmPetIdSli).
		Find(&list); err != nil {
		return
	}
	return
}
