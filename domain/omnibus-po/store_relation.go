package omnibus_po

import (
	"xorm.io/xorm"
)

type StoreRelation struct {
	Id             int    `json:"id" xorm:"pk autoincr not null INT 'id'"`
	FinanceCode    string `json:"finance_code" xorm:"default 'null' comment('财务编码') VARCHAR(50) 'finance_code'"`
	ChannelId      int    `json:"channel_id" xorm:"not null default 0 comment('渠道id(1-阿闻，2-美团，3-饿了么，4-京东到家)') INT 'channel_id'"`
	ChannelStoreId string `json:"channel_store_id" xorm:"default 'null' comment('渠道门店id') VARCHAR(50) 'channel_store_id'"`
	CustomCode     string `json:"custom_code" xorm:"default 'null' comment('全渠道往来单位') VARCHAR(255) 'custom_code'"`
	IsCreate       int    `json:"is_create" xorm:"default 'null' comment('是否创建了配送门店') INT 'is_create'"`
}

func (t StoreRelation) TableName() string {
	return "datacenter.store_relation"
}
func NewStoreRelation() *StoreRelation {
	return new(StoreRelation)
}

// out 结构体说明： map[财务编码][渠道id]{StoreRelation}
func GetStoreMapChannelStoreId(db *xorm.Engine, where map[string]interface{}) (out map[string]map[int]StoreRelation, err error) {
	session := db.NewSession()
	defer session.Close()

	financeCode, ok := where["financeCode"]
	if ok {
		session = session.Where("finance_code=?", financeCode)
	}

	out = make(map[string]map[int]StoreRelation)
	data := make([]StoreRelation, 0)
	if err = session.Table("datacenter.store_relation").Find(&data); err != nil {
		return
	}

	for _, v := range data {
		if _, ok := out[v.FinanceCode]; !ok {
			out[v.FinanceCode] = make(map[int]StoreRelation)
		}
		out[v.FinanceCode][v.ChannelId] = v
	}

	return
}

// 根据渠道id和渠道门店id获取财务编码
func (s *StoreRelation) GetFinanceCodeByChannel(session *xorm.Session, channelId int, channelStoreId string) (string, error) {
	has, err := session.Table("datacenter.store_relation").
		Where("channel_id = ? AND channel_store_id = ?", channelId, channelStoreId).
		Get(s)

	if err != nil {
		return "", err
	}

	if !has {
		return "", nil
	}

	return s.FinanceCode, nil
}
