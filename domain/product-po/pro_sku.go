package product_po

import (
	"errors"

	"xorm.io/xorm"
)

type ProSku struct {
	Id int `json:"id" xorm:"pk autoincr not null comment('SKU ID') INT 'id'"`
	//商品库中的商品ID
	ProductId int `json:"product_id" xorm:"default 'null' comment('商品库中的商品ID') INT 'product_id'"`
	//市场价
	MarketPrice int `json:"market_price" xorm:"default 'null' comment('市场价') INT 'market_price'"`
	//建议价格/零售价
	RetailPrice int `json:"retail_price" xorm:"default 'null' comment('建议价格/零售价') INT 'retail_price'"`
	//成本价
	BasicPrice int `json:"basic_price" xorm:"default 0 comment('成本价') INT 'basic_price'"`
	//商品条码
	BarCode string `json:"bar_code" xorm:"default 'null' comment('商品条码') VARCHAR(36) 'bar_code'"`
	//重量
	WeightForUnit float64 `json:"weight_for_unit" xorm:"default 'null' comment('重量') DOUBLE(8) 'weight_for_unit'"`
	//前置仓价格
	PreposePrice int `json:"prepose_price" xorm:"default 'null' comment('前置仓价格') INT 'prepose_price'"`
	//门店仓价格
	StorePrice int `json:"store_price" xorm:"default 'null' comment('门店仓价格') INT 'store_price'"`
	//库存单位key
	StoreUnitKey string `json:"store_unit_key" xorm:"default '' comment('库存单位key') VARCHAR(255) 'store_unit_key'"`
	//库存单位
	StoreUnit string `json:"store_unit" xorm:"default '' comment('库存单位') VARCHAR(255) 'store_unit'"`
	//规格信息
	ProductSpecs string `json:"product_specs" xorm:"default '' comment('规格信息') VARCHAR(64) 'product_specs'"`
	//商品货号
	SkuCode string `json:"sku_code" xorm:"not null default '' comment('商品货号') VARCHAR(64) 'sku_code'"`
	//添加时间
	CreateDate string `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('添加时间') DATETIME 'create_date' created"`
	//修改时间
	UpdateDate string `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'update_date' updated"`
	// 小程序价
	XcxPrice int `json:"xcx_price" xorm:"default 0 comment('小程序价，单位分') INT 'xcx_price'"`
	// 美团价
	MtPrice int `json:"mt_price" xorm:"default 0 comment('美团价，单位分') INT 'mt_price'"`
	// 饿了么价
	ElmPrice int `json:"elm_price" xorm:"default 0 comment('饿了么价，单位分') INT 'elm_price'"`
	// 税率(示例10%即存入10)'
	TaxRate float64 `json:"tax_rate" xorm:"not null default '0.00' comment('税率(示例10%即存入10)') DECIMAL(5) 'tax_rate'"`
	//是否删除
	IsDel int `json:"is_del" xorm:"default 0 comment('是否删除') INT 'is_del'"`
	// 库位码
	LocationCode          string `json:"location_code" xorm:"-"`
	InventoryTotalNum     int    `json:"inventory_total_num"  xorm:"-"`     //总库存
	InventoryFreezeNum    int    `json:"inventory_freeze_num"  xorm:"-"`    //锁定库存
	InventoryAvailableNum int    `json:"inventory_available_num"  xorm:"-"` //可用库存
}

// SkuQuery 查询SKU的请求参数
type SkuQuery struct {
	ProductIds []int  // 商品ID列表
	ProductId  int    // 商品ID
	SkuId      int    // SKU ID
	SkuIdSli   []int  // SKU ID列表
	OrderBy    string // 排序字段
}

// GetSkuMapInfo 获取SKU信息
// db 可以是 *xorm.Engine 或 *xorm.Session 类型
func (s *ProSku) GetSkuMapInfo(session *xorm.Session, query SkuQuery) (out map[int][]ProSku, skuMap map[int]ProSku, err error) {

	if len(query.ProductIds) > 0 {
		session = session.In("product_id", query.ProductIds)
	}
	if query.ProductId > 0 {
		session = session.Where("product_id=?", query.ProductId)
	}
	if query.SkuId > 0 {
		session = session.Where("id=?", query.SkuId)
	}
	if len(query.SkuIdSli) > 0 {
		session = session.In("id", query.SkuIdSli)
	}
	if query.OrderBy != "" {
		session = session.OrderBy(query.OrderBy)
	}

	out = make(map[int][]ProSku)
	skuMap = make(map[int]ProSku)
	data := make([]ProSku, 0)
	if err = session.Table("eshop.pro_sku").Where("is_del=0").Find(&data); err != nil {
		return
	}

	for _, v := range data {
		if _, ok := out[v.ProductId]; !ok {
			out[v.ProductId] = make([]ProSku, 0)
		}
		out[v.ProductId] = append(out[v.ProductId], v)
		skuMap[v.Id] = v
	}

	return
}

// 获取最大skuid
func GetMaxSkuId(db interface{}, productId int) (maxId int, err error) {
	var session *xorm.Session
	// 根据传入类型获取session
	switch v := db.(type) {
	case *xorm.Engine:
		session = v.NewSession()
		defer session.Close()
	case *xorm.Session:
		session = v
	default:
		return 0, errors.New("db参数类型错误,必须是 *xorm.Engine 或 *xorm.Session")
	}

	// 查询指定商品的最大skuid
	if _, err = session.Table("eshop.pro_sku").Where("product_id = ?", productId).Cols("id").Desc("id").Get(&maxId); err != nil {
		return 0, err
	}

	return maxId, nil
}

// 商品条码
func SkuBarcodeIsExist(session *xorm.Session, where map[string]interface{}) (out bool, err error) {

	barCode, ok := where["barCode"]
	if ok {
		session = session.Where("a.bar_code=?", barCode)
	}
	chainId, ok := where["chainId"]
	if ok {
		session = session.Where("b.chain_id=?", chainId)
	}
	notEqualSkuId, ok := where["notEqualSkuId"]
	if ok {
		session = session.Where("a.id!=?", notEqualSkuId)
	}

	skuid := 0
	out, err = session.Table("eshop.pro_sku").Alias("a").
		Join("inner", "eshop.pro_product b", "a.product_id = b.id and b.is_del=0").
		Cols("a.id").Get(&skuid)
	return
}
