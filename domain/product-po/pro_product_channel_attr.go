package product_po

import (
	"xorm.io/xorm"
)

type ProProductChannelAttr struct {
	Id int `json:"id" xorm:"pk autoincr not null INT 'id'"`
	//商品ID
	ProductId int `json:"product_id" xorm:"default 'null' comment('商品ID') INT 'product_id'"`
	//渠道id(1-小程序，2-美团，3-饿了么，4-京东到家
	ChannelId int `json:"channel_id" xorm:"default 'null' comment('渠道id(1-小程序，2-美团，3-饿了么，4-京东到家)') INT 'channel_id'"`
	//属性ID
	AttrId int `json:"attr_id" xorm:"default 'null' comment('属性ID') INT 'attr_id'"`
	//属性名
	AttrName string `json:"attr_name" xorm:"default 'null' comment('属性名') VARCHAR(50) 'attr_name'"`
	//属性值ID
	AttrValueId string `json:"attr_value_id" xorm:"not null default '' comment('属性值ID') VARCHAR(1000) 'attr_value_id'"`
	//属性值
	AttrValue string `json:"attr_value" xorm:"not null default '' comment('属性值') VARCHAR(2000) 'attr_value'"`
	//类目销售属性是否必传
	AttrSaleProp bool `json:"attr_sale_prop" xorm:"not null default false comment('类目销售属性是否必传') 'attr_sale_prop'"`
	//添加时间
	CreateDate string `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('添加时间') DATETIME 'create_date' created"`
	//修改时间
	UpdateDate string `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'update_date' updated"`
}

// 商品渠道属性查询条件
type ProductChannelAttrReq struct {
	ProductIds []int // 商品ID列表
	ProductId  int   // 商品ID
	ChannelIds []int // 渠道ID列表
	ChannelId  int   // 渠道ID
	OutType    int   // 输出类型 0-按商品ID映射 1-按商品ID和渠道ID双重映射
}

func (p *ProProductChannelAttr) GetProductChannelAttrInfo(session *xorm.Session, where ProductChannelAttrReq) (map[int][]ProProductChannelAttr, map[int]map[int][]ProProductChannelAttr, error) {

	if len(where.ProductIds) > 0 {
		session = session.In("product_id", where.ProductIds)
	}
	if where.ProductId > 0 {
		session = session.Where("product_id = ?", where.ProductId)
	}
	if len(where.ChannelIds) > 0 {
		session = session.In("channel_id", where.ChannelIds)
	}
	if where.ChannelId > 0 {
		session = session.Where("channel_id = ?", where.ChannelId)
	}

	data := make([]ProProductChannelAttr, 0)
	if err := session.Table("eshop.pro_product_channel_attr").Find(&data); err != nil {
		return nil, nil, err
	}

	productMap := make(map[int][]ProProductChannelAttr)
	productChannelMap := make(map[int]map[int][]ProProductChannelAttr)

	for _, v := range data {
		switch where.OutType {
		case 1:
			if _, ok := productChannelMap[v.ProductId]; !ok {
				productChannelMap[v.ProductId] = make(map[int][]ProProductChannelAttr)
			}
			productChannelMap[v.ProductId][v.ChannelId] = append(productChannelMap[v.ProductId][v.ChannelId], v)
		default:
			productMap[v.ProductId] = append(productMap[v.ProductId], v)
		}
	}

	return productMap, productChannelMap, nil
}
