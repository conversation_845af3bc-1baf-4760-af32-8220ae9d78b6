package product_po

import (
	"eShop/infra/log"
	"errors"
	"time"

	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
	"xorm.io/xorm"
)

const (
	ProductTypeGoods   = 1 // 实物商品
	ProductTypeVirtual = 2 // 虚拟商品
	ProductTypeGroup   = 3 // 组合商品
	ProductTypeService = 4 // 服务
	ProductTypeLive    = 5 // 活体
)
const (
	//   eshop.pro_product.`new_sell` int unsigned DEFAULT '0' COMMENT '是否开启新零售，1：未开启，2：已开启',
	NewSellStop = 1
	NewSellOpen = 2
)

var NewSellMap = map[int]string{
	NewSellStop: "未开启",
	NewSellOpen: "已开启",
}

type ProProduct struct {
	Id int `json:"id" xorm:"pk autoincr not null INT 'id'"`
	//连锁ID
	ChainId string `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	// 前台分类id
	CategoryId        int    `json:"category_id" xorm:"default 'null' comment('分类id，线上') INT 'category_id'"`
	CategoryNavOnline string `json:"category_nav_online" xorm:"-"`
	// 后台分类id
	CategoryIdOffline int `json:"category_id_offline" xorm:"not null comment('商品分类,关联商品分类表(原武汉那边的分类)') BIGINT 'category_id_offline'"`
	//后台末级分类名称
	CategoryName string `json:"category_name" xorm:"default 'null' comment('分类名称') VARCHAR(100) 'category_name'"`
	//后台分类路径，多层级的组合起来显示，如：猫站>猫咪专区（实物商品）>生活日用>口腔清洁
	CategoryNav string `json:"category_nav" xorm:"default 'null' comment('分类别名，多层级的组合起来显示，如：猫站>猫咪专区（实物商品）>生活日用>口腔清洁') VARCHAR(500) 'category_nav'"`
	//商品名称
	Name string `json:"name" xorm:"default 'null' comment('商品名称') VARCHAR(255) 'name'"`
	//商品短标题
	ShortName string `json:"short_name" xorm:"default 'null' comment('商品短标题') VARCHAR(100) 'short_name'"`
	//商品编号
	Code string `json:"code" xorm:"default 'null' comment('商品编号') VARCHAR(36) 'code'"`

	//商品添加日期
	CreateDate string `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品添加日期') DATETIME 'create_date' created"`
	//商品最后更新日期
	UpdateDate string `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品最后更新日期') DATETIME 'update_date' updated"`
	//是否删除
	IsDel int `json:"is_del" xorm:"default 0 comment('是否删除') INT 'is_del'"`
	//商品图片（多图）
	Pic string `json:"pic" xorm:"default 'null' comment('商品图片（多图）') VARCHAR(1000) 'pic'"`
	//商品卖点
	SellingPoint string `json:"selling_point" xorm:"default 'null' comment('商品卖点') VARCHAR(200) 'selling_point'"`
	//商品视频地址
	Video string `json:"video" xorm:"default 'null' comment('商品视频地址') VARCHAR(500) 'video'"`
	//电脑端详情内容
	ContentPc string `json:"content_pc" xorm:"comment('电脑端详情内容') MEDIUMTEXT 'content_pc'"`
	//手机端详情内容
	ContentMobile        string `json:"content_mobile" xorm:"comment('手机端详情内容') MEDIUMTEXT 'content_mobile'"`
	IsDiscount           int    `json:"is_discount" xorm:"default 'null' comment('是否参与优惠折扣') INT 'is_discount'"`
	ProductType          int    `json:"product_type" xorm:"default 'null' comment('商品类型（1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体)') INT 'product_type'"`
	IsUse                int    `json:"is_use" xorm:"default 0 comment('商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）') INT 'is_use'"`
	SplitProductId       int    `json:"split_product_id" xorm:"default 'null' comment('拆分商品的原id') INT 'split_product_id'"`
	DelDate              string `json:"del_date" xorm:"default 'null' comment('删除时间') DATETIME 'del_date'"`
	ChannelId            string `json:"channel_id" xorm:"default 'null' comment('渠道id，多渠道用逗号分隔') VARCHAR(100) 'channel_id'"`
	IsDrugs              int    `json:"is_drugs" xorm:"default 0 comment('是否为药品 0否，1是') INT 'is_drugs'"`
	IsPrescribedDrug     int8   `json:"is_prescribed_drug" xorm:"default 0 comment('是否处方药') TINYINT 'is_prescribed_drug'"`
	UseRange             string `json:"use_range" xorm:"not null default '' comment('商品应用范围（1电商，2前置仓，3门店仓）,字符串拼接') VARCHAR(100) 'use_range'"`
	GroupType            int    `json:"group_type" xorm:"not null default 0 comment('组合类型(1:实实组合,2:虚虚组合,3.虚实组合)') INT 'group_type'"`
	TermType             int    `json:"term_type" xorm:"not null default 0 comment('只有虚拟商品才有值(1.有效期至多少  2.有效期天数)') INT 'term_type'"`
	TermValue            int    `json:"term_value" xorm:"not null default 0 comment('如果term_type=1 存：时间戳  如果term_type=2 存多少天') INT 'term_value'"`
	VirtualInvalidRefund int    `json:"virtual_invalid_refund" xorm:"not null default 0 comment('是否支持过期退款 1：是  0：否') INT 'virtual_invalid_refund'"`
	WarehouseType        int    `json:"warehouse_type" xorm:"default 0 comment('药品仓类型：0:默认否, 1:巨星药品仓') INT 'warehouse_type'"`
	IsIntelGoods         int    `json:"is_intel_goods" xorm:"default 0 comment('是否互联网医疗产品') INT 'is_intel_goods'"`
	Disabled             int    `json:"disabled" xorm:"default 0 comment('oms同步的商品启用禁用状态1启用 0禁用') INT 'disabled'"`
	FromOms              int    `json:"from_oms" xorm:"default 0 comment('来自oms的商品，前端需要控制货号修改 0不是  1是') INT 'from_oms'"`
	SourceType           int    `json:"source_type" xorm:"default 0 comment('1：OMS同步 2：后台新增 3：后台导入') INT 'source_type'"`
	Disease              string `json:"disease" xorm:"comment('对应病症') TEXT 'disease'"`
	DrugDosage           string `json:"drug_dosage" xorm:"comment('药品剂量') TEXT 'drug_dosage'"`
	DosingDays           int    `json:"dosing_days" xorm:"default 0 comment('投药天数') INT 'dosing_days'"`
	//是否开启新零售，1：未开启，2：已开启
	NewSell int `json:"new_sell" xorm:"default 0 comment('是否开启新零售，1：未开启，2：已开启') INT 'new_sell'"`
	//新渠道信息（1-小程序，2-美团，3-饿了么，4-京东到家）,字符串拼接
	NewSellStr string `json:"new_sell_str" xorm:"not null default '' comment('新渠道信息（1-小程序，2-美团，3-饿了么，4-京东到家）,字符串拼接') VARCHAR(100) 'new_sell_str'"`
	//品牌ID,关联品牌表
	BrandId string `json:"brand_id" xorm:"not null default 0 comment('品牌ID,关联品牌表') BIGINT 'brand_id'"`
	//品牌名称,只商品编辑取这个字段
	BrandName string `json:"brand_name" xorm:"default '' comment('品牌名称,只商品编辑取这个字段') VARCHAR(64) 'brand_name'"`
	//供应商ID,关联供应商表id
	SupplierId string `json:"supplier_id" xorm:"not null default 0 comment('供应商ID,关联供应商表id') BIGINT 'supplier_id'"`
	//供应商名称
	SupplierName string `json:"supplier_name" xorm:"default '' comment('供应商名称') VARCHAR(64) 'supplier_name'"`
	//创建人ID
	CreatedBy string `json:"created_by" xorm:"not null default 0 comment('创建人ID') BIGINT 'created_by'"`
	//创建人名称
	CreatedName string `json:"created_name" xorm:"not null default '' comment('创建人名称') VARCHAR(100) 'created_name'"`
	//更新人ID
	UpdatedBy string `json:"updated_by" xorm:"not null default 0 comment('更新人ID') BIGINT 'updated_by'"`
	//更新人名称
	UpdatedName string `json:"updated_name" xorm:"not null default '' comment('更新人名称') VARCHAR(100) 'updated_name'"`
}

type ProProductExd struct {
	ProProduct `xorm:"extends"`
	// 分类id
	ChannelCategoryId string `json:"channel_category_id" xorm:"default '' comment('分类id') VARCHAR(50) 'channel_category_id'"`
	//分类名称
	ChannelCategoryName string `json:"channel_category_name" xorm:"default '' comment('分类名称') VARCHAR(50) 'channel_category_name'"`
}

const ProductFiled = `chain_id,category_id,category_id_offline,category_name,category_nav,name,short_name,code,bar_code,pic,selling_point,video,content_pc,content_mobile,source_type,new_sell,new_sell_str,brand_id,brand_name,supplier_id,supplier_name,created_by,created_name,updated_by,updated_name`

// xorm 更新时， 数据库大整型的字段 ， 在po里也必须时整型，所以这里单独定义一个结构体。ProProduct这个结构体，可用于插入和查询
type ProProductForUpdate struct {
	Id int `json:"id" xorm:"pk autoincr not null INT 'id'"`
	//连锁ID
	ChainId int64 `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	//分类id，线上
	CategoryId int `json:"category_id" xorm:"default 'null' comment('分类id，线上') INT 'category_id'"`
	//商品分类,关联商品分类表(原武汉那边的分类)
	CategoryIdOffline int64 `json:"category_id_offline" xorm:"not null comment('商品分类,关联商品分类表(原武汉那边的分类)') BIGINT 'category_id_offline'"`
	//分类名称
	CategoryName string `json:"category_name" xorm:"default 'null' comment('分类名称') VARCHAR(100) 'category_name'"`
	//分类别名，多层级的组合起来显示，如：猫站>猫咪专区（实物商品）>生活日用>口腔清洁
	CategoryNav string `json:"category_nav" xorm:"default 'null' comment('分类别名，多层级的组合起来显示，如：猫站>猫咪专区（实物商品）>生活日用>口腔清洁') VARCHAR(500) 'category_nav'"`
	//商品名称
	Name string `json:"name" xorm:"default 'null' comment('商品名称') VARCHAR(255) 'name'"`
	//商品短标题
	ShortName string `json:"short_name" xorm:"default 'null' comment('商品短标题') VARCHAR(100) 'short_name'"`
	//商品编号
	Code string `json:"code" xorm:"default 'null' comment('商品编号') VARCHAR(36) 'code'"`

	//商品添加日期
	CreateDate string `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品添加日期') DATETIME 'create_date' created"`
	//商品最后更新日期
	UpdateDate string `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品最后更新日期') DATETIME 'update_date' updated"`
	//是否删除
	IsDel int `json:"is_del" xorm:"default 0 comment('是否删除') INT 'is_del'"`
	//商品图片（多图）

	Pic string `json:"pic" xorm:"default 'null' comment('商品图片（多图）') VARCHAR(1000) 'pic'"`
	//商品卖点
	SellingPoint string `json:"selling_point" xorm:"default 'null' comment('商品卖点') VARCHAR(200) 'selling_point'"`
	//商品视频地址
	Video string `json:"video" xorm:"default 'null' comment('商品视频地址') VARCHAR(500) 'video'"`
	//电脑端详情内容
	ContentPc string `json:"content_pc" xorm:"comment('电脑端详情内容') MEDIUMTEXT 'content_pc'"`
	//手机端详情内容
	ContentMobile        string    `json:"content_mobile" xorm:"comment('手机端详情内容') MEDIUMTEXT 'content_mobile'"`
	IsDiscount           int       `json:"is_discount" xorm:"default 'null' comment('是否参与优惠折扣') INT 'is_discount'"`
	ProductType          int       `json:"product_type" xorm:"default 'null' comment('商品类别（1-实物商品，2-虚拟商品，3-组合商品）') INT 'product_type'"`
	IsUse                int       `json:"is_use" xorm:"default 0 comment('商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）') INT 'is_use'"`
	SplitProductId       int       `json:"split_product_id" xorm:"default 'null' comment('拆分商品的原id') INT 'split_product_id'"`
	DelDate              time.Time `json:"del_date" xorm:"default 'null' comment('删除时间') DATETIME 'del_date'"`
	ChannelId            string    `json:"channel_id" xorm:"default 'null' comment('渠道id，多渠道用逗号分隔') VARCHAR(100) 'channel_id'"`
	IsDrugs              int       `json:"is_drugs" xorm:"default 0 comment('是否为药品 0否，1是') INT 'is_drugs'"`
	IsPrescribedDrug     int8      `json:"is_prescribed_drug" xorm:"default 0 comment('是否处方药') TINYINT 'is_prescribed_drug'"`
	UseRange             string    `json:"use_range" xorm:"not null default '' comment('商品应用范围（1电商，2前置仓，3门店仓）,字符串拼接') VARCHAR(100) 'use_range'"`
	GroupType            int       `json:"group_type" xorm:"not null default 0 comment('组合类型(1:实实组合,2:虚虚组合,3.虚实组合)') INT 'group_type'"`
	TermType             int       `json:"term_type" xorm:"not null default 0 comment('只有虚拟商品才有值(1.有效期至多少  2.有效期天数)') INT 'term_type'"`
	TermValue            int       `json:"term_value" xorm:"not null default 0 comment('如果term_type=1 存：时间戳  如果term_type=2 存多少天') INT 'term_value'"`
	VirtualInvalidRefund int       `json:"virtual_invalid_refund" xorm:"not null default 0 comment('是否支持过期退款 1：是  0：否') INT 'virtual_invalid_refund'"`
	WarehouseType        int       `json:"warehouse_type" xorm:"default 0 comment('药品仓类型：0:默认否, 1:巨星药品仓') INT 'warehouse_type'"`
	IsIntelGoods         int       `json:"is_intel_goods" xorm:"default 0 comment('是否互联网医疗产品') INT 'is_intel_goods'"`
	Disabled             int       `json:"disabled" xorm:"default 0 comment('oms同步的商品启用禁用状态1启用 0禁用') INT 'disabled'"`
	FromOms              int       `json:"from_oms" xorm:"default 0 comment('来自oms的商品，前端需要控制货号修改 0不是  1是') INT 'from_oms'"`
	SourceType           int       `json:"source_type" xorm:"default 0 comment('1：OMS同步 2：后台新增 3：后台导入') INT 'source_type'"`
	Disease              string    `json:"disease" xorm:"comment('对应病症') TEXT 'disease'"`
	DrugDosage           string    `json:"drug_dosage" xorm:"comment('药品剂量') TEXT 'drug_dosage'"`
	DosingDays           int       `json:"dosing_days" xorm:"default 0 comment('投药天数') INT 'dosing_days'"`
	//是否开启新零售，1：未开启，2：已开启
	NewSell int `json:"new_sell" xorm:"default 0 comment('是否开启新零售，1：未开启，2：已开启') INT 'new_sell'"`
	//新渠道信息（1-小程序，2-美团，3-饿了么，4-京东到家）,字符串拼接
	NewSellStr string `json:"new_sell_str" xorm:"not null default '' comment('新渠道信息（1-小程序，2-美团，3-饿了么，4-京东到家）,字符串拼接') VARCHAR(100) 'new_sell_str'"`
	//品牌ID,关联品牌表
	BrandId int64 `json:"brand_id" xorm:"not null default 0 comment('品牌ID,关联品牌表') BIGINT 'brand_id'"`
	//品牌名称,只商品编辑取这个字段
	BrandName string `json:"brand_name" xorm:"default '' comment('品牌名称,只商品编辑取这个字段') VARCHAR(64) 'brand_name'"`
	//供应商ID,关联供应商表id
	SupplierId int64 `json:"supplier_id" xorm:"not null default 0 comment('供应商ID,关联供应商表id') BIGINT 'supplier_id'"`
	//供应商名称
	SupplierName string `json:"supplier_name" xorm:"default '' comment('供应商名称') VARCHAR(64) 'supplier_name'"`
	//创建人ID
	CreatedBy int64 `json:"created_by" xorm:"not null default 0 comment('创建人ID') BIGINT 'created_by'"`
	//创建人名称
	CreatedName string `json:"created_name" xorm:"not null default '' comment('创建人名称') VARCHAR(100) 'created_name'"`
	//更新人ID
	UpdatedBy int64 `json:"updated_by" xorm:"not null default 0 comment('更新人ID') BIGINT 'updated_by'"`
	//更新人名称
	UpdatedName string `json:"updated_name" xorm:"not null default '' comment('更新人名称') VARCHAR(100) 'updated_name'"`
}

func CopyProProductForUpdate(in ProProduct) (out ProProductForUpdate) {
	if err := copier.Copy(&out, in); err != nil {
		log.Error("将类型ProProduct转换为ProProductForUpdate失败,err=", err.Error())
	}
	out.ChainId = cast.ToInt64(in.ChainId)
	out.BrandId = cast.ToInt64(in.BrandId)
	out.SupplierId = cast.ToInt64(in.SupplierId)
	out.CreatedBy = cast.ToInt64(in.CreatedBy)
	out.UpdatedBy = cast.ToInt64(in.UpdatedBy)

	return
}

func GetProductMapInfo(session *xorm.Session, where map[string]interface{}) (out []ProProduct, out1 map[int]ProProduct, out2 map[string]ProProduct, err error) {

	productIdSli, ok := where["productIdSli"]
	if ok {
		session = session.In("id", productIdSli)
	}
	productId, ok := where["productId"]
	if ok {
		session = session.Where("id=?", productId)
	}

	notEqualProductId, ok := where["notEqualProductId"]
	if ok {
		session = session.Where("id!=?", notEqualProductId)
	}

	chainId, ok := where["chainId"]
	if ok {
		session = session.Where("chain_id=?", chainId)
	}

	productName, ok := where["productName"]
	if ok {
		session = session.Where("name=?", productName)
	}
	categoryId, ok := where["categoryId"]
	if ok {
		session = session.Where("category_id=?", categoryId)
	}

	categoryIdOffline, ok := where["categoryIdOffline"]
	if ok {
		session = session.Where("category_id_offline=?", categoryIdOffline)
	}
	out1 = make(map[int]ProProduct)
	out2 = make(map[string]ProProduct)
	out = make([]ProProduct, 0)
	if err = session.Table("eshop.pro_product").Find(&out); err != nil {
		return
	}

	outType := 0
	if v, ok := where["outType"]; ok {
		outType = v.(int)
	}
	for _, v := range out {
		switch outType {
		case 1:
			out1[v.Id] = v
		case 2:
			out2[v.Name] = v
		}
	}

	return
}

type ProductSnapReq struct {
	ChannelId  int    `json:"channel_id"`  // 渠道id
	ChainId    int64  `json:"chain_id"`    // 连锁id
	StoreId    string `json:"store_id"`    // 店铺id
	ProductIds []int  `json:"product_ids"` // spuid
	SkuIds     []int  `json:"sku_ids"`     // 规格id

}

// 通过skuid获取商品相关信息
func GetProductSnapBySkuid(session *xorm.Session, query ProductSnapReq) (productMap map[int]ProProduct, skuMap map[int]ProSku, spuMap map[string]ProProductStoreSpu, productStoreInfoMap map[string]ProProductStoreInfoExt2, err error) {

	if len(query.StoreId) == 0 {
		err = errors.New("店铺id不能传空")
		return
	}
	if len(query.SkuIds) == 0 {
		err = errors.New("skuid不能为空")
		return
	}

	// 获取规格信息

	_, skuMap, err = new(ProSku).GetSkuMapInfo(session, SkuQuery{
		ProductIds: query.ProductIds,
		SkuIdSli:   query.SkuIds,
	})
	if err != nil {
		return
	}
	query.ProductIds = make([]int, 0)
	for _, v := range skuMap {
		query.ProductIds = append(query.ProductIds, v.ProductId)
	}

	// 获取商品基本信息
	_, productMap, _, err = GetProductMapInfo(session, map[string]interface{}{
		"productIdSli": query.ProductIds,
		"outType":      1,
	})
	if err != nil {
		return
	}

	// 获取渠道门店商品信息
	where := GetProductStoreInfoReq{
		StoreId:    query.StoreId,
		ProductIds: query.ProductIds,
		SkuIds:     query.SkuIds,
		ChannelId:  query.ChannelId,
		OutType:    2,
	}
	_, _, productStoreInfoMap, err = GetProductStoreInfo(session, where)

	if err != nil {
		return
	}

	// 获取spu信息
	spuMap, _, err = new(ProProductStoreSpu).QueryMap(session, SpuQuery{
		ProductIds: query.ProductIds,
		ChannelId:  query.ChannelId,
		StoreId:    query.StoreId,
	})
	if err != nil {
		return
	}
	return
}

// 通过productid获取商品相关信息
func GetProductSnapByPid(session *xorm.Session, query ProductSnapReq) (productMap map[int]ProProduct, skuMap map[int]ProSku, spuMap map[string]ProProductStoreSpu, productStoreInfoMap map[string]ProProductStoreInfoExt2, err error) {
	if len(query.StoreId) == 0 {
		err = errors.New("店铺id不能传空")
		return
	}
	if len(query.ProductIds) == 0 {
		err = errors.New("productid不能为空")
		return
	}

	// 获取商品基本信息
	_, productMap, _, err = GetProductMapInfo(session, map[string]interface{}{
		"productIdSli": query.ProductIds,
		"chainId":      query.ChainId,
		"outType":      1,
	})
	if err != nil {
		return
	}

	// 获取规格信息
	_, skuMap, err = new(ProSku).GetSkuMapInfo(session, SkuQuery{
		ProductIds: query.ProductIds,
		SkuIdSli:   query.SkuIds,
	})
	if err != nil {
		return
	}
	query.ProductIds = make([]int, 0)
	for _, v := range skuMap {
		query.ProductIds = append(query.ProductIds, v.ProductId)
	}

	// 获取渠道门店商品信息
	_, _, productStoreInfoMap, err = GetProductStoreInfo(session, GetProductStoreInfoReq{
		StoreId:    query.StoreId,
		ProductIds: query.ProductIds,
		SkuIds:     query.SkuIds,
		ChannelId:  query.ChannelId,
		OutType:    2,
	})

	if err != nil {
		return
	}

	// 获取spu信息
	spuMap, _, err = new(ProProductStoreSpu).QueryMap(session, SpuQuery{
		ProductIds: query.ProductIds,
		ChannelId:  query.ChannelId,
		StoreId:    query.StoreId,
	})
	if err != nil {
		return
	}
	return
}
