package product_po

type GetElmShopCategoryRequest struct {
	ShopId     string `json:"shop_id"`
	AppChannel int32  `json:"app_channel"`
}

type GetElmShopCategoryResponse struct {
	Code  int32           `json:"code"`
	Error string          `json:"error"`
	Data  []*ShopCategory `json:"data"`
}

type ShopCategory struct {
	CategoryId   string `json:"category_id"`
	CategoryName string `json:"category_name"`
	ShopCustomId string `json:"shop_custom_id"`
	Rank         string `json:"rank"`
}

type DelElmShopCategoryRequest struct {
	ShopId     string `json:"shop_id"`
	CategoryId string `json:"category_id"`
	AppChannel int32  `json:"app_channel"`
}
