package product_po

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	proproductstoreinfo "eShop/services/product-service/enum/pro-product-store-info"
	"fmt"
	"time"

	"github.com/spf13/cast"
	"xorm.io/xorm"
)

type ProProductStoreInfo struct {
	// 主键
	Id int `json:"id" xorm:"pk autoincr not null INT 'id'"`
	// skuId
	SkuId int `json:"sku_id" xorm:"default 0 INT 'sku_id'"`

	// 商品ID
	ProductId int `json:"product_id" xorm:"default 0 comment('商品ID') INT 'product_id'"`

	// 门店的主键
	StoreId string `json:"store_id" xorm:"default 0 comment('门店的主键') VARCHAR(50) 'store_id'"`

	// 渠道id(1-小程序，2-美团，3-饿了么，4-京东到家，100-线下门店)
	ChannelId int `json:"channel_id" xorm:"not null default 0 comment('渠道id(1-小程序，2-美团，3-饿了么，4-京东到家，100-线下门店)') INT 'channel_id'"`

	// 上下架状态（1-上架，0-下架）
	UpDownState int `json:"up_down_state" xorm:"default 0 comment('上下架状态（1-上架，0-下架）') INT 'up_down_state'"`

	// 1正常 2操作中 3铺品失败 4上架失败 5下架失败 6更新失败
	Status int `json:"status" xorm:"default 0 comment('1正常 2操作中 3铺品失败 4上架失败 5下架失败 6更新失败 ') INT 'status'"`

	// 建议价格/零售价
	RetailPrice int `json:"retail_price" xorm:"default 0 comment('建议价格/零售价') INT 'retail_price'"`
	//市场价
	MarketPrice int `json:"market_price" xorm:"default 'null' comment('市场价') INT 'market_price'"`
	// 0未铺品，1已铺品 ：指的是在第三方门店创建商品是否成功
	IsDistribution int `json:"is_distribution" xorm:"default 0 comment('0未铺品，1已铺品 ：指的是在第三方门店创建商品是否成功') INT 'is_distribution'"`

	// 铺品或者上架报错信息
	SyncError string `json:"sync_error" xorm:"not null comment('铺品或者上架报错信息') VARCHAR(500) 'sync_error'"`

	// 第三方回写的skuID
	SkuThirdId string `json:"sku_third_id" xorm:"default '' comment('第三方回写的skuID') VARCHAR(50) 'sku_third_id'"`

	// 商品添加日期
	CreateDate time.Time `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品添加日期') DATETIME 'create_date' created"`

	// 商品最后更新日期
	UpdateDate time.Time `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品最后更新日期') DATETIME 'update_date' updated"`
	//商品名称
	ProductName string `json:"product_name" xorm:"default '' comment('商品名称') VARCHAR(255) 'product_name'"`
	//商品描述
	ProductDesc string `json:"product_desc" xorm:"default '' comment('商品描述') VARCHAR(255) 'product_desc'"`
	ProductPic  string `json:"product_pic" xorm:"default '' comment('商品图片') VARCHAR(255) 'product_pic'"`

	//商品类型（1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体)
	ProductType int `json:"product_type" xorm:"default 1 comment('商品类型：1-商品，2-服务 3-活体') INT 'product_type'"`
	//服务时长
	ServiceDuration int `json:"service_duration" xorm:"default 0 comment('服务时长') INT 'service_duration'"`
	//出生日期
	BirthDate string `json:"birth_date" xorm:"default 'null' comment('出生日期') VARCHAR(255) 'birth_date'"`
	//宠物品种
	PetVariety string `json:"pet_variety" xorm:"default '' comment('宠物品种') VARCHAR(255) 'pet_variety'"`
	//宠物品种名称
	PetVarietyName string `json:"pet_variety_name" xorm:"default '' comment('宠物品种名称') VARCHAR(255) 'pet_variety_name'"`
	//条码
	BarCode string `json:"bar_code" xorm:"default 'null' comment('条码') VARCHAR(36) 'bar_code'"`
}

type ProProductStoreInfoExt2 struct {
	ProProductStoreInfo `xorm:"extends"`
	ChannelCategoryId   int `json:"channel_category_id" xorm:"<-"` //前台分类
	CategoryIdOffline   int `json:"category_id_offline" xorm:"<-"` // 后台分类
}

type ProProductStoreInfoExt struct {
	ProProductStoreInfo `xorm:"extends"`
	//库存单位
	StoreUnit string `json:"store_unit" xorm:"default '' comment('库存单位') VARCHAR(255) 'store_unit'"`
	//规格信息
	ProductSpecs string `json:"product_specs" xorm:"default '' comment('规格信息') VARCHAR(64) 'product_specs'"`
	//重量
	WeightForUnit float64 `json:"weight_for_unit" xorm:"default 'null' comment('重量') DOUBLE(8) 'weight_for_unit'"`
	//sku商品条码
	SkuUpc string `json:"sku_upc"`
}

type ProProductStoreAppChannel struct {
	ProProductStoreSpu `xorm:"extends"`
	//连锁ID
	ChainId int64 `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	//分类名称
	CategoryName string `json:"category_name" xorm:"default 'null' comment('分类名称') VARCHAR(100) 'category_name'"`
	//商品名称
	Name string `json:"name" xorm:"default 'null' comment('商品名称') VARCHAR(255) 'name'"`
	//商品卖点
	SellingPoint string `json:"selling_point" xorm:"default 'null' comment('商品卖点') VARCHAR(200) 'selling_point'"`
	//商品图片（多图）
	Pic string `json:"pic" xorm:"default 'null' comment('商品图片（多图）') VARCHAR(1000) 'pic'"`
	//电脑端详情内容
	ContentPc string `json:"content_pc" xorm:"comment('电脑端详情内容') MEDIUMTEXT 'content_pc'"`
	// 第三方类目ID
	CategoryThirdId int `json:"category_third_id" xorm:"default 'null' comment('第三方类目ID') INT 'category_third_id'"`
	// 第三方类目名称
	CategoryThirdName string `json:"category_third_name" xorm:"default 'null' comment('第三方类目名称') INT 'category_third_name'"`
	//新渠道信息（1-小程序，2-美团，3-饿了么，4-京东到家）,字符串拼接
	NewSellStr string                   `json:"new_sell_str" xorm:"not null default '' comment('新渠道信息（1-小程序，2-美团，3-饿了么，4-京东到家）,字符串拼接') VARCHAR(100) 'new_sell_str'"`
	NewSell    int                      `json:"new_sell" xorm:"default 0 comment('是否开启新零售，1：未开启，2：已开启') INT 'new_sell'"`
	Skus       []ProProductStoreInfoExt `json:"skus"`
	Attr       []ProProductChannelAttr
}

// 获取商品铺品信息
// 入参 outType 是 返回数据类型 1-店铺id_商品id_skuId 2-渠道id_店铺id_商品id_skuId
// data 是 铺品数据列表
// out1 是map[店铺id_商品id_skuId]{渠道切片}
// out2 是map[渠道id_店铺id_商品id_skuId]{渠道切片}
type GetProductStoreInfoReq struct {
	ProductIds          []int    // 商品ID列表
	ProductId           int      // 商品ID
	SkuIds              []int    // SKU ID列表
	SkuId               int      // SKU ID
	SkuIdsNo            []int    // 排除的SKU ID列表
	SkuOrBarcode        string   // SKU或条码
	ChannelId           int      // 渠道ID
	ChannelIds          []int    // 渠道ID列表
	ExcludeProductTypes []int    // 排除的商品类型
	IsDistribution      int      // 是否已铺品
	StoreId             string   // 店铺ID
	StoreIds            []string // 店铺ID列表
	UpDownState         int      // 上下架状态
	OutType             int      // 返回数据类型
}

// Info 获取商品数据
func Info(session *xorm.Session, req GetProductStoreInfoReq) (data []ProProductStoreInfo, err error) {
	session = session.Table("eshop.pro_product_store_info").Alias("a")
	if len(req.ProductIds) > 0 {
		session = session.In("a.product_id", req.ProductIds)
	}

	if len(req.SkuIds) > 0 {
		session = session.In("a.sku_id", req.SkuIds)
	}

	if len(req.ChannelIds) > 0 {
		session = session.In("a.channel_id", req.ChannelIds)
	}

	if err = session.Find(&data); err != nil {
		return
	}

	return
}

func GetProductStoreInfo(session *xorm.Session, req GetProductStoreInfoReq) (data []ProProductStoreInfoExt2, out1 map[string][]ProProductStoreInfoExt2, out2 map[string]ProProductStoreInfoExt2, err error) {

	if len(req.ProductIds) > 0 {
		session = session.In("a.product_id", req.ProductIds)
	}
	if req.ProductId > 0 {
		session = session.Where("a.product_id=?", req.ProductId)
	}

	if len(req.SkuIds) > 0 {
		session = session.In("a.sku_id", req.SkuIds)
	}
	if req.SkuId > 0 {
		session = session.Where("a.sku_id=?", req.SkuId)
	}

	if len(req.SkuIdsNo) > 0 {
		session = session.NotIn("a.sku_id", req.SkuIdsNo)
	}

	if req.SkuOrBarcode != "" {
		session = session.Join("left", "eshop.pro_sku b", "a.sku_id = b.id").Where("a.sku_id=? or b.bar_code=?", cast.ToInt(req.SkuOrBarcode), req.SkuOrBarcode)
	}
	if req.ChannelId > 0 {
		session = session.Where("a.channel_id=?", req.ChannelId)
	}
	if len(req.ChannelIds) > 0 {
		session = session.In("a.channel_id", req.ChannelIds)
	}
	if len(req.ExcludeProductTypes) > 0 {
		session = session.NotIn("a.product_type", req.ExcludeProductTypes)
	}

	// 未铺品，1已铺品 ：指的是在第三方门店创建商品是否成功
	if req.IsDistribution > 0 {
		session = session.Where("a.is_distribution=?", req.IsDistribution)
	}

	if req.StoreId != "" {
		session = session.Where("a.store_id=?", req.StoreId)
	}

	if len(req.StoreIds) > 0 {
		session = session.In("a.store_id", req.StoreIds)
	}

	if req.UpDownState > 0 {
		session = session.Where("a.up_down_state=?", req.UpDownState)
	}

	// 返回数据 结构体类型
	out1 = make(map[string][]ProProductStoreInfoExt2)
	out2 = make(map[string]ProProductStoreInfoExt2)
	data = make([]ProProductStoreInfoExt2, 0)
	if err = session.Table("eshop.pro_product_store_info").Alias("a").
		Join("left", "eshop.pro_product_store_spu c", "a.product_id=c.product_id and a.store_id=c.store_id and a.channel_id=c.channel_id").
		Join("left", "eshop.pro_product d", "a.product_id=d.id").
		Select("a.*,c.channel_category_id,d.category_id_offline").
		OrderBy("a.channel_id asc").Find(&data); err != nil {
		return
	}

	for _, v := range data {
		switch req.OutType {
		case 1:
			k := fmt.Sprintf("%s_%d_%d", v.StoreId, v.ProductId, v.SkuId)
			if _, ok := out1[k]; !ok {
				out1[k] = make([]ProProductStoreInfoExt2, 0)
			}
			out1[k] = append(out1[k], v)
		case 2:
			k := fmt.Sprintf("%d_%s_%d_%d", v.ChannelId, v.StoreId, v.ProductId, v.SkuId)
			out2[k] = v
		}
	}

	return
}

func GetProductStoreInfoExist(db *xorm.Engine, where map[string]interface{}) (out map[string]ProProductStoreInfo, err error) {
	session := db.NewSession()
	defer session.Close()

	productId, ok := where["productId"]
	if ok {
		session = session.Where("product_id=?", productId)
	}

	productIds, ok := where["productIds"]
	if ok {
		session = session.In("product_id", productIds)
	}

	skuIds, ok := where["skuIds"]
	if ok {
		session = session.In("sku_id", skuIds)
	}

	channelId, ok := where["channelId"]
	if ok {
		session = session.Where("channel_id=?", channelId)
	}
	channelIds, ok := where["channelIds"]
	if ok {
		session = session.In("channel_id", channelIds)
	}

	storeId, ok := where["storeId"]
	if ok {
		session = session.Where("store_id=?", storeId)
	}

	storeIds, ok := where["storeIds"]
	if ok {
		session = session.In("store_id", storeIds)
	}
	// 排除
	excludeProductTypes, ok := where["excludeProductTypes"]
	if ok {
		session = session.NotIn("product_type", excludeProductTypes)
	}

	out = make(map[string]ProProductStoreInfo)
	data := make([]ProProductStoreInfo, 0)
	if err = session.Table("eshop.pro_product_store_info").Find(&data); err != nil {
		return
	}

	for _, v := range data {
		k := fmt.Sprintf("%d_%s_%d_%d", v.ChannelId, v.StoreId, v.ProductId, v.SkuId)
		out[k] = v
	}
	return

}

func (m ProProductStoreInfo) UpdateProductStoreInfo(db *xorm.Engine, where map[string]interface{}, cols string) (err error) {
	session := db.NewSession()
	defer session.Close()

	id, ok := where["id"]
	if ok {
		session = session.Where("id=?", id)
	}

	productId, ok := where["productId"]
	if ok {
		session = session.Where("product_id=?", productId)
	}

	productIds, ok := where["productIds"]
	if ok {
		session = session.In("product_id", productIds)
	}

	channelId, ok := where["channelId"]
	if ok {
		session = session.Where("channel_id=?", channelId)
	}
	channelIds, ok := where["channelIds"]
	if ok {
		session = session.In("channel_id", channelIds)
	}

	storeId, ok := where["storeId"]
	if ok {
		session = session.Where("store_id=?", storeId)
	}

	storeIds, ok := where["storeIds"]
	if ok {
		session = session.In("store_id", storeIds)
	}
	if cols != "" {
		session = session.Cols(cols)
	}
	if _, err = session.Table("eshop.pro_product_store_info").Update(&m); err != nil {
		log.Error("更新铺品表失败，err=", err.Error())
		return
	}

	return

}

// 统计已上架、已下架、未铺品
func StatsProductStoreInfo(db *xorm.Engine, where map[string]interface{}) (out int, err error) {
	session := db.NewSession()
	defer session.Close()
	storeId, ok := where["storeId"]
	if ok {
		session = session.Where("store_id=?", storeId)
	}

	upDownState, ok := where["upDownState"]
	if ok {
		session = session.Where("up_down_state=?", upDownState)
	}
	isDistribution, ok := where["isDistribution"]
	if ok {
		session = session.Where("is_distribution=?", isDistribution)
	}
	channelId, ok := where["channelId"]
	if ok {
		session = session.Where("channel_id=?", channelId)
	}

	_, err = session.Table("eshop.pro_product_store_info").Select("id").Get(&out)
	return
}

type RetailDeleteReq struct {
	// APP方门店id   必填
	AppPoiCode string `protobuf:"bytes,1,opt,name=App_poi_code,json=AppPoiCode,proto3" json:"App_poi_code"`
	// APP方商品id   必填
	AppFoodCode string `protobuf:"bytes,2,opt,name=App_food_code,json=AppFoodCode,proto3" json:"App_food_code"`
	// 当商品目前只有一个sku时，删除此sku即会彻底此商品；而如果此商品为其分类下的最后一个商品，通过本参数选择当商品被删除的同时是否删除此分类，取值范围：1-删除分类；2-保留分类。
	//如未传本参数，则默认为1，表示删除分类。  非必填
	IsDeleteRetailCat int32 `protobuf:"varint,3,opt,name=Is_delete_retail_cat,json=IsDeleteRetailCat,proto3" json:"Is_delete_retail_cat"`
}

// DeleteProductStoreInfo 删除商品店铺信息
func (p *ProProductStoreInfo) DeleteProductStoreInfo(session *xorm.Session, storeProduct *ProProductStoreAppChannel) error {
	//查询要删除的storeProduct.sku
	skuIds := make([]int, 0)
	for _, sku := range storeProduct.Skus {
		skuIds = append(skuIds, sku.SkuId)
	}

	query := session.Table("eshop.pro_product_store_info").
		In("sku_id", skuIds).
		Where("product_id = ? and store_id = ? and channel_id = ?", storeProduct.ProductId, storeProduct.StoreId, storeProduct.ChannelId)
	if _, err := query.Delete(); err != nil {
		log.Errorf("删除商品店铺信息失败, productId: %d, skuId: %d, storeId: %s, channelId: %d, error: %s",
			storeProduct.ProductId, storeProduct.Skus[0].SkuId, storeProduct.StoreId, storeProduct.ChannelId, err.Error())
	}
	return nil
}

// UpdateStoreInfoStatus 更新商品店铺信息状态
func (p *ProProductStoreInfo) UpdateStoreInfoStatus(db *xorm.Engine, storeProduct *ProProductStoreAppChannel, fields string, data *ProProductStoreInfo) error {
	session := db.Table("eshop.pro_product_store_info")

	// 获取所有SKU ID
	skuIds := make([]int, 0)
	for _, sku := range storeProduct.Skus {
		skuIds = append(skuIds, sku.SkuId)
	}

	// 构建更新条件
	session = session.In("sku_id", skuIds).
		Where("store_id = ? and product_id = ? and channel_id = ?",
			storeProduct.StoreId,
			storeProduct.ProductId,
			storeProduct.ChannelId)

	if _, err := session.Cols(fields).Update(data); err != nil {
		log.Errorf("更新商品状态失败,params：%s,error:%s", utils.JsonEncode(storeProduct), err)
		return err
	}
	return nil
}

// UpdateShelveStatus 更新铺品状态
func (p *ProProductStoreInfo) UpdateShelveStatus(db *xorm.Engine, storeProduct *ProProductStoreAppChannel, syncError string) error {
	data := &ProProductStoreInfo{}
	fields := "sync_error,status"

	if syncError == "" {
		data.IsDistribution = proproductstoreinfo.IsDistributionLaunched
		data.Status = proproductstoreinfo.StatusNormal
		fields += ",is_distribution"
	} else {
		data.Status = proproductstoreinfo.StatusLaunchFailed
		data.SyncError = fmt.Sprintf("创建编辑失败：%s", syncError)
	}

	return p.UpdateStoreInfoStatus(db, storeProduct, fields, data)
}

// UpdateUpDownStatus 更新上下架状态
func (p *ProProductStoreInfo) UpdateUpDownStatus(db *xorm.Engine, storeProduct *ProProductStoreAppChannel, syncError string, upDownState int) error {
	data := &ProProductStoreInfo{
		SyncError: syncError,
	}

	if syncError == "" {
		data.UpDownState = upDownState
		data.Status = proproductstoreinfo.StatusNormal
	} else {
		if upDownState == proproductstoreinfo.UpDownStateUp {
			data.Status = proproductstoreinfo.StatusUpFailed
		} else {
			data.Status = proproductstoreinfo.StatusDownFailed
		}
		data.SyncError = fmt.Sprintf("%s失败：%s",
			map[int]string{
				proproductstoreinfo.UpDownStateUp:   "上架",
				proproductstoreinfo.UpDownStateDown: "下架",
			}[upDownState],
			syncError)
	}

	return p.UpdateStoreInfoStatus(db, storeProduct, "status,up_down_state,sync_error", data)
}
