package product_po

import (
	"context"
	"eShop/infra/log"
	"errors"
	"fmt"
	"time"

	"xorm.io/xorm"
)

type ProProductStore struct {
	// 主键
	Id int `json:"id" xorm:"pk autoincr not null INT 'id'"`

	// 商品ID
	ProductId int `json:"product_id" xorm:"default 'null' comment('商品ID') INT 'product_id'"`

	// 门店的ID
	StoreId string `json:"store_id" xorm:"default 'null' comment('门店的主键') VARCHAR(50) 'store_id'"`

	// 店铺名称
	StoreName string `json:"store_name" xorm:"<-"`

	// 添加时间
	CreateDate time.Time `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('添加时间') DATETIME 'create_date' created"`

	// 修改时间
	UpdateDate time.Time `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'update_date' updated"`
}

// NewProProductStoreModel 创建商品店铺充血模型对象
func NewProProductStoreModel() *ProProductStore {
	return &ProProductStore{}
}

// QueryProductStoresReq 查询商品已下发的店铺列表请求参数
type QueryProductStoresReq struct {
	ProductId  int      // 商品ID
	ProductIds []int    // 商品ID列表
	StoreIds   []string // 店铺ID列表
}

// QueryProductStores 查询商品已下发的店铺列表
func (p *ProProductStore) QueryProductStores(ctx context.Context, db interface{}, req QueryProductStoresReq) (map[int][]ProProductStore, map[string]bool, error) {
	// 初始化结果
	result := make(map[int][]ProProductStore)
	isPushed := make(map[string]bool)
	stores := make([]ProProductStore, 0)

	var session *xorm.Session

	// 根据传入类型获取session
	switch v := db.(type) {
	case *xorm.Engine:
		session = v.NewSession()
		defer session.Close()
	case *xorm.Session:
		session = v
	default:
		return nil, nil, errors.New("db参数类型错误,必须是 *xorm.Engine 或 *xorm.Session")
	}

	// 处理商品ID条件
	if req.ProductId > 0 {
		session = session.Where("a.product_id=?", req.ProductId)
	}
	if len(req.StoreIds) > 0 {
		session = session.In("a.store_id", req.StoreIds)
	}

	if len(req.ProductIds) > 0 {
		session = session.In("a.product_id", req.ProductIds)
	}

	// 查询数据库
	err := session.Table("eshop.pro_product_store").
		Alias("a").
		Select("a.id, a.product_id, a.store_id, b.name as store_name").
		Join("INNER", "eshop_saas.t_tenant b", "a.store_id = b.id").
		Where("b.is_deleted = 0").
		And("b.state = 1").
		Find(&stores)

	if err != nil {
		return nil, nil, fmt.Errorf("查询店铺列表失败: %v", err)
	}

	// 按商品ID分组，并记录下发状态
	for _, store := range stores {
		result[store.ProductId] = append(result[store.ProductId], store)
		key := fmt.Sprintf("%s_%d", store.StoreId, store.ProductId)
		isPushed[key] = true
	}

	return result, isPushed, nil
}

// 获取已经下发的信息
func GetPushedProductStore(db *xorm.Engine, where map[string]interface{}) (out map[string]bool, err error) {
	session := db.NewSession()
	defer session.Close()

	productId, ok := where["productId"]
	if ok {
		session = session.Where("product_id=?", productId)
	}

	productIds, ok := where["productIds"]
	if ok {
		session = session.In("product_id", productIds)
	}

	storeId, ok := where["storeId"]
	if ok {
		session = session.Where("store_id=?", storeId)
	}

	storeIds, ok := where["storeIds"]
	if ok {
		session = session.In("store_id", storeIds)
	}

	out = make(map[string]bool)
	data := make([]ProProductStore, 0)
	if err = session.Select("store_id,product_id,create_date,update_date").Table("eshop.pro_product_store").Find(&data); err != nil {
		return
	}

	for _, v := range data {
		k := fmt.Sprintf("%s_%d", v.StoreId, v.ProductId)
		out[k] = true
	}
	return

}

// DeleteProductStore 删除商品店铺关联
func (p *ProProductStore) DeleteProductStore(session *xorm.Session, productId int, storeId string) {
	go func() {
		// 先检查 pro_product_store_spu 表是否还有数据
		var count int64
		var err error
		if count, err = session.Table("eshop.pro_product_store_spu").
			Where("product_id = ? and store_id = ?", productId, storeId).Count(); err != nil {
			log.Errorf("查询商品SPU数据失败, productId: %d, storeId: %s, error: %s", productId, storeId, err.Error())
			return
		}

		// 如果没有SPU数据，则删除店铺关联
		if count == 0 {
			if _, err = session.Table("eshop.pro_product_store").
				Where("product_id = ? and store_id = ?", productId, storeId).Delete(); err != nil {
				log.Errorf("删除商品店铺关联失败, productId: %d, storeId: %s, error: %s",
					productId, storeId, err.Error())
			}
		}
	}()
}
