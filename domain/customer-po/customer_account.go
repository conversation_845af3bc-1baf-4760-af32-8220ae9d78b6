package customer_po

import (
	"errors"
	"time"

	"xorm.io/xorm"
)

// CustomerAccount 用户账户表
type CustomerAccount struct {
	Id                 int64     `json:"id" xorm:"pk autoincr 'id'"`                           // 唯一数据id
	ChainId            int64     `json:"chain_id" xorm:"'chain_id'"`                           // 连锁ID
	CustomerId         int64     `json:"customer_id" xorm:"'customer_id'"`                     // 用户id
	Balance            float64   `json:"balance" xorm:"'balance'"`                             // 余额
	Deposit            float64   `json:"deposit" xorm:"'deposit'"`                             // 押金
	NumberTotal        int       `json:"number_total" xorm:"'number_total'"`                   // 次卡剩余次数的和
	CouponsTotal       int       `json:"coupons_total" xorm:"'coupons_total'"`                 // 优惠卷总和
	OrderTotal         int       `json:"order_total" xorm:"'order_total'"`                     // 已消费订单总和
	ConsumptionTotal   float64   `json:"consumption_total" xorm:"'consumption_total'"`         // 消费总金额
	LastTime           time.Time `json:"last_time" xorm:"'last_time'"`                         // 上次到店时间
	LastAmount         float64   `json:"last_amount" xorm:"'last_amount'"`                     // 最近一次消费金额
	StoreCardPrincipal float64   `json:"store_card_principal" xorm:"'store_card_principal'"`   // 储值卡本金余额
	TimeCardPrincipal  int       `json:"time_card_principal" xorm:"'time_card_principal'"`     // 次卡购买次数余额
	StoreCardBuyNum    int       `json:"store_card_buy_num" xorm:"'store_card_buy_num'"`       // 储值卡购买数量
	StoreCardRemainNum int       `json:"store_card_remain_num" xorm:"'store_card_remain_num'"` // 储值卡剩余卡数
	TimeCardBuyNum     int       `json:"time_card_buy_num" xorm:"'time_card_buy_num'"`         // 次卡购买数量
	TimeCardRemainNum  int       `json:"time_card_remain_num" xorm:"'time_card_remain_num'"`   // 次卡剩余卡数
	IsDeleted          bool      `json:"is_deleted" xorm:"'is_deleted'"`                       // 删除标识: 0未删除,1已删除
	CreatedTime        time.Time `json:"created_time" xorm:"'created_time'"`                   // 创建时间
	UpdatedTime        time.Time `json:"updated_time" xorm:"'updated_time'"`                   // 更新时间

}

// TableName 表名
func (m *CustomerAccount) TableName() string {
	return "eshop_saas.c_customer_account"
}

// Update 根据CustomerId更新CustomerAccount记录
func (m *CustomerAccount) Update(session *xorm.Session, cols ...string) (int64, error) {
	if m.CustomerId == 0 {
		return 0, errors.New("CustomerId cannot be zero")
	}
	if session == nil {
		return 0, errors.New("session cannot be nil")
	}
	if len(cols) > 0 {
		return session.Cols(cols...).Update(m, &CustomerAccount{CustomerId: m.CustomerId})
	}
	// 使用xorm进行更新操作
	return session.Update(m, &CustomerAccount{CustomerId: m.CustomerId})
}
