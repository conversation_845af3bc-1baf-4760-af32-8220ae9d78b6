package order_po

import (
	"eShop/infra/log"
	"fmt"

	"xorm.io/xorm"
)

// OrderDetailInfo 订单详细信息(包含商品)
type OrderDetailInfo struct {
	*OrderMain                    // 订单主表信息
	Products   []*OrderProductExt // 订单商品信息
}

// OrderQuery 订单查询
type OrderQuery struct {
	session *xorm.Session
}

// OrderQueryReq 订单查询请求
type OrderQueryReq struct {
	// 订单号
	OrderSn string `json:"order_sn"`
	// 订单类型
	OrderType int `json:"order_type"`
	// 订单状态
	OrderStatus int `json:"order_status"`
	// 页码
	PageIndex int `json:"page_index"`
	// 每页数量
	PageSize int `json:"page_size"`
	// 主体ID
	OrgId int `json:"org_id"`
	// 用户ID
	ScrmUserId string `json:"scrm_user_id"`
}

// NewOrderQuery 创建订单查询实例
func NewOrderQuery(session *xorm.Session) *OrderQuery {
	return &OrderQuery{session: session}
}

// GetOrderList 获取订单列表(包含商品信息)
func (q *OrderQuery) GetOrderList(req OrderQueryReq) ([]*OrderDetailInfo, int64, error) {
	// 1. 先查询订单主表
	session := q.session.Table("dc_order.order_main").Where("org_id = ? and parent_order_sn != ''", req.OrgId)

	// 构建查询条件
	if req.OrderSn != "" {
		session.Where("order_sn = ?", req.OrderSn)
	}
	if req.OrderType > 0 {
		session.Where("order_type = ?", req.OrderType)
	}
	if req.ScrmUserId != "" {
		session.Where("member_id = ?", req.ScrmUserId)
	}

	// 查询订单主表数据
	var orders []*OrderMain
	total, err := session.OrderBy("create_time DESC").Limit(req.PageSize, (req.PageIndex-1)*req.PageSize).FindAndCount(&orders)

	if err != nil {
		log.Errorf("查询订单列表失败: %v", err)
		return nil, 0, fmt.Errorf("查询订单列表失败: %v", err)
	}

	if len(orders) == 0 {
		return nil, total, nil
	}

	// 2. 收集订单号
	orderSns := make([]string, 0, len(orders))
	for _, order := range orders {
		orderSns = append(orderSns, order.OrderSn)
	}

	// 3. 查询订单商品信息
	orderProduct := &OrderProduct{}
	productReq := OrderProductRequest{
		OrderSns: orderSns,
	}
	_, orderProductMap, err := orderProduct.GetOrderProduct(q.session, productReq)
	if err != nil {
		log.Errorf("查询订单商品列表失败: %v", err)
		return nil, 0, fmt.Errorf("查询订单商品列表失败: %v", err)
	}

	// 4. 组装订单详情
	result := make([]*OrderDetailInfo, 0, len(orders))
	for _, order := range orders {
		orderDetail := &OrderDetailInfo{
			OrderMain: order,
		}
		// 关联订单商品
		if products, ok := orderProductMap[order.OrderSn]; ok {
			// 转换为OrderProductExt
			productExts := make([]*OrderProductExt, 0, len(products))
			for _, p := range products {
				productExts = append(productExts, &OrderProductExt{
					OrderProduct: *p,
				})
			}
			orderDetail.Products = productExts
		}
		result = append(result, orderDetail)
	}

	return result, total, nil
}
