package order_po

import "xorm.io/xorm"

type OrderOriginData struct {
	Id         int    `json:"id" xorm:"pk autoincr not null comment('自增主键') INT 'id'"`
	ChannelId  int    `json:"channel_id" xorm:"not null default 0 comment('渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家') INT 'channel_id'"`
	OldOrderSn string `json:"old_order_sn" xorm:"not null default '' comment('渠道订单号') VARCHAR(50) 'old_order_sn'"`
	BodyJson   string `json:"body_json" xorm:"comment('原始内容') TEXT 'body_json'"`
	CreateTime string `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
}

func NewOrderOriginData() *OrderOriginData {
	return new(OrderOriginData)
}

func (o *OrderOriginData) Insert(session *xorm.Session, orderOriginData *OrderOriginData) error {
	_, err := session.Insert(orderOriginData)
	return err
}

// SaveOrderOriginData 保存第三方回调原数据
func (o *OrderOriginData) SaveOrderOriginData(session *xorm.Session, oldOrderSn string, channelId int, bodyJson string) error {
	o.OldOrderSn = oldOrderSn
	o.ChannelId = channelId
	o.BodyJson = bodyJson

	_, err := session.Insert(o)
	if err != nil {
		return err
	}
	return nil
}
