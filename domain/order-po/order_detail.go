package order_po

import (
	"errors"
	"time"
	"xorm.io/xorm"
)

type OrderDetail struct {
	OrderSn                 string    `json:"order_sn" xorm:"pk not null comment('订单编号') VARCHAR(50) 'order_sn'"`
	ChildChannelId          string    `json:"child_channel_id" xorm:"not null default '' comment('子渠道id，命名规则为channel_id+子渠道编码') VARCHAR(10) 'child_channel_id'"`
	ChildChannelName        string    `json:"child_channel_name" xorm:"not null comment('子渠道名称') VARCHAR(20) 'child_channel_name'"`
	PerformanceStaffName    string    `json:"performance_staff_name" xorm:"not null default '' comment('业绩所属员工姓名') VARCHAR(30) 'performance_staff_name'"`
	PerformanceOperatorName string    `json:"performance_operator_name" xorm:"not null default '' comment('业绩操作人姓名') VARCHAR(30) 'performance_operator_name'"`
	PerformanceOperatorTime time.Time `json:"performance_operator_time" xorm:"default 'null' comment('业绩操作时间') DATETIME 'performance_operator_time'"`
	NoticeTime              int       `json:"notice_time" xorm:"not null default 0 comment('订单拣货提醒通知时间') INT 'notice_time'"`
	Invoice                 string    `json:"invoice" xorm:"not null default '' comment('发票信息') VARCHAR(255) 'invoice'"`
	BuyerMemo               string    `json:"buyer_memo" xorm:"not null default '' comment('买家留言') VARCHAR(255) 'buyer_memo'"`
	SellerMemo              string    `json:"seller_memo" xorm:"not null default '' comment('卖家留言') VARCHAR(255) 'seller_memo'"`
	GyDeliverStatus         int8      `json:"gy_deliver_status" xorm:"not null default 0 comment('管易发货状态,0未发货,1已发货,2部分发货') TINYINT 'gy_deliver_status'"`
	DeliveryRemark          string    `json:"delivery_remark" xorm:"not null default '' comment('配送备注') VARCHAR(255) 'delivery_remark'"`
	PushDelivery            int8      `json:"push_delivery" xorm:"not null default 0 comment('是否推送美团配送,1是0否') TINYINT 'push_delivery'"`
	PushDeliveryReason      string    `json:"push_delivery_reason" xorm:"not null default '' comment('推送美团配送失败原因') VARCHAR(255) 'push_delivery_reason'"`
	PushThirdOrder          int8      `json:"push_third_order" xorm:"not null default 0 comment('是否推送子龙或全渠道,1是0否') TINYINT 'push_third_order'"`
	PushThirdOrderReason    string    `json:"push_third_order_reason" xorm:"comment('推送子龙或全渠道失败原因') TEXT 'push_third_order_reason'"`
	SplitOrderResult        int8      `json:"split_order_result" xorm:"not null default 0 comment('拆分订单结果,0拆单中1成功2失败') TINYINT 'split_order_result'"`
	SplitOrderFailReason    string    `json:"split_order_fail_reason" xorm:"not null default '' comment('拆分订单失败原因') VARCHAR(255) 'split_order_fail_reason'"`
	AcceptUsername          string    `json:"accept_username" xorm:"not null default '' comment('接单人') VARCHAR(50) 'accept_username'"`
	AcceptTime              time.Time `json:"accept_time" xorm:"default 'null' comment('美团接单时间') DATETIME 'accept_time'"`
	IsPicking               int8      `json:"is_picking" xorm:"not null default 0 comment('是否拣货,0否1是') TINYINT 'is_picking'"`
	PickingTime             time.Time `json:"picking_time" xorm:"default 'null' comment('美团拣货时间') DATETIME 'picking_time'"`
	PickUserId              int       `json:"pick_user_id" xorm:"not null default 0 comment('领取拣货任务人id') BIGINT 'pick_user_id'"`
	PickUserName            string    `json:"pick_user_name" xorm:"not null default '' comment('领取拣货任务人名称') VARCHAR(100) 'pick_user_name'"`
	ExpectedTime            time.Time `json:"expected_time" xorm:"default 'null' comment('预计送达时间') DATETIME 'expected_time'"`
	LockedStock             int8      `json:"locked_stock" xorm:"not null default 0 comment('是否锁定库存,0否1是') TINYINT 'locked_stock'"`
	Extras                  string    `json:"extras" xorm:"comment('美团附加优惠信息json') MEDIUMTEXT 'extras'"`
	RemindTime              string    `json:"remind_time" xorm:"not null default '' comment('催单时间戳（如用户发起了多次催单，此字段信息会推送多个催单时间）') VARCHAR(255) 'remind_time'"`
	Latitude                float64   `json:"latitude" xorm:"not null default '0.000000' comment('收货地址纬度') DOUBLE(10) 'latitude'"`
	Longitude               float64   `json:"longitude" xorm:"not null default '0.000000' comment('收货地址经度') DOUBLE(10) 'longitude'"`
	PickupCode              string    `json:"pickup_code" xorm:"not null default '' comment('取货码') VARCHAR(30) 'pickup_code'"`
	PayType                 string    `json:"pay_type" xorm:"not null default '' comment('Cod=货到付款, NoCod=非货到付款') VARCHAR(20) 'pay_type'"`
	IsAdjust                int8      `json:"is_adjust" xorm:"not null default 0 comment('是否订单调整，1是0否') TINYINT 'is_adjust'"`
	PowerId                 int       `json:"power_id" xorm:"not null default 0 comment('电商助力id') INT 'power_id'"`
	CreateTime              time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime              time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	DelGyOrderSn            string    `json:"del_gy_order_sn" xorm:"not null default '' comment('管易订单号、第三方订单号') VARCHAR(50) 'del_gy_order_sn'"`
	DelGjpStatus            string    `json:"del_gjp_status" xorm:"not null default '' comment('管家婆状态NoPay = 未付款 Payed = 已付款 Sended = 已发货 TradeSuccess = 交易成功 TradeClosed = 交易关闭 PartSend = 部分发货') VARCHAR(20) 'del_gjp_status'"`
	IsNewCustomer           int       `json:"is_new_customer" xorm:"not null default 0 comment('默认值0,1-新客户,2-老客户') INT 'is_new_customer'"`
	PickupStationId         int       `json:"pickup_station_id" xorm:"default 0 comment('社区团购自提点id') INT 'pickup_station_id'"`
	GoodsReturnDeliveryId   int       `json:"goods_return_delivery_id" xorm:"default 0 comment('需要确认商品返回的配送单号（有需要确认的才会有值，没有则不需要）') BIGINT 'goods_return_delivery_id'"`
	ShopDisMemberId         string    `json:"shop_dis_member_id" xorm:"default 'null' comment('店铺海报分销员id') VARCHAR(32) 'shop_dis_member_id'"`
	ShopDisChainId          int       `json:"shop_dis_chain_id" xorm:"default 0 comment('店铺海报分销员业绩所属门店id') INT 'shop_dis_chain_id'"`
	ConsultOrderSn          string    `json:"consult_order_sn" xorm:"default '' comment('医疗互联网订单号处方ID推荐ID') VARCHAR(80) 'consult_order_sn'"`
	BillCompletedTime       time.Time `json:"bill_completed_time" xorm:"default 'null' comment('账单订单完成时间') DATETIME 'bill_completed_time'"`
	BillCanceledTime        time.Time `json:"bill_canceled_time" xorm:"default 'null' comment('账单订单取消时间') DATETIME 'bill_canceled_time'"`
	TradeCreatedTime        time.Time `json:"trade_created_time" xorm:"default 'null' comment('账户时间') DATETIME 'trade_created_time'"`
	TradePaymentTime        time.Time `json:"trade_payment_time" xorm:"default 'null' comment('入账时间') DATETIME 'trade_payment_time'"`
	TradeTime               time.Time `json:"trade_time" xorm:"default 'null' comment('账单日期') DATETIME 'trade_time'"`
	CashierName             string    `json:"cashier_name" xorm:"default 'null' comment('收银员') VARCHAR(100) 'cashier_name'"`
	CashierId               string    `json:"cashier_id" xorm:"default 'null' comment('收银员ID') VARCHAR(100) 'cashier_id'"`
	PetName                 string    `json:"pet_name" xorm:"default 'null' comment('宠物') VARCHAR(100) 'pet_name'"`
	PetId                   string    `json:"pet_id" xorm:"default 'null' comment('宠物名称') VARCHAR(100) 'pet_id'"`
}

func (od *OrderDetail) TableName() string {
	return "dc_order.order_detail"
}

// BatchCreate 批量创建订单详情
func (d *OrderDetail) BatchCreate(session *xorm.Session, details []*OrderDetail) (err error) {
	if session == nil {
		return errors.New("session is nil")
	}
	if len(details) == 0 {
		return nil
	}
	_, err = session.Insert(details)
	return err
}

type OrderDetailRequest struct {
	OrderSn  string   `json:"order_sn"`  // 订单编号
	OrderSns []string `json:"order_sns"` // 订单编号列表
	Fields   string   `json:"fields"`    // 查询字段
}

func (od *OrderDetail) GetOrderDetail(session *xorm.Session, req OrderDetailRequest) (out []*OrderDetail, out1 map[string]*OrderDetail, err error) {
	out = make([]*OrderDetail, 0)

	if req.Fields == "" {
		req.Fields = "*"
	}
	session = session.Table("dc_order.order_detail").Select(req.Fields)
	if req.OrderSn != "" {
		session = session.Where("order_sn = ?", req.OrderSn)
	}
	if len(req.OrderSns) > 0 {
		session = session.In("order_sn", req.OrderSns)
	}

	err = session.Find(&out)

	if err != nil {
		return nil, nil, err
	}

	out1 = make(map[string]*OrderDetail)

	for _, order := range out {
		out1[order.OrderSn] = order

	}
	return out, out1, nil
}
