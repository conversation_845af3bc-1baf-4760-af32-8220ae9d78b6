package order_po

import (
	"eShop/infra/log"
	"fmt"
	"time"

	"xorm.io/xorm"
)

type OrderMain struct {
	Id                 int        `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	OrderSn            string     `xorm:"not null default '''' comment('订单号') unique VARCHAR(50)"`
	OldOrderSn         string     `xorm:"not null default '''' comment('原电商父订单号') index VARCHAR(50)"`
	ParentOrderSn      string     `xorm:"not null default '''' comment('拆单前父订单号') index VARCHAR(50)"`
	OrderStatus        int        `xorm:"not null default 0 comment('订单状态：0已取消,10(默认)未付款,20已付款,30已完成') INT(11)"`
	OrderStatusChild   int        `xorm:"not null default 0 comment('子状态：20101（默认）未接单,20102已接单,20103配送中,20104已送达,20105已取货,20106已完成,20107已取消;10201(电商默认，以下状态电商专用)未付款,20201待发货,20202全部发货,20203确认收货,20204部分发货,20205已取消;虚拟订单专用,30101待核销,30102部分核销,30103已核销') INT(11)"`
	ShopId             string     `xorm:"not null default '''' comment('商户或门店id(财务编码)') index VARCHAR(80)"`
	ShopName           string     `xorm:"not null default '''' comment('商户名称') VARCHAR(100)"`
	WarehouseId        int        `xorm:"not null default 0 comment('仓库id') INT(11)"`
	WarehouseCode      string     `xorm:"not null default '''' comment('仓库代码') VARCHAR(255)"`
	WarehouseName      string     `xorm:"not null default '''' comment('仓库名称') VARCHAR(255)"`
	MemberId           string     `xorm:"not null default '''' comment('会员id') index VARCHAR(50)"`
	MemberName         string     `xorm:"not null default '''' comment('会员名称') VARCHAR(50)"`
	MemberTel          string     `xorm:"not null default '''' comment('会员手机号') VARCHAR(20)"`
	EnMemberTel        string     `xorm:"not null default '''' comment('加密手机号') VARCHAR(20)"`
	ReceiverName       string     `xorm:"not null default '''' comment('收件人') VARCHAR(50)"`
	ReceiverState      string     `xorm:"not null default '''' comment('收件省') VARCHAR(50)"`
	ReceiverCity       string     `xorm:"not null default '''' comment('收件市') VARCHAR(50)"`
	ReceiverDistrict   string     `xorm:"not null default '''' comment('收件区') VARCHAR(50)"`
	ReceiverAddress    string     `xorm:"not null default '''' comment('收件地址') VARCHAR(255)"`
	ReceiverPhone      string     `xorm:"not null default '''' comment('收件电话') VARCHAR(30)"`
	EnReceiverPhone    string     `xorm:"not null default '''' comment('加密收件电话') VARCHAR(30)"`
	ReceiverMobile     string     `xorm:"not null default '''' comment('收件手机') VARCHAR(30)"`
	EnReceiverMobile   string     `xorm:"not null default '''' comment('加密收件手机') VARCHAR(30)"`
	Total              int        `xorm:"not null default 0 comment('实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额）') INT(11)"`
	PayTotal           int        `xorm:"not null default 0 comment('当前订单实付金额') INT(10)"`
	GoodsTotal         int        `xorm:"not null default 0 comment('商品总金额（去掉优惠，运费，包装费，服务费等的金额）') INT(11)"`
	GoodsPayTotal      int        `xorm:"not null default 0 comment('商品实付总金额') INT(10)"`
	Privilege          int        `xorm:"not null default 0 comment('总优惠金额(分)') INT(11)"`
	CombinePrivilege   int        `xorm:"not null default 0 comment('组合商品总优惠金额(分)') INT(11)"`
	Freight            int        `xorm:"not null default 0 comment('总运费(分)') INT(11)"`
	FreightPrivilege   int        `xorm:"default 0 comment('商家运费优惠金额(分)') INT(11)"`
	PackingCost        int        `xorm:"not null default 0 comment('包装费(分)') INT(11)"`
	ServiceCharge      int        `xorm:"not null default 0 comment('服务费(分)') INT(11)"`
	ContractFee        int        `xorm:"not null default 0 comment('履约费(分)') INT(11)"`
	PoiChargeTotal     int        `xorm:"not null default 0 comment('商家补贴') INT(10)"`
	PtChargeTotal      int        `xorm:"not null default 0 comment('平台补贴') INT(10)"`
	PtFreightPrivilege int        `xorm:"not null default 0 comment('平台配送费优惠') INT(10)"`
	ActualReceiveTotal int        `xorm:"not null default 0 comment('商家预计收入') INT(10)"`
	RefundAmount       int        `xorm:"not null default 0 comment('已退款金额(分)') INT(11)"`
	TotalWeight        int        `xorm:"not null default 0 comment('总重量(克)') INT(11)"`
	IsPay              int        `xorm:"not null default 0 comment('是否支付，0否1是') TINYINT(4)"`
	PayTime            time.Time  `xorm:"default 'NULL' comment('支付时间') "`
	PaySn              string     `xorm:"not null default '''' comment('支付单号') VARCHAR(50)"`
	PayMode            int        `xorm:"not null default 0 comment('支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付,8储值卡支付') TINYINT(4)"`
	PayAmount          int        `xorm:"not null default 0 comment('实际支付金额') INT(11)"`
	ConfirmTime        time.Time  `xorm:"default 'NULL' comment('完成时间') "`
	DeliverTime        time.Time  `xorm:"default 'NULL' comment('发货时间') "`
	CancelTime         time.Time  `xorm:"default 'NULL' comment('取消订单时间') "`
	CancelReason       string     `xorm:"not null default '''' comment('取消订单原因') VARCHAR(255)"`
	OrderType          int        `xorm:"not null default 1 comment('订单类型 1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购 10新人专享 11预售 12新秒杀 13在线问诊 14年夜饭活动 15团长开团 16爱心币订单 17付费会员卡 18家庭医生服务包 19会员卡0元购 21：电商VIP实物订单 22：次卡 23：储值卡 99助力订单') INT(11)"`
	Source             int        `xorm:"not null default 0 comment('仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）') TINYINT(4)"`
	DeliveryType       int        `xorm:"not null default 1 comment('配送类型,1快递,2外卖,3自提,4同城送,5商家自配') TINYINT(4)"`
	LogisticsCode      string     `xorm:"not null default '''' comment('配送方式编码,如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等 饿了么物流类型：1 蜂鸟 2 蜂鸟自配送 3 蜂鸟众包 4 饿了么众包 5 蜂鸟配送 6 饿了么自配送 7 全城送 8 快递配送') VARCHAR(20)"`
	ChannelId          int        `xorm:"not null default 0 comment('渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店') INT(11)"`
	UserAgent          int        `xorm:"not null default 0 comment('渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它') INT(11)"`
	IsVirtual          int        `xorm:"not null default 0 comment('是否是虚拟订单，0否1是') TINYINT(4)"`
	CreateTime         time.Time  `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime         time.Time  `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	IsPushTencent      int        `xorm:"not null default 0 comment('1.已经推送到腾讯有数') INT(11)"`
	AppChannel         int        `xorm:"not null default 1 comment('1.阿闻自有,2.TP代运营') INT(11)"`
	OrderPayType       string     `xorm:"not null default '''' comment('交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)') VARCHAR(10)"`
	Lng                string     `xorm:"not null default '''' comment('下单经度') VARCHAR(56)"`
	Lat                string     `xorm:"not null default '''' comment('下单纬度') VARCHAR(56)"`
	OrgId              int        `xorm:"not null default 1 comment('主体ID，对应organization_info主键') INT(11)"`
	ParentOrderInfo    *OrderMain `xorm:"-"` // 父单信息
	LogisticsName      string     `xorm:"not null default '''' comment('SAAS对应配送方式名称') VARCHAR(255)"`
}

type Order struct {
	*OrderMain              `xorm:"extends"`
	ChildChannelId          string    `xorm:"not null default '''' comment('子渠道id，命名规则为channel_id+子渠道编码') VARCHAR(10)"`
	ChildChannelName        string    `xorm:"not null comment('子渠道名称') VARCHAR(20)"`
	PerformanceStaffName    string    `xorm:"not null default '''' comment('业绩所属员工姓名') VARCHAR(30)"`
	PerformanceOperatorName string    `xorm:"not null default '''' comment('业绩操作人姓名') VARCHAR(30)"`
	PerformanceOperatorTime time.Time `xorm:"default 'NULL' comment('业绩操作时间') DATETIME"`
	NoticeTime              int64     `xorm:"not null default 0 comment('订单拣货提醒通知时间') INT(11)"`
	Invoice                 string    `xorm:"not null default '''' comment('发票信息') VARCHAR(255)"`
	BuyerMemo               string    `xorm:"not null default '''' comment('买家留言') VARCHAR(255)"`
	SellerMemo              string    `xorm:"not null default '''' comment('卖家留言') VARCHAR(255)"`
	GyDeliverStatus         int32     `xorm:"not null default 0 comment('管易发货状态,0未发货,1已发货,2部分发货') TINYINT(4)"`
	DeliveryRemark          string    `xorm:"not null default '''' comment('配送备注') VARCHAR(255)"`
	PushDelivery            int32     `xorm:"not null default 0 comment('是否推送美团配送,1是0否') TINYINT(4)"`
	PushDeliveryReason      string    `xorm:"not null default '''' comment('推送美团配送失败原因') VARCHAR(255)"`
	PushThirdOrder          int32     `xorm:"not null default 0 comment('是否推送子龙或全渠道,1是0否') TINYINT(4)"`
	PushThirdOrderReason    string    `xorm:"not null default '''' comment('推送子龙或全渠道失败原因') TEXT"`
	SplitOrderResult        int32     `xorm:"not null default 0 comment('拆分订单结果,0拆单中1成功2失败') TINYINT(4)"`
	SplitOrderFailReason    string    `xorm:"not null default '''' comment('拆分订单失败原因') VARCHAR(255)"`
	AcceptUsername          string    `xorm:"not null default '''' comment('接单人') VARCHAR(50)"`
	AcceptTime              time.Time `xorm:"default 'NULL' comment('美团接单时间') DATETIME"`
	IsPicking               int32     `xorm:"not null default 0 comment('是否拣货,0否1是') TINYINT(4)"`
	PickingTime             time.Time `xorm:"default 'NULL' comment('美团拣货时间') DATETIME"`
	PickUserId              int64     `xorm:"not null default 0 comment('领取拣货任务人id') BIGINT(20)"`
	PickUserName            string    `xorm:"not null default '' comment('领取拣货任务人名称') VARCHAR(100)"`
	ExpectedTime            time.Time `xorm:"default 'NULL' comment('预计送达时间') DATETIME"`
	LockedStock             int32     `xorm:"not null default 0 comment('是否锁定库存,0否1是') TINYINT(4)"`
	Extras                  string    `xorm:"not null default '''' comment('美团附加优惠信息json') VARCHAR(3000)"`
	RemindTime              string    `xorm:"not null default '''' comment('催单时间戳（如用户发起了多次催单，此字段信息会推送多个催单时间）') VARCHAR(255)"`
	Latitude                float64   `xorm:"not null default 0.000000 comment('收货地址纬度') DOUBLE(10,6)"`
	Longitude               float64   `xorm:"not null default 0.000000 comment('收货地址经度') DOUBLE(10,6)"`
	PickupCode              string    `xorm:"not null default '''' comment('取货码') VARCHAR(30)"`
	PayType                 string    `xorm:"not null default '''' comment('Cod=货到付款, NoCod=非货到付款') VARCHAR(20)"`
	IsAdjust                int32     `xorm:"not null default 0 comment('是否订单调整，1是0否') TINYINT(4)"`
	PowerId                 int32     `xorm:"not null default 0 comment('电商助力id') INT(11)"`
	GoodsReturnDeliveryId   int64     `xorm:"not null default 0 comment('需要确认商品返回的配送单号（有需要确认的才会有值，没有则不需要）') BIGINT(32)"`
	PickupStationId         int32     `xorm:"default 0 comment('社区团购自提点id') INT(11)"`
}

func (om *OrderMain) TableName() string {
	return "dc_order.order_main"
}

// Create 创建单个订单
func (m *OrderMain) Create(session *xorm.Session, order *OrderMain) error {
	if order == nil {
		return fmt.Errorf("订单信息不能为空")
	}

	// 使用InsertOne并启用Sync，会自动回写ID
	_, err := session.Insert(order)
	if err != nil {
		return fmt.Errorf("插入订单主表失败: %v", err)
	}

	return nil
}

func (o *OrderMain) Update(session *xorm.Session, order OrderMainView, fields map[string]interface{}) error {
	// 查询订单
	var Order OrderMain
	_, err := session.Table(o.TableName()).Where("order_sn = ?", order.OrderSn).Get(&Order)
	if err != nil {
		return err
	}

	if order.Id == 0 {
		var Order OrderMain
		_, err := session.Table(o.TableName()).Where("parent_order_sn = ?", order.OrderSn).Get(&Order)
		if err != nil {
			return err
		}
	}
	if order.Id == 0 {
		return fmt.Errorf("订单不存在")
	}

	if _, err := session.Table(o.TableName()).
		Where("id = ?", Order.Id).
		Update(fields); err != nil {
		log.Errorf("order_sn=%s,更新失败，err=%s", order.OrderSn, err.Error())
	}

	// 判断是否存在 parent_order_sn
	if Order.ParentOrderSn != "" {
		if _, err = session.Table(o.TableName()).
			Where("order_sn = ?", order.ParentOrderSn).
			Update(fields); err != nil {
			log.Errorf("order_sn=%s,更新父订单失败，err=%s", order.OrderSn, err.Error())
		}
		return err
	} else {
		if _, err = session.Table(o.TableName()).
			Where("parent_order_sn = ?", order.OrderSn).
			Update(fields); err != nil {
			log.Errorf("order_sn=%s,更新子订单失败，err=%s", order.OrderSn, err.Error())
		}
	}
	return nil
}

type OrderInfoRequest struct {
	PageIndex     int      `json:"page_index"`      // 页码
	PageSize      int      `json:"page_size"`       // 每页数量
	ParentOrderSn string   `json:"parent_order_sn"` // 父订单编号
	OrderSn       string   `json:"order_sn"`        // 订单编号
	OrderSns      []string `json:"order_sns"`       // 订单编号列表
	Fields        string   `json:"fields"`          // 查询字段

}

func (om *OrderMain) GetOrderInfo(session *xorm.Session, req OrderInfoRequest) (out []*OrderMain, out1 map[string]*OrderMain, out2 map[int]*OrderMain, total int64, err error) {
	out = make([]*OrderMain, 0)

	if req.Fields == "" {
		req.Fields = "*"
	}
	session = session.Table("dc_order.order_main").Select(req.Fields)
	if req.OrderSn != "" {
		session = session.Where("order_sn = ?", req.OrderSn)

	}
	if len(req.OrderSns) > 0 {
		session = session.In("order_sn", req.OrderSns)
	}
	if req.ParentOrderSn != "" {
		session = session.Where("parent_order_sn = ?", req.ParentOrderSn)
	}
	if req.PageIndex > 0 && req.PageSize > 0 {
		total, err = session.Limit(req.PageSize, (req.PageIndex-1)*req.PageSize).FindAndCount(&out)
	} else {
		err = session.Find(&out)
	}
	if err != nil {
		return nil, nil, nil, 0, err
	}

	out1 = make(map[string]*OrderMain)
	out2 = make(map[int]*OrderMain)
	for _, order := range out {

		out1[order.OrderSn] = order

		out2[order.Id] = order

	}
	return out, out1, out2, total, nil
}

// UpdateOrderStatus 更新订单状态
func (o *OrderMain) UpdateOrderStatus(session *xorm.Session, order OrderMainView) error {
	_, err := session.Table(o.TableName()).
		Where("order_sn = ? or order_sn = ?", order.OrderSn, order.ParentOrderSn).
		Update(map[string]interface{}{
			"order_status":       0,
			"order_status_child": 20107,
		})
	if err != nil {
		log.Errorf("更新订单状态失败, err: %v", err)
		return err
	}
	return nil
}

// 更新订单退款金额
func (o *OrderMain) UpdateRefundAmount(session *xorm.Session, orderSn string, refundAmount int) error {
	_, err := session.Table(o.TableName()).Where("order_sn = ?", orderSn).Update(map[string]interface{}{
		"refund_amount": refundAmount,
	})
	if err != nil {
		return err
	}
	return nil
}

func (om *OrderMain) GetOrderDetail(session *xorm.Session, req OrderInfoRequest) (out OrderMainView, err error) {

	_, err = session.Table("dc_order.order_main").Alias("a").Join("left", "datacenter.store b", "a.shop_id = b.finance_code").Where("order_sn=?", req.OrderSn).Get(&out)
	if err != nil {
		return out, err
	}
	return out, nil
}

type OrderMainView struct {
	Id                 int       `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	OrderSn            string    `xorm:"not null default '''' comment('订单号') unique VARCHAR(50)"`
	OldOrderSn         string    `xorm:"not null default '''' comment('原电商父订单号') index VARCHAR(50)"`
	ParentOrderSn      string    `xorm:"not null default '''' comment('拆单前父订单号') index VARCHAR(50)"`
	OrderStatus        int       `xorm:"not null default 0 comment('订单状态：0已取消,10(默认)未付款,20已付款,30已完成') INT(11)"`
	OrderStatusChild   int       `xorm:"not null default 0 comment('子状态：20101（默认）未接单,20102已接单,20103配送中,20104已送达,20105已取货,20106已完成,20107已取消;10201(电商默认，以下状态电商专用)未付款,20201待发货,20202全部发货,20203确认收货,20204部分发货,20205已取消;虚拟订单专用,30101待核销,30102部分核销,30103已核销') INT(11)"`
	ShopId             string    `xorm:"not null default '''' comment('商户或门店id(财务编码)') index VARCHAR(80)"`
	ShopName           string    `xorm:"not null default '''' comment('商户名称') VARCHAR(100)"`
	WarehouseId        int       `xorm:"not null default 0 comment('仓库id') INT(11)"`
	WarehouseCode      string    `xorm:"not null default '''' comment('仓库代码') VARCHAR(255)"`
	WarehouseName      string    `xorm:"not null default '''' comment('仓库名称') VARCHAR(255)"`
	MemberId           string    `xorm:"not null default '''' comment('会员id') index VARCHAR(50)"`
	MemberName         string    `xorm:"not null default '''' comment('会员名称') VARCHAR(50)"`
	MemberTel          string    `xorm:"not null default '''' comment('会员手机号') VARCHAR(20)"`
	EnMemberTel        string    `xorm:"not null default '''' comment('加密手机号') VARCHAR(20)"`
	ReceiverName       string    `xorm:"not null default '''' comment('收件人') VARCHAR(50)"`
	ReceiverState      string    `xorm:"not null default '''' comment('收件省') VARCHAR(50)"`
	ReceiverCity       string    `xorm:"not null default '''' comment('收件市') VARCHAR(50)"`
	ReceiverDistrict   string    `xorm:"not null default '''' comment('收件区') VARCHAR(50)"`
	ReceiverAddress    string    `xorm:"not null default '''' comment('收件地址') VARCHAR(255)"`
	ReceiverPhone      string    `xorm:"not null default '''' comment('收件电话') VARCHAR(30)"`
	EnReceiverPhone    string    `xorm:"not null default '''' comment('加密收件电话') VARCHAR(30)"`
	ReceiverMobile     string    `xorm:"not null default '''' comment('收件手机') VARCHAR(30)"`
	EnReceiverMobile   string    `xorm:"not null default '''' comment('加密收件手机') VARCHAR(30)"`
	Total              int       `xorm:"not null default 0 comment('实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额）') INT(11)"`
	PayTotal           int       `xorm:"not null default 0 comment('当前订单实付金额') INT(10)"`
	GoodsTotal         int       `xorm:"not null default 0 comment('商品总金额（去掉优惠，运费，包装费，服务费等的金额）') INT(11)"`
	GoodsPayTotal      int       `xorm:"not null default 0 comment('商品实付总金额') INT(10)"`
	Privilege          int       `xorm:"not null default 0 comment('总优惠金额(分)') INT(11)"`
	CombinePrivilege   int       `xorm:"not null default 0 comment('组合商品总优惠金额(分)') INT(11)"`
	Freight            int       `xorm:"not null default 0 comment('总运费(分)') INT(11)"`
	FreightPrivilege   int       `xorm:"default 0 comment('商家运费优惠金额(分)') INT(11)"`
	PackingCost        int       `xorm:"not null default 0 comment('包装费(分)') INT(11)"`
	ServiceCharge      int       `xorm:"not null default 0 comment('服务费(分)') INT(11)"`
	ContractFee        int       `xorm:"not null default 0 comment('履约费(分)') INT(11)"`
	PoiChargeTotal     int       `xorm:"not null default 0 comment('商家补贴') INT(10)"`
	PtChargeTotal      int       `xorm:"not null default 0 comment('平台补贴') INT(10)"`
	PtFreightPrivilege int       `xorm:"not null default 0 comment('平台配送费优惠') INT(10)"`
	ActualReceiveTotal int       `xorm:"not null default 0 comment('商家预计收入') INT(10)"`
	RefundAmount       int       `xorm:"not null default 0 comment('已退款金额(分)') INT(11)"`
	TotalWeight        int       `xorm:"not null default 0 comment('总重量(克)') INT(11)"`
	IsPay              int       `xorm:"not null default 0 comment('是否支付，0否1是') TINYINT(4)"`
	PayTime            time.Time `xorm:"default 'NULL' comment('支付时间') "`
	PaySn              string    `xorm:"not null default '''' comment('支付单号') VARCHAR(50)"`
	PayMode            int       `xorm:"not null default 0 comment('支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付,8储值卡支付') TINYINT(4)"`
	PayAmount          int       `xorm:"not null default 0 comment('实际支付金额') INT(11)"`
	ConfirmTime        time.Time `xorm:"default 'NULL' comment('完成时间') "`
	DeliverTime        time.Time `xorm:"default 'NULL' comment('发货时间') "`
	CancelTime         time.Time `xorm:"default 'NULL' comment('取消订单时间') "`
	CancelReason       string    `xorm:"not null default '''' comment('取消订单原因') VARCHAR(255)"`
	OrderType          int       `xorm:"not null default 1 comment('订单类型 1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购 10新人专享 11预售 12新秒杀 13在线问诊 14年夜饭活动 15团长开团 16爱心币订单 17付费会员卡 18家庭医生服务包 19会员卡0元购 99助力订单') INT(11)"`
	Source             int       `xorm:"not null default 0 comment('仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）') TINYINT(4)"`
	DeliveryType       int       `xorm:"not null default 1 comment('配送类型,1快递,2外卖,3自提,4同城送,5商家自配') TINYINT(4)"`
	LogisticsCode      string    `xorm:"not null default '''' comment('配送方式编码,如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等 饿了么物流类型：1 蜂鸟 2 蜂鸟自配送 3 蜂鸟众包 4 饿了么众包 5 蜂鸟配送 6 饿了么自配送 7 全城送 8 快递配送') VARCHAR(20)"`
	ChannelId          int       `xorm:"not null default 0 comment('渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店') INT(11)"`
	UserAgent          int       `xorm:"not null default 0 comment('渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它') INT(11)"`
	IsVirtual          int       `xorm:"not null default 0 comment('是否是虚拟订单，0否1是') TINYINT(4)"`
	CreateTime         string    `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime         string    `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	IsPushTencent      int       `xorm:"not null default 0 comment('1.已经推送到腾讯有数') INT(11)"`
	AppChannel         int       `xorm:"not null default 1 comment('1.阿闻自有,2.TP代运营') INT(11)"`
	OrderPayType       string    `xorm:"not null default '''' comment('交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)') VARCHAR(10)"`
	Lng                string    `xorm:"not null default '''' comment('下单经度') VARCHAR(56)"`
	Lat                string    `xorm:"not null default '''' comment('下单纬度') VARCHAR(56)"`
	OrgId              int       `xorm:"not null default 1 comment('主体ID，对应organization_info主键') INT(11)"`
	ChainId            int64     `xorm:"chain_id"`
}
