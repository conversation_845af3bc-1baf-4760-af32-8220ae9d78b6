package order_po

type OrderMtData struct {
	OrderSn       string `json:"order_sn" xorm:"pk not null comment('美团订单号') VARCHAR(50) 'order_sn'"`
	Data          string `json:"data" xorm:"not null comment('mt 提交订单json 数据') TEXT 'data'"`
	ProcessStatus int    `json:"process_status" xorm:"not null default 1 comment('处理状态, 1待处理, 2处理失败, 3已处理') TINYINT 'process_status'"`
	Reason        string `json:"reason" xorm:"default 'null' comment('原因') VARCHAR(500) 'reason'"`
	CreateTime    string `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
}

func NewOrderMtData() *OrderMtData {
	return new(OrderMtData)
}
