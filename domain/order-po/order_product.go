package order_po

import (
	"errors"
	"fmt"
	"time"

	"xorm.io/xorm"
)

type OrderProduct struct {
	Id                   int       `json:"id" xorm:"pk autoincr not null comment('自增主键') INT 'id'"`
	OrderSn              string    `json:"order_sn" xorm:"not null default '' comment('主订单号') VARCHAR(50) 'order_sn'"`
	SkuId                string    `json:"sku_id" xorm:"not null default '' comment('商品skuid') VARCHAR(50) 'sku_id'"`
	ParentSkuId          string    `json:"parent_sku_id" xorm:"not null default '' comment('组合商品父级skuid') VARCHAR(50) 'parent_sku_id'"`
	ChildrenSku          string    `json:"children_sku" xorm:"comment('组合商品子商品集合') TEXT 'children_sku'"`
	ThirdSkuId           string    `json:"third_sku_id" xorm:"not null default '' comment('第三方货号') VARCHAR(36) 'third_sku_id'"`
	ProductId            string    `json:"product_id" xorm:"not null default '' comment('商品id') VARCHAR(50) 'product_id'"`
	ProductName          string    `json:"product_name" xorm:"not null default '' comment('商品名称') VARCHAR(200) 'product_name'"`
	ProductType          int       `json:"product_type" xorm:"not null default 1 comment('商品类别（1-实物商品，2-虚拟商品，3-组合商品） 4-服务 5-活体 6-寄养') TINYINT 'product_type'"`
	CombineType          int       `json:"combine_type" xorm:"not null default 0 comment('组合商品组合类型0-非组合 1-实物实物 2-虚拟虚拟 3-实物虚拟') TINYINT 'combine_type'"`
	ChannelCategoryName  string    `json:"channel_category_name" xorm:"default '' comment('渠道分类名称') VARCHAR(100) 'channel_category_name'"`
	Image                string    `json:"image" xorm:"not null comment('商品图片') VARCHAR(5000) 'image'"`
	BarCode              string    `json:"bar_code" xorm:"not null default '' comment('商品编码') VARCHAR(50) 'bar_code'"`
	MarkingPrice         int       `json:"marking_price" xorm:"not null default 0 comment('商品原单价') INT 'marking_price'"`
	DiscountPrice        int       `json:"discount_price" xorm:"not null default 0 comment('商品优惠单价') INT 'discount_price'"`
	VipPrice             int       `json:"vip_price" xorm:"not null default 0 comment('付费会员价') INT 'vip_price'"`
	PayPrice             int       `json:"pay_price" xorm:"not null default 0 comment('商品均摊后实际支付单价') INT 'pay_price'"`
	Number               int       `json:"number" xorm:"not null default 0 comment('数量') INT 'number'"`
	Specs                string    `json:"specs" xorm:"not null default '' comment('规格') VARCHAR(100) 'specs'"`
	PaymentTotal         int       `json:"payment_total" xorm:"not null default 0 comment('sku实付总金额，discount_price*number') INT 'payment_total'"`
	SkuPayTotal          int       `json:"sku_pay_total" xorm:"not null default 0 comment('包含平台优惠的sku实付总金额，payment_total+privilege_pt') INT 'sku_pay_total'"`
	Privilege            int       `json:"privilege" xorm:"not null default 0 comment('商家优惠金额') INT 'privilege'"`
	PrivilegePt          int       `json:"privilege_pt" xorm:"not null default 0 comment('平台优惠金额') INT 'privilege_pt'"`
	PrivilegeTotal       int       `json:"privilege_total" xorm:"not null default 0 comment('总优惠金额') INT 'privilege_total'"`
	Freight              int       `json:"freight" xorm:"not null default 0 comment('sku分摊运费') INT 'freight'"`
	DeliverStatus        int       `json:"deliver_status" xorm:"not null default 0 comment('发货状态0未发货  1已发货') TINYINT 'deliver_status'"`
	DeliverNum           int       `json:"deliver_num" xorm:"not null default 0 comment('发货数量') INT 'deliver_num'"`
	RefundNum            int       `json:"refund_num" xorm:"not null default 0 comment('退货数量') INT 'refund_num'"`
	GroupItemNum         int       `json:"group_item_num" xorm:"not null default 0 comment('组合子商品在单份组合中的售卖数量') INT 'group_item_num'"`
	SubBizOrderId        string    `json:"sub_biz_order_id" xorm:"not null default '' comment('饿了么子订单ID') VARCHAR(200) 'sub_biz_order_id'"`
	PromotionId          int       `json:"promotion_id" xorm:"not null default 0 comment('限时折扣活动id') INT 'promotion_id'"`
	PromotionType        int       `json:"promotion_type" xorm:"not null default 1 comment('商品级别促销类型(1、无优惠;2、秒杀(已经下线);3、单品直降;4、限时抢购;1202、加价购;1203、满赠(标识商品);6、买赠(买A送B，标识B);9999、表示一个普通商品参与捆绑促销，设置的捆绑类型;9998、表示一个商品参与了捆绑促销，并且还参与了其他促销类型;9997、表示一个商品参与了捆绑促销，但是金额拆分不尽,9996:组合购,8001:商家会员价,8:第二件N折,9:拼团促销)') INT 'promotion_type'"`
	DelOrderId           string    `json:"del_order_id" xorm:"not null default '' comment('订单id') VARCHAR(50) 'del_order_id'"`
	DelId                string    `json:"del_id" xorm:"not null default '' comment('原主键，待删除') VARCHAR(50) 'del_id'"`
	CommissionRate       int       `json:"commission_rate" xorm:"not null default 0 comment('佣金比例，单位%') TINYINT 'commission_rate'"`
	IsDistribute         int       `json:"is_distribute" xorm:"not null default 0 comment('是否分销') TINYINT 'is_distribute'"`
	IsSettlement         int       `json:"is_settlement" xorm:"default 0 comment('是否生成佣金记录 1：是  0：否') TINYINT 'is_settlement'"`
	TermType             int       `json:"term_type" xorm:"not null default 0 comment('只有虚拟商品才有值(1.有效期至多少  2.有效期天数)') TINYINT 'term_type'"`
	TermValue            int       `json:"term_value" xorm:"not null default 0 comment('如果term_type=1 存：时间戳  如果term_type=2 存多少天') INT 'term_value'"`
	VirtualInvalidRefund int       `json:"virtual_invalid_refund" xorm:"not null default 0 comment('是否支持过期退款 1：是  0：否') TINYINT 'virtual_invalid_refund'"`
	MallOrderProductId   int       `json:"mall_order_product_id" xorm:"not null default 0 comment('商城订单商品主键id') INT 'mall_order_product_id'"`
	CreateTime           time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime           time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	WarehouseType        int       `json:"warehouse_type" xorm:"default 0 comment('药品仓类型：0:默认否, 1:巨星药品仓') INT 'warehouse_type'"`
	IsPrescribedDrug     int       `json:"is_prescribed_drug" xorm:"default 0 comment('是否处方药') TINYINT 'is_prescribed_drug'"`
	EmployeeId           string    `json:"employee_id" xorm:"default 'null' comment('员工ID') VARCHAR(100) 'employee_id'"`
	EmployeeName         string    `json:"employee_name" xorm:"default 'null' comment('员工姓名') VARCHAR(100) 'employee_name'"`
	BuyType              int       `json:"buy_type" xorm:"default 0 comment('购买类型：0 普通购买，1次卡，2赠品') INT 'buy_type'"`
	LocationCode         string    `json:"location_code" xorm:"default 'null' comment('库位码') VARCHAR(100) 'location_code'"`
	IsPicking            int       `json:"is_picking" xorm:"not null default 0 comment('是否拣货,0否1是') TINYINT 'is_picking'"`
	PickedNumber         int       `json:"picked_number" xorm:"not null default 0 comment('实际拣货数量') INT 'picked_number'"`
	//重量
	WeightForUnit float64 `json:"weight_for_unit" xorm:"default 'null' comment('重量') DOUBLE(8) 'weight_for_unit'"`
}

// 拓展OrderProduct 加个退款金额
type OrderProductExt struct {
	OrderProduct `xorm:"extends"`
	RefundAmount float64 `json:"refund_amount" xorm:"not null default 0 comment('退款金额') FLOAT 'refund_amount'"`
}

func (op *OrderProduct) TableName() string {
	return "dc_order.order_product"
}

// BatchCreate 批量创建订单商品
func (op *OrderProduct) BatchCreate(session *xorm.Session, products []*OrderProduct) error {
	if len(products) == 0 {
		return errors.New("订单商品信息不能为空")
	}

	// 先执行批量插入
	_, err := session.InsertMulti(products)
	if err != nil {
		return fmt.Errorf("插入订单商品表失败: %v", err)
	}

	//// 查询获取插入的记录
	//var subBizOrderIds []string
	//for _, product := range products {
	//	subBizOrderIds = append(subBizOrderIds, product.SubBizOrderId)
	//}
	//
	//var insertedProducts []OrderProduct
	//err = session.In("sub_biz_order_id", subBizOrderIds).Find(&insertedProducts)
	//if err != nil {
	//	return fmt.Errorf("查询插入的订单商品失败: %v", err)
	//}
	//
	//// 回写ID
	//productMap := make(map[string]int)
	//for _, product := range insertedProducts {
	//	productMap[product.SubBizOrderId] = product.Id
	//}
	//
	//for i := range products {
	//	if id, ok := productMap[products[i].SubBizOrderId]; ok {
	//		products[i].Id = id
	//	}
	//}

	return nil
}

// 获取订单的商品
type OrderProductRequest struct {
	OrderSn  string   `json:"order_sn"`  // 订单编号
	OrderSns []string `json:"order_sns"` // 订单编号列表
	Fields   string   `json:"fields"`    // 查询字段
	Ids      []int    `json:"ids"`       // 商品id列表
}

func (op *OrderProduct) GetOrderProduct(session *xorm.Session, req OrderProductRequest) (out []*OrderProduct, out1 map[string][]*OrderProduct, err error) {
	out = make([]*OrderProduct, 0)
	if req.Fields == "" {
		req.Fields = "*"
	}
	session = session.Table("dc_order.order_product")
	if req.OrderSn != "" {
		session = session.Where("order_sn = ?", req.OrderSn)
	}
	if len(req.OrderSns) > 0 {
		session = session.In("order_sn", req.OrderSns)
	}

	err = session.Find(&out)

	if err != nil {
		return nil, nil, err
	}

	out1 = make(map[string][]*OrderProduct)

	for _, product := range out {
		out1[product.OrderSn] = append(out1[product.OrderSn], product)
	}
	return out, out1, nil
}

// 获取实物商品，也就是需要计算库存的商品
func (op *OrderProduct) GetOrderProductPM(session *xorm.Session, req OrderProductRequest) (out []*OrderProduct, err error) {
	out = make([]*OrderProduct, 0)
	session = session.Table("dc_order.order_product")
	session = session.Where("order_sn = ? and product_type=1", req.OrderSn)

	err = session.Find(&out)

	if err != nil {
		return nil, err
	}

	return out, nil
}

// GetOrderProducts 获取订单商品信息
func (o *OrderProduct) GetOrderProducts(session *xorm.Session, req OrderProductRequest) ([]*OrderProductExt, error) {
	products := make([]*OrderProductExt, 0)

	// 先查询订单商品基础信息
	err := session.Table("dc_order.order_product").Alias("op").
		Select("op.*, COALESCE(r.total_refund_amount, 0) as refund_amount").
		Join("LEFT", "(SELECT rop.order_product_id, SUM(CAST(rop.refund_amount AS DECIMAL(10,2))) as total_refund_amount "+
			"FROM dc_order.refund_order r "+
			"LEFT JOIN dc_order.refund_order_product rop ON r.refund_sn = rop.refund_sn "+
			"WHERE r.refund_state = 3 AND r.order_sn = ? "+
			"GROUP BY rop.order_product_id) r", "op.id = r.order_product_id", req.OrderSn).
		Where("op.order_sn = ?", req.OrderSn).
		Find(&products)

	if err != nil {
		return nil, fmt.Errorf("查询订单商品失败: %v", err)
	}
	return products, nil
}
