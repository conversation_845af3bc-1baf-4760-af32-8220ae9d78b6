package order_po

import (
	"errors"
	"time"

	"xorm.io/xorm"
)

// *** 增加新的类型直接往后追加 ***//
// *** 顺序不可更改 ***//
const (
	OrderLogSubmitOrder                = iota + 1 //提交订单
	OrderLogPayedOrder                            //已支付
	OrderLogSplitedOrder                          //已拆单
	OrderLogAcceptedOrder                         //商家已接单
	OrderLogPushedOrder                           //已推送第三方
	OrderLogDelivering                            //美团配送调度中
	OrderLogCourierAcceptedOrder                  //美团骑手已接单
	OrderLogCourierTakedProduct                   //美团骑手已取货
	OrderLogCourierDeliveryed                     //美团骑手已送达
	OrderLogCompleted                             //已完成
	UserApplyForRefund                            //用户申请退款
	StoreApplyForRefund                           //商家申请退款
	CustomerServiceApplyForRefund                 //客服申请退款
	BDApplyForRefund                              //BD申请退款
	SystemApplyForRefund                          //系统申请退款
	OpenPlatformApplyForRefund                    //开放平台申请退款
	StorePassFinalExamination                     //商家终审通过
	RefundPaymentSuccess                          //退款支付成功
	OrderLogCourierArrived                        //美团骑手已到店
	OrderLogSavePerformance                       //分配业绩
	OrderLogPickedOrder                           //完成拣货
	OrderLogPushOrderFailed                       //推送第三方失败
	OrderLogCourierCanceled                       //美团骑手已取消
	OrderLogConfirmGoodsReturn                    //闪送骑手送回商品
	OrderLogSSDelivering                          //闪送配送调度中
	OrderLogSSCourierArrived                      //闪送骑手已到店
	OrderLogSSCourierAcceptedOrder                //闪送骑手已接单
	OrderLogSSCourierTakedProduct                 //闪送骑手已取货
	OrderLogSSCourierDeliveryed                   //闪送骑手已送达
	OrderLogSSCourierCanceled                     //闪送骑手已取消
	OrderLogBySelfDelivering                      //商家自配调度中
	OrderLogBySelfCourierArrived                  //商家自配骑手已到店
	OrderLogBySelfCourierAcceptedOrder            //商家自配骑手已接单
	OrderLogBySelfCourierTakedProduct             //商家自配骑手已取货
	OrderLogBySelfCourierDeliveryed               //商家自配骑手已送达
	OrderLogBySelfCourierCanceled                 //商家自配骑手已取消
	OrderLogreturning                             //物品返回中
	OrderLogreturned                              //物品返回完成
	OrderLogShopSure                              //商家点击了确认收货
)

var OrderLodMap = map[int]string{
	OrderLogSubmitOrder:                "提交订单",
	OrderLogPayedOrder:                 "已支付",
	OrderLogSplitedOrder:               "已拆单",
	OrderLogAcceptedOrder:              "商家已接单",
	OrderLogPushedOrder:                "已推送第三方",
	OrderLogDelivering:                 "配送调度中",
	OrderLogCourierAcceptedOrder:       "骑手已接单",
	OrderLogCourierTakedProduct:        "骑手已取货",
	OrderLogCourierDeliveryed:          "骑手已送达",
	OrderLogCompleted:                  "已完成",
	UserApplyForRefund:                 "用户申请退款",
	StoreApplyForRefund:                "商家申请退款",
	CustomerServiceApplyForRefund:      "客服申请退款",
	BDApplyForRefund:                   "BD申请退款",
	SystemApplyForRefund:               "系统申请退款",
	OpenPlatformApplyForRefund:         "开放平台申请退款",
	StorePassFinalExamination:          "商家终审通过",
	RefundPaymentSuccess:               "退款支付成功",
	OrderLogCourierArrived:             "骑手已到店",
	OrderLogSavePerformance:            "分配业绩",
	OrderLogPickedOrder:                "完成拣货",
	OrderLogPushOrderFailed:            "推送第三方失败",
	OrderLogCourierCanceled:            "骑手已取消",
	OrderLogConfirmGoodsReturn:         "闪送等待骑手送回商品",
	OrderLogSSDelivering:               "配送调度中",
	OrderLogSSCourierArrived:           "骑手已到店",
	OrderLogSSCourierAcceptedOrder:     "骑手已接单",
	OrderLogSSCourierTakedProduct:      "骑手已取货",
	OrderLogSSCourierDeliveryed:        "骑手已送达",
	OrderLogSSCourierCanceled:          "骑手已取消",
	OrderLogBySelfDelivering:           "送调度中",
	OrderLogBySelfCourierArrived:       "骑手已到店",
	OrderLogBySelfCourierAcceptedOrder: "骑手已接单",
	OrderLogBySelfCourierTakedProduct:  "骑手已取货",
	OrderLogBySelfCourierDeliveryed:    "骑手已送达",
	OrderLogBySelfCourierCanceled:      "骑手已取消",
	OrderLogreturning:                  "妥投异常物品返回中",
	OrderLogreturned:                   "妥投异常物品返回完成",
	OrderLogShopSure:                   "妥投异常物品商家确认",
}

type OrderLog struct {
	Id              int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	OrderSn         string    `xorm:"not null default '''' comment('订单编号') index VARCHAR(50)"`
	LogType         int       `xorm:"not null default 0 comment('日志类型，1已下单,2已支付,3已拆单,4商家已接单,5已推送第三方,6配送调度中,7骑手已接单,8骑手已取货,9骑手已送达,10已完成,11用户申请退款,12商家申请退款,13客服申请退款,14BD申请退款,15系统申请退款,16开放平台申请退款,17商家终审通过,18退款支付成功,19骑手已到店,20分配业绩,21完成拣货,22推送第三方失败,23骑手已取消') TINYINT(4)"`
	OperateTypeName string    `xorm:"not null default '''' comment('操作渠道名称') VARCHAR(30)"`
	OperateUser     string    `xorm:"not null default '''' comment('操作人') VARCHAR(30)"`
	CreateTime      time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime      time.Time `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
	Reason          string    `xorm:"default '''' comment('推送第三方失败原因') TEXT"`
}

func (ol *OrderLog) TableName() string {
	return "dc_order.order_log"
}

// BatchCreate 批量创建订单日志
func (ol *OrderLog) BatchCreate(session *xorm.Session, logs []*OrderLog) (err error) {
	if session == nil {
		return errors.New("session is nil")
	}
	if len(logs) == 0 {
		return nil
	}
	_, err = session.Insert(logs)
	return err
}
