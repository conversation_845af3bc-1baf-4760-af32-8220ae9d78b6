package order_po

import (
	"errors"

	"xorm.io/xorm"
)

// PromotionType 优惠活动类型定义
const (
	PromotionTypeAdjustPrice           = 1  // 改价
	PromotionTypeSpecialPrice          = 2  // 特价
	PromotionTypeStoredCard            = 3  // 储值卡
	PromotionTypeDiscount              = 4  // 满减
	PromotionTypeCoupon                = 5  // 优惠券
	PromotionTypeCard                  = 6  // 次卡
	PromotionTypeGift                  = 7  // 赠品
	PromotionTypeStoredCard2           = 8  // 储值卡
	PromotionTypeRoundOff              = 9  // 抹零
	PromotionTypeWholeOrderAdjustPrice = 10 // 整单改价
)

// OrderPromotionRecord 订单商品优惠明细
type OrderPromotionRecord struct {
	ID             string `json:"id" xorm:"pk not null default 'uuid()' VARCHAR(50) 'id'"`
	OrderSn        string `json:"order_sn" xorm:"not null default '' comment('订单号') VARCHAR(50) 'order_sn'"`
	OrderDcId      string `json:"order_dc_id" xorm:"not null default '' comment('订单商品表id') VARCHAR(50) 'order_dc_id'"`
	Sku            string `json:"sku" xorm:"not null default '' comment('sku') VARCHAR(50) 'sku'"`
	Promotion      int    `json:"promotion" xorm:"not null default 0 comment('总优惠金额(分)') INT 'promotion'"`
	SkuPromotion   int    `json:"sku_promotion" xorm:"not null default 0 comment('单SKU优惠金额(分)') INT 'sku_promotion'"`
	Num            int    `json:"num" xorm:"not null default 0 comment('数量') INT 'num'"`
	PromotionId    int    `json:"promotion_id" xorm:"not null default 0 comment('优惠活动id') INT 'promotion_id'"`
	PromotionType  int    `json:"promotion_type" xorm:"not null default 0 comment('优惠活动类型:1改价,2特价,3储值卡,4满减,5优惠券,6次卡,7赠品,8储值卡,9抹零,10整单改价') INT 'promotion_type'"`
	PromotionTitle string `json:"promotion_title" xorm:"not null default '' comment('活动名称') VARCHAR(256) 'promotion_title'"`
	CardNo         string `json:"card_no" xorm:"not null default '' comment('储值卡号,次卡号,优惠券编码') VARCHAR(100) 'card_no'"`
}

// TableName 表名
func (t OrderPromotionRecord) TableName() string {
	return "dc_order.order_promotion_record"
}

// BatchCreate 批量创建订单优惠明细
func (r *OrderPromotionRecord) BatchCreate(session *xorm.Session, records []*OrderPromotionRecord) (err error) {
	if session == nil {
		return errors.New("session is nil")
	}
	if len(records) == 0 {
		return nil
	}
	_, err = session.Insert(records)
	return err
}

type OrderPromotionRecordRequest struct {
	OrderSn  string   `json:"order_sn"`  // 订单编号
	OrderSns []string `json:"order_sns"` // 订单编号列表
	Fields   string   `json:"fields"`    // 查询字段
}

func (r *OrderPromotionRecord) GetOrderPromotionRecord(session *xorm.Session, req OrderPromotionRecordRequest) (out []*OrderPromotionRecord, out1 map[string][]*OrderPromotionRecord, out2 map[string][]*OrderPromotionRecord, err error) {
	out = make([]*OrderPromotionRecord, 0)

	if req.Fields == "" {
		req.Fields = "*"
	}
	session = session.Table("dc_order.order_promotion_record").Select(req.Fields)
	if req.OrderSn != "" {
		session = session.Where("order_sn = ?", req.OrderSn)
	}
	if len(req.OrderSns) > 0 {
		session = session.In("order_sn", req.OrderSns)
	}

	err = session.Find(&out)

	if err != nil {
		return nil, nil, nil, err
	}

	out1 = make(map[string][]*OrderPromotionRecord)
	out2 = make(map[string][]*OrderPromotionRecord)

	for _, record := range out {
		out1[record.OrderSn] = append(out1[record.OrderSn], record)
		out2[record.OrderDcId] = append(out2[record.OrderDcId], record)
	}
	return out, out1, out2, nil
}
