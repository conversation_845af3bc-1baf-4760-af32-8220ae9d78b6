package order_po

import (
	"errors"
	"fmt"
	"time"

	"eShop/infra/log"

	"xorm.io/xorm"
)

// PayTypeEnum 支付方式枚举 1:现金,2:余额,3:押金,4:标记收款,5:微信,6:支付宝,7:自有POS：8:挂账,9其它,10储值卡，11扫码支付 12次卡
const (
	PayTypeCash      = 1  // 现金
	PayTypeBalance   = 2  // 余额
	PayTypeDeposit   = 3  // 押金
	PayTypeMarkPaid  = 4  // 标记收款
	PayTypeWechat    = 5  // 微信
	PayTypeAlipay    = 6  // 支付宝
	PayTypePos       = 7  // 自有POS
	PayTypeCredit    = 8  // 挂账
	PayTypeOther     = 9  // 其他
	PayTypeStoreCard = 10 // 储值卡
	PayTypeScanPay   = 11 // 扫码支付
	PayTypeTimeCard  = 12 // 次卡
)

var PayTypeTextMap = map[int]string{
	PayTypeCash:      "现金",
	PayTypeBalance:   "余额",
	PayTypeDeposit:   "押金",
	PayTypeMarkPaid:  "标记收款",
	PayTypeWechat:    "自有微信",
	PayTypeAlipay:    "自有支付宝",
	PayTypePos:       "自有POS",
	PayTypeCredit:    "挂账",
	PayTypeOther:     "其他",
	PayTypeStoreCard: "储值卡",
	PayTypeScanPay:   "扫码支付",
	PayTypeTimeCard:  "次卡",
}

type OrderPayment struct {
	Id            int       `json:"id" xorm:"pk not null comment('主键') BIGINT 'id'"`
	TenantId      int64     `json:"tenant_id" xorm:"not null default 0 comment('店铺id') BIGINT 'tenant_id'"`
	OrderId       int       `json:"order_id" xorm:"not null default 0 comment('订单表id') BIGINT 'order_id'"`
	OrderSn       string    `json:"order_sn" xorm:"not null default 0 comment('订单号') VARCHAR(50) 'order_sn'"`
	PayType       int       `json:"pay_type" xorm:"default 0 comment('支付方式:1:现金,2:余额,3:押金,4:标记收款,5:微信,6:支付宝,7:自有POS，8：挂账，10：储值卡') INT 'pay_type'"`
	Amount        int       `json:"amount" xorm:"not null default 0 comment('支付金额') INT 'amount'"`
	RemainAmount  int       `json:"remain_amount" xorm:"not null default 0 comment('剩余可退金额') INT 'remain_amount'"`
	RetreatAmount int       `json:"retreat_amount" xorm:"not null default 0 comment('已退金额') INT 'retreat_amount'"`
	Change        int       `json:"change" xorm:"not null default 0 comment('找零金额') INT 'change'"`
	Pay           int       `json:"pay" xorm:"not null default 0 comment('实收金额') INT 'pay'"`
	PaymentStatus string    `json:"payment_status" xorm:"not null default '' comment('支付状态：FAIL:支付失败，SUCCESS:支付成功') VARCHAR(16) 'payment_status'"`
	TradeNo       string    `json:"trade_no" xorm:"not null default '' comment('支付交易号') VARCHAR(64) 'trade_no'"`
	CardNo        string    `json:"card_no" xorm:"not null default '' comment('储值卡号,次卡号,押金支付为0') VARCHAR(100) 'card_no'"`
	Remark        string    `json:"remark" xorm:"not null default '' comment('备注') VARCHAR(100) 'remark'"`
	IsDeleted     int       `json:"is_deleted" xorm:"not null default 0 comment('删除标志：0:未删除,1:已删除') TINYINT 'is_deleted'"`
	CreatedBy     int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime   time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedBy     int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime   time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
}

func (om OrderPayment) TableName() string {
	return "dc_order.order_payment"
}

// BatchCreate 插入订单支付数据
func (p *OrderPayment) BatchCreate(session *xorm.Session, OrderPayments []*OrderPayment) (err error) {
	if session == nil {
		return errors.New("session is nil")
	}
	if len(OrderPayments) == 0 {
		return nil
	}
	_, err = session.Insert(OrderPayments)
	return err
}

// 获取订单的支付列表
type OrderPaymentRequest struct {
	OrderSn  string   `json:"order_sn"`  // 订单编号
	OrderSns []string `json:"order_sns"` // 订单编号列表
	Fields   string   `json:"fields"`    // 查询字段
}

func (op *OrderPayment) GetOrderPayment(session *xorm.Session, req OrderPaymentRequest) (out []*OrderPayment, out1 map[string][]*OrderPayment, err error) {
	out = make([]*OrderPayment, 0)

	if req.Fields == "" {
		req.Fields = "*"
	}
	session = session.Table("dc_order.order_payment").Select(req.Fields)
	if req.OrderSn != "" {
		session = session.Where("order_sn = ?", req.OrderSn)
	}
	if len(req.OrderSns) > 0 {
		session = session.In("order_sn", req.OrderSns)
	}

	err = session.Find(&out)

	if err != nil {
		return nil, nil, err
	}

	out1 = make(map[string][]*OrderPayment)

	for _, payment := range out {
		out1[payment.OrderSn] = append(out1[payment.OrderSn], payment)
	}
	return out, out1, nil
}

// UpdateRefundAmount 更新退款金额
func (p *OrderPayment) UpdateRefundAmount(session *xorm.Session, Id, refundAmount int) error {
	// 1. 先查询当前支付记录
	payment := new(OrderPayment)
	exists, err := session.Where("id = ?", Id).Get(payment)
	if err != nil {
		log.Errorf("查询支付记录失败, err: %v", err)
		return err
	}
	if !exists {
		return fmt.Errorf("支付记录不存在")
	}

	// 2. 计算剩余可退金额和已退金额
	newRemainAmount := payment.RemainAmount - refundAmount   // 更新剩余可退金额
	newRetreatAmount := payment.RetreatAmount + refundAmount // 更新已退金额

	// 3. 检查金额是否合法
	if newRemainAmount < 0 {
		return fmt.Errorf("退款金额超过可退金额")
	}

	// 4. 更新退款相关金额
	_, err = session.Table(p.TableName()).
		Where("id = ?", Id).
		Update(map[string]interface{}{
			"remain_amount":  newRemainAmount,  // 剩余可退金额
			"retreat_amount": newRetreatAmount, // 已退金额
		})
	if err != nil {
		log.Errorf("更新支付记录退款金额失败, err: %v", err)
		return err
	}

	return nil
}

// GetPaymentTypeText 获取支付方式文本
func (p *OrderPayment) GetPaymentTypeText(payType int) string {
	if text, ok := PayTypeTextMap[payType]; ok {
		return text
	}
	return PayTypeTextMap[PayTypeOther]
}
