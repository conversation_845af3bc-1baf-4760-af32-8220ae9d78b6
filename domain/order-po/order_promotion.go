package order_po

import (
	"errors"
	"time"
	"xorm.io/xorm"
)

type OrderPromotion struct {
	Id          int    `json:"id" xorm:"pk autoincr not null comment('自增主键') INT 'id'"`
	OrderSn     string `json:"order_sn" xorm:"not null default '' comment('订单号码') VARCHAR(32) 'order_sn'"`
	ChannelId   int    `json:"channel_id" xorm:"not null default 0 comment('渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店') INT 'channel_id'"`
	PromotionId int    `json:"promotion_id" xorm:"not null default 0 comment('优惠活动id') INT 'promotion_id'"`
	// 活动类型： 1-满减 2-特价
	PromotionType  int       `json:"promotion_type" xorm:"not null default 0 comment('优惠活动类型') INT 'promotion_type'"`
	PromotionTitle string    `json:"promotion_title" xorm:"not null default '' comment('活动名称') VARCHAR(256) 'promotion_title'"`
	PoiCharge      int       `json:"poi_charge" xorm:"not null default 0 comment('商家优惠') INT 'poi_charge'"`
	PtCharge       int       `json:"pt_charge" xorm:"not null default 0 comment('平台优惠') INT 'pt_charge'"`
	PromotionFee   int       `json:"promotion_fee" xorm:"not null default 0 comment('总优惠金额(商家加平台总和)') INT 'promotion_fee'"`
	CreateTime     time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime     time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	DelId          string    `json:"del_id" xorm:"not null default 'uuid()' comment('原主键，待删除') VARCHAR(64) 'del_id'"`
	CardId         string    `json:"card_id" xorm:"default 'null' comment('卡或者券ID') VARCHAR(100) 'card_id'"`
}

func (op *OrderPromotion) TableName() string {
	return "dc_order.order_promotion"
}

// BatchCreate 批量创建订单优惠
func (p *OrderPromotion) BatchCreate(session *xorm.Session, promotions []*OrderPromotion) (err error) {
	if session == nil {
		return errors.New("session is nil")
	}
	if len(promotions) == 0 {
		return nil
	}
	_, err = session.Insert(promotions)
	return err
}

type OrderPromotionRequest struct {
	OrderSn  string   `json:"order_sn"`  // 订单编号
	OrderSns []string `json:"order_sns"` // 订单编号列表
	Fields   string   `json:"fields"`    // 查询字段
}

func (op *OrderPromotion) GetOrderPromotion(session *xorm.Session, req OrderPromotionRequest) (out []*OrderPromotion, out1 map[string][]*OrderPromotion, err error) {
	out = make([]*OrderPromotion, 0)

	if req.Fields == "" {
		req.Fields = "*"
	}
	session = session.Table("dc_order.order_promotion").Select(req.Fields)
	if req.OrderSn != "" {
		session = session.Where("order_sn = ?", req.OrderSn)
	}
	if len(req.OrderSns) > 0 {
		session = session.In("order_sn", req.OrderSns)
	}

	err = session.Find(&out)

	if err != nil {
		return nil, nil, err
	}

	out1 = make(map[string][]*OrderPromotion)

	for _, promotion := range out {
		out1[promotion.OrderSn] = append(out1[promotion.OrderSn], promotion)
	}
	return out, out1, nil
}
