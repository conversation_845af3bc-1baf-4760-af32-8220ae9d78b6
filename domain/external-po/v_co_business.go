package external_po

import (
	"errors"

	"xorm.io/xorm"
)

type VCoBusiness struct {
	Name            string `xorm:"name" json:"name"`
	SocialReditCode string `xorm:"social_redit_code" json:"social_redit_code"`
}

func (v *VCoBusiness) TableName() string {
	return "dm_mdm.v_co_business"
}

// 根据社会信用代码查询公司工商信息
func (v *VCoBusiness) GetBySocialReditCode(session *xorm.Engine, socialReditCode string) (inSystem int8, err error) {
	if session == nil {
		err = errors.New("session不能为nil")
		return
	}
	if socialReditCode == "" {
		err = errors.New("socialReditCode不能为空")
		return
	}
	has, err := session.Table(v.TableName()).Select("name,social_redit_ode as social_redit_code").Where("social_redit_ode = ?", socialReditCode).Get(v)
	if err != nil {
		return 0, err
	}

	if has {
		inSystem = 1
	}
	return inSystem, nil
}
