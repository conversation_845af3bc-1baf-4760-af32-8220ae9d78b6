package offline

import "time"

type CustomerInfo struct {
	Id          int64     `gorm:"primaryKey;autoIncrement" json:"id"`                                       // 主键，自增
	ChainId     string    `gorm:"default:0;not null" json:"chain_id"`                                       // 连锁id
	TenantId    string    `gorm:"default:0;not null" json:"tenant_id"`                                      // 首次注册店铺Id--只是一个属性
	UnionId     string    `gorm:"default:'';size:64" json:"union_id"`                                       // 小程序Id
	WxOpenId    string    `gorm:"default:'';not null;size:64" json:"wx_open_id"`                            // 微信openId
	Name        string    `gorm:"default:'';not null;size:64" json:"name"`                                  // 姓名
	NiceName    string    `gorm:"default:'';not null;size:64" json:"nice_name"`                             // 客户昵称
	Phone       string    `gorm:"default:'';not null;size:20" json:"phone"`                                 // 手机号
	Gender      int8      `gorm:"default:1;not null" json:"gender"`                                         // 性别 默认1男 2女
	Birthday    time.Time `gorm:"default:'0001-01-01';not null" json:"birthday"`                            // 出生日期
	IsCredit    int8      `gorm:"default:0;not null" json:"is_credit"`                                      // 是否允许挂账 0不允许挂账，1允许挂账
	LevelId     int64     `gorm:"default:1;not null" json:"level_id"`                                       // 会员等级id
	LevelName   string    `gorm:"default:'';not null;size:20" json:"level_name"`                            // 会员等级名称
	Integral    float64   `gorm:"default:0.0;not null;precision:18,1" json:"integral"`                      // 积分
	InnerFrom   string    `gorm:"default:'';not null;size:64" json:"inner_from"`                            // 内部
	Source      string    `gorm:"default:'0';not null;size:20" json:"source"`                               // 注册来源
	ImgUrl      string    `gorm:"default:'';not null;size:255" json:"img_url"`                              // 头像
	Remark      string    `gorm:"size:256" json:"remark"`                                                   // 备注
	Tags        string    `gorm:"default:'';not null;size:200" json:"tags"`                                 // 标签
	IsDeleted   bool      `gorm:"default:false;not null" json:"is_deleted"`                                 // 删除标识 0未删除，1已删除
	IsStop      bool      `gorm:"default:false;not null" json:"is_stop"`                                    // 是否停用 0启用1启用
	CreatedBy   int64     `gorm:"default:0;not null" json:"created_by"`                                     // 创建人
	CreatedTime time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_time"`                            // 创建时间
	UpdatedBy   int64     `gorm:"default:0;not null" json:"updated_by"`                                     // 更新人
	UpdatedTime time.Time `gorm:"default:CURRENT_TIMESTAMP;onUpdate:CURRENT_TIMESTAMP" json:"updated_time"` // 更新时间
}

func (t CustomerInfo) TableName() string {
	return "eshop_saas.c_customer_info"
}
