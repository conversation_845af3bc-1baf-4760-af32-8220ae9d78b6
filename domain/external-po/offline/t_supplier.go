package offline

import "xorm.io/xorm"

type TSupplier struct {
	Id             int64   `json:"id" xorm:"pk not null comment('主键') BIGINT 'id'"`
	ChainId        int64   `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	SupplierCode   string  `json:"supplier_code" xorm:"not null default '' comment('供应商编码') VARCHAR(64) 'supplier_code'"`
	SupplierName   string  `json:"supplier_name" xorm:"default '' comment('供应商名称') VARCHAR(64) 'supplier_name'"`
	Address        string  `json:"address" xorm:"default '' comment('供应商地址') VARCHAR(200) 'address'"`
	SalesmanName   string  `json:"salesman_name" xorm:"default '' comment('销售联系人') VARCHAR(100) 'salesman_name'"`
	SalesmanMobile string  `json:"salesman_mobile" xorm:"default '' comment('销售手机') VARCHAR(100) 'salesman_mobile'"`
	SalesmanPhone  string  `json:"salesman_phone" xorm:"default '' comment('销售座机') VARCHAR(100) 'salesman_phone'"`
	SalesmanMail   string  `json:"salesman_mail" xorm:"default '' comment('销售邮箱') VARCHAR(100) 'salesman_mail'"`
	SalesmanWeixin string  `json:"salesman_weixin" xorm:"default '' comment('销售微信') VARCHAR(100) 'salesman_weixin'"`
	LicenseFile    string  `json:"license_file" xorm:"default '' comment('资质文件') VARCHAR(500) 'license_file'"`
	LicenseImg     string  `json:"license_img" xorm:"default '' comment('资质图片,逗号分隔,最多5张') VARCHAR(1000) 'license_img'"`
	Remark         string  `json:"remark" xorm:"default '' comment('备注') VARCHAR(500) 'remark'"`
	FinanceName    string  `json:"finance_name" xorm:"default '' comment('财务-账户名称') VARCHAR(100) 'finance_name'"`
	FinanceAccount string  `json:"finance_account" xorm:"default '' comment('银行账号') VARCHAR(100) 'finance_account'"`
	FinanceBank    string  `json:"finance_bank" xorm:"default '' comment('开户银行') VARCHAR(100) 'finance_bank'"`
	FinanceAdress  string  `json:"finance_adress" xorm:"default '' comment('开户地区') VARCHAR(100) 'finance_adress'"`
	FinanceSubBank string  `json:"finance_sub_bank" xorm:"default '' comment('支行名称') VARCHAR(100) 'finance_sub_bank'"`
	FinanceCompany string  `json:"finance_company" xorm:"default '' comment('企业名称') VARCHAR(100) 'finance_company'"`
	IsForbidden    []uint8 `json:"is_forbidden" xorm:"not null default 0 comment('是否禁用，1是，0否') BIT(1) 'is_forbidden'"`
	IsDeleted      []uint8 `json:"is_deleted" xorm:"not null default 0 comment('是否删除，1是，0否') BIT(1) 'is_deleted'"`
	CreatedBy      int64   `json:"created_by" xorm:"not null default 0 comment('创建人ID') BIGINT 'created_by'"`
	CreatedTime    string  `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time'"`
	UpdatedBy      int64   `json:"updated_by" xorm:"not null default 0 comment('更新人ID') BIGINT 'updated_by'"`
	UpdatedTime    string  `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time'"`
}

func GetSupplierId(db *xorm.Engine, where map[string]interface{}) (out map[string]int64, err error) {
	session := db.NewSession()
	defer session.Close()

	chainId, ok := where["chainId"]
	if ok {
		session = session.Where("chain_id=?", chainId)
	}

	supplierNames, ok := where["supplierNames"]
	if ok {
		session = session.In("supplier_name", supplierNames)
	}

	out = make(map[string]int64, 0)
	data := make([]TSupplier, 0)
	if err = session.Table("eshop_saas.t_supplier").Alias("a").Where("a.is_deleted=0").Find(&data); err != nil {
		return
	}
	for _, v := range data {
		out[v.SupplierName] = v.Id
	}

	return

}
