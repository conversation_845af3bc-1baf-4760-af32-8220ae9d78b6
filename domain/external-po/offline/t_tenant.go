package offline

import (
	"time"

	"xorm.io/xorm"
)

// TTenant 店铺表
type TTenant struct {
	Id             int64     `xorm:"pk 'id' comment('主键ID')"`
	ChainId        int64     `xorm:"'chain_id' notnull default(0) comment('所属连锁ID')"`
	Name           string    `xorm:"'name' notnull default('') comment('店铺名称')"`
	Type           int       `xorm:"'type' notnull default(1) comment('店铺类型：1-直营点、2-加盟店、3-其他')"`
	BusinessType   string    `xorm:"'business_type' notnull default('') comment('经营类型')"`
	RegisterPhone  string    `xorm:"'register_phone' comment('注册手机号')"`
	ContactPerson  string    `xorm:"'contact_person' comment('联系人')"`
	ContactPhone   string    `xorm:"'contact_phone' comment('联系方式')"`
	Recommender    string    `xorm:"'recommender' default('') comment('推荐人')"`
	ProvinceId     int64     `xorm:"'province_id' comment('省')"`
	ProvinceName   string    `xorm:"'province_name' comment('省')"`
	CityId         int64     `xorm:"'city_id' comment('市')"`
	DistrictName   string    `xorm:"'district_name' comment('区')"`
	CityName       string    `xorm:"'city_name' comment('市')"`
	DistrictId     int64     `xorm:"'district_id' comment('区')"`
	Address        string    `xorm:"'address' comment('详细地址')"`
	Longitude      float64   `xorm:"'longitude' default(0.0000000) comment('经度')"`
	Latitude       float64   `xorm:"'latitude' default(0.0000000) comment('纬度')"`
	LogoUrl        string    `xorm:"'logo_url' default('') comment('店铺logo地址')"`
	QrcodeUrl      string    `xorm:"'qrcode_url' default('') comment('店铺二维码地址')"`
	RegisterType   string    `xorm:"'register_type' default('CREATE') comment('类型;CREATE:创建;REGISTER:注册')"`
	RegisterSource string    `xorm:"'register_source' notnull default('PC') comment('注册来源：PC-PC端，APPLET-小程序')"`
	State          bool      `xorm:"'state' default(1) comment('状态;0-禁用 1-启用')"`
	Status         string    `xorm:"'status' notnull default('NORMAL') comment('审核状态;[NORMAL:正常;WAIT_INIT:待初始化;FORBIDDEN:禁用;WAITING:待审核;REFUSE:拒绝]')"`
	ReviewComments string    `xorm:"'review_comments' default('') comment('审核意见')"`
	ExpirationTime time.Time `xorm:"'expiration_time' comment('有效期;为空表示永久')"`
	IsDeleted      bool      `xorm:"'is_deleted' default(0) comment('删除状态;0-未删除 1-已删除')"`
	CreatedTime    time.Time `xorm:"'created_time' comment('创建时间')"`
	CreatedBy      int64     `xorm:"'created_by' comment('创建人')"`
	UpdatedTime    time.Time `xorm:"'updated_time' comment('修改时间')"`
	UpdatedBy      int64     `xorm:"'updated_by' comment('修改人')"`
	HasGuide       bool      `xorm:"'has_guide' default(0) comment('是否经过新手引导 0-未经过 1-经过')"`
	IsReferral     bool      `xorm:"'is_referral' default(0) comment('是否维护转诊')"`
	DeleteRemark   string    `xorm:"'delete_remark' default('') comment('删除备注')"`
	Area           float64   `xorm:"'area' decimal(18,4) comment('店铺经营面积')"`
	AreaUpdateTime time.Time `xorm:"'area_update_time' comment('店铺经营面积修改时间')"`
	IsMain         bool      `xorm:"'is_main' notnull default(0) comment('是否为主店铺：0-否，1-是')"`
}

// Type 店铺类型
type Type int

const (
	TypeDirect Type = 1 // 直营点
	TypeJoin   Type = 2 // 加盟店
	TypeOther  Type = 3 // 其他
)

// Status 审核状态
type Status string

const (
	StatusNormal    Status = "NORMAL"    // 正常
	StatusWaitInit  Status = "WAIT_INIT" // 待初始化
	StatusForbidden Status = "FORBIDDEN" // 禁用
	StatusWaiting   Status = "WAITING"   // 待审核
	StatusRefuse    Status = "REFUSE"    // 拒绝
)

// RegisterType 注册类型
type RegisterType string

const (
	RegisterTypeCreate   RegisterType = "CREATE"   // 创建
	RegisterTypeRegister RegisterType = "REGISTER" // 注册
)

// RegisterSource 注册来源
type RegisterSource string

const (
	RegisterSourcePC     RegisterSource = "PC"     // PC端
	RegisterSourceApplet RegisterSource = "APPLET" // 小程序
)

func (t TTenant) TableName() string {
	return "eshop_saas.t_tenant"
}

// GetChainStores 获取指定连锁下未删除的店铺列表
func (t TTenant) GetChainStores(engine *xorm.Session, chainId int64) ([]TTenant, error) {
	stores := make([]TTenant, 0)
	err := engine.Where("chain_id = ?", chainId).
		Where("is_deleted = 0").
		Find(&stores)
	if err != nil {
		return nil, err
	}
	return stores, nil
}
