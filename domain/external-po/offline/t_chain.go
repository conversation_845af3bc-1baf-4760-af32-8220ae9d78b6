package offline

import "time"

type T<PERSON><PERSON>n struct {
	Id             int64     `json:"id" xorm:"pk autoincr not null BIGINT 'id'"`          // 主键ID
	Name           string    `json:"name" xorm:"not null default '' VARCHAR(255) 'name'"` // 连锁名称
	<PERSON><PERSON>erson  string    `json:"contact_person" xorm:"VARCHAR(255) 'contact_person'"` // 联系人
	ContactPhone   string    `json:"contact_phone" xorm:"VARCHAR(255) 'contact_phone'"`   // 联系方式
	State          bool      `json:"state" xorm:"default 1 BIT(1) 'state'"`               // 状态;0-禁用 1-启用
	ExpirationTime time.Time `json:"expiration_time" xorm:"DATETIME 'expiration_time'"`   // 有效期;为空表示永久
	IsDeleted      bool      `json:"is_deleted" xorm:"default 0 BIT(1) 'is_deleted'"`     // 删除状态;0-未删除 1-已删除
	Icon           string    `json:"icon" xorm:"VARCHAR(1000) 'icon'"`                    // 连锁图标
	CorpId         int       `json:"corp_id" xorm:"not null default 6 INT 'corp_id'"`     // 主体id
	CreatedTime    time.Time `json:"created_time" xorm:"DATETIME 'created_time'"`         // 创建时间
	CreatedBy      int64     `json:"created_by" xorm:"BIGINT 'created_by'"`               // 创建人
	UpdatedTime    time.Time `json:"updated_time" xorm:"DATETIME 'updated_time'"`         // 修改时间
	UpdatedBy      int64     `json:"updated_by" xorm:"BIGINT 'updated_by'"`               // 修改人
}

func (t TChain) TableName() string {
	return "eshop_saas.t_chain"
}
