package offline

import (
	"errors"
	"time"

	"xorm.io/xorm"
)

const (
	OperationTypeIndependent = 1 // 独立运营
	OperationTypeAuthorized  = 2 // 代运营
)

type TTenantRetailCfg struct {
	Id                int       `json:"id" xorm:"pk not null comment('主键ID') BIGINT 'id'"`
	ChainId           int64     `json:"chain_id" xorm:"not null comment('店铺所属连锁ID') BIGINT 'chain_id'"`
	TenantId          int64     `json:"tenant_id" xorm:"not null comment('店铺ID') BIGINT 'tenant_id'"`
	OperationType     int       `json:"operation_type" xorm:"not null default 1 comment('运营模式：1-独立运营，2-代运营模式') TINYINT 'operation_type'"`
	PurchaseType      string    `json:"purchase_type" xorm:"not null default 1 comment('采购模式：1-连锁集采，2-店铺自采') VARCHAR(10) 'purchase_type'"`
	AuthorizedChainId int64     `json:"authorized_chain_id" xorm:"not null default 0 comment('授权连锁ID（店铺选择分仓运营时，关联的授权连锁ID）') BIGINT 'authorized_chain_id'"`
	SettlementUnit    string    `json:"settlement_unit" xorm:"not null default '' comment('结算单位') VARCHAR(100) 'settlement_unit'"`
	SettlementType    int       `json:"settlement_type" xorm:"default 0 comment('结算类型：1-先货后款，2-先款后货，3-账期，4-其他') TINYINT 'settlement_type'"`
	SettlementRemark  string    `json:"settlement_remark" xorm:"not null default '' comment('结算备注') VARCHAR(500) 'settlement_remark'"`
	CreatedBy         int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime       time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time'"`
	UpdatedBy         int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime       time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time'"`
}

// 查询条件结构体
type RetailCfgQuery struct {
	TenantId          int64   // 店铺ID
	TenantIds         []int64 // 店铺ID
	ChainId           int64   // 连锁ID
	AuthorizedChainId int64   // 授权连锁ID
	OutType           int     // 输出类型 1-店铺授权信息
}

func (t *TTenantRetailCfg) QueryByTenantId(db interface{}, query RetailCfgQuery) (data []TTenantRetailCfg, out1 map[int64]TTenantRetailCfg, err error) {

	var session *xorm.Session
	// 根据传入类型获取session
	switch v := db.(type) {
	case *xorm.Engine:
		session = v.NewSession()
		defer session.Close()
	case *xorm.Session:
		session = v
	default:
		return nil, nil, errors.New("db参数类型错误,必须是 *xorm.Engine 或 *xorm.Session")
	}

	data = make([]TTenantRetailCfg, 0)
	out1 = make(map[int64]TTenantRetailCfg, 0)
	session = session.Table("eshop_saas.t_tenant_retail_cfg")
	if len(query.TenantIds) > 0 {
		session = session.In("tenant_id", query.TenantIds)
	}
	if query.TenantId > 0 {
		session = session.Where("tenant_id = ?", query.TenantId)
	}
	if query.ChainId > 0 {
		session = session.Where("chain_id = ?", query.ChainId)
	}

	if query.AuthorizedChainId > 0 {
		session = session.Where("authorized_chain_id = ?", query.AuthorizedChainId)
	}

	err = session.Find(&data)
	if err != nil {
		return nil, nil, err
	}
	for _, v := range data {
		switch query.OutType {
		case 1:
			out1[v.TenantId] = v
		}
	}

	return data, out1, nil
}
