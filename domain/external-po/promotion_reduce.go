package external_po

import (
	product_vo "eShop/view-model/product-vo"
)

const (
	PromotionTypeReachReduce    = 1
	PromotionTypeTimeDiscount   = 2
	PromotionTypeReduceDelivery = 3
)

type PromotionReduce struct {
	Id          int64  `json:"Id" xorm:"pk autoincr not null comment('主键') INT 'Id'"`
	PromotionId int64  `json:"PromotionId" xorm:"not null default 0 INT 'PromotionId'"`
	UUID        string `json:"UUID" xorm:"not null default '' comment('关联promotion') CHAR(36) 'UUID'"`
	ReachMoney  int64  `json:"ReachMoney" xorm:"not null comment('满足金额,以分为') INT 'ReachMoney'"`
	ReduceMoney int64  `json:"ReduceMoney" xorm:"not null comment('减免金额,以分为单位') INT 'ReduceMoney'"`
}

func (model *PromotionReduce) TableName() string {
	return "datacenter.promotion_reduce"
}

func (model *PromotionReduce) ToPromotionReduceDto(promotionId int64) *product_vo.PromotionReduceDto {
	var dto = new(product_vo.PromotionReduceDto)
	dto.PromotionId = int32(promotionId)
	dto.ReachMoney = float64(model.ReachMoney) / 100
	dto.ReduceMoney = float64(model.ReduceMoney) / 100
	return dto
}

func (model *PromotionReduce) ToPromotionCalcDto(promotionId int64) *product_vo.PromotionCalcDto {
	var calc = new(product_vo.PromotionCalcDto)
	calc.PromotionType = 1
	calc.PromotionTitle = "满减优惠"
	calc.PromotionId = int32(promotionId)
	return calc
}
