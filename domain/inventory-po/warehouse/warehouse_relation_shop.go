package warehouse

import (
	"errors"
	"time"
	"xorm.io/xorm"
)

type WarehouseRelationShop struct {
	Id            int       `json:"id" xorm:"pk autoincr not null comment('自增id') INT 'id'"`
	ShopName      string    `json:"shop_name" xorm:"default 'null' comment('门店名称') VARCHAR(255) 'shop_name'"`
	ShopId        string    `json:"shop_id" xorm:"not null comment('门店id') VARCHAR(255) 'shop_id'"`
	WarehouseName string    `json:"warehouse_name" xorm:"default 'null' comment('仓库名称') VARCHAR(255) 'warehouse_name'"`
	WarehouseId   int       `json:"warehouse_id" xorm:"not null comment('仓库id') INT 'warehouse_id'"`
	CreateTime    time.Time `json:"create_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	ChannelId     int       `json:"channel_id" xorm:"not null comment('1、阿闻渠道-外卖，2、美团，3、饿了么，4、京东到家，9、互联网医院，10、阿闻渠道-自提') INT 'channel_id'"`
}

func (w *WarehouseRelationShop) TableName() string {
	return "dc_dispatch.warehouse_relation_shop"
}

// GetWarehouseByShopAndChannel 根据门店ID和渠道ID查询仓库关系
func (w *WarehouseRelationShop) GetWarehouseByShopAndChannel(session *xorm.Session, shopId string, channelId int) (*WarehouseRelationShop, error) {
	var relation WarehouseRelationShop
	exists, err := session.Where("shop_id = ? AND channel_id = ?", shopId, channelId).Get(&relation)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.New("店铺仓库绑定关系不存在")
	}
	return &relation, nil
}
