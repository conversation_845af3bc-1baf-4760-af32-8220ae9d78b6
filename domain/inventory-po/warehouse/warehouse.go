package warehouse

import (
	"context"
	jwt "eShop/infra/jwtauth"
	vo "eShop/view-model/inventory-vo/warehouse"
	"errors"
	"time"

	"xorm.io/xorm"
)

// Warehouse 仓库实体
type Warehouse struct {
	Id         int       `json:"id" xorm:"pk autoincr 'id'"`              // 主键Id
	ThirdId    string    `json:"third_id" xorm:"'thirdid'"`               // 第三方仓库Id
	Code       string    `json:"code" xorm:"'code'"`                      // 仓库编号
	Name       string    `json:"name" xorm:"'name'"`                      // 仓库名称
	ComeFrom   int       `json:"come_from" xorm:"'comefrom'"`             // 仓库归属
	Level      int       `json:"level" xorm:"'level'"`                    // 仓库等级
	Category   int       `json:"category" xorm:"'category'"`              // 仓库类型
	Address    string    `json:"address" xorm:"'address'"`                // 仓库地址
	Contacts   string    `json:"contacts" xorm:"'contacts'"`              // 仓库联系人
	Tel        string    `json:"tel" xorm:"'tel'"`                        // 仓库联系方式
	Status     int       `json:"status" xorm:"'status'"`                  // 仓库状态
	CreateDate time.Time `json:"create_date" xorm:"created 'createdate'"` // 创建时间
	LastDate   time.Time `json:"last_date" xorm:"updated 'lastdate'"`     // 最后更新时间
	Subsystem  int       `json:"subsystem" xorm:"'subsystem'"`            // 所属系统
	Ratio      int       `json:"ratio" xorm:"'ratio'"`                    // 仓库比例
	Lng        int       `json:"lng" xorm:"'lng'"`                        // 仓库经度
	Lat        int       `json:"lat" xorm:"'lat'"`                        // 仓库纬度
	Region     string    `json:"region" xorm:"'region'"`                  // 所属区域
	City       string    `json:"city" xorm:"'city'"`                      // 所属城市
	ChainId    int64     `json:"chain_id" xorm:"'chain_id'"`              // 连锁Id
	OrgId      int       `json:"org_id" xorm:"'org_id'"`                  // 主体Id
}

// TableName 表名
func (w *Warehouse) TableName() string {
	return "dc_dispatch.warehouse"
}

// Create 创建仓库
func (w *Warehouse) Create(ctx context.Context, session *xorm.Session) error {
	_, err := session.Insert(w)
	return err
}

// Update 更新仓库
func (w *Warehouse) Update(ctx context.Context, session *xorm.Session) error {
	_, err := session.ID(w.Id).Update(w)
	return err
}

// Delete 删除仓库（软删除）
//func (w *Warehouse) Delete(ctx context.Context, session *xorm.Session) error {
//	w.IsDeleted = true
//	return w.Update(ctx, session)
//}

// GetByID 根据ID获取仓库
func (w *Warehouse) GetByID(ctx context.Context, session *xorm.Session, id int) (vo.WarehouseVO, error) {
	var warehouse vo.WarehouseVO

	query := session.Table("dc_dispatch.warehouse").Alias("w").
		Join("LEFT", "dc_dispatch.warehouse_relation_shop wrs", "w.id=wrs.warehouse_id").
		Join("LEFT", "eshop_saas.t_tenant t", "CAST(wrs.shop_id AS SIGNED)=t.id").
		Join("LEFT", "eshop_saas.t_tenant_retail_cfg trc", "t.id=trc.tenant_id").
		Join("LEFT", "eshop_saas.t_chain c", "trc.authorized_chain_id=c.id").
		Select(`
			w.id,
			w.chain_id,
			t.id AS store_id,
			t.name AS store_name,
			trc.operation_type,
			w.name AS warehouse_name,
			w.category,
			w.address,
			w.lng AS longitude,
			w.lat AS latitude,
			trc.authorized_chain_id,
			c.name as authorized_chain_name
		`).
		Where("w.id = ?", id)

	_, err := query.GroupBy("w.id").Get(&warehouse)
	if err != nil {
		return warehouse, err
	}

	return warehouse, nil
}

// List 查询仓库列表
func (w *Warehouse) GetByStoreId(ctx context.Context, session *xorm.Session, storeId string) ([]vo.WarehouseVO, error) {
	var warehouses []vo.WarehouseVO

	chainId := jwt.CtxGet[int64](ctx, "ChainId")
	sourceChainId := jwt.CtxGet[int64](ctx, "SourceChainId")

	query := session.Table("dc_dispatch.warehouse").Alias("w").
		Join("LEFT", "dc_dispatch.warehouse_relation_shop wrs", "w.id=wrs.warehouse_id").
		Join("LEFT", "eshop_saas.t_tenant t", "CAST(wrs.shop_id AS SIGNED)=t.id").
		Join("LEFT", "eshop_saas.t_tenant_retail_cfg trc", "t.id=trc.tenant_id").
		Join("LEFT", "eshop_saas.t_chain c", "trc.authorized_chain_id=c.id").
		Select(`
			w.id,
			w.chain_id,
			t.id AS store_id,
			t.name AS store_name,
			trc.operation_type,
			w.name AS warehouse_name,
			w.category,
			w.address,
			w.lng AS longitude,
			w.lat AS latitude,
			trc.authorized_chain_id,
			c.name as authorized_chain_name,
wrs.channel_id
		`).
		Where("wrs.shop_id = ?", storeId).
		And("w.status = ?", 1)

	if sourceChainId > 0 && sourceChainId != chainId {
		query = query.And("w.category = ?", CategoryFranchise)
	}

	err := query.Find(&warehouses)

	return warehouses, err
}

// GetWarehouseIdBySkuId 根据渠道id获取仓库id
func (w *Warehouse) GetWarehouseIdBySkuId(ctx context.Context, session *xorm.Session, storeId string, skuId int, channelId int) (int, error) {
	var warehouseId int
	_, err := session.Table("pro_product_store_info").Alias("ppsi").
		Join("LEFT", "dc_dispatch.warehouse_relation_shop wrs", "wrs.shop_id=ppsi.store_id AND wrs.channel_id=ppsi.channel_id").
		Select("wrs.warehouse_id").
		Where("ppsi.store_id = ? AND ppsi.sku_id = ? AND wrs.channel_id = ?", storeId, skuId, channelId).
		Limit(1).
		Get(&warehouseId)
	return warehouseId, err
}

// GetByStoreIdAndSkuId 根据店铺id和skuId查询仓库信息
func (w *Warehouse) GetByStoreIdAndSkuId(ctx context.Context, session *xorm.Session, storeId string, skuId int) (Warehouse, error) {
	var warehouse Warehouse
	_, err := session.Context(ctx).Table("dc_dispatch.warehouse").Alias("w").
		Join("LEFT", "dc_dispatch.warehouse_relation_shop wrs", "wrs.warehouse_id=w.id").
		Join("LEFT", "eshop.pro_product_store_info ppsi", "ppsi.store_id=wrs.shop_id AND ppsi.channel_id=wrs.channel_id").
		Select("w.*").
		Where("wrs.shop_id=? AND ppsi.sku_id=?", storeId, skuId).
		GroupBy("w.id").
		Get(&warehouse)
	return warehouse, err
}

// 仓库类型常量
const (
	TypeMain     = 1 // 主仓库
	TypeVirtual  = 2 // 虚拟仓库
	TypePhysical = 3 // 实体仓库

	// 3-门店仓 4-加盟仓
	CategoryStore     = 3
	CategoryFranchise = 4
)

// 错误定义
var (
	ErrWarehouseNotFound = errors.New("warehouse not found")
)
