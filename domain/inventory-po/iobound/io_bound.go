package iobound

import (
	"context"
	"eShop/infra/errors"
	vo "eShop/view-model/inventory-vo/iobound"
	"time"

	"xorm.io/xorm"
)

const (
	// 出库/入库类型：1-入库，2-出库
	BoundType_In  = 1
	BoundType_Out = 2

	// 出入库关联的单据类型，1.无，2.采购单，3.订单，4.盘点单，5.其他
	ItemRefType_None      = 1
	ItemRefType_Purchase  = 2
	ItemRefType_Order     = 3
	ItemRefType_Inventory = 4
	ItemRefType_Other     = 5

	// 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 11. 初始化入库, 12. 自用出库
	BoundItemType_PurchaseIn       = 1  // 采购入库
	BoundItemType_PurchaseOut      = 2  // 采购退货出库
	BoundItemType_UnFreeze         = 3  // 取消锁定库存
	BoundItemType_Freeze           = 4  // 锁定库存
	BoundItemType_RefundIn         = 5  // 销售退货入库
	BoundItemType_OrderOut         = 6  // 销售出库
	BoundItemType_InventoryPlusIn  = 7  // 盘盈入库
	BoundItemType_InventoryLossOut = 8  // 盘亏出库
	BoundItemType_OtherIn          = 9  // 其它入库
	BoundItemType_OtherOut         = 10 // 其它出库
	BoundItemType_InitOut          = 11 // 其它出库
	BoundItemType_SelfUseOut       = 12 // 自用出库
)

// IoBound 库存
type IoBound struct {
	Id             int       `json:"id" xorm:"pk autoincr 'id'" `                            // 主键ID
	ChainId        int64     `json:"chain_id"`                                               // 连锁id
	StoreId        string    `json:"store_id" xorm:"varchar(50) default '0'"`                // 门店的主键
	WarehouseId    int       `json:"warehouse_id"`                                           // 仓库id
	BoundNo        string    `json:"bound_no" xorm:"varchar(32) default ''"`                 // 出入库单号
	BoundType      int       `json:"bound_type" xorm:"tinyint default 1"`                    // 出库/入库类型：1-入库，2-出库
	ItemType       int       `json:"item_type" xorm:"varchar(16) default ''"`                // 单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 11. 初始化入库, 12.自用出库
	ItemRefId      int       `json:"item_ref_id" xorm:"default 0"`                           // 出入库关联的单据id
	ItemRefNo      string    `json:"item_ref_no" xorm:"varchar(50) default ''"`              // 出入库关联的单据编号
	ItemRefType    int       `json:"item_ref_type" xorm:"int default 0'"`                    // 出入库关联的单据类型: 1.
	TotalNum       int       `json:"total_num" xorm:"default 0"`                             // 出入库总数量
	TotalAmount    int       `json:"total_amount" xorm:"default 0"`                          // 出入库总价(分)
	SellAmount     int       `json:"sell_amount" xorm:"default 0"`                           // 出库售卖总价, 入库则为0(分)
	Remark         string    `json:"remark" xorm:"varchar(255) default ''"`                  // 备注
	IsDeleted      int       `json:"is_deleted" xorm:"int default 0"`                        // 删除标识:0未删除,1已删除
	Operator       string    `json:"operator" xorm:"varchar(255) not null"`                  // 操作人
	OccurrenceTime time.Time `json:"occurrence_time" xorm:"not null"`                        // 发生时间
	CreatedTime    time.Time `json:"created_time" xorm:"default CURRENT_TIMESTAMP not null"` // 创建时间
	UpdatedTime    time.Time `json:"updated_time" xorm:"default CURRENT_TIMESTAMP not null"` // 修改时间
}

func (s IoBound) TableName() string {
	return "inventory_in_out_bound"
}

// insert 插入出入库记录
func (s IoBound) Create(ctx context.Context, session *xorm.Session) (int, error) {
	_, err := session.Context(ctx).Insert(&s)
	if err != nil {
		return 0, errors.Wrap(err, "创建出入库记录失败")
	}
	return s.Id, nil
}

// GetFreezeItem 获取锁库数据
func (s IoBound) GetFreezeItem(ctx context.Context, session *xorm.Session, itemRefId int, itemRefType int) IoBound {
	ioBound := IoBound{}
	session.Context(ctx).
		Where("item_ref_id = ? AND item_ref_type = ? AND is_deleted = 0 AND bound_type = ? AND item_type = ?", itemRefId, itemRefType, BoundType_Out, BoundItemType_Freeze).
		Get(&ioBound)
	return ioBound
}

// UpdateById 更新出入库记录
func (s IoBound) UpdateById(ctx context.Context, session *xorm.Session) error {
	_, err := session.Context(ctx).ID(s.Id).Update(s)
	if err != nil {
		return errors.Wrap(err, "更新出入库记录失败")
	}
	return nil
}

func (s IoBound) Page(ctx context.Context, session *xorm.Session, req vo.IoBoundPageRequest) ([]vo.IoBoundResponse, int64, error) {
	// 1. 构建查询条件
	query := session.Context(ctx).Table("inventory_in_out_bound").Alias("i").
		Join("LEFT", "dc_dispatch.warehouse w", "w.id = i.warehouse_id").
		Where("warehouse_id = ?", req.WarehouseId).
		And("item_type NOT IN (3, 4, 11)") // 展示屏蔽冻结、解锁、初始化库存记录

	if len(req.Query) > 0 {
		query = query.And("(i.bound_no LIKE ? OR i.item_ref_no LIKE ? OR i.remark LIKE ?)", "%"+req.Query+"%", "%"+req.Query+"%", "%"+req.Query+"%")
	}
	if req.BoundType > 0 {
		query = query.And("i.bound_type = ?", req.BoundType)
	}
	if req.ItemType > 0 {
		query = query.And("i.item_type = ?", req.ItemType)
	}
	if req.ItemRefId > 0 {
		query = query.And("i.item_ref_id = ?", req.ItemRefId)
	}
	// 操作人
	if len(req.Operator) > 0 {
		query = query.And("i.operator = ?", req.Operator)
	}
	if len(req.TimeStart) > 0 {
		query = query.And("i.created_time >= ?", req.TimeStart)
	}
	if len(req.TimeEnd) > 0 {
		query = query.And("i.created_time <= ?", req.TimeEnd)
	}

	query = query.And("is_deleted = ?", 0)
	// 使用FindAndCount查询总数和分页数据
	var responses []vo.IoBoundResponse
	total, err := query.Select("i.*,w.name as warehouse_name").
		OrderBy("i.occurrence_time DESC").
		Limit(req.Size, (req.Current-1)*req.Size).
		FindAndCount(&responses)
	if err != nil {
		return nil, 0, errors.Wrap(err, "分页查询出入库单失败")
	}

	return responses, total, nil
}

// GetById 获取出入库记录
func (s IoBound) GetById(ctx context.Context, session *xorm.Session, id int) (IoBound, error) {
	var ioBound IoBound
	_, err := session.Context(ctx).ID(id).Get(&ioBound)
	return ioBound, err
}
