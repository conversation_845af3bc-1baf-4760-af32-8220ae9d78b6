package upetmart_po

type UpetDistriMemberTemporaryFans struct {
	DisTemFansId int `xorm:"not null pk autoincr INT(10)"`
	DisMemberId  int `xorm:"default 0 comment('分销员id') INT(10)"`
	MemberId     int `xorm:"default 0 comment('member') INT(10)"`
	State        int `xorm:"default 0 comment('0待确认,1已确认') TINYINT(3)"`
	CreateTime   int `xorm:"INT(11)"`
	DisType      int `xorm:"default 0 comment('1，分享连接2，扫码') TINYINT(2)"`
}
