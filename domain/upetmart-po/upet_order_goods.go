package upetmart_po

type UpetOrderGoods struct {
	RecId               int     `xorm:"not null pk autoincr comment('订单商品表索引id') INT(11)"`
	OrderId             int     `xorm:"not null comment('订单id') index INT(11)"`
	GoodsId             int64   `xorm:"not null comment('商品id') index INT(11)"`
	GoodsName           string  `xorm:"not null comment('商品名称') VARCHAR(50)"`
	GoodsPrice          float64 `xorm:"not null comment('商品价格') DECIMAL(10,2)"`
	GoodsNum            int32   `xorm:"not null default 1 comment('商品数量') SMALLINT(5)"`
	GoodsImage          string  `xorm:"comment('商品图片') VARCHAR(100)"`
	GoodsPayPrice       float64 `xorm:"not null comment('商品实际成交价') DECIMAL(10,2)"`
	StoreId             int32   `xorm:"not null default 0 comment('店铺ID') INT(10)"`
	BuyerId             int32   `xorm:"not null default 0 comment('买家ID') INT(10)"`
	GoodsType           int     `xorm:"not null default 1 comment('1默认 2团购 3限时折扣 4组合套装 5赠品 6秒杀 7闪购 8加价购活动 9加价购换购 10拼团 12会员价 20砍价 99助力') TINYINT(1)"`
	PromotionsId        int     `xorm:"not null default 0 comment('促销活动ID（团购ID/限时折扣ID/优惠套装ID）与goods_type搭配使用') MEDIUMINT(8)"`
	CommisRate          int     `xorm:"not null default 0 comment('佣金比例') SMALLINT(5)"`
	GcId                int     `xorm:"not null default 0 comment('商品最底级分类ID') MEDIUMINT(8)"`
	GoodsSpec           string  `xorm:"comment('商品规格') VARCHAR(255)"`
	GoodsContractid     string  `xorm:"comment('商品开启的消费者保障服务id') VARCHAR(100)"`
	GoodsCommonid       int     `xorm:"not null default 0 comment('商品公共表ID') index INT(10)"`
	AddTime             int64   `xorm:"not null default 0 comment('订单添加时间') INT(10)"`
	IsDis               int     `xorm:"not null default 0 comment('是否分销商品') TINYINT(1)"`
	DisCommisRate       float64 `xorm:"not null default 0 comment('分销佣金比例') TINYINT(1)"`
	DisMemberId         int     `xorm:"not null default 0 comment('分销会员ID') index INT(10)"`
	CustomerServiceId   int     `xorm:"not null default 0 comment('客服id') index INT(10)"`
	CustomerServiceRate float64 `xorm:"not null default 0 comment('客服分成比列') TINYINT(1)"`
	OutMemberId         int32   `xorm:"default 0 comment('推荐人ID') index INT(10)"`
	AppOrderId          string  `xorm:"comment('药品清单唯一订单号（只用于保存）') VARCHAR(255)"`
	ChainId             int     `xorm:"default 0 comment('门店id') INT(10)"`
	Sku                 string  `xorm:"comment('商品sku') VARCHAR(50)"`
	OcId                string  `xorm:"default '0' comment('订单中心id') VARCHAR(50)"`
	IsLive              int     `xorm:"default 0 comment('0默认1直播下单') TINYINT(1)"`
	IsGroupGoods        int     `xorm:"default 0 comment('0非组合，1实实组合，2虚虚组合，3虚实组合') TINYINT(1)"`
	VoucherInfo         string  `xorm:"comment('商品分摊优惠信息') TEXT"`
}

func (m UpetOrderGoods) TableName() string {
	return "upetmart.upet_order_goods"
}
