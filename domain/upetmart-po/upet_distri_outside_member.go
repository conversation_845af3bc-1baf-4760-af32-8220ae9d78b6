package upetmart_po

type UpetDistriOutsideMember struct {
	DmId        int `xorm:"not null pk autoincr INT(11)"`
	DisMemberId int `xorm:"not null default 0 comment('父级邀请人id') INT(11)"`
	MemberId    int `xorm:"not null default 0 comment('外部代理人id') INT(11)"`
	ChainId     int `xorm:"not null default 0 comment('内部分销员门店ID') INT(11)"`
	State       int `xorm:"not null default 1 comment('0待审核，1正常，2失效') TINYINT(2)"`
	Addtime     int `xorm:"not null default 0 comment('添加时间') INT(11)"`
	Updatetime  int `xorm:"not null default 0 comment('更新时间') INT(11)"`
}
