package upetmart_po

import (
	"strings"

	"xorm.io/xorm"
)

// 子龙员工信息
type ZlOrganUserInfo struct {
	UserId       int64  //员工id 主键
	UserRealName string //员工名称
	UserMobile   string //手机号码
}

func (z ZlOrganUserInfo) TableName() string {
	return "hill_organ.t_organ_user_info"
}

// 根据手机号码获取员工信息
func (z *ZlOrganUserInfo) GetByMobile(session *xorm.Session, mobile string) error {
	if !strings.HasPrefix(mobile, "+") {
		mobile = "+86" + mobile
	}
	_, err := session.Where("user_status = 0 and user_mobile = ?", mobile).Get(z)
	return err
}

// 根据手机号码获取员工信息
func (z *ZlOrganUserInfo) GetByUserId(session *xorm.Session, userId int64) error {
	_, err := session.Where("user_id = ?", userId).Get(z)
	return err
}
