package distribution_po

import (
	"eShop/infra/utils"
	"time"

	"github.com/spf13/cast"
	"xorm.io/xorm"
)

type StatsEnterpriseDaily struct {
	Id       int    `json:"id" xorm:"pk autoincr not null comment('主键') BIGINT 'id'"`
	StatDate string `json:"stat_date" xorm:"not null comment('日期') DATE 'stat_date'"`
	EndDate  string `json:"end_date" xorm:"not null comment('日期') DATE 'end_date'"`
	// 累计分销企业
	TotalEnterprise int `json:"total_enterprise" xorm:"not null default 0 comment('累计分销企业') INT 'total_enterprise'"`
	// 累计分销店铺
	TotalShop int `json:"total_shop" xorm:"not null default 0 comment('累计分销店铺') INT 'total_shop'"`
	// 商品订单累计成交企业
	TotalTransEnterprise int `json:"total_trans_enterprise"`
	// 保险订单累计成交企业
	InsTotalTransEnterprise int `json:"ins_total_trans_enterprise"`
	// 累计分销员
	TotalDistributor int `json:"total_distributor" xorm:"not null default 0 comment('累计分销员') INT 'total_distributor'"`
	// 商品订单累计成交分销员
	TotalTransDistributor int `json:"total_trans_distributor"`
	// 保险订单累计成交分销员
	InsTotalTransDistributor int `json:"ins_total_trans_distributor"`
	// 累计业务员
	TotalSalesman int `json:"total_salesman" xorm:"not null default 0 comment('累计业务员') INT 'total_salesman'"`
	// 创建时间
	CreateTime string `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	// 更新时间
	UpdateTime string `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
}

func GetEnterpriseData(db *xorm.Engine, where map[string]interface{}) (data StatsEnterpriseDaily, err error) {
	session := db.NewSession()
	defer session.Close()
	s := `sum(total_salesman) as total_salesman,
		sum(total_enterprise) as total_enterprise,
		sum(total_shop) as total_shop,
		sum(total_distributor) as total_distributor,
		sum(total_trans_enterprise) as total_trans_enterprise,
		sum(ins_total_trans_enterprise) as ins_total_trans_enterprise,
		sum(total_trans_distributor) as total_trans_distributor,
		sum(ins_total_trans_distributor) as ins_total_trans_distributor`
	session = session.Table("eshop.stats_enterprise_daily").Select(s)

	statDateS := where["statDateStart"]
	statDateE, ok := where["statDateEnd"]

	if ok {
		//把statDateE的类型转化成时间类型
		t, _ := time.ParseInLocation(time.DateTime, statDateE.(string)+" 23:59:59", time.Local)
		//判断是否大于当前时间
		if t.After(time.Now()) {
			session = session.Where("stat_date >= ?", statDateS)
			session = session.Where("end_date<=?", statDateE)
			session = session.Where("stat_date=end_date") //排除周 、月、年， 只是 日 数据求和
		} else {
			session = session.Where("stat_date=?", statDateS)
			session = session.Where("end_date=?", statDateE)
		}
	} else {
		session = session.Where("stat_date=end_date")
	}

	_, err = session.Get(&data)

	// 累计企业数实时统计
	var (
		TotalTransEnterprise     int
		TotalTransDistributor    int
		InsTotalTransEnterprise  int
		InsTotalTransDistributor int
	)

	session2 := db.Table("upetmart.upet_order_goods").Alias("og").
		Join("left", "upetmart.upet_orders o", "og.order_id = o.order_id").
		Select(`COUNT(DISTINCT og.shop_id) AS total_trans_enterprise,
    COUNT(DISTINCT og.dis_member_id ) AS total_trans_distributor`).Where("og.store_id = 3 and o.store_id = 3 and o.payment_time > 0 and og.shop_id > 0 ")

	if statDateS != nil && statDateE != nil {
		startStamp := utils.Date2Timestamp(cast.ToString(statDateS))
		endStamp := utils.AddDate2Timestamp(cast.ToString(statDateE))
		session2 = session2.And("o.add_time between ? and ?", startStamp, endStamp)
	}

	_, err = session2.Get(&TotalTransEnterprise, &TotalTransDistributor)
	if err != nil {
		return
	}
	data.TotalTransEnterprise = TotalTransEnterprise
	data.TotalTransDistributor = TotalTransDistributor

	// 保险订单累计成交分销员ins_total_trans_enterprise
	session3 := db.Table("insurance_business.pi_order_info").Alias("o").Select(`COUNT(DISTINCT o.shop_id) AS ins_total_trans_enterprise,
       COUNT(DISTINCT o.dis_id) AS ins_total_trans_distributor`).Where("o.shop_id > 0 and o.pay_time > 0")
	if statDateS != nil && statDateE != nil {
		session3 = session3.Where("DATE(o.create_time)  >= ? and DATE(o.create_time) <= ?", statDateS, statDateE)
	}
	_, err = session3.Get(&InsTotalTransEnterprise, &InsTotalTransDistributor)
	if err != nil {
		return
	}
	data.InsTotalTransEnterprise = InsTotalTransEnterprise
	data.InsTotalTransDistributor = InsTotalTransDistributor

	return
}
