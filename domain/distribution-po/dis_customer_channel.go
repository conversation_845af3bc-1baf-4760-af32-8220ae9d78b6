package distribution_po

import "time"

type DisCustomerChannel struct {
	// 自增id
	Id int `json:"id" xorm:"pk autoincr not null comment('自增id') INT"`
	// 渠道层级：1为一级渠道,2为二级渠道
	Level int `json:"level" xorm:"not null default 0 comment('渠道层级：1为一级渠道,2为二级渠道') INT"`
	// 企业销售渠道类型
	ChannelType int `json:"channel_type" xorm:"not null default 0 comment('企业销售渠道类型') INT"`
	// 企业销售渠道类型名称
	ChannelName string `json:"channel_name" xorm:"not null default '' comment('企业销售渠道类型名称') VARCHAR(255)"`
	// 渠道路径
	ChannelPath string `json:"channel_path" xorm:"not null default '' comment('渠道路径') VARCHAR(255)"`
	// 父节点
	Parent     int       `json:"parent" xorm:"not null default 0 comment('父节点') INT"`
	CreateTime time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
}

func (d *DisCustomerChannel) TableName() string {
	return "eshop.dis_customer_channel"
}
