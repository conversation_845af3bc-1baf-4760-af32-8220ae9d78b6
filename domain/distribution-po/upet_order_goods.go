package distribution_po

import (
	"xorm.io/xorm"
)

type UpetOrderGoodsSimple struct {
	//商品id
	GoodsId int `json:"goods_id"`
	// 商品名称
	GoodsName string `json:"goods_name"`
	// 商品数量
	GoodsNum int `json:"goods_num"`
	// 商品图片
	GoodsImage string `json:"goods_image"`
	// 商品实际成交价
	GoodsPayPrice float64 `json:"goods_pay_price"`
	// 店铺ID
	StoreId int `json:"store_id"`
	// 是否分销商品
	IsDis int `json:"is_dis"`
	// 线上分销店铺id
	ShopId int `json:"shop_id"`
	// 分销佣金比例
	DisCommisRate float64 `json:"dis_commis_rate"`
	// 分销会员ID
	DisMemberId int `json:"dis_member_id"`
}
type StatsDisCenterGoods struct {
	// 商品Id
	GoodsId int `json:"goods_id"`
	// 商品名称
	GoodsName string `json:"goods_name"`
	// 商品图片
	GoodsImage string `json:"goods_image"`
	// 分销成交件数
	TransCount int `json:"trans_count"`
	// 分销成交金额(单位分)
	TransAmount float64 `json:"trans_amount"`
	// 分销佣金(单位分)
	Commission float64 `json:"commission"`
}

func StatsOrderGoods(db *xorm.Engine, where map[string]interface{}) (out []StatsDisCenterGoods, total int, err error) {
	session := db.NewSession()
	defer session.Close()
	out = make([]StatsDisCenterGoods, 0)
	s := `a.goods_id,a.goods_name,a.goods_image,sum(a.goods_num) as trans_count,SUM(a.goods_pay_price * 100) as trans_amount,sum(ROUND(a.dis_commis_rate*a.goods_pay_price)) as commission`

	session = session.Table("upetmart.upet_order_goods").Alias("a").
		Join("left", "upetmart.upet_orders b", "a.order_id=b.order_id").Select(s)

	session.Where("b.order_father>0")
	// 已支付的订单
	_, ok := where["isPayed"]
	if ok {
		session = session.Where("b.payment_time>0")
	}
	addTimeS, ok := where["addTimeStart"]
	if ok {
		session = session.Where("a.add_time>=?", addTimeS)
	}
	addTimeE, ok := where["addTimeEnd"]
	if ok {
		session = session.Where("a.add_time<=?", addTimeE)
	}
	shopId, ok := where["shopId"]
	if ok {
		session = session.Where("a.shop_id=?", shopId)
	}

	orgId, ok := where["orgId"]
	if ok {
		session = session.Where("a.store_id=?", orgId)
	}
	isDis, ok := where["isDis"]
	if ok {
		session = session.Where("a.is_dis=?", isDis)
	}

	disMemberId, ok := where["disMemberId"]
	if ok {
		session = session.Where("a.dis_member_id=?", disMemberId)
	}

	disMemberIds, ok := where["disMemberIds"]
	if ok {
		session = session.In("a.dis_member_id", disMemberIds)
	}
	goodsName, ok := where["goodsName"]

	if ok {
		goodsNameStr := goodsName.(string)
		session = session.Where("a.goods_name like ?", "%"+goodsNameStr+"%")
	}

	groupBy, ok := where["groupBy"]
	groupByStr := groupBy.(string)
	if ok {
		session = session.GroupBy(groupByStr)
	}
	orderBy, ok := where["orderBy"]

	if ok {
		orderByStr := orderBy.(string)
		session = session.OrderBy(orderByStr)
	}
	pageSize, ok1 := where["pageSize"]
	pageIndex, ok2 := where["pageIndex"]
	if ok1 && ok2 {
		ps := pageSize.(int)
		pi := pageIndex.(int)
		session = session.Limit((ps), int(ps*(pi-1)))
	}

	cnt, err := session.FindAndCount(&out)
	if err != nil {
		return
	}
	total = int(cnt)
	return
}
