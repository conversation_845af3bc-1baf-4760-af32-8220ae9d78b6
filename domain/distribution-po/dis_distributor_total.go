package distribution_po

import (
	vo "eShop/view-model/distribution-vo"
	"errors"
	"time"

	"xorm.io/xorm"
)

type DisDistributorTotal struct {
	Id                  int       `json:"id" xorm:"pk autoincr not null comment('ID') INT 'id'"`
	OrgId               int       `json:"org_id" xorm:"not null default 0 comment('主体ID') INT 'org_id'"`
	DisId               int       `json:"dis_id" xorm:"not null comment('分销员id') INT 'dis_id'"`
	MemberId            int       `json:"member_id" xorm:"not null comment('分销员用户id') INT 'member_id'"`
	ShopId              int       `json:"shop_id" xorm:"default 0 comment('分销店铺') INT 'shop_id'"`
	OrderNum            int       `json:"order_num" xorm:"default 0 comment('分销单数') INT 'order_num'"`
	TotalSales          int       `json:"total_sales" xorm:"default 0 comment('分销销售额(分)') INT 'total_sales'"`
	SettledCommission   int       `json:"settled_commission" xorm:"default 0 comment('已结佣金(分)') INT 'settled_commission'"`
	UnsettledCommission int       `json:"unsettled_commission" xorm:"default 0 comment('未结佣金(分)') INT 'unsettled_commission'"`
	WithdrawSuccess     int       `json:"withdraw_success" xorm:"default 0 comment('提现成功(分)') INT 'withdraw_success'"`
	WithdrawApply       int       `json:"withdraw_apply" xorm:"default 0 comment('提现申请(分)') INT 'withdraw_apply'"`
	WaitWithdraw        int       `json:"wait_withdraw" xorm:"default 0 comment('待提现(分)') INT 'wait_withdraw'"`
	TotalCustomer       int       `json:"total_customer" xorm:"default 0 comment('累计客户数') INT 'total_customer'"`
	CreateTime          time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'create_time' created"`
	UpdateTime          time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'update_time' updated"`
}

func StatsDistributorData(db *xorm.Engine, where map[string]interface{}) (out vo.StatsDisCenterTotal, err error) {
	session := db.NewSession()
	defer session.Close()
	s := `sum(total_pay_sales) as total_pay_sales,
		sum(ins_total_pay_sales) as ins_total_pay_sales,
		sum(order_pay_num) as order_pay_num,
		sum(ins_order_pay_num) as ins_order_pay_num,
		sum(settled_commission) as settled_commission1,
		sum(unsettled_commission) as unsettled_commission1,
		sum(ins_settled_commission) as ins_settled_commission,
		sum(ins_unsettled_commission) as ins_unsettled_commission,
		(sum(total_pay_sales)+sum(ins_total_pay_sales)) as trans_amount,
		(sum(order_pay_num)+sum(ins_order_pay_num)) as trans_count,
		(sum(settled_commission)+sum(unsettled_commission)+sum(ins_settled_commission)+sum(ins_unsettled_commission)) as commission,
		(sum(settled_commission)+sum(ins_settled_commission)) as settled_commission,
		(sum(unsettled_commission)+sum(ins_unsettled_commission)) as unsettled_commission,
		sum(total_customer) as total_customer,
		sum(withdraw_success) as withdraw_success,
		sum(withdraw_apply) as withdraw_apply,
		sum(wait_withdraw) as wait_withdraw,
		count(distinct dis_id)  as distributor_cnt
		`
	session = session.Table("eshop.dis_distributor_total").Select(s)

	disId, ok := where["disId"]
	if ok {
		session = session.Where("dis_id=?", disId)
	}
	shopId, ok := where["shopId"]
	if ok {
		session = session.Where("shop_id=?", shopId)
	}
	orgId, ok := where["orgId"]
	if ok {
		session = session.Where("org_id=?", orgId)
	}

	_, err = session.Get(&out)

	return
}

func (d *DisDistributorTotal) Insert(session *xorm.Session, disId int, orgId int, shopId int, memberId int) (err error) {
	if session == nil {
		return errors.New("session is nil")
	}
	_, err = session.Table("eshop.dis_distributor_total").Where("dis_id=?", disId).And("org_id=?", orgId).And("shop_id=?", shopId).Get(d)
	if err != nil {
		return err
	}
	disTotal := DisDistributorTotal{
		DisId:    disId,
		OrgId:    orgId,
		ShopId:   shopId,
		MemberId: memberId,
	}
	if d.Id <= 0 {
		_, err = session.Table("eshop.dis_distributor_total").Insert(&disTotal)
	}

	return
}

// 判断该分销员是否曾经为百林康源医生角色
func (d *DisDistributorTotal) IsBLKYDoctor(session *xorm.Session, disId int, orgId int) (bool, error) {
	// 先获取分销员信息
	dis := DisDistributor{}
	_, err := session.Table("eshop.dis_distributor").Where("id=?", disId).And("org_id=?", orgId).Get(&dis)
	if err != nil {
		return false, err
	}
	if dis.DisRole == DisRoleDoctor {
		return false, nil
	}
	// 判断dis_distributor_total表中，shop_id=0的记录是否存在
	cnt := 0
	_, err = session.Table("eshop.dis_distributor_total").Select("count(1)").Where("dis_id=?", disId).And("org_id=?", orgId).And("shop_id=0").Get(&cnt)
	if err != nil {
		return false, err
	}
	return cnt > 0, nil
}
