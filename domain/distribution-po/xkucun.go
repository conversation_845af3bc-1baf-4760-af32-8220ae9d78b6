package distribution_po

// Xkucun 表示 xkucun 表的模型结构体
//type Xkucun struct {
//	IID        int        `gorm:"primaryKey;autoIncrement;column:iid" json:"iid"`                     // 主键ID
//	Swlm       string     `gorm:"not null;column:swlm;comment:商品物流码;index:idx_swlm" json:"swlm"`      // 商品物流码
//	Sspmc      string     `gorm:"not null;column:sspmc" json:"sspmc"`                                 // 商品名称
//	Spici      *string    `gorm:"column:spici" json:"spici"`                                          // 商品批次
//	sdxtm      *string    `gorm:"column:sdxtm;comment:箱码" json:"sdxtm"`                               // 箱码
//	Dregtime   time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP;column:dregtime" json:"dregtime"` // 注册时间，默认当前时间
//	Drktime    *time.Time `gorm:"column:drktime" json:"drktime"`                                      // 入库时间
//	Dcktime    *time.Time `gorm:"column:dcktime" json:"dcktime"`                                      // 出库时间
//	Izt        int        `gorm:"not null;default:0;column:izt" json:"izt"`                           // 状态，0-初始状态
//	Saddman    *string    `gorm:"column:saddman" json:"saddman"`                                      // 添加人
//	Sfwm       *string    `gorm:"column:sfwm;index:idx_sfwm" json:"sfwm"`                             // 扫描条形码
//	Sddbh      *string    `gorm:"column:sddbh" json:"sddbh"`                                          // 单据编号
//	Dbdatetime *time.Time `gorm:"column:dbdatetime" json:"dbdatetime"`                                // 单据日期
//}
//
//// TableName 返回与该结构体对应的数据库表名
//func (Xkucun) TableName() string {
//	return "xkucun"
//}
