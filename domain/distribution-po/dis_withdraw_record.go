package distribution_po

import (
	"time"
)

type DisWithdrawRecord struct {
	Id              int       `json:"id" xorm:"pk autoincr not null comment('ID') INT 'id'"`
	OrgId           int       `json:"org_id" xorm:"not null default 0 comment('主体ID') INT 'org_id'"`
	ShopId          int       `json:"shop_id" xorm:"default 0 comment('分销店铺id(eshop.shop.id)') INT 'shop_id'"`
	DisId           int       `json:"dis_id" xorm:"not null default 0 comment('分销员id') INT 'dis_id'"`
	WithdrawDisId   int       `json:"withdraw_dis_id" xorm:"not null default 0 comment('分销员id(提现人)') INT 'withdraw_dis_id'"`
	Type            int8      `json:"type" xorm:"not null default 0 comment('操作类型：0-初始，1-结算转可提现，2-提现申请时，3-提现申请审核通过，4-提现申请审核驳回 5-佣金扣减 6-佣金扣减平账') TINYINT 'type'"`
	ThirdId         int64     `json:"third_id" xorm:"not null default 0 comment('若type=1,则dis_settlement.id;若type=2、3、4、6时,则dis_withdraw.id;若type=5时，对应dis_distributor_swlm_cps.id') INT 'third_id'"`
	WithdrawSuccess int       `json:"withdraw_success" xorm:"default 0 comment('提现成功(分)') INT 'withdraw_success'"`
	WithdrawApply   int       `json:"withdraw_apply" xorm:"default 0 comment('提现申请(分)') INT 'withdraw_apply'"`
	WaitWithdraw    int       `json:"wait_withdraw" xorm:"default 0 comment('待提现(分)') INT 'wait_withdraw'"`
	CreateTime      time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime      time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
}
