package distribution_po

import "time"

type XcodeDetail struct {
	Id            int       `xorm:"pk autoincr not null INT 'id'"`
	Swlm          string    `xorm:"VARCHAR(50)" json:"swlm"`
	OrgType       int       `xorm:"default 0 'org_type'"`
	Sdxtm         string    `xorm:"VARCHAR(50)" json:"sdxtm"`
	DisCommisRate int       `xorm:"default 0 'dis_commis_rate'"`
	CreateTime    time.Time `xorm:"default CURRENT_TIMESTAMP NULL 'create_time' created"`
	UpdateTime    time.Time `xorm:"default CURRENT_TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP 'update_time' updated"`
}
