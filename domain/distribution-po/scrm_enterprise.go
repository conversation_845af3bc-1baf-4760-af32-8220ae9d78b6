package distribution_po

import (
	"errors"
	"time"

	"xorm.io/xorm"
)

type ScrmEnterprise struct {
	Id                  int64     `json:"id" xorm:"pk not null comment('id') BIGINT 'id'"`
	Code                string    `json:"code" xorm:"default '' comment('企业编码') VARCHAR(50) 'code'"`
	EnterpriseName      string    `json:"enterprise_name" xorm:"not null default '' comment('企业名称') VARCHAR(128) 'enterprise_name'"`
	EnterpriseType      int       `json:"enterprise_type" xorm:"comment('类型（0企业 1个人）') TINYINT 'enterprise_type'"`
	MarketingChannel    int       `json:"marketing_channel" xorm:"comment('渠道（1:宠物医院,2:宠物店,3:军警犬渠道,4:电商,5:繁育场,6:本地生活,7:散客,8:内部往来,9:商超,10:宠物诊所,11:经销商,12:OMS,13:其他招投标）') TINYINT 'marketing_channel'"`
	SettlementCycleType int       `json:"settlement_cycle_type" xorm:"comment('结算类型（1现结 2账期 3预收款）') TINYINT 'settlement_cycle_type'"`
	Province            string    `json:"province" xorm:"not null default '' comment('省') VARCHAR(255) 'province'"`
	City                string    `json:"city" xorm:"not null default '' comment('市') VARCHAR(255) 'city'"`
	District            string    `json:"district" xorm:"not null default '' comment('区') VARCHAR(255) 'district'"`
	Address             string    `json:"address" xorm:"default '' comment('详细地址') VARCHAR(255) 'address'"`
	Phone               string    `json:"phone" xorm:"default '' comment('手机号') VARCHAR(20) 'phone'"`
	SocialCreditCode    string    `json:"social_credit_code" xorm:"default '' comment('统一社会信用代码') VARCHAR(50) 'social_credit_code'"`
	IdCardNo            string    `json:"id_card_no" xorm:"comment('身份证号') VARCHAR(20) 'id_card_no'"`
	EnterpriseStatus    int       `json:"enterprise_status" xorm:"not null default 1 comment('状态（0停用 1启用）') TINYINT(1) 'enterprise_status'"`
	DataSource          int       `json:"data_source" xorm:"not null default 1 comment('来源：1、自建   2 云订货') TINYINT 'data_source'"`
	LastTrajectoryId    int64     `json:"last_trajectory_id" xorm:"comment('最后跟进id') BIGINT 'last_trajectory_id'"`
	CreateTime          time.Time `json:"create_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	CreateUser          string    `json:"create_user" xorm:"not null default '' comment('创建人') VARCHAR(20) 'create_user'"`
	CreateUserId        int64     `json:"create_user_id" xorm:"not null comment('创建人id') BIGINT 'create_user_id'"`
	UpdateTime          time.Time `json:"update_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
	UpdateUser          string    `json:"update_user" xorm:"not null default '' comment('更新人') VARCHAR(20) 'update_user'"`
	UpdateUserId        int64     `json:"update_user_id" xorm:"not null comment('更新人id') BIGINT 'update_user_id'"`
	RegisterTime        time.Time `json:"register_time" xorm:"comment('云订货注册时间') DATETIME 'register_time'"`
	VisitLocation       string    `json:"visit_location" xorm:"default '' comment('拜访位置') VARCHAR(255) 'visit_location'"`
	Longitude           string    `json:"longitude" xorm:"default '' comment('经度') VARCHAR(20) 'longitude'"`
	Latitude            string    `json:"latitude" xorm:"default '' comment('纬度') VARCHAR(20) 'latitude'"`
	CompletedBusInfo    int       `json:"completed_bus_info" xorm:"not null default 0 comment('是否已经完善经营信息（0未完善 1已完善）') TINYINT(1) 'completed_bus_info'"`
}

func (s *ScrmEnterprise) TableName() string {
	return "eshop.scrm_enterprise"
}

type GetScrmEnterprisesReq struct {
	Id               int64  `json:"id"`
	Phone            string `json:"phone"`
	SocialCreditCode string `json:"social_credit_code"`
}

// 获取云订货企业
func (s *ScrmEnterprise) GetScrmEnterprises(session *xorm.Session, req GetScrmEnterprisesReq) (scrmEnterprises []ScrmEnterprise, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	scrmEnterprises = make([]ScrmEnterprise, 0)
	session = session.Table(s.TableName())
	if req.Id > 0 {
		session = session.Where("id = ?", req.Id)
	}
	if req.Phone != "" {
		session = session.Where("phone = ?", req.Phone)
	}
	if req.SocialCreditCode != "" {
		session = session.Where("social_credit_code = ?", req.SocialCreditCode)
	}
	err = session.Where("enterprise_status = 1").Find(&scrmEnterprises)
	if err != nil {
		return nil, err
	}

	return scrmEnterprises, nil
}
