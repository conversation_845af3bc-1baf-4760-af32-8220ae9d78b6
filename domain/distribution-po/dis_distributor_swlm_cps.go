package distribution_po

import (
	"eShop/infra/log"
	"eShop/services/distribution-service/enum"
	distribution_vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"time"

	"xorm.io/xorm"
)

const (
	StatusDefault   = 0 // 默认
	StatusNotDeduct = 1 // 未扣款
	StatusDeducted  = 2 // 已扣款
	StatusDeducting = 3 // 扣款中

	IsSelfBalanceDefault = 0 // 默认
	IsSelfBalance        = 1 // 是
	IsNotSelfBalance     = 2 // 否
)

type DisDistributorSwlmCps struct {
	ID              int       `xorm:"pk autoincr 'id'"`
	DisMemberID     int       `xorm:"'dis_member_id' notnull default(0)"`
	Swlm            string    `xorm:"'swlm' varchar(20) notnull default(' ')"`
	OrderSn         string    `xorm:"'order_sn' notnull default(0)"`
	DisCommisAmount int       `xorm:"'dis_commis_amount' notnull default(0)"`
	Status          int       `xorm:"'status' notnull default(0) comment('扣减佣金状态：0-默认, 1-未扣款，2-已扣款,3-扣款中')"`
	IsSelfBalance   int       `xorm:"'is_self_balance' notnull default(0) comment('是否自己平账：0-初始，1是，2否(若订单未提现过，则扣减佣金时，扣减是自己订单的登记佣金)')"`
	CreateTime      time.Time `xorm:"'create_time' default(NULL)"`
	UpdateTime      time.Time `xorm:"'update_time' default(NULL) updated"`
}

func (d *DisDistributorSwlmCps) TableName() string {
	return "eshop.dis_distributor_swlm_cps"
}

type DistributorSwlmCpsListReq struct {
	Swlm          string
	OrderSn       []int64
	Status        int
	IsSelfBalance int
	ShopId        int
	MustOrderSn   []int64
}

func (d *DisDistributorSwlmCps) DistributorSwlmCpsList(session *xorm.Session, in DistributorSwlmCpsListReq) (out []distribution_vo.DistributorSwlmCpsListRes, err error) {
	logPrefix := fmt.Sprintf("获取扣除佣金订单-shopId:%d,入参:%v", in.ShopId, in)
	log.Info(logPrefix)
	out = make([]distribution_vo.DistributorSwlmCpsListRes, 0)
	selectStr := `
				a.id as swlm_cps_id,
				a.dis_member_id,
				a.swlm,
				a.order_sn,
				a.dis_commis_amount as amount,
				a.status,
				a.is_self_balance,
				b.store_id as org_id,
				b.org_type,
				DATE_FORMAT(FROM_UNIXTIME(b.add_time), '%Y-%m-%d %H:%i:%s') as add_time,
				c.shop_id,
				c.goods_id,
				c.goods_name,
				d.id as dis_id
				`

	session.Table("eshop.dis_distributor_swlm_cps").Alias("a").Select(selectStr).
		Join("left", "upetmart.upet_orders b", "a.order_sn=b.order_sn").
		Join("left", "upetmart.upet_order_goods c", "b.order_id=c.order_id").
		Join("left", "eshop.dis_distributor d", "c.dis_member_id=d.member_id").
		Where("b.store_id=?", enum.BLKYOrgId).
		Where("c.shop_id=?", in.ShopId).
		Where("d.org_id=?", enum.BLKYOrgId)
	if len(in.OrderSn) > 0 {
		session.In("a.order_sn", in.OrderSn)
	}
	if in.MustOrderSn != nil {
		session.In("a.order_sn", in.MustOrderSn)
	}
	if in.Status != 0 {
		session.Where("a.status=?", in.Status)
	}
	if in.IsSelfBalance != 0 {
		session.Where("a.is_self_balance=?", IsSelfBalance)
	}

	if err = session.Find(&out); err != nil {
		log.Errorf("%s 获取扣除佣金订单失败-错误为%s", logPrefix, err.Error())
		err = errors.New("获取扣除佣金订单失败")
		return
	}
	return
}

// 获取已经自平账的订单（未提现时， 扣除自己的佣金 则是自己平自己的账）
func (d *DisDistributorSwlmCps) BanlancedSwlmCpsList(session *xorm.Session, shopId int, orderNoSli []int64) (out []string, err error) {
	out = make([]string, 0)
	if cpsList, e := new(DisDistributorSwlmCps).DistributorSwlmCpsList(session, DistributorSwlmCpsListReq{
		ShopId:        shopId,
		OrderSn:       orderNoSli,
		Status:        StatusDeducted,
		IsSelfBalance: IsSelfBalance,
	}); e != nil {
		err = errors.New("获取扣除佣金订单失败")
		return
	} else if len(cpsList) > 0 {
		for _, v := range cpsList {
			out = append(out, v.OrderSn)
		}

	}
	return

}
