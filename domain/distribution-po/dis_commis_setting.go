package distribution_po

import (
	"errors"
	"time"

	"xorm.io/xorm"
)

type DisCommisSetting struct {
	// 自增id
	Id int `json:"id" xorm:"pk autoincr not null comment('自增id') INT"`
	// 所属主体id
	OrgId int `json:"org_id" xorm:"not null default 0 comment('所属主体id') INT"`
	// eshop.dis_customer_channel.id
	CustomerChannelId int `json:"customer_channel_id" xorm:"not null default 0 comment('eshop.dis_customer_channel.id') INT"`
	// 分销佣金比例(示例：佣金比例是5%，这里存5)
	DisCommisRate float64 `json:"dis_commis_rate" xorm:"not null default 0.00 comment('分销佣金比例(示例：佣金比例是5%，这里存5)') DECIMAL(4,2)"`
	// 佣金设置：1默认设置，0自定义佣金比例
	IsDefault  int       `json:"is_default" xorm:"not null default 0 comment('佣金设置：1默认设置，0自定义佣金比例') INT"`
	CreateTime time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	// 是否删除
	IsDel int `json:"is_del" xorm:"not null default 0 comment('是否删除') INT"`
	// 删除时间
	DelDate time.Time `json:"del_date" xorm:"default NULL comment('删除时间') DATETIME"`
}

type DisCommisSettingExt struct {
	DisCommisSetting `xorm:"extends"`
	ChannelPath      string `json:"channel_path" xorm:"<-"`
}

func (d *DisCommisSetting) TableName() string {
	return "eshop.dis_commis_setting"
}

func (d *DisCommisSetting) Get(session *xorm.Session, id int) (out DisCommisSettingExt, err error) {
	if session == nil {
		return out, errors.New("session is nil")
	}
	exists, err := session.Table("eshop.dis_commis_setting").Alias("a").
		Select("a.*, b.channel_path").
		Join("LEFT", "eshop.dis_customer_channel b", "a.customer_channel_id = b.id").
		Where("a.id = ?", id).
		Where("a.is_del = 0").
		Get(&out)
	if err != nil {
		return out, err
	}
	if !exists {
		return out, errors.New("佣金设置不存在")
	}
	return out, nil
}

// GetOneAnyway 获取佣金设置，不管是否删除
func (d *DisCommisSetting) GetOneAnyway(session *xorm.Session, id int) (out DisCommisSettingExt, err error) {
	if session == nil {
		return out, errors.New("session is nil")
	}
	exists, err := session.Table("eshop.dis_commis_setting").Alias("a").
		Select("a.*, b.channel_path").
		Join("LEFT", "eshop.dis_customer_channel b", "a.customer_channel_id = b.id").
		Where("a.id = ?", id).
		Get(&out)
	if err != nil {
		return out, err
	}
	if !exists {
		return out, errors.New("佣金设置不存在")
	}
	return out, nil
}

type FindDisCommisSettingPageListReq struct {
	OrgId     int `json:"org_id"`
	PageIndex int `json:"page_index"`
	PageSize  int `json:"page_size"`
}

func (d *DisCommisSetting) FindPage(session *xorm.Session, req FindDisCommisSettingPageListReq) (out []DisCommisSettingExt, total int64, err error) {
	if session == nil {
		return out, 0, errors.New("session is nil")
	}
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	total, err = session.Table("eshop.dis_commis_setting").Alias("a").
		Select("a.*, b.channel_path").
		Join("LEFT", "eshop.dis_customer_channel b", "a.customer_channel_id = b.id").
		Where("a.is_del = 0").
		Limit(req.PageSize, (req.PageIndex-1)*req.PageSize).
		OrderBy("a.id DESC").
		FindAndCount(&out)

	if err != nil {
		return out, 0, err
	}

	return out, total, nil
}
