package distribution_po

import (
	"time"

	"xorm.io/xorm"
)

type Shop struct {
	Id                    int       `json:"id" xorm:"pk autoincr not null comment('自增id') BIGINT 'id'"`
	OrgId                 int       `json:"org_id" xorm:"not null default 0 comment('所属主体id') INT 'org_id'"`
	SaasShopId            string    `json:"saas_shop_id" xorm:"default '' comment('saas店铺id') varchar(50) 'saas_shop_id'"`
	EnterpriseId          int64     `json:"enterprise_id" xorm:"not null default 0 comment('润和云店是企业R1编码即scrm_enterprise.id，宠利扫是dis_enterprise.id') BIGINT 'enterprise_id'"`
	ShopName              string    `json:"shop_name" xorm:"not null default '' comment('线上小店名称') VARCHAR(127) 'shop_name'"`
	HeadImage             string    `json:"head_image" xorm:"not null default '' comment('小店头像') VARCHAR(511) 'head_image'"`
	Welcome               string    `json:"welcome" xorm:"not null default '' comment('欢迎语') VARCHAR(255) 'welcome'"`
	OrderNum              int       `json:"order_num" xorm:"not null default 0 comment('分销订单数') INT 'order_num'"`
	Sales                 int       `json:"sales" xorm:"not null default 0 comment('分销销售额') INT 'sales'"`
	IsMain                int       `json:"is_main" xorm:"not null default 2 comment('是否主店 0-初始，1-是，2否') INT 'is_main'"`
	SettledCommission     int       `json:"settled_commission" xorm:"default 0 comment('已结佣金(分)') INT 'settled_commission'"`
	UnsettledCommission   int       `json:"unsettled_commission" xorm:"not null default 0 comment('未结佣金(分)') INT 'unsettled_commission'"`
	WithdrawSuccess       int       `json:"withdraw_success" xorm:"default 0 comment('提现成功(分)') INT 'withdraw_success'"`
	WithdrawApply         int       `json:"withdraw_apply" xorm:"default 0 comment('提现申请(分)') INT 'withdraw_apply'"`
	WaitWithdraw          int       `json:"wait_withdraw" xorm:"default 0 comment('待提现(分)') INT 'wait_withdraw'"`
	IsSettedShop          int       `json:"is_setted_shop" xorm:"not null default 2 comment('是否已经设置线上小店:0-初始，1-是，2-否') INT 'is_setted_shop'"`
	RegisteredSalesperson int64     `json:"registered_salesperson" xorm:"default 0 comment('注册业务员') INT 'registered_salesperson'"`
	CreateTime            time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime            time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	IsSettedTime          time.Time `json:"is_setted_time" xorm:"default 'null' comment('设置店铺时间') DATETIME 'is_setted_time'"`
}

func (s *Shop) TableName() string {
	return "eshop.shop"
}

// Insert 插入店铺数据
func (s *Shop) Insert(session *xorm.Session) (err error) {
	if _, err = session.Insert(s); err != nil {
		return err
	}
	return nil
}

type GetShopsReq struct {
	ShopId       int   `json:"shop_id"`
	EnterpriseId int64 `json:"enterprise_id"`
	OrgId        int   `json:"org_id"`
}

// GetShops 获取店铺信息
func (s *Shop) GetShops(session *xorm.Session, req GetShopsReq) (shops []Shop, err error) {
	shops = make([]Shop, 0)
	query := session.Table(s.TableName())
	if req.ShopId != 0 {
		query = query.Where("id = ?", req.ShopId)
	}
	if req.EnterpriseId != 0 {
		query = query.Where("enterprise_id = ?", req.EnterpriseId)
	}
	if req.OrgId != 0 {
		query = query.Where("org_id = ?", req.OrgId)
	}
	err = query.Find(&shops)
	if err != nil {
		return nil, err
	}

	return shops, nil
}
