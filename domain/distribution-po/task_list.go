package distribution_po

import (
	"time"
)

type TaskList struct {
	Id               int       `json:"id" xorm:"pk autoincr not null comment('任务id') INT 'id'"`
	TaskContent      int       `json:"task_content" xorm:"not null default 0 comment('任务内容:1:业务员导入 2:业务员导出') TINYINT(1) 'task_content'"`
	TaskStatus       int       `json:"task_status" xorm:"not null default 1 comment('任务状态:1:未开始;2:进行中;3:已完成') TINYINT(1) 'task_status'"`
	TaskDetail       string    `json:"task_detail" xorm:"not null default '' comment('任务详情') VARCHAR(10000) 'task_detail'"`
	OperationFileUrl string    `json:"operation_file_url" xorm:"not null comment('操作文件路径或者参数') TEXT 'operation_file_url'"`
	RequestHeader    string    `json:"request_header" xorm:"default 'null' comment('操作请求的token值，类似userinfo') VARCHAR(255) 'request_header'"`
	ResulteFileUrl   string    `json:"resulte_file_url" xorm:"not null default '' comment('操作结果文件路径') VARCHAR(255) 'resulte_file_url'"`
	CreateId         string    `json:"create_id" xorm:"not null default 0 comment('创建人id') VARCHAR(100) 'create_id'"`
	CreateTime       time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'create_time' created"`
	CreateName       string    `json:"create_name" xorm:"default '' comment('创建人姓名') VARCHAR(50) 'create_name'"`
	CreateMobile     string    `json:"create_mobile" xorm:"default '' comment('创建人手机号') VARCHAR(50) 'create_mobile'"`
	CreateIp         string    `json:"create_ip" xorm:"default '' comment('创建人ip') VARCHAR(100) 'create_ip'"`
	IpLocation       string    `json:"ip_location" xorm:"default '' comment('ip所属位置') VARCHAR(50) 'ip_location'"`
	SuccessNum       int       `json:"success_num" xorm:"default 0 comment('成功数量') INT 'success_num'"`
	FailNum          int       `json:"fail_num" xorm:"default 0 comment('失败数量') INT 'fail_num'"`
	ExtendedData     string    `json:"extended_data" xorm:"comment('任务名称扩展字段') TEXT 'extended_data'"`
	ContextData      string    `json:"context_data" xorm:"comment('上下文数据') LONGTEXT 'context_data'"`
	OrgId            int       `json:"org_id" xorm:"default 'null' comment('主体ID') INT 'org_id'"`
	DoCount          int       `json:"do_count" xorm:"default 'null' comment('执行失败次数') INT 'do_count'"`
	ErrMes           string    `json:"err_mes" xorm:"default 'null' comment('上次执行失败原因') VARCHAR(500) 'err_mes'"`
	UpdateTime       time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'update_time' updated"`
	OperationType    int       `json:"operation_type" xorm:"default 0 comment('操作类型 0:导出 1:导入') INT 'operation_type'"`
	OrgType          int       `json:"org_type" xorm:"default 0 comment('归属企业 1-北京百林康源 2-深圳利都') INT 'org_type'"`
}
