package distribution_po

import (
	"time"
)

type DisDistributorFans struct {
	Id          int       `json:"id" xorm:"pk autoincr not null comment('ID') INT 'id'"`
	DisId       int       `json:"dis_id" xorm:"not null comment('分销员id') INT 'dis_id'"`
	MemberId    int       `json:"member_id" xorm:"not null comment('客户户id') INT 'member_id'"`
	OrgId       int       `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	ShopId      int       `json:"shop_id" xorm:"default 0 comment('分销店铺') INT 'shop_id'"`
	OrderAmount int       `json:"order_amount" xorm:"default 0 comment('订单金额(分)') INT 'order_amount'"`
	OrderNum    int       `json:"order_num" xorm:"default 0 comment('订单数') INT 'order_num'"`
	ExpireTime  time.Time `json:"expire_time" xorm:"default 'null' comment('过期时间') DATETIME 'expire_time'"`
	CreateTime  time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime  time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
}

//
//func (t *DisDistributorFans) Get() (*DisDistributorFans, error) {
//	ok, err := GlobalEngine.Get(t)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans get Error:%v", err)
//		log.Error(err)
//		return nil, err
//	}
//	if ok {
//		return t, nil
//	}
//	return nil, errors.New("DisDistributorFans not exists")
//}
//
//func (t *DisDistributorFans) GetList() ([]*DisDistributorFans, error) {
//	var CodeInfo []*DisDistributorFans
//	var err error
//	err = GlobalEngine.Find(&CodeInfo, t)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans get list Error:%v", err)
//		log.Error(err)
//		return nil, err
//	}
//	return CodeInfo, err
//}
//
//func (t *DisDistributorFans) Delete() (err error) {
//	affected, err := GlobalEngine.Delete(t)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans delete Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	log.Infof("DisDistributorFans delete id:%v, affect:%v", t.Id, affected)
//	return nil
//}
//
//func (t *DisDistributorFans) DeleteWithSession(session *xorm.Session) (err error) {
//	affected, err := session.Delete(t)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans delete Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	log.Infof("DisDistributorFans delete id:%v, affect:%v", t.Id, affected)
//	return nil
//}
//
//func (t *DisDistributorFans) Insert() (err error) {
//	_, err = GlobalEngine.Insert(t)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans insert Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *DisDistributorFans) InsertWithSession(session *xorm.Session) (err error) {
//	_, err = session.Insert(t)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans insert Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *DisDistributorFans) Modify() (err error) {
//	if t.Id == 0 {
//		err = errors.New("DisDistributorFans modify Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//	_, err = GlobalEngine.ID(t.Id).Update(t)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans modify Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *DisDistributorFans) ModifyWithSession(session *xorm.Session) (err error) {
//	if t.Id == 0 {
//		err = errors.New("DisDistributorFans modify with session Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//	_, err = session.ID(t.Id).Update(t)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans modify with session Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *DisDistributorFans) SetDisId(val int) (err error) {
//	if t.Id == 0 {
//		err = errors.New("DisDistributorFans set DisId Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//
//	tp := new(DisDistributorFans)
//	tp.DisId = val
//	_, err = GlobalEngine.ID(t.Id).Cols("dis_id").Update(tp)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans set DisId Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *DisDistributorFans) SetMemberId(val int) (err error) {
//	if t.Id == 0 {
//		err = errors.New("DisDistributorFans set MemberId Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//
//	tp := new(DisDistributorFans)
//	tp.MemberId = val
//	_, err = GlobalEngine.ID(t.Id).Cols("member_id").Update(tp)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans set MemberId Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *DisDistributorFans) SetOrgId(val int) (err error) {
//	if t.Id == 0 {
//		err = errors.New("DisDistributorFans set OrgId Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//
//	tp := new(DisDistributorFans)
//	tp.OrgId = val
//	_, err = GlobalEngine.ID(t.Id).Cols("org_id").Update(tp)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans set OrgId Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *DisDistributorFans) SetShopId(val int) (err error) {
//	if t.Id == 0 {
//		err = errors.New("DisDistributorFans set ShopId Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//
//	tp := new(DisDistributorFans)
//	tp.ShopId = val
//	_, err = GlobalEngine.ID(t.Id).Cols("shop_id").Update(tp)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans set ShopId Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *DisDistributorFans) SetOrderAmount(val int) (err error) {
//	if t.Id == 0 {
//		err = errors.New("DisDistributorFans set OrderAmount Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//
//	tp := new(DisDistributorFans)
//	tp.OrderAmount = val
//	_, err = GlobalEngine.ID(t.Id).Cols("order_amount").Update(tp)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans set OrderAmount Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *DisDistributorFans) SetOrderNum(val int) (err error) {
//	if t.Id == 0 {
//		err = errors.New("DisDistributorFans set OrderNum Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//
//	tp := new(DisDistributorFans)
//	tp.OrderNum = val
//	_, err = GlobalEngine.ID(t.Id).Cols("order_num").Update(tp)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans set OrderNum Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *DisDistributorFans) SetExpireTime(val time.Time) (err error) {
//	if t.Id == 0 {
//		err = errors.New("DisDistributorFans set ExpireTime Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//
//	tp := new(DisDistributorFans)
//	tp.ExpireTime = val
//	_, err = GlobalEngine.ID(t.Id).Cols("expire_time").Update(tp)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans set ExpireTime Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *DisDistributorFans) SetCreateTime(val time.Time) (err error) {
//	if t.Id == 0 {
//		err = errors.New("DisDistributorFans set CreateTime Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//
//	tp := new(DisDistributorFans)
//	tp.CreateTime = val
//	_, err = GlobalEngine.ID(t.Id).Cols("create_time").Update(tp)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans set CreateTime Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *DisDistributorFans) SetUpdateTime(val time.Time) (err error) {
//	if t.Id == 0 {
//		err = errors.New("DisDistributorFans set UpdateTime Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//
//	tp := new(DisDistributorFans)
//	tp.UpdateTime = val
//	_, err = GlobalEngine.ID(t.Id).Cols("update_time").Update(tp)
//	if err != nil {
//		err = fmt.Errorf("DisDistributorFans set UpdateTime Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
