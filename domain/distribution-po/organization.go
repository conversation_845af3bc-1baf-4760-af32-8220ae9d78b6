package distribution_po

import "time"

type OrganizationInfo struct {
	Id                int       `json:"id" xorm:"pk autoincr not null comment('主体主键') INT 'id'"`
	OrgName           string    `json:"org_name" xorm:"default 'null' comment('主体名称') VARCHAR(50) 'org_name'"`
	DsOrgId           string    `json:"ds_org_id" xorm:"default 'null' comment('电商主体ID标识') VARCHAR(50) 'ds_org_id'"`
	ZlOrgId           string    `json:"zl_org_id" xorm:"default 'null' comment('子龙主体ID标识') VARCHAR(50) 'zl_org_id'"`
	IsUse             int       `json:"is_use" xorm:"default 0 comment('0：未启用 1：启用') INT 'is_use'"`
	CreateTime        time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime        time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'update_time'"`
	WxAppid           string    `json:"wx_appid" xorm:"default '' comment('小程序appid') VARCHAR(50) 'wx_appid'"`
	MiniProgramMchId  string    `json:"mini_program_mch_id" xorm:"default '' comment('小程序实物电银商户号') VARCHAR(50) 'mini_program_mch_id'"`
	MiniVProgramMchId string    `json:"mini_v_program_mch_id" xorm:"default '' comment('小程序虚拟电银商户号') VARCHAR(50) 'mini_v_program_mch_id'"`
	AppMchId          string    `json:"app_mch_id" xorm:"default '' comment('App实物商户号') VARCHAR(50) 'app_mch_id'"`
	AppVMchId         string    `json:"app_v_mch_id" xorm:"default '' comment('App虚拟商户号') VARCHAR(50) 'app_v_mch_id'"`
}
