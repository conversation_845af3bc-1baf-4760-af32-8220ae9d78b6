package petai_po

import (
	omnibus_po "eShop/domain/omnibus-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"errors"
	"fmt"
	"time"

	"xorm.io/xorm"
)

const (
	IsAuthorizedYes    = 1 // 已授权
	IsAuthorizedNo     = 0 // 未授权
	IsAuthorizedRefuse = 2 // 拒绝
)

// eshop.user_info 用户信息
type EshopUserInfo struct {
	Id         int    `xorm:"not null pk autoincr INT(10)"`
	UserInfoId string `xorm:"not null unique VARCHAR(50)"`
	// 用户ID作为唯一标示
	UserId string
	// 用户IM唯一标示
	UserImId string
	// im用户token密码
	UserImToken string
	// 用户姓名
	UserName string
	// 用户性别 0未知 1男 2女
	UserSex int
	// 用户加*手机号
	UserMobile string
	// 用户加密手机号
	EncryptUserMobile string
	// 用户来源 0阿闻小程序 1000子龙 2000百度 3000保险 4000新宠
	UserSource int
	// 用户状态 0正常 1锁定 -1删除
	UserStatus int
	// 用户头像
	UserAvatar string
	// 用户生日
	UserBirthday time.Time
	// 初次养宠时间
	FirstRaisesPet time.Time
	// 用户所在国家
	Country string
	// 用户所在省份
	Province string
	// 用户所在城市
	City string
	// 用户所在区域
	Area string
	// 用户备注
	UserRemark string
	// 创建时间
	CreateTime string `xorm:"<-"`
	// 更新时间
	UpdateTime        string `xorm:"<-"`
	BigdataUserId     string //大数据唯一Id
	RemoteTreatStatus int
	IsReal            int
	// 备用姓名1
	BackupName1 string
	// 备用手机号1
	BackupMobile1 string
	// 备用姓名2
	BackupName2 string
	// 备用手机号2
	BackupMobile2   string
	ChangeTimes     int
	ModifyPhoneDate time.Time
	// 会员等级
	LevelId string
	// 用户所在国家编码
	CountryCode string
	// 用户所在省份编码
	ProvinceCode string
	// 用户所在城市编码
	CityCode string
	// 用户所在区域编码
	AreaCode string
	//所属主体id'
	OrgId int
	//用户是否授权使用阿闻数据，0-否，1-是 ,2-拒绝
	IsAuthorized     int
	WeixinUnionid    string // 微信用户统一标识
	WeixinMiniOpenid string // 小程序openid
}

func (s *EshopUserInfo) TableName() string {
	return "eshop.user_info"
}

func (dto *EshopUserInfo) ToMemberInfoData(t *omnibus_po.TScrmUserInfo) {

	if t.UserBirthday != "" {
		dto.UserBirthday, _ = time.ParseInLocation("2006-01-02 15:04:05", t.UserBirthday, time.Local)
	}
	if t.FirstRaisesPet != "" {
		dto.FirstRaisesPet, _ = time.ParseInLocation("2006-01-02 15:04:05", t.FirstRaisesPet, time.Local)
	}
	if t.ModifyPhoneDate != "" {
		dto.ModifyPhoneDate, _ = time.ParseInLocation("2006-01-02 15:04:05", t.ModifyPhoneDate, time.Local)
	}

	dto.UserId = t.UserId
	dto.UserImId = t.UserImId
	dto.UserImToken = t.UserImToken
	dto.UserName = t.UserName

	dto.UserSex = t.UserSex
	dto.UserMobile = utils.AddStar(t.UserMobile)
	dto.EncryptUserMobile = utils.MobileEncrypt(t.UserMobile)
	dto.UserSource = t.UserSource
	dto.UserStatus = t.UserStatus

	dto.UserAvatar = t.UserAvatar

	dto.City = t.City
	dto.Country = t.Country
	dto.Province = t.Province
	dto.Area = t.Area

	dto.UserRemark = t.UserRemark
	dto.CreateTime = t.CreateTime
	dto.UpdateTime = t.UpdateTime
	dto.BigdataUserId = t.BigdataUserId
	dto.IsReal = t.IsReal
	dto.RemoteTreatStatus = t.RemoteTreatStatus
	dto.BackupName1 = t.BackupName1
	dto.BackupMobile1 = t.BackupMobile1
	dto.BackupName2 = t.BackupName2
	dto.BackupMobile2 = t.BackupMobile2
	dto.ChangeTimes = t.ChangeTimes
	dto.LevelId = t.LevelId
	dto.CountryCode = t.CountryCode
	dto.ProvinceCode = t.ProvinceCode
	dto.CityCode = t.CityCode
	dto.AreaCode = t.AreaCode

}

// 查询用户信息
func (s *EshopUserInfo) GetUserInfoByUserId(session *xorm.Session, userInfoId string) (out *EshopUserInfo, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	out = &EshopUserInfo{}
	if _, err := session.Table(s.TableName()).Where("user_info_id = ?", userInfoId).Get(out); err != nil {
		log.Errorf("查询eshop用户信息失败: %v", err)
		return nil, err
	}
	return
}

// 更新用户信息
func (s *EshopUserInfo) UpdateUserInfo(session *xorm.Session) (err error) {
	logPrefix := fmt.Sprintf("====更新eshop用户信息,入参为%s", utils.JsonEncode(s))
	log.Info(logPrefix)
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	if s == nil {
		err = errors.New("in is nil")
		return
	}
	if len(s.UserInfoId) == 0 {
		err = errors.New("UserInfoId is nil")
		return
	}
	if _, err = session.Table(s.TableName()).Where("user_info_id =?", s.UserInfoId).Update(s); err != nil {
		log.Errorf("%s 更新eshop用户信息失败: %v", logPrefix, err)
		return
	}
	return
}

// 授权scrm用户数据 到 小闻养宠助手
type AuthDataReq struct {
	UserInfoId string
}

func (s *EshopUserInfo) AuthData(session *xorm.Session, in AuthDataReq) (err error) {
	logPrefix := fmt.Sprintf("====用户同意授权scrm用户数据 到 小闻养宠助手,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	// 查询用户信息
	eshopUserInfo, err := s.GetUserInfoByUserId(session, in.UserInfoId)
	if err != nil {
		log.Error(logPrefix + fmt.Sprintf("查询eshop用户信息失败,err:%s", err.Error()))
		err = errors.New("查询用户信息失败")
		return
	}
	if eshopUserInfo == nil || len(eshopUserInfo.UserInfoId) == 0 {
		log.Error(logPrefix + fmt.Sprintf("用户信息不存在,userInfoId:%s", in.UserInfoId))
		err = errors.New("用户信息不存在")
		return
	}
	// 查询scrm用户信息
	scrmUser, err := new(omnibus_po.TScrmUserInfo).GetUserByMobile(session, eshopUserInfo.EncryptUserMobile)
	if err != nil {
		log.Error(logPrefix + fmt.Sprintf("查询scrm用户信息失败,err:%s", err.Error()))
		err = errors.New("查询scrm用户信息失败")
		return
	}
	if scrmUser == nil || scrmUser.Id == 0 {
		return
	}

	eshopUserInfo.ToMemberInfoData(scrmUser)
	log.Info(logPrefix + fmt.Sprintf("授权用户信息,eshopUserInfo:%s", utils.JsonEncode(eshopUserInfo)))
	if err = eshopUserInfo.UpdateUserInfo(session); err != nil {
		log.Error(logPrefix + fmt.Sprintf("更新用户信息失败,err:%s", err.Error()))
		err = errors.New("授权用户信息失败")
		return
	}
	return
}
