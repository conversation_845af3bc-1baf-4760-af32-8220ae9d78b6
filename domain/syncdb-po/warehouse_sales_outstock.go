package syncdb_po

type WarehouseSalesOutstock struct {
	// 主键ID
	Id int64 `json:"id" xorm:"pk autoincr 'id'"`
	// 租户no
	TenantNo int `json:"tenant_no" xorm:"'tenant_no'"`
	// 状态（0：待审核，1审核通过待出库，2：已出库，3：审核拒绝，4：部分出库，5：已到货，6：部分到货 7:已取消 9:待提交）
	Status int `json:"status" xorm:"'status'"`
	// 出货单号
	Code string `json:"code" xorm:"'code'"`
	// 客户id
	CustomerId int `json:"customer_id" xorm:"'customer_id'"`
	// 客户编码
	CustomerCode string `json:"customer_code" xorm:"'customer_code'"`
	// 客户渠道
	CustomerChannel int `json:"customer_channel" xorm:"'customer_channel'"`
	// 客户名称
	CustomerName string `json:"customer_name" xorm:"'customer_name'"`
	// 组织id
	OrgId int `json:"org_id" xorm:"'org_id'"`
	// 组织编码
	OrgCode string `json:"org_code" xorm:"'org_code'"`
	// 组织名称
	OrgName string `json:"org_name" xorm:"'org_name'"`
	// 采购仓id (机构类型为仓库的机构id)
	WarehouseId int `json:"warehouse_id" xorm:"'warehouse_id'"`
	// 仓库编码
	WarehouseCode string `json:"warehouse_code" xorm:"'warehouse_code'"`
	// 仓库名称
	WarehouseName string `json:"warehouse_name" xorm:"'warehouse_name'"`
}

func (w *WarehouseSalesOutstock) TableName() string {
	return "syncdb.warehouse_sales_outstock"
}

// 根据出货单号查询出货单,判断是一级渠道还是二级渠道:customerChannel 客户渠道;level 渠道等级：1一级渠道，2二级渠道
// func (w *WarehouseSalesOutstock) GetByCode(session *xorm.Session, code string) (customerChannel int, level int, err error) {
// 	logPrefix := fmt.Sprintf("根据出货单号 获取客户渠道和渠道等级.出货单号:%s", code)
// 	log.Info(logPrefix)
// 	data := WarehouseSalesOutstock{}
// 	if _, err = session.Where("code = ?", code).Get(&data); err != nil {
// 		log.Error(logPrefix, "查询失败:", err)
// 		return
// 	}
// 	log.Info(logPrefix, "查询成功:", data)
// 	customerChannel = data.CustomerChannel
// 	if utils.IsProEnv() {
// 		if customerChannel > 0 && data.OrgName == "深圳市利都发展有限公司" && data.WarehouseName == "临沂贵族总仓" {
// 			level = 1
// 		} else {
// 			level = 2
// 		}
// 	} else {
// 		if customerChannel > 0 && data.OrgName == "深圳市利都发展有限公司" && data.WarehouseName == "利都仓库1" {
// 			level = 1
// 		} else {
// 			level = 2
// 		}
// 	}

// 	return
// }
